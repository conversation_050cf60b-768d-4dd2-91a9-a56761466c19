import { X } from "lucide-react";
import React from "react";

const DuoConfirmModal = ({ confirmText, setModalClose, setFormYes }) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    setFormYes(true);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => setModalClose(false)}
      />
      <div
        className="inline-block transform overflow-hidden rounded-lg bg-boxdark text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-headline"
      >
        <form>
          <div className="flex w-full items-center justify-between border-b border-strokedark bg-boxdark px-6 py-4">
            <h3
              className="text-xl font-semibold text-white"
              id="modal-headline"
            >
              Confirm Action
            </h3>
            <button
              type="button"
              onClick={() => setModalClose(false)}
              className="rounded-full p-1 transition-colors hover:bg-meta-4"
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>

          <div className="p-6">
            <div className="w-full">
              <p className="text-lg text-white">{confirmText}</p>
            </div>
          </div>

          <div className="flex justify-end gap-4 border-t border-strokedark bg-boxdark px-6 py-4">
            <button
              type="button"
              className="hover:bg-danger/40/90 inline-flex items-center justify-center rounded-md bg-danger px-6 py-2.5 text-center font-medium text-white transition-colors"
              onClick={() => setModalClose(false)}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white transition-colors hover:bg-primary/90"
            >
              Confirm
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DuoConfirmModal;
