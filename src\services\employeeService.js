import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const retrieveAllEmployeeAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/employee/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const addEmployeeAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/employee/add`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getEmployeeDetailsAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/employee/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const updateEmployeeAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/employee/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllEmployeeByGroupAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/employee/retrieve/group`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteEmployeeAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/employee/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'DELETE');
    return res;
  } catch (error) {
    return error;
  }
};
