export const masterData = [
  {
    id: 1,
    user_id: 1,
    create_at: '2023-05-30',
    update_at: '2023-06-09T13:59:32.000Z',
    client_id: 1,
    mix_season_id: 1,
    mix_date: '2023-08-12',
    team_name: 'Test Team Name',
    mix_type_id: 1,
    team_type: 2,
    division: 'Test Division',
    colors: 'red',
    discount: '5',
    content_status: 'Pending',
    program_name: 'Test Program Name',
    program_owner_name: '<PERSON> test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Test Mix Type',
    subprojects: [
      {
        id: 1,
        type_name: 'Voiceover 1',
        project_id: 1,
        workorder_id: 1,
        lyrics:
          "Yellow diamonds in the light\r\nNow we're standing side by side\r\nAs your shadow crosses mine\r\nWhat it takes to come alive\r\nTest 123",
        eight_count: 4,
        status: 0,
        create_at: '2023-06-02',
        update_at: '2023-07-21T14:41:52.000Z',
        team_name: 'Test Team Name',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 1,
            subproject_id: 1,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-06-02',
            update_at: '2023-06-02T19:10:03.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
          {
            id: 12,
            subproject_id: 1,
            employee_id: 8,
            employee_type: 'writer',
            employee_cost: 15,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:05:03.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 13,
            subproject_id: 1,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:05:41.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 2,
        type_name: 'Voiceover 2',
        project_id: 1,
        workorder_id: 1,
        lyrics:
          'I would never fall in love again until I found her\r\nI said, "I would never fall unless it\'s you I fall into"\r\nI was lost within the darkness, but then I found her\r\nI found you',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-07',
        update_at: '2023-06-15T18:46:10.000Z',
        team_name: 'Test Team Name',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 14,
            subproject_id: 2,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:05:43.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 15,
            subproject_id: 2,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:05:46.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 16,
            subproject_id: 2,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:07:36.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 5,
        type_name: 'Voiceover 3',
        project_id: 1,
        workorder_id: 1,
        lyrics:
          "Lord, I'm one, Lord, I'm two\r\nLord, I'm three, Lord, I'm four\r\nLord, I'm five hundred miles away from home",
        eight_count: 4,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-15T13:19:40.000Z',
        team_name: 'Test Team Name',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 17,
            subproject_id: 5,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:08:56.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 18,
            subproject_id: 5,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:09:14.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 19,
            subproject_id: 5,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:09:34.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 6,
        type_name: 'Song 1',
        project_id: 1,
        workorder_id: 1,
        lyrics:
          "Away from home, away from home\r\nAway from home, away from home\r\nLord, I'm five hundred miles away from home",
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-16T17:13:47.000Z',
        team_name: 'Test Team Name',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 20,
            subproject_id: 6,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:10:02.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 21,
            subproject_id: 6,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:10:27.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 22,
            subproject_id: 6,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:10:47.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 7,
        type_name: 'Song 2',
        project_id: 1,
        workorder_id: 1,
        lyrics:
          "Not a shirt on my back\r\nNot a penny to my name\r\nLord, I can't go back home this ole way",
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-16T16:28:47.000Z',
        team_name: 'Test Team Name',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 23,
            subproject_id: 7,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:11:07.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 24,
            subproject_id: 7,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:11:25.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 25,
            subproject_id: 7,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-06-14',
            update_at: '2023-06-14T16:11:39.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 8,
        type_name: 'Song 3',
        project_id: 1,
        workorder_id: 1,
        lyrics:
          "This ole way, this ole way\r\nThis ole way, this ole way\r\nLord, I can't go back home this this ole way",
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-07-26T21:52:03.000Z',
        team_name: 'Test Team Name',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 7,
            subproject_id: 8,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-13',
            update_at: '2023-06-13T18:10:57.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 8,
            subproject_id: 8,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-13',
            update_at: '2023-06-13T18:10:57.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 9,
            subproject_id: 8,
            employee_id: 8,
            employee_type: 'engineer',
            employee_cost: 30,
            create_at: '2023-06-13',
            update_at: '2023-06-13T18:10:57.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 103,
        type_name: 'Voiceover 4',
        project_id: 1,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-08-08',
        update_at: '2023-08-08T23:48:18.000Z',
        team_name: 'Test Team Name',
        program_name: 'Test Program Name',
        employees: [],
      },
    ],
  },
  {
    id: 2,
    user_id: 1,
    create_at: '2023-06-06',
    update_at: '2023-06-06T17:47:34.000Z',
    client_id: 2,
    mix_season_id: 1,
    mix_date: '2023-07-12',
    team_name: 'Team 2',
    mix_type_id: 1,
    team_type: 1,
    division: 'division 2',
    colors: 'green',
    discount: '40',
    content_status: 'Pending',
    program_name: 'Program 2',
    program_owner_name: 'dani carvajal test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Test Mix Type',
    subprojects: [
      {
        id: 29,
        type_name: 'Voiceover 1',
        project_id: 2,
        workorder_id: null,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-07-03',
        update_at: '2023-07-14T04:26:32.000Z',
        team_name: 'Team 2',
        program_name: 'Program 2',
        employees: [
          {
            id: 47,
            subproject_id: 29,
            employee_id: 8,
            employee_type: 'writer',
            employee_cost: 15,
            create_at: '2023-07-09',
            update_at: '2023-07-09T21:37:32.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 48,
            subproject_id: 29,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-09',
            update_at: '2023-07-09T21:37:37.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 52,
        type_name: 'Tracking 1',
        project_id: 2,
        workorder_id: null,
        lyrics: null,
        eight_count: null,
        status: 0,
        create_at: '2023-07-14',
        update_at: '2023-07-14T20:39:32.000Z',
        team_name: 'Team 2',
        program_name: 'Program 2',
        employees: [
          {
            id: 56,
            subproject_id: 52,
            employee_id: 10,
            employee_type: 'producer',
            employee_cost: 500,
            create_at: '2023-07-14',
            update_at: '2023-07-14T20:39:32.000Z',
            name: 'Florentino Perez',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: 1,
            producer_cost: '500',
          },
        ],
      },
      {
        id: 54,
        type_name: 'Voiceover 2',
        project_id: 2,
        workorder_id: 10,
        lyrics: null,
        eight_count: null,
        status: 0,
        create_at: '2023-07-14',
        update_at: '2023-07-14T21:50:15.000Z',
        team_name: 'Team 2',
        program_name: 'Program 2',
        employees: [
          {
            id: 130,
            subproject_id: 54,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 131,
            subproject_id: 54,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 132,
            subproject_id: 54,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 200,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 56,
        type_name: 'Song 1',
        project_id: 2,
        workorder_id: 11,
        lyrics: null,
        eight_count: null,
        status: 0,
        create_at: '2023-07-14',
        update_at: '2023-07-14T21:50:53.000Z',
        team_name: 'Team 2',
        program_name: 'Program 2',
        employees: [
          {
            id: 145,
            subproject_id: 56,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 146,
            subproject_id: 56,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 147,
            subproject_id: 56,
            employee_id: 5,
            employee_type: 'engineer',
            employee_cost: 55,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
    ],
  },
  {
    id: 3,
    user_id: 1,
    create_at: '2023-06-08',
    update_at: '2023-07-03T18:22:44.000Z',
    client_id: 3,
    mix_season_id: 1,
    mix_date: '2023-07-03',
    team_name: 'Test Team',
    mix_type_id: 4,
    team_type: 2,
    division: 'A division',
    colors: 'blue',
    discount: '100',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Rumman mix',
    subprojects: [
      {
        id: 27,
        type_name: 'Song 1',
        project_id: 3,
        workorder_id: null,
        lyrics: 'Hello',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-29',
        update_at: '2023-07-28T19:10:53.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 87,
            subproject_id: 27,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 800,
            create_at: '2023-07-24',
            update_at: '2023-07-24T20:28:42.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 88,
            subproject_id: 27,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-24',
            update_at: '2023-07-24T20:28:53.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 45,
        type_name: 'Tracking 1',
        project_id: 3,
        workorder_id: null,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-07-09',
        update_at: '2023-08-11T14:27:03.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 213,
            subproject_id: 45,
            employee_id: 10,
            employee_type: 'producer',
            employee_cost: 500,
            create_at: '2023-08-11',
            update_at: '2023-08-11T14:27:03.000Z',
            name: 'Florentino Perez',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: 1,
            producer_cost: '500',
          },
        ],
      },
      {
        id: 75,
        type_name: 'Voiceover 1',
        project_id: 3,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-28',
        update_at: '2023-07-28T19:12:15.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [],
      },
    ],
  },
  {
    id: 4,
    user_id: 1,
    create_at: '2023-06-08',
    update_at: '2023-06-08T19:20:06.000Z',
    client_id: 3,
    mix_season_id: 1,
    mix_date: '2023-06-15',
    team_name: 'Test Team',
    mix_type_id: 4,
    team_type: 2,
    division: 'A division',
    colors: 'yellow',
    discount: '0',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Rumman mix',
    subprojects: [
      {
        id: 3,
        type_name: 'Voiceover 1',
        project_id: 4,
        workorder_id: null,
        lyrics: 'Look at the stars\nLook how they shine for you\ntest',
        eight_count: 2,
        status: 0,
        create_at: '2023-06-08',
        update_at: '2023-08-11T14:36:57.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 2,
            subproject_id: 3,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-08',
            update_at: '2023-06-08T21:08:42.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 3,
            subproject_id: 3,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-08',
            update_at: '2023-06-08T21:08:42.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 4,
            subproject_id: 3,
            employee_id: 5,
            employee_type: 'engineer',
            employee_cost: 55,
            create_at: '2023-06-08',
            update_at: '2023-06-08T21:08:42.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 4,
        type_name: 'Song 1',
        project_id: 4,
        workorder_id: null,
        lyrics: 'And everything you do\nYeah, they were all yellow',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-08',
        update_at: '2023-08-02T19:04:22.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 41,
            subproject_id: 4,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 0,
            create_at: '2023-07-07',
            update_at: '2023-07-07T18:08:43.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 42,
            subproject_id: 4,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-07',
            update_at: '2023-07-07T18:25:09.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 44,
        type_name: 'Tracking 1',
        project_id: 4,
        workorder_id: null,
        lyrics: '',
        eight_count: 7,
        status: 0,
        create_at: '2023-07-07',
        update_at: '2023-08-07T17:26:50.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 43,
            subproject_id: 44,
            employee_id: 10,
            employee_type: 'producer',
            employee_cost: 0,
            create_at: '2023-07-07',
            update_at: '2023-07-07T19:25:39.000Z',
            name: 'Florentino Perez',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: 1,
            producer_cost: '500',
          },
        ],
      },
      {
        id: 67,
        type_name: 'Voiceover 2',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-29T16:47:51.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 64,
            subproject_id: 67,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-17',
            update_at: '2023-07-17T14:22:33.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 83,
        type_name: 'Tracking 2',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 6,
        status: 0,
        create_at: '2023-07-31',
        update_at: '2023-07-31T20:48:21.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 101,
            subproject_id: 83,
            employee_id: 11,
            employee_type: 'producer',
            employee_cost: 250,
            create_at: '2023-07-31',
            update_at: '2023-07-31T20:48:21.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 87,
        type_name: 'Song 2',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T19:54:02.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 154,
            subproject_id: 87,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: null,
            create_at: '2023-08-04',
            update_at: '2023-08-04T19:54:02.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 155,
            subproject_id: 87,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: null,
            create_at: '2023-08-04',
            update_at: '2023-08-04T19:54:02.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 88,
        type_name: 'Voiceover 3',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T19:54:22.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 156,
            subproject_id: 88,
            employee_id: 8,
            employee_type: 'writer',
            employee_cost: null,
            create_at: '2023-08-04',
            update_at: '2023-08-04T19:54:22.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 157,
            subproject_id: 88,
            employee_id: 11,
            employee_type: 'artist',
            employee_cost: null,
            create_at: '2023-08-04',
            update_at: '2023-08-04T19:54:22.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 89,
        type_name: 'Song 3',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T20:35:23.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 182,
            subproject_id: 89,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: null,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:35:23.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 183,
            subproject_id: 89,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: null,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:35:23.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 100,
        type_name: 'Song 4',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-08-07',
        update_at: '2023-08-07T17:42:05.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 210,
            subproject_id: 100,
            employee_id: 8,
            employee_type: 'writer',
            employee_cost: null,
            create_at: '2023-08-07',
            update_at: '2023-08-07T17:42:05.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 211,
            subproject_id: 100,
            employee_id: 12,
            employee_type: 'artist',
            employee_cost: null,
            create_at: '2023-08-07',
            update_at: '2023-08-07T17:42:05.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
        ],
      },
      {
        id: 101,
        type_name: 'Tracking 3',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 6,
        status: 0,
        create_at: '2023-08-07',
        update_at: '2023-08-07T17:44:43.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 212,
            subproject_id: 101,
            employee_id: 11,
            employee_type: 'producer',
            employee_cost: null,
            create_at: '2023-08-07',
            update_at: '2023-08-07T17:44:43.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 104,
        type_name: 'Voiceover 4',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-08-11',
        update_at: '2023-08-11T14:35:57.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [],
      },
      {
        id: 105,
        type_name: 'Voiceover 5',
        project_id: 4,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-08-11',
        update_at: '2023-08-11T14:36:09.000Z',
        team_name: 'Test Team',
        program_name: 'EQ Program',
        employees: [],
      },
    ],
  },
  {
    id: 5,
    user_id: 1,
    create_at: '2023-06-09',
    update_at: '2023-06-09T13:28:03.000Z',
    client_id: 1,
    mix_season_id: 1,
    mix_date: '2023-07-12',
    team_name: 'team name 21123',
    mix_type_id: 4,
    team_type: 1,
    division: 'test division 213',
    colors: 'red',
    discount: '0',
    content_status: 'Pending',
    program_name: 'Test Program Name',
    program_owner_name: 'John Doe test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Rumman mix',
    subprojects: [],
  },
  {
    id: 6,
    user_id: 1,
    create_at: '2023-06-09',
    update_at: '2023-07-05T14:03:58.000Z',
    client_id: 1,
    mix_season_id: 1,
    mix_date: '2023-07-07',
    team_name: 'test 1231',
    mix_type_id: 1,
    team_type: 2,
    division: 'test 123234',
    colors: 'green',
    discount: '0',
    content_status: 'Pending',
    program_name: 'Test Program Name',
    program_owner_name: 'John Doe test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Test Mix Type',
    subprojects: [
      {
        id: 47,
        type_name: 'Tracking 1',
        project_id: 6,
        workorder_id: 9,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-07-12',
        update_at: '2023-07-12T00:06:47.000Z',
        team_name: 'test 1231',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 106,
            subproject_id: 47,
            employee_id: 8,
            employee_type: 'writer',
            employee_cost: 15,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 107,
            subproject_id: 47,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 108,
            subproject_id: 47,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 300,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
    ],
  },
  {
    id: 7,
    user_id: 1,
    create_at: '2023-06-09',
    update_at: '2023-06-09T18:46:46.000Z',
    client_id: 2,
    mix_season_id: 1,
    mix_date: '2023-10-08',
    team_name: 'test team name something',
    mix_type_id: 1,
    team_type: 2,
    division: 'test division 1231',
    colors: 'blue',
    discount: '0',
    content_status: 'Pending',
    program_name: 'Program 2',
    program_owner_name: 'dani carvajal test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Test Mix Type',
    subprojects: [],
  },
  {
    id: 8,
    user_id: 1,
    create_at: '2023-06-13',
    update_at: '2023-06-26T16:37:01.000Z',
    client_id: 2,
    mix_season_id: 1,
    mix_date: '2023-06-30',
    team_name: 'test 123123123',
    mix_type_id: 2,
    team_type: 2,
    division: 'test division',
    colors: 'yellow',
    discount: '0',
    content_status: 'Pending',
    program_name: 'Program 2',
    program_owner_name: 'dani carvajal test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'mix 3',
    subprojects: [
      {
        id: 9,
        type_name: 'Voiceover 1',
        project_id: 8,
        workorder_id: null,
        lyrics:
          'سر کیے یہ پہاڑ\r\nدریاؤں کی گہرائیوں میں تجھے ڈھونڈا ہے\r\nآ بھی جا ایک بار\r\nمیرے یار، ایسے نہ لوٹو میرے من کا قرار',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-07-24T21:48:43.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 10,
            subproject_id: 9,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-14',
            update_at: '2023-06-14T02:37:36.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 11,
            subproject_id: 9,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-06-14',
            update_at: '2023-06-14T02:37:36.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 10,
        type_name: 'Voiceover 2',
        project_id: 8,
        workorder_id: 10,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-13T18:46:10.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 118,
            subproject_id: 10,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 119,
            subproject_id: 10,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 120,
            subproject_id: 10,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 200,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 11,
        type_name: 'Voiceover 3',
        project_id: 8,
        workorder_id: 6,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-07-17T14:33:33.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 32,
            subproject_id: 11,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 49,
            create_at: '2023-06-14',
            update_at: '2023-06-14T20:01:56.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 33,
            subproject_id: 11,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-14',
            update_at: '2023-06-14T20:01:56.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 34,
            subproject_id: 11,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-06-14',
            update_at: '2023-06-14T20:01:56.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 12,
        type_name: 'Voiceover 4',
        project_id: 8,
        workorder_id: 6,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-13T18:46:10.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 35,
            subproject_id: 12,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-06-14',
            update_at: '2023-06-14T20:01:56.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 36,
            subproject_id: 12,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-14',
            update_at: '2023-06-14T20:01:56.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 37,
            subproject_id: 12,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-06-14',
            update_at: '2023-06-14T20:01:56.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 13,
        type_name: 'Song 1',
        project_id: 8,
        workorder_id: 8,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-13T18:46:10.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 95,
            subproject_id: 13,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 96,
            subproject_id: 13,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 97,
            subproject_id: 13,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 14,
        type_name: 'Song 2',
        project_id: 8,
        workorder_id: 9,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-13T18:46:10.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 112,
            subproject_id: 14,
            employee_id: 8,
            employee_type: 'writer',
            employee_cost: 15,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 113,
            subproject_id: 14,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 114,
            subproject_id: 14,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 300,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 15,
        type_name: 'Song 3',
        project_id: 8,
        workorder_id: 10,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-13T18:46:10.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 121,
            subproject_id: 15,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 122,
            subproject_id: 15,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 123,
            subproject_id: 15,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 200,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 16,
        type_name: 'Song 4',
        project_id: 8,
        workorder_id: 10,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-13T18:46:10.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 127,
            subproject_id: 16,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 128,
            subproject_id: 16,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 129,
            subproject_id: 16,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 200,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 17,
        type_name: 'Song 5',
        project_id: 8,
        workorder_id: 10,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-13',
        update_at: '2023-06-13T18:46:10.000Z',
        team_name: 'test 123123123',
        program_name: 'Program 2',
        employees: [
          {
            id: 124,
            subproject_id: 17,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 125,
            subproject_id: 17,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 126,
            subproject_id: 17,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 200,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
    ],
  },
  {
    id: 9,
    user_id: 1,
    create_at: '2023-06-14',
    update_at: '2023-06-26T16:36:43.000Z',
    client_id: 3,
    mix_season_id: 1,
    mix_date: '2023-06-28',
    team_name: 'Rumman Team',
    mix_type_id: 5,
    team_type: 2,
    division: 'A division',
    colors: 'red',
    discount: '0',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'TestSub',
    subprojects: [
      {
        id: 18,
        type_name: 'Voiceover 1',
        project_id: 9,
        workorder_id: null,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-06-16T17:16:06.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 38,
            subproject_id: 18,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-06-16',
            update_at: '2023-06-16T15:24:35.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 39,
            subproject_id: 18,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-06-16',
            update_at: '2023-06-16T15:24:35.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 40,
            subproject_id: 18,
            employee_id: 5,
            employee_type: 'engineer',
            employee_cost: 55,
            create_at: '2023-06-16',
            update_at: '2023-06-16T15:24:35.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 19,
        type_name: 'Voiceover 2',
        project_id: 9,
        workorder_id: 11,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-06-17T13:08:42.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 142,
            subproject_id: 19,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 143,
            subproject_id: 19,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 144,
            subproject_id: 19,
            employee_id: 5,
            employee_type: 'engineer',
            employee_cost: 55,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 20,
        type_name: 'Voiceover 3',
        project_id: 9,
        workorder_id: 11,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-06-14T17:00:12.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 136,
            subproject_id: 20,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 137,
            subproject_id: 20,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 138,
            subproject_id: 20,
            employee_id: 5,
            employee_type: 'engineer',
            employee_cost: 55,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 21,
        type_name: 'Voiceover 4',
        project_id: 9,
        workorder_id: 7,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-06-14T17:00:12.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 76,
            subproject_id: 21,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 77,
            subproject_id: 21,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 78,
            subproject_id: 21,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 22,
        type_name: 'Song 1',
        project_id: 9,
        workorder_id: 7,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-06-14T17:00:12.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 79,
            subproject_id: 22,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 80,
            subproject_id: 22,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 81,
            subproject_id: 22,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 23,
        type_name: 'Song 2',
        project_id: 9,
        workorder_id: 11,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-06-14T17:00:12.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 139,
            subproject_id: 23,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 140,
            subproject_id: 23,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 141,
            subproject_id: 23,
            employee_id: 5,
            employee_type: 'engineer',
            employee_cost: 55,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 24,
        type_name: 'Song 3',
        project_id: 9,
        workorder_id: 7,
        lyrics: '1234',
        eight_count: 8,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-08-02T20:54:07.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 82,
            subproject_id: 24,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 83,
            subproject_id: 24,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 84,
            subproject_id: 24,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 25,
        type_name: 'Voiceover 5',
        project_id: 9,
        workorder_id: null,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-06-14',
        update_at: '2023-06-14T17:06:00.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 158,
            subproject_id: 25,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:04:19.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 159,
            subproject_id: 25,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:04:19.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 160,
            subproject_id: 25,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:04:19.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 70,
        type_name: 'Tracking 1',
        project_id: 9,
        workorder_id: null,
        lyrics: null,
        eight_count: 6,
        status: 0,
        create_at: '2023-07-22',
        update_at: '2023-07-22T20:05:09.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [],
      },
      {
        id: 98,
        type_name: 'Voiceover 6',
        project_id: 9,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-08-05',
        update_at: '2023-08-05T16:40:42.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [],
      },
      {
        id: 99,
        type_name: 'Song 4',
        project_id: 9,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-08-05',
        update_at: '2023-08-05T16:41:02.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [],
      },
    ],
  },
  {
    id: 10,
    user_id: 1,
    create_at: '2023-07-04',
    update_at: '2023-07-05T13:58:56.000Z',
    client_id: 1,
    mix_season_id: 1,
    mix_date: '2023-07-18',
    team_name: 'Inferno',
    mix_type_id: 7,
    team_type: 2,
    division: 'Senior Level 3',
    colors: 'green',
    discount: '200',
    content_status: 'Pending',
    program_name: 'Test Program Name',
    program_owner_name: 'John Doe test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Global-X',
    subprojects: [
      {
        id: 30,
        type_name: 'Voiceover 1',
        project_id: 10,
        workorder_id: null,
        lyrics: '',
        eight_count: 6,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-24T17:12:43.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 44,
            subproject_id: 30,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-09',
            update_at: '2023-07-09T18:42:28.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 45,
            subproject_id: 30,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-07-09',
            update_at: '2023-07-09T18:42:33.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 31,
        type_name: 'Voiceover 2',
        project_id: 10,
        workorder_id: null,
        lyrics: '',
        eight_count: 20,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-11T05:38:35.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 46,
            subproject_id: 31,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-07-09',
            update_at: '2023-07-09T21:35:06.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 32,
        type_name: 'Voiceover 3',
        project_id: 10,
        workorder_id: 8,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-04T03:12:24.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 89,
            subproject_id: 32,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 90,
            subproject_id: 32,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 91,
            subproject_id: 32,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 33,
        type_name: 'Voiceover 4',
        project_id: 10,
        workorder_id: 7,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-04T03:12:24.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 73,
            subproject_id: 33,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 74,
            subproject_id: 33,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 75,
            subproject_id: 33,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 34,
        type_name: 'Voiceover 5',
        project_id: 10,
        workorder_id: 10,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-04T03:12:24.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 115,
            subproject_id: 34,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 116,
            subproject_id: 34,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 117,
            subproject_id: 34,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 200,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:46:18.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 35,
        type_name: 'Song 1',
        project_id: 10,
        workorder_id: null,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-12T17:26:40.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 54,
            subproject_id: 35,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-07-12',
            update_at: '2023-07-12T17:26:40.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 36,
        type_name: 'Song 2',
        project_id: 10,
        workorder_id: 8,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-04T03:12:24.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 92,
            subproject_id: 36,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 93,
            subproject_id: 36,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 94,
            subproject_id: 36,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-25',
            update_at: '2023-07-25T00:04:51.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 48,
        type_name: 'Tracking 1',
        project_id: 10,
        workorder_id: 11,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-07-13',
        update_at: '2023-07-13T15:03:20.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 133,
            subproject_id: 48,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 134,
            subproject_id: 48,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 135,
            subproject_id: 48,
            employee_id: 5,
            employee_type: 'engineer',
            employee_cost: 55,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:48:40.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 73,
        type_name: 'Voiceover 6',
        project_id: 10,
        workorder_id: 9,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-26',
        update_at: '2023-07-26T21:54:44.000Z',
        team_name: 'Inferno',
        program_name: 'Test Program Name',
        employees: [
          {
            id: 109,
            subproject_id: 73,
            employee_id: 8,
            employee_type: 'writer',
            employee_cost: 15,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'Ashraf',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '15',
            is_artist: null,
            artist_cost: '',
            is_engineer: 1,
            engineer_cost: '30',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 110,
            subproject_id: 73,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 111,
            subproject_id: 73,
            employee_id: 11,
            employee_type: 'engineer',
            employee_cost: 300,
            create_at: '2023-08-03',
            update_at: '2023-08-03T21:40:58.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
    ],
  },
  {
    id: 11,
    user_id: 1,
    create_at: '2023-07-04',
    update_at: '2023-07-31T22:14:11.000Z',
    client_id: 3,
    mix_season_id: 1,
    mix_date: '2023-08-01',
    team_name: 'Ignite',
    mix_type_id: 7,
    team_type: 1,
    division: 'Senior 2',
    colors: 'blue',
    discount: '0',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Global-X',
    subprojects: [
      {
        id: 39,
        type_name: 'Voiceover 3',
        project_id: 11,
        workorder_id: null,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-31T22:11:11.000Z',
        team_name: 'Ignite',
        program_name: 'EQ Program',
        employees: [
          {
            id: 102,
            subproject_id: 39,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-31',
            update_at: '2023-07-31T22:10:06.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 103,
            subproject_id: 39,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-31',
            update_at: '2023-07-31T22:11:11.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 40,
        type_name: 'Voiceover 4',
        project_id: 11,
        workorder_id: null,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-04T03:16:08.000Z',
        team_name: 'Ignite',
        program_name: 'EQ Program',
        employees: [
          {
            id: 164,
            subproject_id: 40,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:12:14.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 165,
            subproject_id: 40,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:12:14.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 166,
            subproject_id: 40,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:12:14.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 41,
        type_name: 'Voiceover 5',
        project_id: 11,
        workorder_id: null,
        lyrics: '',
        eight_count: 4,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-04T03:16:08.000Z',
        team_name: 'Ignite',
        program_name: 'EQ Program',
        employees: [
          {
            id: 176,
            subproject_id: 41,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:22:58.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 177,
            subproject_id: 41,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:22:58.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 178,
            subproject_id: 41,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:22:58.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 42,
        type_name: 'Song 1',
        project_id: 11,
        workorder_id: null,
        lyrics: '',
        eight_count: 8,
        status: 0,
        create_at: '2023-07-04',
        update_at: '2023-07-31T22:13:04.000Z',
        team_name: 'Ignite',
        program_name: 'EQ Program',
        employees: [
          {
            id: 104,
            subproject_id: 42,
            employee_id: 11,
            employee_type: 'writer',
            employee_cost: 100,
            create_at: '2023-07-31',
            update_at: '2023-07-31T22:12:39.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
          {
            id: 105,
            subproject_id: 42,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-31',
            update_at: '2023-07-31T22:13:04.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 57,
        type_name: 'Voiceover 6',
        project_id: 11,
        workorder_id: null,
        lyrics: null,
        eight_count: null,
        status: 0,
        create_at: '2023-07-15',
        update_at: '2023-07-31T16:23:59.000Z',
        team_name: 'Ignite',
        program_name: 'EQ Program',
        employees: [
          {
            id: 57,
            subproject_id: 57,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 0,
            create_at: '2023-07-17',
            update_at: '2023-07-17T03:51:52.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 74,
        type_name: 'Tracking 1',
        project_id: 11,
        workorder_id: null,
        lyrics: null,
        eight_count: 6,
        status: 0,
        create_at: '2023-07-27',
        update_at: '2023-07-31T22:06:02.000Z',
        team_name: 'Ignite',
        program_name: 'EQ Program',
        employees: [
          {
            id: 98,
            subproject_id: 74,
            employee_id: 11,
            employee_type: 'producer',
            employee_cost: 0,
            create_at: '2023-07-27',
            update_at: '2023-07-27T20:48:47.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
          {
            id: 99,
            subproject_id: 74,
            employee_id: 10,
            employee_type: 'producer',
            employee_cost: 500,
            create_at: '2023-07-31',
            update_at: '2023-07-31T16:24:33.000Z',
            name: 'Florentino Perez',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: 1,
            producer_cost: '500',
          },
        ],
      },
    ],
  },
  {
    id: 12,
    user_id: 1,
    create_at: '2023-07-10',
    update_at: '2023-07-10T20:45:46.000Z',
    client_id: 3,
    mix_season_id: 1,
    mix_date: '2023-07-11',
    team_name: 'Rumman Team',
    mix_type_id: 4,
    team_type: 2,
    division: 'A division',
    colors: 'yellow',
    discount: '0',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Rumman mix',
    subprojects: [
      {
        id: 84,
        type_name: 'Song 1',
        project_id: 12,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-08-03',
        update_at: '2023-08-03T19:19:03.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 167,
            subproject_id: 84,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:12:14.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 168,
            subproject_id: 84,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:12:14.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 169,
            subproject_id: 84,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:12:14.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 85,
        type_name: 'Song 2',
        project_id: 12,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-08-03',
        update_at: '2023-08-03T19:19:10.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 173,
            subproject_id: 85,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:17:00.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 174,
            subproject_id: 85,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:17:00.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 175,
            subproject_id: 85,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:17:00.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 86,
        type_name: 'Song 3',
        project_id: 12,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-08-03',
        update_at: '2023-08-03T19:19:46.000Z',
        team_name: 'Rumman Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 179,
            subproject_id: 86,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:22:58.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 180,
            subproject_id: 86,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:22:58.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 181,
            subproject_id: 86,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:22:58.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
    ],
  },
  {
    id: 13,
    user_id: 1,
    create_at: '2023-07-13',
    update_at: '2023-07-13T20:36:23.000Z',
    client_id: 3,
    mix_season_id: 8,
    mix_date: '2023-07-14',
    team_name: 'QA Team',
    mix_type_id: 4,
    team_type: 2,
    division: 'A',
    colors: 'red',
    discount: '0',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Rumman mix',
    subprojects: [
      {
        id: 68,
        type_name: 'Voiceover 1',
        project_id: 13,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-20',
        update_at: '2023-07-20T20:05:53.000Z',
        team_name: 'QA Team',
        program_name: 'EQ Program',
        employees: [
          {
            id: 65,
            subproject_id: 68,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-20',
            update_at: '2023-07-20T20:05:26.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 66,
            subproject_id: 68,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-20',
            update_at: '2023-07-20T20:05:53.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
    ],
  },
  {
    id: 14,
    user_id: 1,
    create_at: '2023-07-13',
    update_at: '2023-07-13T21:09:16.000Z',
    client_id: 2,
    mix_season_id: 7,
    mix_date: '2023-08-18',
    team_name: 'Ashraf Team Name 123',
    mix_type_id: 4,
    team_type: 2,
    division: 'test div 567',
    colors: 'green',
    discount: '0',
    content_status: 'Pending',
    program_name: 'Program 2',
    program_owner_name: 'dani carvajal test',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Rumman mix',
    subprojects: [],
  },
  {
    id: 15,
    user_id: 1,
    create_at: '2023-07-13',
    update_at: '2023-07-13T21:10:31.000Z',
    client_id: 3,
    mix_season_id: 7,
    mix_date: '2023-07-29',
    team_name: 'new Team QA',
    mix_type_id: 7,
    team_type: 2,
    division: 'A',
    colors: 'blue',
    discount: '0',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Global-X',
    subprojects: [
      {
        id: 53,
        type_name: 'Voiceover 1',
        project_id: 15,
        workorder_id: null,
        lyrics: null,
        eight_count: null,
        status: 0,
        create_at: '2023-07-14',
        update_at: '2023-07-14T04:31:27.000Z',
        team_name: 'new Team QA',
        program_name: 'EQ Program',
        employees: [
          {
            id: 55,
            subproject_id: 53,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-14',
            update_at: '2023-07-14T04:31:27.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 72,
        type_name: 'Voiceover 2',
        project_id: 15,
        workorder_id: 18,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-24',
        update_at: '2023-07-24T23:58:47.000Z',
        team_name: 'new Team QA',
        program_name: 'EQ Program',
        employees: [
          {
            id: 184,
            subproject_id: 72,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:36:08.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 185,
            subproject_id: 72,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:36:08.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 186,
            subproject_id: 72,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:36:08.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
    ],
  },
  {
    id: 18,
    user_id: 1,
    create_at: '2023-07-13',
    update_at: '2023-07-13T21:31:31.000Z',
    client_id: 3,
    mix_season_id: 7,
    mix_date: '2023-08-25',
    team_name: 'Test Team Survey Test',
    mix_type_id: 4,
    team_type: 2,
    division: 'Test div',
    colors: 'yellow',
    discount: '0',
    content_status: 'Pending',
    program_name: 'EQ Program',
    program_owner_name: 'Rumman',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Rumman mix',
    subprojects: [
      {
        id: 49,
        type_name: 'Voiceover 1',
        project_id: 18,
        workorder_id: 18,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-13',
        update_at: '2023-07-13T21:31:31.000Z',
        team_name: 'Test Team Survey Test',
        program_name: 'EQ Program',
        employees: [
          {
            id: 187,
            subproject_id: 49,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:36:08.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 188,
            subproject_id: 49,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:36:08.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 189,
            subproject_id: 49,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:36:08.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 50,
        type_name: 'Song 1',
        project_id: 18,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-07-13',
        update_at: '2023-07-13T21:31:31.000Z',
        team_name: 'Test Team Survey Test',
        program_name: 'EQ Program',
        employees: [
          {
            id: 161,
            subproject_id: 50,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:04:19.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 162,
            subproject_id: 50,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:04:19.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 163,
            subproject_id: 50,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-08-04',
            update_at: '2023-08-04T20:04:19.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
        ],
      },
      {
        id: 51,
        type_name: 'Tracking 1',
        project_id: 18,
        workorder_id: null,
        lyrics: null,
        eight_count: 0,
        status: 0,
        create_at: '2023-07-13',
        update_at: '2023-07-13T21:31:31.000Z',
        team_name: 'Test Team Survey Test',
        program_name: 'EQ Program',
        employees: [],
      },
    ],
  },
  {
    id: 19,
    user_id: 1,
    create_at: '2023-07-17',
    update_at: '2023-07-17T04:32:22.000Z',
    client_id: 5,
    mix_season_id: 8,
    mix_date: '2023-07-11',
    team_name: 'mkd QA',
    mix_type_id: 9,
    team_type: 2,
    division: 'xD',
    colors: 'red',
    discount: '0',
    content_status: 'Pending',
    program_name: 'Mkd Team',
    program_owner_name: 'Rumman Mohsin',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'QA-mix-type',
    subprojects: [
      {
        id: 58,
        type_name: 'Voiceover 1',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-17T04:54:27.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [
          {
            id: 58,
            subproject_id: 58,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-07-17',
            update_at: '2023-07-17T04:52:38.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 62,
            subproject_id: 58,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-17',
            update_at: '2023-07-17T04:54:27.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 59,
        type_name: 'Voiceover 2',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-21T19:25:31.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [
          {
            id: 59,
            subproject_id: 59,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-17',
            update_at: '2023-07-17T04:52:44.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 63,
            subproject_id: 59,
            employee_id: 7,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-07-17',
            update_at: '2023-07-17T04:54:32.000Z',
            name: 'RummanArtist',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 60,
        type_name: 'Song 1',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-17T04:52:50.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [
          {
            id: 60,
            subproject_id: 60,
            employee_id: 11,
            employee_type: 'writer',
            employee_cost: 100,
            create_at: '2023-07-17',
            update_at: '2023-07-17T04:52:50.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 61,
        type_name: 'Song 2',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 8,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-20T20:46:01.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [
          {
            id: 61,
            subproject_id: 61,
            employee_id: 11,
            employee_type: 'writer',
            employee_cost: 100,
            create_at: '2023-07-17',
            update_at: '2023-07-17T04:53:00.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
          {
            id: 67,
            subproject_id: 61,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-20',
            update_at: '2023-07-20T20:46:01.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
      {
        id: 62,
        type_name: 'Song 3',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 0,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-21T19:26:14.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [
          {
            id: 68,
            subproject_id: 62,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: 10,
            create_at: '2023-07-20',
            update_at: '2023-07-20T20:46:25.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 69,
            subproject_id: 62,
            employee_id: 11,
            employee_type: 'artist',
            employee_cost: 150,
            create_at: '2023-07-20',
            update_at: '2023-07-20T20:46:34.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 63,
        type_name: 'Tracking 1',
        project_id: 19,
        workorder_id: 7,
        lyrics: 'HAHAHAAHAHHA',
        eight_count: 0,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-08-02T20:53:35.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [
          {
            id: 70,
            subproject_id: 63,
            employee_id: 2,
            employee_type: 'writer',
            employee_cost: 50,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Luka Modric',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '50',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 71,
            subproject_id: 63,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: 43,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 72,
            subproject_id: 63,
            employee_id: 1,
            employee_type: 'engineer',
            employee_cost: 45,
            create_at: '2023-07-21',
            update_at: '2023-07-21T14:46:38.000Z',
            name: 'Ashraf Kabir',
            email: '<EMAIL>',
            is_writer: 0,
            writer_cost: '0',
            is_artist: 0,
            artist_cost: '0',
            is_engineer: 1,
            engineer_cost: '45',
            is_producer: 0,
            producer_cost: '0',
          },
          {
            id: 100,
            subproject_id: 63,
            employee_id: 11,
            employee_type: 'producer',
            employee_cost: 0,
            create_at: '2023-07-31',
            update_at: '2023-07-31T16:25:48.000Z',
            name: 'Rumman',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '100',
            is_artist: 1,
            artist_cost: '150',
            is_engineer: 1,
            engineer_cost: '200',
            is_producer: 1,
            producer_cost: '250',
          },
        ],
      },
      {
        id: 64,
        type_name: 'Tracking 2',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 0,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-17T04:32:23.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [],
      },
      {
        id: 65,
        type_name: 'Tracking 3',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 0,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-17T04:32:23.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [],
      },
      {
        id: 66,
        type_name: 'Tracking 4',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 0,
        status: 0,
        create_at: '2023-07-17',
        update_at: '2023-07-17T04:32:23.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [],
      },
      {
        id: 69,
        type_name: 'Voiceover 3',
        project_id: 19,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-07-21',
        update_at: '2023-07-21T19:22:41.000Z',
        team_name: 'mkd QA',
        program_name: 'Mkd Team',
        employees: [
          {
            id: 85,
            subproject_id: 69,
            employee_id: 6,
            employee_type: 'writer',
            employee_cost: null,
            create_at: '2023-07-21',
            update_at: '2023-07-21T19:22:41.000Z',
            name: 'RummanWriter',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '10',
            is_artist: null,
            artist_cost: '',
            is_engineer: null,
            engineer_cost: '',
            is_producer: null,
            producer_cost: '',
          },
          {
            id: 86,
            subproject_id: 69,
            employee_id: 5,
            employee_type: 'artist',
            employee_cost: null,
            create_at: '2023-07-21',
            update_at: '2023-07-21T19:22:41.000Z',
            name: 'Eder Militao',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: '',
            is_artist: 1,
            artist_cost: '43',
            is_engineer: 1,
            engineer_cost: '55',
            is_producer: null,
            producer_cost: '',
          },
        ],
      },
    ],
  },
  {
    id: 21,
    user_id: 1,
    create_at: '2023-08-04',
    update_at: '2023-08-04T21:23:01.000Z',
    client_id: 6,
    mix_season_id: 1,
    mix_date: '2023-08-16',
    team_name: 'Inferno',
    mix_type_id: 7,
    team_type: 2,
    division: 'Senior Coed 3',
    colors: 'Red and Black',
    discount: '0',
    content_status: 'Pending',
    program_name: 'Eric Rodriguez AllStars',
    program_owner_name: 'Eric Rodriguez',
    program_owner_email: '<EMAIL>',
    mix_type_name: 'Global-X',
    subprojects: [
      {
        id: 90,
        type_name: 'Voiceover 1',
        project_id: 21,
        workorder_id: null,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:36:21.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [
          {
            id: 190,
            subproject_id: 90,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:36:02.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 191,
            subproject_id: 90,
            employee_id: 12,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:36:21.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
        ],
      },
      {
        id: 91,
        type_name: 'Voiceover 2',
        project_id: 21,
        workorder_id: null,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:37:20.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [
          {
            id: 192,
            subproject_id: 91,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:36:33.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 193,
            subproject_id: 91,
            employee_id: 14,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:37:20.000Z',
            name: 'Eric A E',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: null,
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 1,
            engineer_cost: '50',
            is_producer: null,
            producer_cost: null,
          },
        ],
      },
      {
        id: 92,
        type_name: 'Voiceover 3',
        project_id: 21,
        workorder_id: 21,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:23:01.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [
          {
            id: 195,
            subproject_id: 92,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 196,
            subproject_id: 92,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 197,
            subproject_id: 92,
            employee_id: 13,
            employee_type: 'engineer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric Engineer',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: null,
            is_artist: null,
            artist_cost: null,
            is_engineer: 1,
            engineer_cost: '50',
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 204,
            subproject_id: 92,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 207,
            subproject_id: 92,
            employee_id: 12,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
        ],
      },
      {
        id: 93,
        type_name: 'Voiceover 4',
        project_id: 21,
        workorder_id: 21,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:23:01.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [
          {
            id: 198,
            subproject_id: 93,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 199,
            subproject_id: 93,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 200,
            subproject_id: 93,
            employee_id: 13,
            employee_type: 'engineer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric Engineer',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: null,
            is_artist: null,
            artist_cost: null,
            is_engineer: 1,
            engineer_cost: '50',
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 205,
            subproject_id: 93,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 208,
            subproject_id: 93,
            employee_id: 12,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
        ],
      },
      {
        id: 94,
        type_name: 'Voiceover 5',
        project_id: 21,
        workorder_id: 21,
        lyrics: null,
        eight_count: 5,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:23:01.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [
          {
            id: 201,
            subproject_id: 94,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 202,
            subproject_id: 94,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 203,
            subproject_id: 94,
            employee_id: 13,
            employee_type: 'engineer',
            employee_cost: 50,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric Engineer',
            email: '<EMAIL>',
            is_writer: null,
            writer_cost: null,
            is_artist: null,
            artist_cost: null,
            is_engineer: 1,
            engineer_cost: '50',
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 206,
            subproject_id: 94,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
          {
            id: 209,
            subproject_id: 94,
            employee_id: 12,
            employee_type: 'artist',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:50:11.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
        ],
      },
      {
        id: 95,
        type_name: 'Song 1',
        project_id: 21,
        workorder_id: null,
        lyrics: null,
        eight_count: 6,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:40:42.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [
          {
            id: 194,
            subproject_id: 95,
            employee_id: 12,
            employee_type: 'writer',
            employee_cost: 20,
            create_at: '2023-08-04',
            update_at: '2023-08-04T21:40:42.000Z',
            name: 'Eric W A ',
            email: '<EMAIL>',
            is_writer: 1,
            writer_cost: '20',
            is_artist: 1,
            artist_cost: '20',
            is_engineer: 0,
            engineer_cost: null,
            is_producer: null,
            producer_cost: null,
          },
        ],
      },
      {
        id: 96,
        type_name: 'Song 2',
        project_id: 21,
        workorder_id: null,
        lyrics: null,
        eight_count: 6,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:23:01.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [],
      },
      {
        id: 97,
        type_name: 'Voiceover 6',
        project_id: 21,
        workorder_id: null,
        lyrics: null,
        eight_count: 4,
        status: 0,
        create_at: '2023-08-04',
        update_at: '2023-08-04T21:34:19.000Z',
        team_name: 'Inferno',
        program_name: 'Eric Rodriguez AllStars',
        employees: [],
      },
    ],
  },
];
