import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const AddNoteModal = ({ setModalClose, setFormData }) => {
  const [note, setNote] = React.useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    setFormData(note);
  };

  return (
    <div className='fixed inset-0 z-10 overflow-y-auto'>
      <div className='flex min-h-screen items-center justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0'>
        <div className='fixed inset-0 transition-opacity' aria-hidden='true'>
          <div className='absolute inset-0 bg-gray-500 opacity-75'></div>
        </div>
        <span
          className='hidden sm:inline-block sm:h-screen sm:align-middle'
          aria-hidden='true'
        >
          &#8203;
        </span>
        <div
          className='inline-block transform overflow-hidden rounded-lg bg-gray-800 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle'
          role='dialog'
          aria-modal='true'
          aria-labelledby='modal-headline'
        >
          <form>
            <div className='flex w-full justify-between border-b-2 border-gray-500 bg-slate-950 px-6 py-4'>
              <h3
                className='text-2xl font-medium leading-6 text-gray-100'
                id='modal-headline'
              >
                Send back with notes
              </h3>
              <span
                className='absolute right-3 top-4 cursor-pointer'
                onClick={() => setModalClose(false)}
              >
                <FontAwesomeIcon
                  icon='fa-solid fa-circle-xmark'
                  width={32}
                  height={32}
                  color='#fff'
                />
              </span>
            </div>

            <div className='flex items-start justify-start p-6'>
              <div className='mt-3 w-full text-center sm:mt-0 sm:text-left'>
                <div className='mb-4'>
                  <textarea
                    className='focus:shadow-outline h-32 w-full rounded-lg border bg-gray-700 px-3 py-2 text-base text-gray-100 placeholder-gray-100'
                    placeholder='Enter your note here...'
                    name='note'
                    required
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                  ></textarea>
                </div>
              </div>
            </div>

            <div className='flex flex-row-reverse border-t-2 border-gray-500 bg-slate-950 px-6 py-4'>
              <div
                onClick={(e) => handleSubmit(e)}
                className='ml-3 inline-flex w-auto cursor-pointer justify-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 sm:text-sm'
              >
                Submit
              </div>
              <div
                className='ml-3 inline-flex w-auto cursor-pointer justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:text-sm'
                onClick={() => setModalClose(false)}
              >
                Cancel
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddNoteModal;
