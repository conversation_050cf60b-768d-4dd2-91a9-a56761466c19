import React, { useState } from "react";
import { X } from "lucide-react";
import MkdSDK from "../../utils/MkdSDK";
import { GlobalContext } from "../../globalContext";

const RefundModal = ({
  isOpen,
  onClose,
  payment,
  onRefundComplete,
  formatCurrency,
}) => {
  const [reason, setReason] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState("");
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  // Reset form when modal opens with new payment
  React.useEffect(() => {
    if (payment) {
      setReason("");
      setError("");
    }
  }, [payment]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    try {
      setIsProcessing(true);

      const sdk = new MkdSDK();
      const payload = {
        paymentId: payment.id,
        reason: "requested_by_customer",
      };

      const result = await sdk.refundPayment(payload);

      if (result.error) {
        throw new Error(result.message || "Refund failed");
      }

      // Show success message
      globalDispatch({
        type: "SHOW_TOAST",
        payload: {
          text: "Payment refunded successfully",
          type: "success",
        },
      });

      // Call the callback to refresh data
      if (onRefundComplete) {
        onRefundComplete();
      }

      // Close the modal
      onClose();
    } catch (error) {
      console.error("Refund error:", error);
      setError(error.message || "Failed to process refund");

      globalDispatch({
        type: "SHOW_TOAST",
        payload: {
          text: error.message || "Failed to process refund",
          type: "error",
        },
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen || !payment) return null;

  // Convert amount from cents to dollars for display
  const amountInDollars = payment.amount
    ? (parseFloat(payment.amount) / 100).toFixed(2)
    : "0.00";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      <div
        className="inline-block transform overflow-hidden rounded-lg bg-boxdark text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-headline"
      >
        <form onSubmit={handleSubmit}>
          <div className="flex w-full items-center justify-between border-b border-strokedark bg-boxdark px-6 py-4">
            <h3
              className="text-xl font-semibold text-white"
              id="modal-headline"
            >
              Refund Payment
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="rounded-full p-1 transition-colors hover:bg-meta-4"
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>

          <div className="px-6 py-4">
            <div className="mb-4">
              <p className="mb-2 text-white">Payment Details:</p>
              <div className="rounded-lg bg-meta-4/20 p-3">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-sm text-bodydark2">Date</p>
                    <p className="font-medium text-white">
                      {new Date(payment.create_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-bodydark2">Method</p>
                    <p className="font-medium capitalize text-white">
                      {payment.payment_method}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-bodydark2">Amount</p>
                    <p className="font-medium text-white">${amountInDollars}</p>
                  </div>
                  <div>
                    <p className="text-sm text-bodydark2">Status</p>
                    <p className="font-medium capitalize text-white">
                      {payment.status}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-white">
                You are about to issue a full refund of{" "}
                <span className="font-bold">${amountInDollars}</span>
              </p>
              <p className="mt-2 text-sm text-bodydark2">
                This action cannot be undone. The full payment amount will be
                refunded to the customer.
              </p>
            </div>

            <div className="mb-4">
              <label
                htmlFor="reason"
                className="mb-2 block text-sm font-medium text-white"
              >
                Reason (Optional)
              </label>
              <textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="w-full rounded-lg border border-stroke bg-meta-4/30 px-5 py-2.5 text-white focus:border-primary focus:outline-none"
                placeholder="Reason for refund"
                rows="3"
              />
            </div>

            {error && (
              <div className="mb-4 rounded-md bg-danger/10 p-3 text-danger">
                {error}
              </div>
            )}
          </div>

          <div className="border-t border-strokedark px-6 py-4">
            <div className="flex justify-end gap-3">
              <button
                type="button"
                onClick={onClose}
                className="rounded-md border border-stroke px-4 py-2 text-white hover:bg-meta-4"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isProcessing}
                className="rounded-md bg-danger px-4 py-2 text-white hover:bg-opacity-90 disabled:cursor-not-allowed disabled:opacity-70"
              >
                {isProcessing ? "Processing..." : "Process Refund"}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RefundModal;
