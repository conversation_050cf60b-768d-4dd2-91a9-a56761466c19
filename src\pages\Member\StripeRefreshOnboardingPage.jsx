import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import { ClipLoader } from "react-spinners";

const StripeRefreshOnboardingPage = () => {
  const navigate = useNavigate();
  const { state } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState("Refreshing your Stripe onboarding...");

  useEffect(() => {
    const handleStripeRefresh = async () => {
      try {
        setIsLoading(true);
        
        // Get current user ID
        const userId = localStorage.getItem("user");
        if (!userId) {
          showToast(globalDispatch, "User session not found", 4000, "error");
          navigate("/member/login");
          return;
        }

        // Fetch updated user details to check Stripe status
        const userResult = await getUserDetailsByIdAPI(userId);
        
        if (userResult.error) {
          showToast(globalDispatch, "Failed to fetch user details", 4000, "error");
          navigate("/member/dashboard");
          return;
        }

        const userDetails = userResult.model;
        
        // Check if Stripe onboarding is still needed
        if (userDetails.has_stripe === false && userDetails.account_link?.url) {
          setMessage("Redirecting you back to Stripe onboarding...");
          // Redirect back to Stripe onboarding
          setTimeout(() => {
            window.location.href = userDetails.account_link.url;
          }, 2000);
        } else if (userDetails.has_stripe === true) {
          // Stripe setup is complete, show success and redirect
          setMessage("Stripe setup completed successfully!");
          showToast(globalDispatch, "Stripe setup completed successfully!", 4000, "success");
          
          setTimeout(() => {
            // Check onboarding status and redirect appropriately
            let onboardingComplete = false;
            if (userDetails.steps) {
              try {
                const stepData = JSON.parse(userDetails.steps);
                onboardingComplete = stepData.onboarding_complete === true;
              } catch (e) {
                console.error("Error parsing step data:", e);
              }
            }

            if (onboardingComplete) {
              navigate("/member/dashboard");
            } else {
              navigate("/member/onboarding");
            }
          }, 2000);
        } else {
          // Something went wrong, redirect to dashboard
          setMessage("Unable to complete Stripe setup. Redirecting...");
          showToast(globalDispatch, "Unable to complete Stripe setup", 4000, "error");
          setTimeout(() => {
            navigate("/member/dashboard");
          }, 2000);
        }
      } catch (error) {
        console.error("Error handling Stripe refresh:", error);
        showToast(globalDispatch, "An error occurred during Stripe setup", 4000, "error");
        navigate("/member/dashboard");
      } finally {
        setIsLoading(false);
      }
    };

    handleStripeRefresh();
  }, [navigate, globalDispatch]);

  return (
    <div className="flex h-screen items-center justify-center bg-boxdark-2">
      <div className="text-center">
        <div className="mb-6">
          <ClipLoader size={50} color="#3C50E0" />
        </div>
        <h2 className="mb-4 text-2xl font-bold text-white">
          Stripe Onboarding
        </h2>
        <p className="text-lg text-bodydark">
          {message}
        </p>
        <div className="mt-6">
          <p className="text-sm text-bodydark2">
            Please wait while we process your Stripe setup...
          </p>
        </div>
      </div>
    </div>
  );
};

export default StripeRefreshOnboardingPage;
