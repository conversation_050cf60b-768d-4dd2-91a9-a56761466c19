import { FixedSizeList as List } from "react-window";
import AutoSizer from "react-virtualized-auto-sizer";

const VirtualizedTable = ({ data, rowHeight = 50, Row }) => {
  return (
    <AutoSizer>
      {({ height, width }) => (
        <List
          height={height || 600}
          itemCount={data.length}
          itemSize={rowHeight}
          width={width || "100%"}
        >
          {({ index, style }) => (
            <div style={style}>
              <Row index={index} data={data[index]} />
            </div>
          )}
        </List>
      )}
    </AutoSizer>
  );
};

export default VirtualizedTable;
