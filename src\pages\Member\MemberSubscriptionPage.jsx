import React, { useState, useEffect, Fragment } from "react";
import { AuthContext, tokenExpireError } from "Src/authContext";
import {
  GlobalContext,
  showToast,
  setGLobalProperty,
  setProjectCounts,
  setProjectLimit,
  calculateProjectLimit,
} from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import { LazyLoad } from "Components/LazyLoad";
import { InteractiveButton } from "Components/InteractiveButton";
import CustomSelect2 from "Components/CustomSelect2";
import PaginationBar from "Components/PaginationBar";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { ClipboardDocumentIcon } from "@heroicons/react/24/outline";
import {
  CardElement,
  Elements,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import Spinner from "Components/Spinner";
import { useNavigate } from "react-router";
import { getUserDetailsByIdAPI } from "Src/services/userService";

const stripePromise = loadStripe(
  "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"
);

export const CardDetailsModal = ({
  isOpen,
  closeModal,
  onSubmit,
  loading,
  couponCode,
  setCouponCode,
}) => {
  const [cardReady, setCardReady] = useState(false);
  const stripe = useStripe();
  const elements = useElements();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!stripe || !elements) return;

    const result = await stripe.createToken(elements.getElement(CardElement));
    onSubmit(result, couponCode);
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-boxdark p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between">
                  <Dialog.Title className="text-lg font-semibold text-white">
                    Enter Card Details
                  </Dialog.Title>
                  <button
                    onClick={closeModal}
                    className="text-bodydark hover:text-white"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="mt-4">
                  <div className="mb-4">
                    <CardElement
                      className="rounded border border-form-strokedark bg-form-input p-4 text-bodydark"
                      options={{
                        hidePostalCode: true,
                        style: {
                          base: {
                            fontSize: "16px",
                            color: "#E5E7EB",
                            "::placeholder": {
                              color: "#9CA3AF",
                            },
                          },
                        },
                      }}
                      onReady={() => setCardReady(true)}
                    />
                  </div>

                  <div className="mb-4">
                    <label
                      htmlFor="couponCode"
                      className="mb-2 block text-sm font-medium text-white"
                    >
                      Coupon Code (Optional)
                    </label>
                    <input
                      type="text"
                      id="couponCode"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                      placeholder="Enter coupon code if you have one"
                      className="w-full rounded border border-form-strokedark bg-form-input p-3 text-bodydark outline-none focus:border-primary"
                    />
                  </div>

                  <div className="mt-6 flex justify-end gap-4">
                    <button
                      type="button"
                      onClick={closeModal}
                      className="rounded-lg border border-stroke px-4 py-2 text-bodydark hover:bg-boxdark/50"
                    >
                      Cancel
                    </button>
                    <InteractiveButton
                      type="submit"
                      loading={loading}
                      disabled={loading || !cardReady}
                      className="rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/80"
                    >
                      {loading ? "Processing..." : "Subscribe"}
                    </InteractiveButton>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

const InvoicesTab = () => {
  const sdk = new MkdSDK();
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [initialId, setInitialId] = React.useState(null);
  const [data, setData] = React.useState({});
  const [pageSize, setPageSize] = React.useState(10);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [loading, setLoading] = React.useState(true);

  const columns = [
    {
      header: "Invoice #",
      accessor: "number",
    },
    {
      header: "Date",
      accessor: "created",
      type: "timestamp",
    },
    {
      header: "Status",
      accessor: "status",
    },
    {
      header: "Total",
      accessor: "total",
      type: "currency",
    },
    {
      header: "Details",
      accessor: "lines",
      type: "lineItems",
    },
    {
      header: "Receipt",
      accessor: "invoice_pdf",
      type: "link",
    },
  ];

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData({ limit });
    })();
  }

  function previousPage() {
    (async function () {
      await getData({ limit: pageSize, before: data?.data[0].id });
    })();
  }

  function nextPage() {
    (async function () {
      await getData({
        limit: pageSize,
        after: data?.data[data?.data.length - 1].id,
      });
    })();
  }

  async function getData(paginationParams) {
    try {
      setLoading(true);
      const {
        list: invoices,
        limit,
        error,
        message,
      } = await sdk.getCustomerStripeInvoices(paginationParams);

      if (error) {
        showToast(globalDispatch, message, 5000);
      }
      if (!invoices) {
        return;
      }

      if (!initialId) {
        setInitialId(invoices?.data[0]?.id ?? "");
      }
      console.log("Invoice data:", invoices.data[0]);
      console.log("Invoice line items:", invoices.data[0]?.lines?.data);
      setData(invoices);
      setPageSize(+limit);
      setCanPreviousPage(initialId && initialId !== invoices.data[0]?.id);
      setCanNextPage(invoices.has_more);
      setLoading(false);
    } catch (error) {
      console.error("ERROR", error);
      showToast(globalDispatch, error.message, 5000);
      tokenExpireError(dispatch, error.message);
      setLoading(false);
    }
  }

  React.useEffect(() => {
    getData({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading) return <Spinner />;

  return (
    <div className="overflow-hidden rounded border border-strokedark bg-boxdark">
      <div className="flex items-center justify-between border-b border-strokedark px-4 md:px-6 2xl:px-9">
        <h4 className="my-3 text-2xl font-semibold text-white">
          Billing History
        </h4>
      </div>

      <div className="custom-overflow min-h-[140px] overflow-x-auto">
        <table className="w-full table-auto">
          <thead className="bg-meta-4">
            <tr>
              {columns.map((column, i) => (
                <th
                  key={i}
                  className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white ${
                    column.type === "lineItems" ? "w-1/3" : ""
                  }`}
                >
                  {column.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-strokedark bg-boxdark-2">
            {data?.data?.map((row, i) => {
              return (
                <tr key={i}>
                  {columns.map((cell, index) => {
                    if (cell.accessor === "") {
                      return (
                        <td
                          key={index}
                          className="whitespace-nowrap px-4 py-3"
                        ></td>
                      );
                    }
                    if (cell.mapping) {
                      return (
                        <td key={index} className="whitespace-nowrap px-4 py-3">
                          {cell.mapping[row[cell.accessor]]}
                        </td>
                      );
                    }
                    if (cell.type === "timestamp") {
                      return (
                        <td key={index} className="whitespace-nowrap px-4 py-3">
                          {new Date(row[cell.accessor] * 1000).toLocaleString(
                            "en-US"
                          )}
                        </td>
                      );
                    } else if (cell.type === "currency") {
                      return (
                        <td key={index} className="whitespace-nowrap px-4 py-3">
                          ${Number(row[cell.accessor] / 100).toFixed(2)}
                        </td>
                      );
                    } else if (cell.type === "lineItems") {
                      const lineItems = row[cell.accessor]?.data || [];

                      return (
                        <td key={index} className="px-4 py-3">
                          <div className="max-h-40 overflow-y-auto">
                            {lineItems.map((item, i) => {
                              const isRefund = item.amount < 0;
                              const planName =
                                item.plan?.nickname ||
                                item.price?.nickname ||
                                "Unknown Plan";

                              return (
                                <div
                                  key={i}
                                  className={`mb-2 ${
                                    isRefund ? "text-green-500" : "text-white"
                                  }`}
                                >
                                  <div className="flex justify-between">
                                    <span className="font-medium">
                                      {planName}
                                    </span>
                                    <span>
                                      {isRefund ? "-" : ""}$
                                      {Math.abs(
                                        Number(item.amount / 100)
                                      ).toFixed(2)}
                                    </span>
                                  </div>
                                  <div className="text-xs text-gray-400">
                                    {item.description}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </td>
                      );
                    } else if (cell.type === "link") {
                      return (
                        <td key={index} className="whitespace-nowrap px-4 py-3">
                          {row[cell.accessor] ? (
                            <a
                              href={row[cell.accessor]}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:underline"
                            >
                              View Receipt
                            </a>
                          ) : (
                            "N/A"
                          )}
                        </td>
                      );
                    }
                    return (
                      <td
                        key={index}
                        className="whitespace-nowrap px-4 py-3 text-white"
                      >
                        {row[cell.accessor]}
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {data?.data?.length === 0 ? (
        <div className="mb-[20px] mt-24 flex flex-col items-center">
          <ClipboardDocumentIcon
            className="h-8 w-8 text-gray-700"
            strokeWidth={2}
          />
          <p className="mt-4 text-center text-base font-medium text-white">
            No Invoices
          </p>
        </div>
      ) : (
        <div className="w-full px-4 py-10 md:px-6 2xl:px-9">
          <PaginationBar
            currentPage={1}
            pageCount={1}
            pageSize={pageSize}
            dataTotal={data?.data?.length || 0}
            canPreviousPage={canPreviousPage}
            canNextPage={canNextPage}
            updatePageSize={updatePageSize}
            previousPage={previousPage}
            nextPage={nextPage}
          />
        </div>
      )}
    </div>
  );
};

const AddOnsTab = ({
  addOnSubscription,
  handleAddOnSubscribe,
  isAddOnProcessing,
  isAddOnModalOpen,
  setIsAddOnModalOpen,
  handleAddOnCardSubmit,
  isLoading,
  currentSubscription,
  couponCode,
  setCouponCode,
  isMainMember,
  userDetails,
}) => {
  // Check if user has the required subscription type (Portal or Complete Suite)
  console.log(currentSubscription, "currentSubscription");
  const hasRequiredSubscription =
    currentSubscription &&
    (currentSubscription.plan_name.includes("The Portal") ||
      currentSubscription.plan_name.includes("Complete Suite"));

  console.log(hasRequiredSubscription, addOnSubscription);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  // Show sub-member notice if user is not a main member
  if (!isMainMember) {
    return (
      <div className="mx-auto h-full">
        <div className="mb-8 rounded-lg border border-primary bg-boxdark p-6">
          <h2 className="mb-4 text-2xl font-bold text-white">
            Team Member Account - Add-ons
          </h2>
          <div className="space-y-4">
            <p className="text-bodydark">
              You are a team member under a main account. Add-on subscriptions
              are managed by your main account holder.
            </p>

            <div className="rounded-lg bg-meta-4 p-4">
              <p className="text-white">
                <strong>Note:</strong> To manage add-on subscriptions, please
                contact your main account holder.
              </p>
            </div>
          </div>
        </div>

        {/* Show current add-on subscription if active */}
        {userDetails?.main_user_details?.has_invoice_subscription && (
          <div className="mb-8 rounded-lg bg-boxdark p-6">
            <h2 className="mb-4 text-2xl font-bold text-white">
              Current Add-On Subscription (Inherited)
            </h2>
            <div className="space-y-2 text-lg font-bold text-white">
              <p>Add-On: Payment System & Automated Project Management</p>
              <p>Amount: $399.99/year</p>
              <p>Status: Active</p>
              <p className="text-sm text-bodydark">
                This add-on subscription is managed by your main account holder.
              </p>
            </div>
          </div>
        )}

        {/* Show available add-ons in read-only mode */}
        <div className="mb-8">
          <h1 className="mb-6 text-3xl font-bold text-white">
            Available Add-Ons
          </h1>
          <p className="mb-6 text-bodydark">
            These are the add-ons available in the system. Your access is
            determined by your main account holder's subscriptions.
          </p>

          <div className="grid gap-8 md:grid-cols-1">
            <div
              className={`flex flex-col rounded-lg border p-6 shadow-lg ${
                userDetails?.main_user_details?.has_invoice_subscription
                  ? "border-primary bg-primary/10"
                  : "border-form-strokedark bg-boxdark-2"
              }`}
            >
              <h3 className="mb-4 text-2xl font-bold text-white">
                Payment System & Automated Project Management
                {userDetails?.main_user_details?.has_invoice_subscription && (
                  <span className="ml-2 text-sm text-primary">(Active)</span>
                )}
              </h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-primary">$399.99</span>
                <span className="text-bodydark">/year</span>
              </div>
              <div className="mb-6 flex-grow">
                <h4 className="mb-2 font-semibold text-white">Features:</h4>
                <ul className="space-y-2">
                  {[
                    "Automated Payment Processing",
                    "Client Invoice Management",
                    "Project Timeline Automation",
                    "Deadline Tracking",
                    "Client Payment Portal",
                    "Payment Reminders",
                    "Deposit Management",
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-bodydark">
                      <svg
                        className="mr-2 h-5 w-5 text-primary"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <button
                disabled={true}
                className="w-full cursor-not-allowed rounded-full border border-strokedark bg-transparent px-8 py-3 font-semibold text-bodydark opacity-50"
              >
                {userDetails?.main_user_details?.has_invoice_subscription
                  ? "Currently Active"
                  : "Contact Main Account Holder"}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto h-full">
      {addOnSubscription && (
        <div className="mb-8 rounded-lg bg-boxdark p-6">
          <h2 className="mb-4 text-2xl font-bold text-white">
            Current Add-On Subscription
          </h2>
          <div className="space-y-2 text-lg font-bold text-white">
            <p>Add-On: Payment System & Automated Project Management</p>
            <p>Amount: $399.99/year</p>
            <p>Status: Active</p>
            <p>Next billing date: {new Date().toLocaleDateString()}</p>
          </div>
        </div>
      )}

      <div className="mb-8">
        <h1 className="mb-6 text-3xl font-bold text-white">
          Available Add-Ons
        </h1>

        <div className="grid gap-8 md:grid-cols-1">
          <div className="flex flex-col rounded-lg border border-form-strokedark bg-boxdark-2 p-6 shadow-lg">
            <h3 className="mb-4 text-2xl font-bold text-white">
              Payment System & Automated Project Management
            </h3>
            <div className="mb-6">
              <span className="text-4xl font-bold text-primary">$399.99</span>
              <span className="text-bodydark">/year</span>
            </div>
            <div className="mb-6 flex-grow">
              <h4 className="mb-2 font-semibold text-white">Features:</h4>
              <ul className="space-y-2">
                {[
                  "Automated Payment Processing",
                  "Client Invoice Management",
                  "Project Timeline Automation",
                  "Deadline Tracking",
                  "Client Payment Portal",
                  "Payment Reminders",
                  "Deposit Management",
                ].map((feature, index) => (
                  <li key={index} className="flex items-center text-bodydark">
                    <svg
                      className="mr-2 h-5 w-5 text-primary"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {!hasRequiredSubscription && (
              <div className="mb-6 rounded-lg bg-meta-4 p-4">
                <p className="text-white">
                  <strong>Note:</strong> This add-on requires a subscription to
                  either "The Portal" or "Complete Suite" plan.
                </p>
              </div>
            )}

            <div className="mb-6">
              <p className="mb-2 text-bodydark">
                Payment will be processed via credit card using our secure
                payment system.
              </p>
            </div>

            <LazyLoad>
              <button
                onClick={() => handleAddOnSubscribe()}
                disabled={!hasRequiredSubscription || addOnSubscription}
                className={`w-full rounded-full border border-primary px-8 py-3 font-semibold transition-all hover:bg-primary/80 ${
                  !hasRequiredSubscription || addOnSubscription
                    ? "cursor-not-allowed opacity-50"
                    : "bg-transparent text-primary hover:text-white"
                }`}
              >
                {addOnSubscription ? "Currently Subscribed" : "Subscribe Now"}
              </button>
            </LazyLoad>
          </div>
        </div>

        <CardDetailsModal
          isOpen={isAddOnModalOpen}
          closeModal={() => setIsAddOnModalOpen(false)}
          onSubmit={handleAddOnCardSubmit}
          loading={isAddOnProcessing}
          couponCode={couponCode}
          setCouponCode={setCouponCode}
        />
      </div>
    </div>
  );
};

const PlansTab = ({
  currentSubscription,
  products,
  setShowCancelModal,
  handleSubscribe,
  isProcessing,
  isModalOpen,
  setIsModalOpen,
  handleCardSubmit,
  selectedProjectRange,
  setSelectedProjectRange,
  selectedInterval,
  setSelectedInterval,
  isLoading,
  isOnboarding, // eslint-disable-line no-unused-vars
  couponCode,
  setCouponCode,
  isMainMember,
  userDetails,
}) => {
  const projectRanges = {
    "1-50": {
      portal: { monthly: 150, annual: 1500 },
      studio: { monthly: 150, annual: 1500 },
      complete: { monthly: 250, annual: 2500 },
    },
    "51-100": {
      portal: { monthly: 175, annual: 1750 },
      studio: { monthly: 175, annual: 1750 },
      complete: { monthly: 275, annual: 2750 },
    },
    "101-150": {
      portal: { monthly: 200, annual: 2000 },
      studio: { monthly: 200, annual: 2000 },
      complete: { monthly: 300, annual: 3000 },
    },
    "151-200": {
      portal: { monthly: 225, annual: 2250 },
      studio: { monthly: 225, annual: 2250 },
      complete: { monthly: 325, annual: 3250 },
    },
    "201+": {
      portal: { monthly: 250, annual: 2500 },
      studio: { monthly: 250, annual: 2500 },
      complete: { monthly: 350, annual: 3500 },
    },
  };

  const planFeatures = {
    "The Portal": [
      "Project Management",
      "Project Calendar",
      "Client Login Portal",
      "Digital 8-count sheets",
      "Automated Music Licenses",
      "Automated Reminder Emails",
      "Automated Music Surveys",
      "Project Edit Management",
      "8-Count Track Management",
      "Custom Email Domain",
    ],
    "The Studio": [
      "Automated Music Surveys",
      "Project Management",
      "Project Calendar",
      "Project Budget Review",
      "Automated Vocal Orders",
      "Excel Style Order View",
      "Automated Reminder Emails",
      "Company Logo Customization",
      "Custom Email Domain",
    ],
    "Complete Suite": [
      "Everything in The Portal",
      "Everything in The Studio",
      "Priority Support",
      "Dedicated Account Manager",
    ],
  };

  const planKeys = {
    "The Portal": "portal",
    "The Studio": "studio",
    "Complete Suite": "complete",
  };

  // Check if this is the user's current subscription tier
  const isCurrentSubscriptionTier = (productId, projectRange, interval) => {
    if (!currentSubscription) return false;

    // Check if product ID matches
    const productMatches = currentSubscription.planId === productId;
    if (!productMatches) return false;

    // Check if project range matches
    const currentRange = extractProjectRange(currentSubscription.plan_name);
    const rangeMatches = currentRange === projectRange;

    // Check if interval matches (monthly/yearly)
    const intervalMatches =
      (interval === "year" && currentSubscription.interval === "year") ||
      (interval === "month" && currentSubscription.interval === "month");

    return productMatches && rangeMatches && intervalMatches;
  };

  const getButtonText = (productId, projectRange, interval) => {
    // Check if this exact tier is the current subscription
    if (isCurrentSubscriptionTier(productId, projectRange, interval)) {
      return "Current Plan";
    }

    // The Portal is ID 2, The Studio is ID 3, Complete Suite is ID 4
    if (
      (currentSubscription?.planId === 2 &&
        (productId === 3 || productId === 4)) || // From Portal to Studio or Complete
      (currentSubscription?.planId === 3 && productId === 4) // From Studio to Complete
    ) {
      return "Upgrade Now";
    }

    return "Subscribe Now";
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  // Show sub-member notice if user is not a main member
  if (!isMainMember) {
    return (
      <div className="mx-auto h-full">
        <div className="mb-8 rounded-lg border border-primary bg-boxdark p-6">
          <h2 className="mb-4 text-2xl font-bold text-white">
            Team Member Account
          </h2>
          <div className="space-y-4">
            <p className="text-bodydark">
              You are a team member under a main account. Your subscription is
              managed by your main account holder.
            </p>

            <div className="rounded-lg bg-meta-4 p-4">
              <p className="text-white">
                <strong>Note:</strong> To manage subscriptions or upgrade plans,
                please contact your main account holder.
              </p>
            </div>
          </div>
        </div>

        {/* Show current subscription details in the same format as main members */}
        {currentSubscription && (
          <div className="mb-8 rounded-lg bg-boxdark p-6">
            <h2 className="mb-4 text-2xl font-bold text-white">
              Current Subscription (Inherited)
            </h2>
            <div className="space-y-2 text-lg font-bold text-white">
              <p>Plan: {currentSubscription.plan_name ?? "None"}</p>
              <p>
                Amount: ${currentSubscription.amount}/
                {currentSubscription.interval}
              </p>
              <p>
                Status: {currentSubscription.status ? "Active" : "Inactive"}
              </p>
              <p className="text-sm text-bodydark">
                This subscription is managed by your main account holder.
              </p>
            </div>
          </div>
        )}

        {/* Show all available plans in read-only mode */}
        <div className="mb-8">
          <h1 className="mb-6 text-3xl font-bold text-white">
            Available Plans
          </h1>
          <p className="mb-6 text-bodydark">
            These are the plans available in the system. Your access is
            determined by your main account holder's subscription.
          </p>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {products.map((product) => {
              const planKey = planKeys[product.name];
              const pricing =
                projectRanges[selectedProjectRange]?.[planKey]?.[
                  selectedInterval === "year" ? "annual" : "monthly"
                ];

              // Check if this is the current plan
              const isCurrentPlan = currentSubscription?.planId === product.id;

              return (
                <div
                  key={product.id}
                  className={`flex flex-col rounded-lg border p-6 shadow-lg ${
                    isCurrentPlan
                      ? "border-primary bg-primary/10"
                      : "border-form-strokedark bg-boxdark-2"
                  }`}
                >
                  <h3 className="mb-4 text-2xl font-bold text-white">
                    {product.name}
                    {isCurrentPlan && (
                      <span className="ml-2 text-sm text-primary">
                        (Current)
                      </span>
                    )}
                  </h3>
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-primary">
                      ${pricing}
                    </span>
                    <span className="text-bodydark">
                      /{selectedInterval === "year" ? "year" : "month"}
                    </span>
                  </div>
                  <div className="mb-6 flex-grow">
                    <h4 className="mb-2 font-semibold text-white">Features:</h4>
                    <ul className="space-y-2">
                      {planFeatures[product.name].map((feature, index) => (
                        <li
                          key={index}
                          className="flex items-center text-bodydark"
                        >
                          <svg
                            className="mr-2 h-5 w-5 text-primary"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <button
                    disabled={true}
                    className="w-full cursor-not-allowed rounded-full border border-strokedark bg-transparent px-8 py-3 font-semibold text-bodydark opacity-50"
                  >
                    {isCurrentPlan
                      ? "Current Plan"
                      : "Contact Main Account Holder"}
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto h-full">
      {currentSubscription && (
        <div className="mb-8 rounded-lg bg-boxdark p-6">
          <h2 className="mb-4 text-2xl font-bold text-white">
            Current Subscription
          </h2>
          <div className="space-y-2 text-lg font-bold text-white">
            <p>Plan: {currentSubscription.plan_name ?? "None"}</p>
            <p>
              Amount: ${currentSubscription.amount}/
              {currentSubscription.interval}
            </p>
            <p>Status: {currentSubscription.status ? "Active" : "Inactive"}</p>
            {currentSubscription.currentPeriodEnd && (
              <p>
                Next billing date:{" "}
                {new Date(
                  currentSubscription.currentPeriodEnd * 1000
                ).toLocaleDateString()}
              </p>
            )}
          </div>

          {/* Cancel Subscription Button */}
          <div className="mt-4">
            <button
              onClick={() => setShowCancelModal(true)}
              className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700"
            >
              Cancel Subscription
            </button>
          </div>
        </div>
      )}

      <div className="mb-8">
        <h1 className="mb-6 text-3xl font-bold text-white">Choose Your Plan</h1>
        <div className="mb-8 flex items-center justify-center gap-4">
          <span className="whitespace-nowrap font-medium text-white">
            Show pricing for
          </span>
          <div className="w-[200px]">
            <CustomSelect2
              value={selectedProjectRange}
              onChange={(value) => setSelectedProjectRange(value)}
              className="rounded-lg border border-stroke bg-transparent px-4 py-2 text-white outline-none focus:border-primary"
            >
              <option value="1-50">1-50 Mix Projects</option>
              <option value="51-100">51-100 Mix Projects</option>
              <option value="101-150">101-150 Mix Projects</option>
              <option value="151-200">151-200 Mix Projects</option>
              <option value="201+">201+ Mix Projects</option>
            </CustomSelect2>
          </div>
          <div className="flex items-center gap-4">
            <label className="relative inline-flex cursor-pointer items-center">
              <input
                type="checkbox"
                className="peer sr-only"
                checked={selectedInterval === "year"}
                onChange={(e) =>
                  setSelectedInterval(e.target.checked ? "year" : "month")
                }
              />
              <div className="peer h-6 w-11 rounded-full bg-stroke after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full"></div>
            </label>
            <span className="text-white">Bill Annually (Save 15-20%)</span>
          </div>
        </div>

        <Elements stripe={stripePromise}>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {products.map((product) => {
              const planKey = planKeys[product.name];
              const pricing =
                projectRanges[selectedProjectRange]?.[planKey]?.[
                  selectedInterval === "year" ? "annual" : "monthly"
                ];

              return (
                <div
                  key={product.id}
                  className="flex flex-col rounded-lg border border-form-strokedark bg-boxdark-2 p-6 shadow-lg"
                >
                  <h3 className="mb-4 text-2xl font-bold text-white">
                    {product.name}
                  </h3>
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-primary">
                      ${pricing}
                    </span>
                    <span className="text-bodydark">
                      /{selectedInterval === "year" ? "year" : "month"}
                    </span>
                  </div>
                  <div className="mb-6 flex-grow">
                    <h4 className="mb-2 font-semibold text-white">Features:</h4>
                    <ul className="space-y-2">
                      {planFeatures[product.name].map((feature, index) => (
                        <li
                          key={index}
                          className="flex items-center text-bodydark"
                        >
                          <svg
                            className="mr-2 h-5 w-5 text-primary"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <LazyLoad>
                    <button
                      onClick={() => handleSubscribe(product.id)}
                      disabled={isCurrentSubscriptionTier(
                        product.id,
                        selectedProjectRange,
                        selectedInterval
                      )}
                      className={`w-full rounded-full border border-primary  px-8 py-3 font-semibold  transition-all hover:bg-primary/80  disabled:cursor-not-allowed ${
                        isCurrentSubscriptionTier(
                          product.id,
                          selectedProjectRange,
                          selectedInterval
                        )
                          ? "bg-primary text-white"
                          : "bg-transparent text-primary hover:text-white"
                      }`}
                    >
                      {getButtonText(
                        product.id,
                        selectedProjectRange,
                        selectedInterval
                      )}
                    </button>
                  </LazyLoad>
                </div>
              );
            })}
          </div>

          <CardDetailsModal
            isOpen={isModalOpen}
            closeModal={() => setIsModalOpen(false)}
            onSubmit={handleCardSubmit}
            loading={isProcessing}
            couponCode={couponCode}
            setCouponCode={setCouponCode}
          />
        </Elements>
      </div>
    </div>
  );
};

// Helper function to extract project range from plan name
const extractProjectRange = (planName) => {
  if (!planName) return null;

  // Example: "Complete Suite - 51-100 Projects - Monthly"
  const matches = planName.match(/(\d+-\d+|\d+\+) Projects/i);
  return matches ? matches[1] : null;
};

const MemberSubscriptionPage = (props) => {
  const { isOnboarding = false, onPlanSelected } = props;
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [activeTab, setActiveTab] = useState("plans");
  const [addOnSubscription, setAddOnSubscription] = useState(false);
  const [isAddOnModalOpen, setIsAddOnModalOpen] = useState(false);
  const [isAddOnProcessing, setIsAddOnProcessing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedProjectRange, setSelectedProjectRange] = useState("1-50");
  const [selectedInterval, setSelectedInterval] = useState("month");
  const [couponCode, setCouponCode] = useState("");
  const [userDetails, setUserDetails] = useState(null);
  const [isMainMember, setIsMainMember] = useState(true); // Default to true for safety

  const [cancelType, setCancelType] = useState(null);
  const navigate = useNavigate();

  const handleSubscribe = async (productId) => {
    // Find the selected product details
    const selectedProduct = products.find(
      (product) => product.id === productId
    );

    // Store the selected project range and interval for use in subscription creation
    localStorage.setItem("selectedProjectRange", selectedProjectRange);
    localStorage.setItem("selectedInterval", selectedInterval);

    if (onPlanSelected) {
      // If onPlanSelected callback is provided, use it instead of showing modal
      onPlanSelected(
        productId,
        selectedProduct,
        selectedInterval,
        selectedProjectRange
      );
    } else {
      // Otherwise, show the modal directly (original behavior)
      setSelectedPlan(productId);
      setIsModalOpen(true);
    }
  };

  const handleAddOnSubscribe = async () => {
    // Check if user has the required subscription type
    if (
      !currentSubscription ||
      (!currentSubscription.plan_name.includes("The Portal") &&
        !currentSubscription.plan_name.includes("Complete Suite"))
    ) {
      showToast(
        globalDispatch,
        "You need to subscribe to The Portal or Complete Suite plan first",
        4000,
        "error"
      );
      return;
    }

    // Show the card modal for payment
    setIsAddOnModalOpen(true);
  };

  const handleAddOnCardSubmit = async (result, couponCode = "") => {
    if (result?.error) {
      showToast(globalDispatch, result?.error?.message, 4000, "error");
      return;
    }

    setIsAddOnProcessing(true);
    const sdk = new MkdSDK();

    try {
      // Prepare payload with optional coupon
      const payload = {
        payment_info: {
          amount: 399.99,
          method: "stripe",
          cardToken: result.token.id,
        },
      };

      // Add coupon to payload if provided
      if (couponCode && couponCode.trim() !== "") {
        payload.coupon = couponCode.trim();
      }

      // Call the invoice-subscription/create endpoint
      const createResponse = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/invoice-subscription/create",
        payload,
        "POST"
      );

      if (createResponse.error) {
        throw new Error(
          createResponse.message || "Failed to create subscription"
        );
      }

      const { client_secret, payment_intent_id } = createResponse;

      if (!client_secret || !payment_intent_id) {
        throw new Error("Missing payment information in the response");
      }

      // Confirm payment with backend directly since we already have the card token
      const confirmResponse = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/user/invoice-subscription/confirm/${payment_intent_id}`,
        {},
        "POST"
      );

      if (confirmResponse.error) {
        throw new Error(confirmResponse.message || "Failed to confirm payment");
      }

      showToast(
        globalDispatch,
        "Add-on subscription created successfully",
        4000,
        "success"
      );

      setIsAddOnModalOpen(false);

      // Set add-on subscription to true after successful subscription
      setAddOnSubscription(true);
    } catch (error) {
      console.error("Error creating add-on subscription:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to create add-on subscription",
        4000,
        "error"
      );
    } finally {
      setIsAddOnProcessing(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!cancelType) {
      showToast(
        globalDispatch,
        "Please select a cancellation option",
        4000,
        "error"
      );
      return;
    }

    try {
      setIsProcessing(true);
      const sdk = new MkdSDK();
      const result = await sdk.cancelStripeSubscription(
        currentSubscription?.subId,
        {
          cancel_type: cancelType,
        }
      );

      if (!result?.error) {
        showToast(
          globalDispatch,
          `Subscription successfully cancelled ${
            cancelType === "at_period_end"
              ? "at the end of the billing period"
              : "immediately"
          }`,
          4000,
          "success"
        );

        // Refresh subscription data
        fetchData();
        setShowCancelModal(false);
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  // Fetch user details to check has_invoice_subscription status and main member status
  const fetchUserDetails = async () => {
    try {
      const userId = localStorage.getItem("user");
      if (userId) {
        const result = await getUserDetailsByIdAPI(Number(userId));
        if (!result.error && result.model) {
          setUserDetails(result.model);

          // Check if user is a main member (not a sub-member)
          const isMain =
            !result.model.main_user_details ||
            result.model.main_user_details.is_self === true;
          setIsMainMember(isMain);

          // Check if user has invoice subscription
          // If has_invoice_subscription is not null, user has a subscription
          setAddOnSubscription(result.model.has_invoice_subscription !== null);

          console.log("User details:", result.model);
          console.log("Is main member:", isMain);
          console.log("Main user details:", result.model.main_user_details);

          // If user is a sub-member, fetch the main member's subscription details
          if (!isMain && result.model.main_user_details) {
            await fetchMainMemberSubscription(result.model.main_user_details);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
    }
  };

  // Fetch main member's subscription details for sub-members
  const fetchMainMemberSubscription = async (mainUserDetails) => {
    try {
      const sdk = new MkdSDK();

      // Fetch prices to map plan ID to actual plan details
      const pricesResult = await sdk.callRawAPI(
        `/v4/api/records/stripe_price?page=${1},${100}`,
        [],
        "GET"
      );

      if (!pricesResult.error && mainUserDetails.plan_id) {
        // Find the price details for the main member's plan
        const mainMemberPrice = pricesResult.list.find(
          (price) => price.product_id === mainUserDetails.plan_id
        );

        if (mainMemberPrice) {
          // Create a subscription object similar to what main members see
          const mainMemberSubscription = {
            planId: mainUserDetails.plan_id,
            subId: mainUserDetails.subscription_id,
            plan_name: mainMemberPrice.name || "Unknown Plan",
            amount: mainMemberPrice.amount || 0,
            interval: mainMemberPrice.object
              ? JSON.parse(mainMemberPrice.object)?.recurring?.interval
              : "month",
            status: true, // Assume active since they have access
            priceId: mainMemberPrice.id || null,
            isMainMemberSubscription: true, // Flag to indicate this is inherited
          };

          setCurrentSubscription(mainMemberSubscription);
          console.log(
            "Main member subscription details:",
            mainMemberSubscription
          );
        }
      }
    } catch (error) {
      console.error("Error fetching main member subscription:", error);
    }
  };

  const handleCardSubmit = async (result, couponCode = "") => {
    if (result?.error) {
      showToast(globalDispatch, result?.error?.message, 4000, "error");
      return;
    }

    setIsProcessing(true);
    const sdk = new MkdSDK();

    try {
      // Create or update Stripe customer
      const customerParams = {
        cardToken: result.token.id,
      };

      let subscriptionResult = null;
      // Get the selected project range and interval from localStorage
      const storedProjectRange =
        localStorage.getItem("selectedProjectRange") || selectedProjectRange;
      const storedInterval =
        localStorage.getItem("selectedInterval") || selectedInterval;

      // Find the appropriate price ID based on the selected product, project range, and interval
      const pricesResult = await sdk.callRawAPI(
        `/v4/api/records/stripe_price?page=${1},${100}`,
        [],
        "GET"
      );

      let priceId = null;

      if (!pricesResult.error) {
        // Find the price that matches the selected plan, project range, and interval
        const selectedPrice = pricesResult.list.find((price) => {
          // Check if this price belongs to the selected product
          const productMatch = price.product_id === selectedPlan;

          // Check if the price name contains the selected project range
          const rangeMatch =
            price.name && price.name.includes(storedProjectRange);

          // Check if the price interval matches the selected interval
          let intervalMatch = false;
          if (price.object) {
            try {
              const priceObject = JSON.parse(price.object);
              intervalMatch =
                (storedInterval === "month" &&
                  priceObject.recurring.interval === "month") ||
                (storedInterval === "year" &&
                  priceObject.recurring.interval === "year");
            } catch (e) {
              console.error("Error parsing price object:", e);
            }
          }

          return productMatch && rangeMatch && intervalMatch;
        });

        if (selectedPrice) {
          priceId = selectedPrice.stripe_id;
        }
      }

      if (!priceId) {
        showToast(
          globalDispatch,
          "Could not find a matching price for the selected plan",
          4000,
          "error"
        );
        setIsProcessing(false);
        return;
      }

      if (!currentSubscription?.subId) {
        const result = await sdk.createStripeCustomer(customerParams);

        // Create subscription with optional coupon
        const subscriptionPayload = {
          planId: selectedPlan,
          priceId: priceId, // Use the specific price ID for the selected tier
        };

        // Add coupon to payload if provided
        if (couponCode && couponCode.trim() !== "") {
          subscriptionPayload.coupon = couponCode.trim();
        }

        subscriptionResult = await sdk.createStripeSubscription(
          subscriptionPayload
        );
        console.log(result);

        return;
      }

      // Create subscription with optional coupon
      const subscriptionPayload = {
        // userId: localStorage.getItem("user"),
        newPlanId: selectedPlan,
        activeSubscriptionId: currentSubscription?.subId,
      };

      // Add coupon to payload if provided
      if (couponCode && couponCode.trim() !== "") {
        subscriptionPayload.coupon = couponCode.trim();
      }

      subscriptionResult = await sdk.changeStripeSubscription(
        subscriptionPayload
      );

      if (!subscriptionResult.error) {
        showToast(
          globalDispatch,
          "Successfully subscribed to plan",
          4000,
          "success"
        );

        // Refresh subscription data
        const newSubData = await sdk.getCustomerStripeSubscription();
        setIsModalOpen(false);
        if (!newSubData?.error) {
          // Fetch price details for the current subscription
          const pricesResult = await sdk.callRawAPI(
            `/v4/api/records/stripe_price?page=${1},${100}`,
            [],
            "GET"
          );

          // Find the price details for the current subscription
          const currentPrice = pricesResult.list.find(
            (price) => price.product_id === newSubData?.customer?.planId
          );

          // Format subscription data consistently with the same structure used elsewhere
          setCurrentSubscription({
            ...newSubData?.customer,
            plan_name: currentPrice?.name || newSubData?.customer?.plan_name,
            amount: currentPrice?.amount || newSubData?.customer?.amount,
            interval: currentPrice?.object
              ? JSON.parse(currentPrice.object)?.recurring?.interval
              : "month",
          });

          if (isOnboarding) {
            navigate("/member/dashboard");
          }
        }
      } else {
        showToast(
          globalDispatch,
          "Failed to create subscription",
          4000,
          "error"
        );
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const fetchData = async () => {
    const sdk = new MkdSDK();
    setIsLoading(true);
    try {
      // Fetch products
      const productsResult = await sdk.callRawAPI(
        `/v4/api/records/stripe_product?page=${1},${100}`,
        [],
        "GET"
      );

      const pricesResult = await sdk.callRawAPI(
        `/v4/api/records/stripe_price?page=${1},${100}`,
        [],
        "GET"
      );

      if (!productsResult.error) {
        // Filter out the test product (plan A) and sort by ID
        const filteredProducts = productsResult.list
          .filter((product) => product.name !== "plan A")
          .sort((a, b) => a.id - b.id);
        setProducts(filteredProducts);

        // Store products in global context
        setGLobalProperty(
          globalDispatch,
          filteredProducts,
          "subscription.products"
        );
      }

      if (!pricesResult.error) {
        // Store prices in global context
        setGLobalProperty(
          globalDispatch,
          pricesResult.list,
          "subscription.prices"
        );
      }

      if (!isOnboarding) {
        // Fetch user details first to determine if user is main member
        await fetchUserDetails();

        // Fetch current subscription only if not in onboarding mode
        const subscriptionResult = await sdk.getCustomerStripeSubscription();
        if (!subscriptionResult.error) {
          // Find the price details for the current subscription
          const currentPrice = pricesResult.list.find(
            (price) => price.product_id === subscriptionResult?.customer?.planId
          );

          // Enhance subscription data with price details
          const enhancedSubscription = {
            ...subscriptionResult?.customer,
            plan_name:
              currentPrice?.name || subscriptionResult?.customer?.plan_name,
            amount:
              currentPrice?.amount || subscriptionResult?.customer?.amount,
            interval: currentPrice?.object
              ? JSON.parse(currentPrice.object)?.recurring?.interval
              : "month",
            priceId: currentPrice?.id || null,
          };

          setCurrentSubscription(enhancedSubscription);

          // Set the initial selectedProjectRange based on the current subscription
          if (enhancedSubscription.plan_name) {
            const currentRange = extractProjectRange(
              enhancedSubscription.plan_name
            );
            if (currentRange) {
              setSelectedProjectRange(currentRange);
            }
          }

          // Set the initial selectedInterval based on the current subscription
          if (enhancedSubscription.interval) {
            setSelectedInterval(enhancedSubscription.interval);
          }

          // Store subscription in global context
          setGLobalProperty(
            globalDispatch,
            enhancedSubscription,
            "subscription.currentSubscription"
          );

          // Calculate and store project limit
          const projectLimit = calculateProjectLimit(
            enhancedSubscription,
            pricesResult.list
          );
          setProjectLimit(globalDispatch, projectLimit);
        }

        // Fetch project counts
        try {
          const projectCountsResult = await sdk.callRawAPI(
            "/v3/api/custom/equality_record/project/subscription/project-counts",
            [],
            "GET"
          );

          if (!projectCountsResult.error) {
            // Store project counts in global context
            setProjectCounts(
              globalDispatch,
              projectCountsResult,
              projectCountsResult.subscription_period
            );
          }
        } catch (error) {
          console.error("Error fetching project counts:", error);
        }
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsLoading(false);
      // Update loading state in global context
      setGLobalProperty(globalDispatch, false, "subscription.isLoading");
    }
  };

  useEffect(() => {
    // Set loading state in global context
    setGLobalProperty(globalDispatch, true, "subscription.isLoading");
    fetchData();
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "subscription",
      },
    });
  }, [dispatch, globalDispatch, isOnboarding]);

  return (
    <div
      className={`${
        isOnboarding ? "w-full" : "mx-auto w-full p-4 md:p-6 2xl:p-10"
      } `}
    >
      {!isOnboarding && (
        <div className="mb-6">
          <h2 className="text-title-md2 font-semibold text-white">
            Subscription Management
          </h2>
        </div>
      )}

      <div className="rounded border border-strokedark bg-boxdark">
        {!isOnboarding && (
          <div className="flex flex-wrap border-b border-strokedark">
            <button
              onClick={() => setActiveTab("plans")}
              className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === "plans"
                  ? "bg-primary text-white"
                  : "text-white hover:bg-meta-4"
              }`}
            >
              Plans
            </button>
            <button
              onClick={() => setActiveTab("addons")}
              className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === "addons"
                  ? "bg-primary text-white"
                  : "text-white hover:bg-meta-4"
              }`}
            >
              Add-ons
            </button>
            <button
              onClick={() => setActiveTab("invoices")}
              className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === "invoices"
                  ? "bg-primary text-white"
                  : "text-white hover:bg-meta-4"
              }`}
            >
              Billing History
            </button>
          </div>
        )}

        <div className="p-6">
          {activeTab === "plans" && (
            <PlansTab
              setShowCancelModal={setShowCancelModal}
              currentSubscription={currentSubscription}
              products={products}
              handleSubscribe={handleSubscribe}
              isProcessing={isProcessing}
              isModalOpen={isModalOpen}
              setIsModalOpen={setIsModalOpen}
              handleCardSubmit={handleCardSubmit}
              selectedProjectRange={selectedProjectRange}
              setSelectedProjectRange={setSelectedProjectRange}
              selectedInterval={selectedInterval}
              setSelectedInterval={setSelectedInterval}
              isLoading={isLoading}
              isOnboarding={isOnboarding}
              couponCode={couponCode}
              setCouponCode={setCouponCode}
              isMainMember={isMainMember}
              userDetails={userDetails}
            />
          )}
          {activeTab === "addons" && !isOnboarding && (
            <AddOnsTab
              addOnSubscription={addOnSubscription}
              handleAddOnSubscribe={handleAddOnSubscribe}
              isAddOnProcessing={isAddOnProcessing}
              isAddOnModalOpen={isAddOnModalOpen}
              setIsAddOnModalOpen={setIsAddOnModalOpen}
              handleAddOnCardSubmit={handleAddOnCardSubmit}
              isLoading={isLoading}
              currentSubscription={currentSubscription}
              couponCode={couponCode}
              setCouponCode={setCouponCode}
              isMainMember={isMainMember}
              userDetails={userDetails}
            />
          )}
          {activeTab === "invoices" && !isOnboarding && <InvoicesTab />}
        </div>
      </div>

      {/* Cancel Subscription Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto overflow-x-hidden bg-black bg-opacity-50">
          <div className="relative w-full max-w-md rounded-lg bg-boxdark p-6 text-white shadow-lg">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-50">
                Cancel Subscription
              </h3>
              <button
                type="button"
                className="text-gray-100 hover:text-gray-500"
                onClick={() => setShowCancelModal(false)}
              >
                <span className="sr-only">Close</span>
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="mb-6">
              <p className="mb-4 text-sm text-gray-100">
                Are you sure you want to cancel your subscription?
              </p>
              <div className="space-y-3">
                <p className="text-sm font-medium text-gray-100">Cancel:</p>
                <div className="flex flex-col space-y-2">
                  <label className="flex items-center text-white">
                    <input
                      type="radio"
                      name="cancel_type"
                      value="at_period_end"
                      onChange={(e) => setCancelType(e.target.value)}
                      className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                    />
                    <span className="ml-2 text-sm text-gray-100">
                      At Period End
                    </span>
                  </label>
                  <label className="flex items-center text-white">
                    <input
                      type="radio"
                      name="cancel_type"
                      value="lifetime"
                      onChange={(e) => setCancelType(e.target.value)}
                      className="h-4 w-4 border-form-strokedark text-primary focus:ring-primary"
                    />
                    <span className="ml-2 text-sm text-white">Instantly</span>
                  </label>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="rounded-md border border-gray-700 bg-boxdark-2 px-4 py-2 text-sm font-medium text-gray-200 shadow-sm hover:bg-gray-600"
                onClick={() => setShowCancelModal(false)}
              >
                Cancel
              </button>
              <button
                type="button"
                className={`rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 ${
                  !cancelType || isProcessing
                    ? "cursor-not-allowed opacity-50"
                    : ""
                }`}
                onClick={handleCancelSubscription}
                disabled={!cancelType || isProcessing}
              >
                {isProcessing ? "Processing..." : "Confirm Cancellation"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemberSubscriptionPage;
