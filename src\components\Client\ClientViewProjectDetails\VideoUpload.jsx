import React from "react";

const VideoUpload = ({
  maxFileSize = 500,

  fileValues,
  setFileValues,
}) => {
  const [maxFileSizeStr, setMaxFileSizeStr] = React.useState(
    `${maxFileSize}MB`
  );
  const convertMBToGB = (mb) => {
    return {
      value: mb / 1024,
      unit: "GB",
    };
  };

  const saveFiles = (e) => {
    const files = e.target.files;

    if (files.length > 0) {
      setFileValues([...fileValues, ...files]);
    }
  };

  React.useEffect(() => {
    if (maxFileSize > 1024) {
      const { value, unit } = convertMBToGB(maxFileSize);
      let maxFileSizeStr = `${value}${unit}`;
      setMaxFileSizeStr(maxFileSizeStr);
    }
  }, [maxFileSize]);

  return (
    <>
      <div className="mt-3 flex h-[45px] w-full items-center rounded border-2 border-form-strokedark bg-form-input">
        <div className="flex h-full min-w-max items-center justify-center bg-boxdark-2 px-3 text-white">
          Choose File
        </div>
        <input
          onChange={saveFiles}
          type="file"
          required
          accept="video/*"
          className="block w-full border-transparent bg-transparent py-2 pl-3 text-white outline-none placeholder:text-gray-500 focus:ring-0 focus-visible:outline-transparent"
          placeholder="No File Choosen"
        />
        {/* <button
          type='button'
          className='w-[60px] rounded bg-green-600 p-2 text-sm font-semibold text-white hover:bg-green-700'
        >
          {isUploading ? (
            <ClipLoader size={12} color='white' />
          ) : (
            <span> Upload</span>
          )}
        </button> */}
      </div>

      <div className="mb-2 text-xs text-gray-500">
        Maximum file size: {maxFileSizeStr}
      </div>
    </>
  );
};

export default VideoUpload;
