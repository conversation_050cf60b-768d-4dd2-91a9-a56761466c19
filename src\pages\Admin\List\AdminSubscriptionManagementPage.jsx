import React, { useState } from "react";
import AdminStripePlansListPage from "./AdminStripePlansListPage";
import AdminStripePricesListPage from "./AdminStripePricesListPage";
import AdminCouponsListPage from "./AdminCouponsListPage";
import AdminStripeSubscriptionsListPage from "Pages/AdminSubscriptionListPage";

const TABS = {
  PLANS: "plans",
  PRICES: "prices",
  COUPONS: "coupons",
  SUBSCRIPTIONS: "subscriptions",
};

const AdminSubscriptionManagementPage = () => {
  const [activeTab, setActiveTab] = useState(TABS.PLANS);

  const renderTabContent = () => {
    switch (activeTab) {
      case TABS.PLANS:
        return <AdminStripePlansListPage />;
      case TABS.PRICES:
        return <AdminStripePricesListPage />;
      case TABS.COUPONS:
        return <AdminCouponsListPage />;
      case TABS.SUBSCRIPTIONS:
        return <AdminStripeSubscriptionsListPage />;
      default:
        return null;
    }
  };

  return (
    <div className="p-4 mx-auto max-w-screen md:p-6 2xl:p-10">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white">
          Subscription Management
        </h2>
      </div>

      <div className="rounded-sm bg-boxdark">
        <div className="flex flex-wrap border-b border-strokedark">
          <button
            onClick={() => setActiveTab(TABS.PLANS)}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === TABS.PLANS
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Plans
          </button>
          <button
            onClick={() => setActiveTab(TABS.PRICES)}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === TABS.PRICES
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Prices
          </button>
          <button
            onClick={() => setActiveTab(TABS.COUPONS)}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === TABS.COUPONS
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Coupons
          </button>
          <button
            onClick={() => setActiveTab(TABS.SUBSCRIPTIONS)}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === TABS.SUBSCRIPTIONS
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Subscriptions
          </button>
        </div>

        {/* Tab Content */}
        <div className="">{renderTabContent()}</div>
      </div>
    </div>
  );
};

export default AdminSubscriptionManagementPage;
