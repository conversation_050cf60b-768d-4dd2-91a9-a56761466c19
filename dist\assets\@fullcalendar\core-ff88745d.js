var Ae,A,Nn,xn,se,$,Ot,Bn,Hn,Be={},Pn=[],Gr=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function V(t,e){for(var n in e)t[n]=e[n];return t}function Un(t){var e=t.parentNode;e&&e.removeChild(t)}function g(t,e,n){var r,i,s,a={};for(s in e)s=="key"?r=e[s]:s=="ref"?i=e[s]:a[s]=e[s];if(arguments.length>2&&(a.children=arguments.length>3?Ae.call(arguments,2):n),typeof t=="function"&&t.defaultProps!=null)for(s in t.defaultProps)a[s]===void 0&&(a[s]=t.defaultProps[s]);return ce(t,a,r,i,null)}function ce(t,e,n,r,i){var s={type:t,props:e,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:i??++Nn};return i==null&&A.vnode!=null&&A.vnode(s),s}function Nt(){return{current:null}}function P(t){return t.children}function qr(t,e,n,r,i){var s;for(s in n)s==="children"||s==="key"||s in e||He(t,s,null,n[s],r);for(s in e)i&&typeof e[s]!="function"||s==="children"||s==="key"||s==="value"||s==="checked"||n[s]===e[s]||He(t,s,e[s],n[s],r)}function xt(t,e,n){e[0]==="-"?t.setProperty(e,n??""):t[e]=n==null?"":typeof n!="number"||Gr.test(e)?n:n+"px"}function He(t,e,n,r,i){var s;e:if(e==="style")if(typeof n=="string")t.style.cssText=n;else{if(typeof r=="string"&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||xt(t.style,e,"");if(n)for(e in n)r&&n[e]===r[e]||xt(t.style,e,n[e])}else if(e[0]==="o"&&e[1]==="n")s=e!==(e=e.replace(/Capture$/,"")),e=e.toLowerCase()in t?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+s]=n,n?r||t.addEventListener(e,s?Ht:Bt,s):t.removeEventListener(e,s?Ht:Bt,s);else if(e!=="dangerouslySetInnerHTML"){if(i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e in t)try{t[e]=n??"";break e}catch{}typeof n=="function"||(n==null||n===!1&&e.indexOf("-")==-1?t.removeAttribute(e):t.setAttribute(e,n))}}function Bt(t){se=!0;try{return this.l[t.type+!1](A.event?A.event(t):t)}finally{se=!1}}function Ht(t){se=!0;try{return this.l[t.type+!0](A.event?A.event(t):t)}finally{se=!1}}function x(t,e){this.props=t,this.context=e}function fe(t,e){if(e==null)return t.__?fe(t.__,t.__.__k.indexOf(t)+1):null;for(var n;e<t.__k.length;e++)if((n=t.__k[e])!=null&&n.__e!=null)return n.__e;return typeof t.type=="function"?fe(t):null}function Fn(t){var e,n;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if((n=t.__k[e])!=null&&n.__e!=null){t.__e=t.__c.base=n.__e;break}return Fn(t)}}function Zr(t){se?setTimeout(t):Bn(t)}function at(t){(!t.__d&&(t.__d=!0)&&$.push(t)&&!Pe.__r++||Ot!==A.debounceRendering)&&((Ot=A.debounceRendering)||Zr)(Pe)}function Pe(){var t,e,n,r,i,s,a,l;for($.sort(function(o,u){return o.__v.__b-u.__v.__b});t=$.shift();)t.__d&&(e=$.length,r=void 0,i=void 0,a=(s=(n=t).__v).__e,(l=n.__P)&&(r=[],(i=V({},s)).__v=s.__v+1,mt(l,s,i,n.__n,l.ownerSVGElement!==void 0,s.__h!=null?[a]:null,r,a??fe(s),s.__h),Wn(r,s),s.__e!=a&&Fn(s)),$.length>e&&$.sort(function(o,u){return o.__v.__b-u.__v.__b}));Pe.__r=0}function Vn(t,e,n,r,i,s,a,l,o,u){var c,p,f,d,m,_,v,b=r&&r.__k||Pn,C=b.length;for(n.__k=[],c=0;c<e.length;c++)if((d=n.__k[c]=(d=e[c])==null||typeof d=="boolean"?null:typeof d=="string"||typeof d=="number"||typeof d=="bigint"?ce(null,d,null,null,d):Array.isArray(d)?ce(P,{children:d},null,null,null):d.__b>0?ce(d.type,d.props,d.key,d.ref?d.ref:null,d.__v):d)!=null){if(d.__=n,d.__b=n.__b+1,(f=b[c])===null||f&&d.key==f.key&&d.type===f.type)b[c]=void 0;else for(p=0;p<C;p++){if((f=b[p])&&d.key==f.key&&d.type===f.type){b[p]=void 0;break}f=null}mt(t,d,f=f||Be,i,s,a,l,o,u),m=d.__e,(p=d.ref)&&f.ref!=p&&(v||(v=[]),f.ref&&v.push(f.ref,null,d),v.push(p,d.__c||m,d)),m!=null?(_==null&&(_=m),typeof d.type=="function"&&d.__k===f.__k?d.__d=o=jn(d,o,t):o=zn(t,d,f,b,m,o),typeof n.type=="function"&&(n.__d=o)):o&&f.__e==o&&o.parentNode!=t&&(o=fe(f))}for(n.__e=_,c=C;c--;)b[c]!=null&&(typeof n.type=="function"&&b[c].__e!=null&&b[c].__e==n.__d&&(n.__d=Ln(r).nextSibling),Gn(b[c],b[c]));if(v)for(c=0;c<v.length;c++)Qn(v[c],v[++c],v[++c])}function jn(t,e,n){for(var r,i=t.__k,s=0;i&&s<i.length;s++)(r=i[s])&&(r.__=t,e=typeof r.type=="function"?jn(r,e,n):zn(n,r,r,i,r.__e,e));return e}function Ue(t,e){return e=e||[],t==null||typeof t=="boolean"||(Array.isArray(t)?t.some(function(n){Ue(n,e)}):e.push(t)),e}function zn(t,e,n,r,i,s){var a,l,o;if(e.__d!==void 0)a=e.__d,e.__d=void 0;else if(n==null||i!=s||i.parentNode==null)e:if(s==null||s.parentNode!==t)t.appendChild(i),a=null;else{for(l=s,o=0;(l=l.nextSibling)&&o<r.length;o+=1)if(l==i)break e;t.insertBefore(i,s),a=s}return a!==void 0?a:i.nextSibling}function Ln(t){var e,n,r;if(t.type==null||typeof t.type=="string")return t.__e;if(t.__k){for(e=t.__k.length-1;e>=0;e--)if((n=t.__k[e])&&(r=Ln(n)))return r}return null}function mt(t,e,n,r,i,s,a,l,o){var u,c,p,f,d,m,_,v,b,C,k,R,U,M,N,E=e.type;if(e.constructor!==void 0)return null;n.__h!=null&&(o=n.__h,l=e.__e=n.__e,e.__h=null,s=[l]),(u=A.__b)&&u(e);try{e:if(typeof E=="function"){if(v=e.props,b=(u=E.contextType)&&r[u.__c],C=u?b?b.props.value:u.__:r,n.__c?_=(c=e.__c=n.__c).__=c.__E:("prototype"in E&&E.prototype.render?e.__c=c=new E(v,C):(e.__c=c=new x(v,C),c.constructor=E,c.render=$r),b&&b.sub(c),c.props=v,c.state||(c.state={}),c.context=C,c.__n=r,p=c.__d=!0,c.__h=[],c._sb=[]),c.__s==null&&(c.__s=c.state),E.getDerivedStateFromProps!=null&&(c.__s==c.state&&(c.__s=V({},c.__s)),V(c.__s,E.getDerivedStateFromProps(v,c.__s))),f=c.props,d=c.state,c.__v=e,p)E.getDerivedStateFromProps==null&&c.componentWillMount!=null&&c.componentWillMount(),c.componentDidMount!=null&&c.__h.push(c.componentDidMount);else{if(E.getDerivedStateFromProps==null&&v!==f&&c.componentWillReceiveProps!=null&&c.componentWillReceiveProps(v,C),!c.__e&&c.shouldComponentUpdate!=null&&c.shouldComponentUpdate(v,c.__s,C)===!1||e.__v===n.__v){for(e.__v!==n.__v&&(c.props=v,c.state=c.__s,c.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.forEach(function(Ce){Ce&&(Ce.__=e)}),k=0;k<c._sb.length;k++)c.__h.push(c._sb[k]);c._sb=[],c.__h.length&&a.push(c);break e}c.componentWillUpdate!=null&&c.componentWillUpdate(v,c.__s,C),c.componentDidUpdate!=null&&c.__h.push(function(){c.componentDidUpdate(f,d,m)})}if(c.context=C,c.props=v,c.__P=t,R=A.__r,U=0,"prototype"in E&&E.prototype.render){for(c.state=c.__s,c.__d=!1,R&&R(e),u=c.render(c.props,c.state,c.context),M=0;M<c._sb.length;M++)c.__h.push(c._sb[M]);c._sb=[]}else do c.__d=!1,R&&R(e),u=c.render(c.props,c.state,c.context),c.state=c.__s;while(c.__d&&++U<25);c.state=c.__s,c.getChildContext!=null&&(r=V(V({},r),c.getChildContext())),p||c.getSnapshotBeforeUpdate==null||(m=c.getSnapshotBeforeUpdate(f,d)),N=u!=null&&u.type===P&&u.key==null?u.props.children:u,Vn(t,Array.isArray(N)?N:[N],e,n,r,i,s,a,l,o),c.base=e.__e,e.__h=null,c.__h.length&&a.push(c),_&&(c.__E=c.__=null),c.__e=!1}else s==null&&e.__v===n.__v?(e.__k=n.__k,e.__e=n.__e):e.__e=Yr(n.__e,e,n,r,i,s,a,o);(u=A.diffed)&&u(e)}catch(Ce){e.__v=null,(o||s!=null)&&(e.__e=l,e.__h=!!o,s[s.indexOf(l)]=null),A.__e(Ce,e,n)}}function Wn(t,e){A.__c&&A.__c(e,t),t.some(function(n){try{t=n.__h,n.__h=[],t.some(function(r){r.call(n)})}catch(r){A.__e(r,n.__v)}})}function Yr(t,e,n,r,i,s,a,l){var o,u,c,p=n.props,f=e.props,d=e.type,m=0;if(d==="svg"&&(i=!0),s!=null){for(;m<s.length;m++)if((o=s[m])&&"setAttribute"in o==!!d&&(d?o.localName===d:o.nodeType===3)){t=o,s[m]=null;break}}if(t==null){if(d===null)return document.createTextNode(f);t=i?document.createElementNS("http://www.w3.org/2000/svg",d):document.createElement(d,f.is&&f),s=null,l=!1}if(d===null)p===f||l&&t.data===f||(t.data=f);else{if(s=s&&Ae.call(t.childNodes),u=(p=n.props||Be).dangerouslySetInnerHTML,c=f.dangerouslySetInnerHTML,!l){if(s!=null)for(p={},m=0;m<t.attributes.length;m++)p[t.attributes[m].name]=t.attributes[m].value;(c||u)&&(c&&(u&&c.__html==u.__html||c.__html===t.innerHTML)||(t.innerHTML=c&&c.__html||""))}if(qr(t,f,p,i,l),c)e.__k=[];else if(m=e.props.children,Vn(t,Array.isArray(m)?m:[m],e,n,r,i&&d!=="foreignObject",s,a,s?s[0]:n.__k&&fe(n,0),l),s!=null)for(m=s.length;m--;)s[m]!=null&&Un(s[m]);l||("value"in f&&(m=f.value)!==void 0&&(m!==t.value||d==="progress"&&!m||d==="option"&&m!==p.value)&&He(t,"value",m,p.value,!1),"checked"in f&&(m=f.checked)!==void 0&&m!==t.checked&&He(t,"checked",m,p.checked,!1))}return t}function Qn(t,e,n){try{typeof t=="function"?t(e):t.current=e}catch(r){A.__e(r,n)}}function Gn(t,e,n){var r,i;if(A.unmount&&A.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||Qn(r,null,e)),(r=t.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(s){A.__e(s,e)}r.base=r.__P=null,t.__c=void 0}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&Gn(r[i],e,n||typeof t.type!="function");n||t.__e==null||Un(t.__e),t.__=t.__e=t.__d=void 0}function $r(t,e,n){return this.constructor(t,n)}function he(t,e,n){var r,i,s;A.__&&A.__(t,e),i=(r=typeof n=="function")?null:n&&n.__k||e.__k,s=[],mt(e,t=(!r&&n||e).__k=g(P,null,[t]),i||Be,Be,e.ownerSVGElement!==void 0,!r&&n?[n]:i?null:e.firstChild?Ae.call(e.childNodes):null,s,!r&&n?n:i?i.__e:e.firstChild,r),Wn(s,t)}function Bo(t,e,n){var r,i,s,a=V({},t.props);for(s in e)s=="key"?r=e[s]:s=="ref"?i=e[s]:a[s]=e[s];return arguments.length>2&&(a.children=arguments.length>3?Ae.call(arguments,2):n),ce(t.type,a,r||t.key,i||t.ref,null)}function Jr(t,e){var n={__c:e="__cC"+Hn++,__:t,Consumer:function(r,i){return r.children(i)},Provider:function(r){var i,s;return this.getChildContext||(i=[],(s={})[e]=this,this.getChildContext=function(){return s},this.shouldComponentUpdate=function(a){this.props.value!==a.value&&i.some(function(l){l.__e=!0,at(l)})},this.sub=function(a){i.push(a);var l=a.componentWillUnmount;a.componentWillUnmount=function(){i.splice(i.indexOf(a),1),l&&l.call(a)}}),r.children}};return n.Provider.__=n.Consumer.contextType=n}Ae=Pn.slice,A={__e:function(t,e,n,r){for(var i,s,a;e=e.__;)if((i=e.__c)&&!i.__)try{if((s=i.constructor)&&s.getDerivedStateFromError!=null&&(i.setState(s.getDerivedStateFromError(t)),a=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(t,r||{}),a=i.__d),a)return i.__E=i}catch(l){t=l}throw t}},Nn=0,xn=function(t){return t!=null&&t.constructor===void 0},se=!1,x.prototype.setState=function(t,e){var n;n=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=V({},this.state),typeof t=="function"&&(t=t(V({},n),this.props)),t&&V(n,t),t!=null&&this.__v&&(e&&this._sb.push(e),at(this))},x.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),at(this))},x.prototype.render=P,$=[],Bn=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Pe.__r=0,Hn=0;var Ge,T,qe,Pt,pe=0,qn=[],Me=[],Ut=A.__b,Ft=A.__r,Vt=A.diffed,jt=A.__c,zt=A.unmount;function vt(t,e){A.__h&&A.__h(T,t,pe||e),pe=0;var n=T.__H||(T.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({__V:Me}),n.__[t]}function Ho(t){return pe=1,Xr($n,t)}function Xr(t,e,n){var r=vt(Ge++,2);if(r.t=t,!r.__c&&(r.__=[n?n(e):$n(void 0,e),function(s){var a=r.__N?r.__N[0]:r.__[0],l=r.t(a,s);a!==l&&(r.__N=[l,r.__[1]],r.__c.setState({}))}],r.__c=T,!T.u)){T.u=!0;var i=T.shouldComponentUpdate;T.shouldComponentUpdate=function(s,a,l){if(!r.__c.__H)return!0;var o=r.__c.__H.__.filter(function(c){return c.__c});if(o.every(function(c){return!c.__N}))return!i||i.call(this,s,a,l);var u=!1;return o.forEach(function(c){if(c.__N){var p=c.__[0];c.__=c.__N,c.__N=void 0,p!==c.__[0]&&(u=!0)}}),!(!u&&r.__c.props===s)&&(!i||i.call(this,s,a,l))}}return r.__N||r.__}function Po(t,e){var n=vt(Ge++,3);!A.__s&&Yn(n.__H,e)&&(n.__=t,n.i=e,T.__H.__h.push(n))}function Uo(t){return pe=5,Zn(function(){return{current:t}},[])}function Zn(t,e){var n=vt(Ge++,7);return Yn(n.__H,e)?(n.__V=t(),n.i=e,n.__h=t,n.__V):n.__}function Fo(t,e){return pe=8,Zn(function(){return t},e)}function Kr(){for(var t;t=qn.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Oe),t.__H.__h.forEach(lt),t.__H.__h=[]}catch(e){t.__H.__h=[],A.__e(e,t.__v)}}A.__b=function(t){T=null,Ut&&Ut(t)},A.__r=function(t){Ft&&Ft(t),Ge=0;var e=(T=t.__c).__H;e&&(qe===T?(e.__h=[],T.__h=[],e.__.forEach(function(n){n.__N&&(n.__=n.__N),n.__V=Me,n.__N=n.i=void 0})):(e.__h.forEach(Oe),e.__h.forEach(lt),e.__h=[])),qe=T},A.diffed=function(t){Vt&&Vt(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(qn.push(e)!==1&&Pt===A.requestAnimationFrame||((Pt=A.requestAnimationFrame)||ei)(Kr)),e.__H.__.forEach(function(n){n.i&&(n.__H=n.i),n.__V!==Me&&(n.__=n.__V),n.i=void 0,n.__V=Me})),qe=T=null},A.__c=function(t,e){e.some(function(n){try{n.__h.forEach(Oe),n.__h=n.__h.filter(function(r){return!r.__||lt(r)})}catch(r){e.some(function(i){i.__h&&(i.__h=[])}),e=[],A.__e(r,n.__v)}}),jt&&jt(t,e)},A.unmount=function(t){zt&&zt(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach(function(r){try{Oe(r)}catch(i){e=i}}),n.__H=void 0,e&&A.__e(e,n.__v))};var Lt=typeof requestAnimationFrame=="function";function ei(t){var e,n=function(){clearTimeout(r),Lt&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);Lt&&(e=requestAnimationFrame(n))}function Oe(t){var e=T,n=t.__c;typeof n=="function"&&(t.__c=void 0,n()),T=e}function lt(t){var e=T;t.__c=t.__(),T=e}function Yn(t,e){return!t||t.length!==e.length||e.some(function(n,r){return n!==t[r]})}function $n(t,e){return typeof e=="function"?e(t):e}function ti(t,e){for(var n in e)t[n]=e[n];return t}function Wt(t,e){for(var n in t)if(n!=="__source"&&!(n in e))return!0;for(var r in e)if(r!=="__source"&&t[r]!==e[r])return!0;return!1}function Qt(t){this.props=t}(Qt.prototype=new x).isPureReactComponent=!0,Qt.prototype.shouldComponentUpdate=function(t,e){return Wt(this.props,t)||Wt(this.state,e)};var Gt=A.__b;A.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),Gt&&Gt(t)};var ni=A.__e;A.__e=function(t,e,n,r){if(t.then){for(var i,s=e;s=s.__;)if((i=s.__c)&&i.__c)return e.__e==null&&(e.__e=n.__e,e.__k=n.__k),i.__c(t,e)}ni(t,e,n,r)};var qt=A.unmount;function Jn(t,e,n){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(r){typeof r.__c=="function"&&r.__c()}),t.__c.__H=null),(t=ti({},t)).__c!=null&&(t.__c.__P===n&&(t.__c.__P=e),t.__c=null),t.__k=t.__k&&t.__k.map(function(r){return Jn(r,e,n)})),t}function Xn(t,e,n){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(r){return Xn(r,e,n)}),t.__c&&t.__c.__P===e&&(t.__e&&n.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=n)),t}function Ze(){this.__u=0,this.t=null,this.__b=null}function Kn(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function De(){this.u=null,this.o=null}A.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&t.__h===!0&&(t.type=null),qt&&qt(t)},(Ze.prototype=new x).__c=function(t,e){var n=e.__c,r=this;r.t==null&&(r.t=[]),r.t.push(n);var i=Kn(r.__v),s=!1,a=function(){s||(s=!0,n.__R=null,i?i(l):l())};n.__R=a;var l=function(){if(!--r.__u){if(r.state.__a){var u=r.state.__a;r.__v.__k[0]=Xn(u,u.__c.__P,u.__c.__O)}var c;for(r.setState({__a:r.__b=null});c=r.t.pop();)c.forceUpdate()}},o=e.__h===!0;r.__u++||o||r.setState({__a:r.__b=r.__v.__k[0]}),t.then(a,a)},Ze.prototype.componentWillUnmount=function(){this.t=[]},Ze.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=Jn(this.__b,n,r.__O=r.__P)}this.__b=null}var i=e.__a&&g(P,null,t.fallback);return i&&(i.__h=null),[g(P,null,e.__a?null:t.children),i]};var Zt=function(t,e,n){if(++n[1]===n[0]&&t.o.delete(e),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.o.size))for(n=t.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;t.u=n=n[2]}};function ri(t){return this.getChildContext=function(){return t.context},t.children}function ii(t){var e=this,n=t.i;e.componentWillUnmount=function(){he(null,e.l),e.l=null,e.i=null},e.i&&e.i!==n&&e.componentWillUnmount(),t.__v?(e.l||(e.i=n,e.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(r){this.childNodes.push(r),e.i.appendChild(r)},insertBefore:function(r,i){this.childNodes.push(r),e.i.appendChild(r)},removeChild:function(r){this.childNodes.splice(this.childNodes.indexOf(r)>>>1,1),e.i.removeChild(r)}}),he(g(ri,{context:e.context},t.__v),e.l)):e.l&&e.componentWillUnmount()}function si(t,e){var n=g(ii,{__v:t,i:e});return n.containerInfo=e,n}(De.prototype=new x).__a=function(t){var e=this,n=Kn(e.__v),r=e.o.get(t);return r[0]++,function(i){var s=function(){e.props.revealOrder?(r.push(i),Zt(e,t,r)):i()};n?n(s):s()}},De.prototype.render=function(t){this.u=null,this.o=new Map;var e=Ue(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&e.reverse();for(var n=e.length;n--;)this.o.set(e[n],this.u=[1,0,this.u]);return t.children},De.prototype.componentDidUpdate=De.prototype.componentDidMount=function(){var t=this;this.o.forEach(function(e,n){Zt(t,n,e)})};var ai=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,li=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,oi=typeof document<"u",ci=function(t){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/i:/fil|che|ra/i).test(t)};x.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(x.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})});var Yt=A.event;function ui(){}function di(){return this.cancelBubble}function fi(){return this.defaultPrevented}A.event=function(t){return Yt&&(t=Yt(t)),t.persist=ui,t.isPropagationStopped=di,t.isDefaultPrevented=fi,t.nativeEvent=t};var $t={configurable:!0,get:function(){return this.class}},Jt=A.vnode;A.vnode=function(t){var e=t.type,n=t.props,r=n;if(typeof e=="string"){var i=e.indexOf("-")===-1;for(var s in r={},n){var a=n[s];oi&&s==="children"&&e==="noscript"||s==="value"&&"defaultValue"in n&&a==null||(s==="defaultValue"&&"value"in n&&n.value==null?s="value":s==="download"&&a===!0?a="":/ondoubleclick/i.test(s)?s="ondblclick":/^onchange(textarea|input)/i.test(s+e)&&!ci(n.type)?s="oninput":/^onfocus$/i.test(s)?s="onfocusin":/^onblur$/i.test(s)?s="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(s)?s=s.toLowerCase():i&&li.test(s)?s=s.replace(/[A-Z0-9]/g,"-$&").toLowerCase():a===null&&(a=void 0),/^oninput$/i.test(s)&&(s=s.toLowerCase(),r[s]&&(s="oninputCapture")),r[s]=a)}e=="select"&&r.multiple&&Array.isArray(r.value)&&(r.value=Ue(n.children).forEach(function(l){l.props.selected=r.value.indexOf(l.props.value)!=-1})),e=="select"&&r.defaultValue!=null&&(r.value=Ue(n.children).forEach(function(l){l.props.selected=r.multiple?r.defaultValue.indexOf(l.props.value)!=-1:r.defaultValue==l.props.value})),t.props=r,n.class!=n.className&&($t.enumerable="className"in n,n.className!=null&&(r.class=n.className),Object.defineProperty(r,"className",$t))}t.$$typeof=ai,Jt&&Jt(t)};var Xt=A.__r;A.__r=function(t){Xt&&Xt(t),t.__c};const er=[],ot=new Map;function hi(t){er.push(t),ot.forEach(e=>{nr(e,t)})}function pi(t){t.isConnected&&t.getRootNode&&tr(t.getRootNode())}function tr(t){let e=ot.get(t);if(!e||!e.isConnected){if(e=t.querySelector("style[data-fullcalendar]"),!e){e=document.createElement("style"),e.setAttribute("data-fullcalendar","");const n=mi();n&&(e.nonce=n);const r=t===document?document.head:t,i=t===document?r.querySelector("script,link[rel=stylesheet],link[as=style],style"):r.firstChild;r.insertBefore(e,i)}ot.set(t,e),gi(e)}}function gi(t){for(const e of er)nr(t,e)}function nr(t,e){const{sheet:n}=t,r=n.cssRules.length;e.split("}").forEach((i,s)=>{i=i.trim(),i&&n.insertRule(i+"}",r+s)})}let Ye;function mi(){return Ye===void 0&&(Ye=vi()),Ye}function vi(){const t=document.querySelector('meta[name="csp-nonce"]');if(t&&t.hasAttribute("content"))return t.getAttribute("content");const e=document.querySelector("script[nonce]");return e&&e.nonce||""}typeof document<"u"&&tr(document);var Ai=':root{--fc-small-font-size:.85em;--fc-page-bg-color:#fff;--fc-neutral-bg-color:hsla(0,0%,82%,.3);--fc-neutral-text-color:grey;--fc-border-color:#ddd;--fc-button-text-color:#fff;--fc-button-bg-color:#2c3e50;--fc-button-border-color:#2c3e50;--fc-button-hover-bg-color:#1e2b37;--fc-button-hover-border-color:#1a252f;--fc-button-active-bg-color:#1a252f;--fc-button-active-border-color:#151e27;--fc-event-bg-color:#3788d8;--fc-event-border-color:#3788d8;--fc-event-text-color:#fff;--fc-event-selected-overlay-color:rgba(0,0,0,.25);--fc-more-link-bg-color:#d0d0d0;--fc-more-link-text-color:inherit;--fc-event-resizer-thickness:8px;--fc-event-resizer-dot-total-width:8px;--fc-event-resizer-dot-border-width:1px;--fc-non-business-color:hsla(0,0%,84%,.3);--fc-bg-event-color:#8fdf82;--fc-bg-event-opacity:0.3;--fc-highlight-color:rgba(188,232,241,.3);--fc-today-bg-color:rgba(255,220,40,.15);--fc-now-indicator-color:red}.fc-not-allowed,.fc-not-allowed .fc-event{cursor:not-allowed}.fc{display:flex;flex-direction:column;font-size:1em}.fc,.fc *,.fc :after,.fc :before{box-sizing:border-box}.fc table{border-collapse:collapse;border-spacing:0;font-size:1em}.fc th{text-align:center}.fc td,.fc th{padding:0;vertical-align:top}.fc a[data-navlink]{cursor:pointer}.fc a[data-navlink]:hover{text-decoration:underline}.fc-direction-ltr{direction:ltr;text-align:left}.fc-direction-rtl{direction:rtl;text-align:right}.fc-theme-standard td,.fc-theme-standard th{border:1px solid var(--fc-border-color)}.fc-liquid-hack td,.fc-liquid-hack th{position:relative}@font-face{font-family:fcicons;font-style:normal;font-weight:400;src:url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype")}.fc-icon{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-family:fcicons!important;font-style:normal;font-variant:normal;font-weight:400;height:1em;line-height:1;text-align:center;text-transform:none;-moz-user-select:none;user-select:none;width:1em}.fc-icon-chevron-left:before{content:"\\e900"}.fc-icon-chevron-right:before{content:"\\e901"}.fc-icon-chevrons-left:before{content:"\\e902"}.fc-icon-chevrons-right:before{content:"\\e903"}.fc-icon-minus-square:before{content:"\\e904"}.fc-icon-plus-square:before{content:"\\e905"}.fc-icon-x:before{content:"\\e906"}.fc .fc-button{border-radius:0;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;text-transform:none}.fc .fc-button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}.fc .fc-button{-webkit-appearance:button}.fc .fc-button:not(:disabled){cursor:pointer}.fc .fc-button{background-color:transparent;border:1px solid transparent;border-radius:.25em;display:inline-block;font-size:1em;font-weight:400;line-height:1.5;padding:.4em .65em;text-align:center;-moz-user-select:none;user-select:none;vertical-align:middle}.fc .fc-button:hover{text-decoration:none}.fc .fc-button:focus{box-shadow:0 0 0 .2rem rgba(44,62,80,.25);outline:0}.fc .fc-button:disabled{opacity:.65}.fc .fc-button-primary{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:hover{background-color:var(--fc-button-hover-bg-color);border-color:var(--fc-button-hover-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:disabled{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button-primary:not(:disabled).fc-button-active,.fc .fc-button-primary:not(:disabled):active{background-color:var(--fc-button-active-bg-color);border-color:var(--fc-button-active-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:not(:disabled).fc-button-active:focus,.fc .fc-button-primary:not(:disabled):active:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button .fc-icon{font-size:1.5em;vertical-align:middle}.fc .fc-button-group{display:inline-flex;position:relative;vertical-align:middle}.fc .fc-button-group>.fc-button{flex:1 1 auto;position:relative}.fc .fc-button-group>.fc-button.fc-button-active,.fc .fc-button-group>.fc-button:active,.fc .fc-button-group>.fc-button:focus,.fc .fc-button-group>.fc-button:hover{z-index:1}.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0;margin-left:-1px}.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child){border-bottom-right-radius:0;border-top-right-radius:0;margin-right:-1px}.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0}.fc .fc-toolbar{align-items:center;display:flex;justify-content:space-between}.fc .fc-toolbar.fc-header-toolbar{margin-bottom:1.5em}.fc .fc-toolbar.fc-footer-toolbar{margin-top:1.5em}.fc .fc-toolbar-title{font-size:1.75em;margin:0}.fc-direction-ltr .fc-toolbar>*>:not(:first-child){margin-left:.75em}.fc-direction-rtl .fc-toolbar>*>:not(:first-child){margin-right:.75em}.fc-direction-rtl .fc-toolbar-ltr{flex-direction:row-reverse}.fc .fc-scroller{-webkit-overflow-scrolling:touch;position:relative}.fc .fc-scroller-liquid{height:100%}.fc .fc-scroller-liquid-absolute{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-scroller-harness{direction:ltr;overflow:hidden;position:relative}.fc .fc-scroller-harness-liquid{height:100%}.fc-direction-rtl .fc-scroller-harness>.fc-scroller{direction:rtl}.fc-theme-standard .fc-scrollgrid{border:1px solid var(--fc-border-color)}.fc .fc-scrollgrid,.fc .fc-scrollgrid table{table-layout:fixed;width:100%}.fc .fc-scrollgrid table{border-left-style:hidden;border-right-style:hidden;border-top-style:hidden}.fc .fc-scrollgrid{border-bottom-width:0;border-collapse:separate;border-right-width:0}.fc .fc-scrollgrid-liquid{height:100%}.fc .fc-scrollgrid-section,.fc .fc-scrollgrid-section table,.fc .fc-scrollgrid-section>td{height:1px}.fc .fc-scrollgrid-section-liquid>td{height:100%}.fc .fc-scrollgrid-section>*{border-left-width:0;border-top-width:0}.fc .fc-scrollgrid-section-footer>*,.fc .fc-scrollgrid-section-header>*{border-bottom-width:0}.fc .fc-scrollgrid-section-body table,.fc .fc-scrollgrid-section-footer table{border-bottom-style:hidden}.fc .fc-scrollgrid-section-sticky>*{background:var(--fc-page-bg-color);position:sticky;z-index:3}.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>*{top:0}.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>*{bottom:0}.fc .fc-scrollgrid-sticky-shim{height:1px;margin-bottom:-1px}.fc-sticky{position:sticky}.fc .fc-view-harness{flex-grow:1;position:relative}.fc .fc-view-harness-active>.fc-view{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-col-header-cell-cushion{display:inline-block;padding:2px 4px}.fc .fc-bg-event,.fc .fc-highlight,.fc .fc-non-business{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-non-business{background:var(--fc-non-business-color)}.fc .fc-bg-event{background:var(--fc-bg-event-color);opacity:var(--fc-bg-event-opacity)}.fc .fc-bg-event .fc-event-title{font-size:var(--fc-small-font-size);font-style:italic;margin:.5em}.fc .fc-highlight{background:var(--fc-highlight-color)}.fc .fc-cell-shaded,.fc .fc-day-disabled{background:var(--fc-neutral-bg-color)}a.fc-event,a.fc-event:hover{text-decoration:none}.fc-event.fc-event-draggable,.fc-event[href]{cursor:pointer}.fc-event .fc-event-main{position:relative;z-index:2}.fc-event-dragging:not(.fc-event-selected){opacity:.75}.fc-event-dragging.fc-event-selected{box-shadow:0 2px 7px rgba(0,0,0,.3)}.fc-event .fc-event-resizer{display:none;position:absolute;z-index:4}.fc-event-selected .fc-event-resizer,.fc-event:hover .fc-event-resizer{display:block}.fc-event-selected .fc-event-resizer{background:var(--fc-page-bg-color);border-color:inherit;border-radius:calc(var(--fc-event-resizer-dot-total-width)/2);border-style:solid;border-width:var(--fc-event-resizer-dot-border-width);height:var(--fc-event-resizer-dot-total-width);width:var(--fc-event-resizer-dot-total-width)}.fc-event-selected .fc-event-resizer:before{bottom:-20px;content:"";left:-20px;position:absolute;right:-20px;top:-20px}.fc-event-selected,.fc-event:focus{box-shadow:0 2px 5px rgba(0,0,0,.2)}.fc-event-selected:before,.fc-event:focus:before{bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:3}.fc-event-selected:after,.fc-event:focus:after{background:var(--fc-event-selected-overlay-color);bottom:-1px;content:"";left:-1px;position:absolute;right:-1px;top:-1px;z-index:1}.fc-h-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-h-event .fc-event-main{color:var(--fc-event-text-color)}.fc-h-event .fc-event-main-frame{display:flex}.fc-h-event .fc-event-time{max-width:100%;overflow:hidden}.fc-h-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-width:0}.fc-h-event .fc-event-title{display:inline-block;left:0;max-width:100%;overflow:hidden;right:0;vertical-align:top}.fc-h-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end){border-bottom-left-radius:0;border-left-width:0;border-top-left-radius:0}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){border-bottom-right-radius:0;border-right-width:0;border-top-right-radius:0}.fc-h-event:not(.fc-event-selected) .fc-event-resizer{bottom:0;top:0;width:var(--fc-event-resizer-thickness)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end{cursor:w-resize;left:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start{cursor:e-resize;right:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-h-event.fc-event-selected .fc-event-resizer{margin-top:calc(var(--fc-event-resizer-dot-total-width)*-.5);top:50%}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end{left:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start{right:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc .fc-popover{box-shadow:0 2px 6px rgba(0,0,0,.15);position:absolute;z-index:9999}.fc .fc-popover-header{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:3px 4px}.fc .fc-popover-title{margin:0 2px}.fc .fc-popover-close{cursor:pointer;font-size:1.1em;opacity:.65}.fc-theme-standard .fc-popover{background:var(--fc-page-bg-color);border:1px solid var(--fc-border-color)}.fc-theme-standard .fc-popover-header{background:var(--fc-neutral-bg-color)}';hi(Ai);class At{constructor(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}request(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),e==null?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))}pause(e=""){let{pauseDepths:n}=this;n[e]=(n[e]||0)+1,this.clearTimeout()}resume(e="",n){let{pauseDepths:r}=this;e in r&&(n?delete r[e]:(r[e]-=1,r[e]<=0&&delete r[e]),this.tryDrain())}isPaused(){return Object.keys(this.pauseDepths).length}tryDrain(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}}clear(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}}clearTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)}drained(){this.drainedOption&&this.drainedOption()}}function bi(t){t.parentNode&&t.parentNode.removeChild(t)}function F(t,e){if(t.closest)return t.closest(e);if(!document.documentElement.contains(t))return null;do{if(_i(t,e))return t;t=t.parentElement||t.parentNode}while(t!==null&&t.nodeType===1);return null}function _i(t,e){return(t.matches||t.matchesSelector||t.msMatchesSelector).call(t,e)}function yi(t,e){let n=t instanceof HTMLElement?[t]:t,r=[];for(let i=0;i<n.length;i+=1){let s=n[i].querySelectorAll(e);for(let a=0;a<s.length;a+=1)r.push(s[a])}return r}const Ei=/(top|left|right|bottom|width|height)$/i;function Ci(t,e){for(let n in e)rr(t,n,e[n])}function rr(t,e,n){n==null?t.style[e]="":typeof n=="number"&&Ei.test(e)?t.style[e]=`${n}px`:t.style[e]=n}function Di(t){var e,n;return(n=(e=t.composedPath)===null||e===void 0?void 0:e.call(t)[0])!==null&&n!==void 0?n:t.target}let Kt=0;function bt(){return Kt+=1,"fc-dom-"+Kt}function wi(t,e){return n=>{let r=F(n.target,t);r&&e.call(r,n,r)}}function ir(t,e,n,r){let i=wi(n,r);return t.addEventListener(e,i),()=>{t.removeEventListener(e,i)}}function Si(t,e,n,r){let i;return ir(t,"mouseover",e,(s,a)=>{if(a!==i){i=a,n(s,a);let l=o=>{i=null,r(o,a),a.removeEventListener("mouseleave",l)};a.addEventListener("mouseleave",l)}})}function sr(t){return Object.assign({onClick:t},ar(t))}function ar(t){return{tabIndex:0,onKeyDown(e){(e.key==="Enter"||e.key===" ")&&(t(e),e.preventDefault())}}}let en=0;function K(){return en+=1,String(en)}function Ri(t){let e=[],n=[],r,i;for(typeof t=="string"?n=t.split(/\s*,\s*/):typeof t=="function"?n=[t]:Array.isArray(t)&&(n=t),r=0;r<n.length;r+=1)i=n[r],typeof i=="string"?e.push(i.charAt(0)==="-"?{field:i.substring(1),order:-1}:{field:i,order:1}):typeof i=="function"&&e.push({func:i});return e}function Ti(t,e,n){let r,i;for(r=0;r<n.length;r+=1)if(i=ki(t,e,n[r]),i)return i;return 0}function ki(t,e,n){return n.func?n.func(t,e):Ii(t[n.field],e[n.field])*(n.order||1)}function Ii(t,e){return!t&&!e?0:e==null?-1:t==null?1:typeof t=="string"||typeof e=="string"?String(t).localeCompare(String(e)):t-e}function $e(t,e){let n=String(t);return"000".substr(0,e-n.length)+n}function ue(t,e,n){return typeof t=="function"?t(...e):typeof t=="string"?e.reduce((r,i,s)=>r.replace("$"+s,i||""),t):n}function Je(t){return t%1===0}function Mi(t){let e=t.querySelector(".fc-scrollgrid-shrink-frame"),n=t.querySelector(".fc-scrollgrid-shrink-cushion");if(!e)throw new Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw new Error("needs fc-scrollgrid-shrink-cushion className");return t.getBoundingClientRect().width-e.getBoundingClientRect().width+n.getBoundingClientRect().width}const Oi=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function y(t,e){return typeof t=="string"?Ni(t):typeof t=="object"&&t?tn(t):typeof t=="number"?tn({[e||"milliseconds"]:t}):null}function Ni(t){let e=Oi.exec(t);if(e){let n=e[1]?-1:1;return{years:0,months:0,days:n*(e[2]?parseInt(e[2],10):0),milliseconds:n*((e[3]?parseInt(e[3],10):0)*60*60*1e3+(e[4]?parseInt(e[4],10):0)*60*1e3+(e[5]?parseInt(e[5],10):0)*1e3+(e[6]?parseInt(e[6],10):0))}}return null}function tn(t){let e={years:t.years||t.year||0,months:t.months||t.month||0,days:t.days||t.day||0,milliseconds:(t.hours||t.hour||0)*60*60*1e3+(t.minutes||t.minute||0)*60*1e3+(t.seconds||t.second||0)*1e3+(t.milliseconds||t.millisecond||t.ms||0)},n=t.weeks||t.week;return n&&(e.days+=n*7,e.specifiedWeeks=!0),e}function xi(t,e){return t.years===e.years&&t.months===e.months&&t.days===e.days&&t.milliseconds===e.milliseconds}function Bi(t,e){return{years:t.years-e.years,months:t.months-e.months,days:t.days-e.days,milliseconds:t.milliseconds-e.milliseconds}}function Hi(t){return ie(t)/365}function Pi(t){return ie(t)/30}function ie(t){return ge(t)/864e5}function ge(t){return t.years*(365*864e5)+t.months*(30*864e5)+t.days*864e5+t.milliseconds}function ct(t){let e=t.milliseconds;if(e){if(e%1e3!==0)return{unit:"millisecond",value:e};if(e%(1e3*60)!==0)return{unit:"second",value:e/1e3};if(e%(1e3*60*60)!==0)return{unit:"minute",value:e/(1e3*60)};if(e)return{unit:"hour",value:e/(1e3*60*60)}}return t.days?t.specifiedWeeks&&t.days%7===0?{unit:"week",value:t.days/7}:{unit:"day",value:t.days}:t.months?{unit:"month",value:t.months}:t.years?{unit:"year",value:t.years}:{unit:"millisecond",value:0}}function Q(t,e,n){if(t===e)return!0;let r=t.length,i;if(r!==e.length)return!1;for(i=0;i<r;i+=1)if(!(n?n(t[i],e[i]):t[i]===e[i]))return!1;return!0}const Ui=["sun","mon","tue","wed","thu","fri","sat"];function Vo(t,e){let n=z(t);return n[2]+=e*7,O(n)}function B(t,e){let n=z(t);return n[2]+=e,O(n)}function J(t,e){let n=z(t);return n[6]+=e,O(n)}function jo(t,e){return le(t,e)/7}function le(t,e){return(e.valueOf()-t.valueOf())/(1e3*60*60*24)}function Fi(t,e){return(e.valueOf()-t.valueOf())/(1e3*60*60)}function Vi(t,e){return(e.valueOf()-t.valueOf())/(1e3*60)}function ji(t,e){return(e.valueOf()-t.valueOf())/1e3}function zi(t,e){let n=w(t),r=w(e);return{years:0,months:0,days:Math.round(le(n,r)),milliseconds:e.valueOf()-r.valueOf()-(t.valueOf()-n.valueOf())}}function Li(t,e){let n=Fe(t,e);return n!==null&&n%7===0?n/7:null}function Fe(t,e){return L(t)===L(e)?Math.round(le(t,e)):null}function w(t){return O([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()])}function Wi(t){return O([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours()])}function Qi(t){return O([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes()])}function Gi(t){return O([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds()])}function qi(t,e,n){let r=t.getUTCFullYear(),i=Xe(t,r,e,n);if(i<1)return Xe(t,r-1,e,n);let s=Xe(t,r+1,e,n);return s>=1?Math.min(i,s):i}function Xe(t,e,n,r){let i=O([e,0,1+Zi(e,n,r)]),s=w(t),a=Math.round(le(i,s));return Math.floor(a/7)+1}function Zi(t,e,n){let r=7+e-n;return-((7+O([t,0,r]).getUTCDay()-e)%7)+r-1}function nn(t){return[t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()]}function rn(t){return new Date(t[0],t[1]||0,t[2]==null?1:t[2],t[3]||0,t[4]||0,t[5]||0)}function z(t){return[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.getUTCMilliseconds()]}function O(t){return t.length===1&&(t=t.concat([0])),new Date(Date.UTC(...t))}function lr(t){return!isNaN(t.valueOf())}function L(t){return t.getUTCHours()*1e3*60*60+t.getUTCMinutes()*1e3*60+t.getUTCSeconds()*1e3+t.getUTCMilliseconds()}function Yi(t,e,n=!1){let r=t.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(e==null?r=r.replace("Z",""):e!==0&&(r=r.replace("Z",_t(e,!0)))),r}function or(t){return t.toISOString().replace(/T.*$/,"")}function zo(t){return t.toISOString().match(/^\d{4}-\d{2}/)[0]}function _t(t,e=!1){let n=t<0?"-":"+",r=Math.abs(t),i=Math.floor(r/60),s=Math.round(r%60);return e?`${n+$e(i,2)}:${$e(s,2)}`:`GMT${n}${i}${s?`:${$e(s,2)}`:""}`}function D(t,e,n){let r,i;return function(...s){if(!r)i=t.apply(this,s);else if(!Q(r,s)){n&&n(i);let a=t.apply(this,s);(!e||!e(a,i))&&(i=a)}return r=s,i}}function Ne(t,e,n){let r,i;return s=>{if(!r)i=t.call(this,s);else if(!G(r,s)){n&&n(i);let a=t.call(this,s);(!e||!e(a,i))&&(i=a)}return r=s,i}}const sn={week:3,separator:0,omitZeroMinute:0,meridiem:0,omitCommas:0},Ve={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},we=/\s*([ap])\.?m\.?/i,$i=/,/g,Ji=/\s+/g,Xi=/\u200e/g,Ki=/UTC|GMT/;class es{constructor(e){let n={},r={},i=0;for(let s in e)s in sn?(r[s]=e[s],i=Math.max(sn[s],i)):(n[s]=e[s],s in Ve&&(i=Math.max(Ve[s],i)));this.standardDateProps=n,this.extendedSettings=r,this.severity=i,this.buildFormattingFunc=D(an)}format(e,n){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,n)(e)}formatRange(e,n,r,i){let{standardDateProps:s,extendedSettings:a}=this,l=as(e.marker,n.marker,r.calendarSystem);if(!l)return this.format(e,r);let o=l;o>1&&(s.year==="numeric"||s.year==="2-digit")&&(s.month==="numeric"||s.month==="2-digit")&&(s.day==="numeric"||s.day==="2-digit")&&(o=1);let u=this.format(e,r),c=this.format(n,r);if(u===c)return u;let p=ls(s,o),f=an(p,a,r),d=f(e),m=f(n),_=os(u,d,c,m),v=a.separator||i||r.defaultSeparator||"";return _?_.before+d+v+m+_.after:u+v+c}getLargestUnit(){switch(this.severity){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}}}function an(t,e,n){let r=Object.keys(t).length;return r===1&&t.timeZoneName==="short"?i=>_t(i.timeZoneOffset):r===0&&e.week?i=>ss(n.computeWeekNumber(i.marker),n.weekText,n.weekTextLong,n.locale,e.week):ts(t,e,n)}function ts(t,e,n){t=Object.assign({},t),e=Object.assign({},e),ns(t,e),t.timeZone="UTC";let r=new Intl.DateTimeFormat(n.locale.codes,t),i;if(e.omitZeroMinute){let s=Object.assign({},t);delete s.minute,i=new Intl.DateTimeFormat(n.locale.codes,s)}return s=>{let{marker:a}=s,l;i&&!a.getUTCMinutes()?l=i:l=r;let o=l.format(a);return rs(o,s,t,e,n)}}function ns(t,e){t.timeZoneName&&(t.hour||(t.hour="2-digit"),t.minute||(t.minute="2-digit")),t.timeZoneName==="long"&&(t.timeZoneName="short"),e.omitZeroMinute&&(t.second||t.millisecond)&&delete e.omitZeroMinute}function rs(t,e,n,r,i){return t=t.replace(Xi,""),n.timeZoneName==="short"&&(t=is(t,i.timeZone==="UTC"||e.timeZoneOffset==null?"UTC":_t(e.timeZoneOffset))),r.omitCommas&&(t=t.replace($i,"").trim()),r.omitZeroMinute&&(t=t.replace(":00","")),r.meridiem===!1?t=t.replace(we,"").trim():r.meridiem==="narrow"?t=t.replace(we,(s,a)=>a.toLocaleLowerCase()):r.meridiem==="short"?t=t.replace(we,(s,a)=>`${a.toLocaleLowerCase()}m`):r.meridiem==="lowercase"&&(t=t.replace(we,s=>s.toLocaleLowerCase())),t=t.replace(Ji," "),t=t.trim(),t}function is(t,e){let n=!1;return t=t.replace(Ki,()=>(n=!0,e)),n||(t+=` ${e}`),t}function ss(t,e,n,r,i){let s=[];return i==="long"?s.push(n):(i==="short"||i==="narrow")&&s.push(e),(i==="long"||i==="short")&&s.push(" "),s.push(r.simpleNumberFormat.format(t)),r.options.direction==="rtl"&&s.reverse(),s.join("")}function as(t,e,n){return n.getMarkerYear(t)!==n.getMarkerYear(e)?5:n.getMarkerMonth(t)!==n.getMarkerMonth(e)?4:n.getMarkerDay(t)!==n.getMarkerDay(e)?2:L(t)!==L(e)?1:0}function ls(t,e){let n={};for(let r in t)(!(r in Ve)||Ve[r]<=e)&&(n[r]=t[r]);return n}function os(t,e,n,r){let i=0;for(;i<t.length;){let s=t.indexOf(e,i);if(s===-1)break;let a=t.substr(0,s);i=s+e.length;let l=t.substr(i),o=0;for(;o<n.length;){let u=n.indexOf(r,o);if(u===-1)break;let c=n.substr(0,u);o=u+r.length;let p=n.substr(o);if(a===c&&l===p)return{before:a,after:l}}}return null}function ln(t,e){let n=e.markerToArray(t.marker);return{marker:t.marker,timeZoneOffset:t.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function je(t,e,n,r){let i=ln(t,n.calendarSystem),s=e?ln(e,n.calendarSystem):null;return{date:i,start:i,end:s,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}class cs{constructor(e){this.cmdStr=e}format(e,n,r){return n.cmdFormatter(this.cmdStr,je(e,null,n,r))}formatRange(e,n,r,i){return r.cmdFormatter(this.cmdStr,je(e,n,r,i))}}class us{constructor(e){this.func=e}format(e,n,r){return this.func(je(e,null,n,r))}formatRange(e,n,r,i){return this.func(je(e,n,r,i))}}function I(t){return typeof t=="object"&&t?new es(t):typeof t=="string"?new cs(t):typeof t=="function"?new us(t):null}const on={navLinkDayClick:h,navLinkWeekClick:h,duration:y,bootstrapFontAwesome:h,buttonIcons:h,customButtons:h,defaultAllDayEventDuration:y,defaultTimedEventDuration:y,nextDayThreshold:y,scrollTime:y,scrollTimeReset:Boolean,slotMinTime:y,slotMaxTime:y,dayPopoverFormat:I,slotDuration:y,snapDuration:y,headerToolbar:h,footerToolbar:h,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:I,dayHeaderClassNames:h,dayHeaderContent:h,dayHeaderDidMount:h,dayHeaderWillUnmount:h,dayCellClassNames:h,dayCellContent:h,dayCellDidMount:h,dayCellWillUnmount:h,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:h,weekNumbers:Boolean,weekNumberClassNames:h,weekNumberContent:h,weekNumberDidMount:h,weekNumberWillUnmount:h,editable:Boolean,viewClassNames:h,viewDidMount:h,viewWillUnmount:h,nowIndicator:Boolean,nowIndicatorClassNames:h,nowIndicatorContent:h,nowIndicatorDidMount:h,nowIndicatorWillUnmount:h,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:h,locale:h,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:h,eventOrder:Ri,eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:h,contentHeight:h,direction:String,weekNumberFormat:I,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:h,initialDate:h,now:h,eventDataTransform:h,stickyHeaderDates:h,stickyFooterScrollbar:h,viewHeight:h,defaultAllDay:Boolean,eventSourceFailure:h,eventSourceSuccess:h,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:h,eventConstraint:h,eventAllow:h,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:h,eventContent:h,eventDidMount:h,eventWillUnmount:h,selectConstraint:h,selectOverlap:h,selectAllow:h,droppable:Boolean,unselectCancel:String,slotLabelFormat:h,slotLaneClassNames:h,slotLaneContent:h,slotLaneDidMount:h,slotLaneWillUnmount:h,slotLabelClassNames:h,slotLabelContent:h,slotLabelDidMount:h,slotLabelWillUnmount:h,dayMaxEvents:h,dayMaxEventRows:h,dayMinWidth:Number,slotLabelInterval:y,allDayText:String,allDayClassNames:h,allDayContent:h,allDayDidMount:h,allDayWillUnmount:h,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:I,rerenderDelay:Number,moreLinkText:h,moreLinkHint:h,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:h,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:y,hiddenDays:h,fixedWeekCount:Boolean,validRange:h,visibleRange:h,titleFormat:h,eventInteractive:Boolean,noEventsText:String,viewHint:h,navLinkHint:h,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:h,moreLinkClassNames:h,moreLinkContent:h,moreLinkDidMount:h,moreLinkWillUnmount:h,monthStartFormat:I,handleCustomRendering:h,customRenderingMetaMap:h,customRenderingReplaces:Boolean},de={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30,monthStartFormat:{month:"long",day:"numeric"}},cn={datesSet:h,eventsSet:h,eventAdd:h,eventChange:h,eventRemove:h,windowResize:h,eventClick:h,eventMouseEnter:h,eventMouseLeave:h,select:h,unselect:h,loading:h,_unmount:h,_beforeprint:h,_afterprint:h,_noEventDrop:h,_noEventResize:h,_resize:h,_scrollRequest:h},un={buttonText:h,buttonHints:h,views:h,plugins:h,initialEvents:h,events:h,eventSources:h},Z={headerToolbar:Y,footerToolbar:Y,buttonText:Y,buttonHints:Y,buttonIcons:Y,dateIncrement:Y,plugins:Se,events:Se,eventSources:Se,resources:Se};function Y(t,e){return typeof t=="object"&&typeof e=="object"&&t&&e?G(t,e):t===e}function Se(t,e){return Array.isArray(t)&&Array.isArray(e)?Q(t,e):t===e}const ds={type:String,component:h,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:h,usesMinMaxTime:Boolean,classNames:h,content:h,didMount:h,willUnmount:h};function Ke(t){return Et(t,Z)}function yt(t,e){let n={},r={};for(let i in e)i in t&&(n[i]=e[i](t[i]));for(let i in t)i in e||(r[i]=t[i]);return{refined:n,extra:r}}function h(t){return t}const{hasOwnProperty:ze}=Object.prototype;function Et(t,e){let n={};if(e){for(let r in e)if(e[r]===Y){let i=[];for(let s=t.length-1;s>=0;s-=1){let a=t[s][r];if(typeof a=="object"&&a)i.unshift(a);else if(a!==void 0){n[r]=a;break}}i.length&&(n[r]=Et(i))}}for(let r=t.length-1;r>=0;r-=1){let i=t[r];for(let s in i)s in n||(n[s]=i[s])}return n}function ae(t,e){let n={};for(let r in t)e(t[r],r)&&(n[r]=t[r]);return n}function be(t,e){let n={};for(let r in t)n[r]=e(t[r],r);return n}function cr(t){let e={};for(let n of t)e[n]=!0;return e}function Ct(t){let e=[];for(let n in t)e.push(t[n]);return e}function G(t,e){if(t===e)return!0;for(let n in t)if(ze.call(t,n)&&!(n in e))return!1;for(let n in e)if(ze.call(e,n)&&t[n]!==e[n])return!1;return!0}const fs=/^on[A-Z]/;function hs(t,e){const n=ut(t,e);for(let r of n)if(!fs.test(r))return!1;return!0}function ut(t,e){let n=[];for(let r in t)ze.call(t,r)&&(r in e||n.push(r));for(let r in e)ze.call(e,r)&&t[r]!==e[r]&&n.push(r);return n}function et(t,e,n={}){if(t===e)return!0;for(let r in e)if(!(r in t&&ps(t[r],e[r],n[r])))return!1;for(let r in t)if(!(r in e))return!1;return!0}function ps(t,e,n){return t===e||n===!0?!0:n?n(t,e):!1}function gs(t,e=0,n,r=1){let i=[];n==null&&(n=Object.keys(t).length);for(let s=e;s<n;s+=r){let a=t[s];a!==void 0&&i.push(a)}return i}let ur={};function ms(t,e){ur[t]=e}function vs(t){return new ur[t]}class As{getMarkerYear(e){return e.getUTCFullYear()}getMarkerMonth(e){return e.getUTCMonth()}getMarkerDay(e){return e.getUTCDate()}arrayToMarker(e){return O(e)}markerToArray(e){return z(e)}}ms("gregory",As);const bs=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function _s(t){let e=bs.exec(t);if(e){let n=new Date(Date.UTC(Number(e[1]),e[3]?Number(e[3])-1:0,Number(e[5]||1),Number(e[7]||0),Number(e[8]||0),Number(e[10]||0),e[12]?+`0.${e[12]}`*1e3:0));if(lr(n)){let r=null;return e[13]&&(r=(e[15]==="-"?-1:1)*(Number(e[16]||0)*60+Number(e[18]||0))),{marker:n,isTimeUnspecified:!e[6],timeZoneOffset:r}}}return null}class ys{constructor(e){let n=this.timeZone=e.timeZone,r=n!=="local"&&n!=="UTC";e.namedTimeZoneImpl&&r&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(n)),this.canComputeOffset=!!(!r||this.namedTimeZoneImpl),this.calendarSystem=vs(e.calendarSystem),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,e.weekNumberCalculation==="ISO"&&(this.weekDow=1,this.weekDoy=4),typeof e.firstDay=="number"&&(this.weekDow=e.firstDay),typeof e.weekNumberCalculation=="function"&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=e.weekText!=null?e.weekText:e.locale.options.weekText,this.weekTextLong=(e.weekTextLong!=null?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}createMarker(e){let n=this.createMarkerMeta(e);return n===null?null:n.marker}createNowMarker(){return this.canComputeOffset?this.timestampToMarker(new Date().valueOf()):O(nn(new Date))}createMarkerMeta(e){if(typeof e=="string")return this.parse(e);let n=null;return typeof e=="number"?n=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(n=this.timestampToMarker(e))):Array.isArray(e)&&(n=O(e)),n===null||!lr(n)?null:{marker:n,isTimeUnspecified:!1,forcedTzo:null}}parse(e){let n=_s(e);if(n===null)return null;let{marker:r}=n,i=null;return n.timeZoneOffset!==null&&(this.canComputeOffset?r=this.timestampToMarker(r.valueOf()-n.timeZoneOffset*60*1e3):i=n.timeZoneOffset),{marker:r,isTimeUnspecified:n.isTimeUnspecified,forcedTzo:i}}getYear(e){return this.calendarSystem.getMarkerYear(e)}getMonth(e){return this.calendarSystem.getMarkerMonth(e)}getDay(e){return this.calendarSystem.getMarkerDay(e)}add(e,n){let r=this.calendarSystem.markerToArray(e);return r[0]+=n.years,r[1]+=n.months,r[2]+=n.days,r[6]+=n.milliseconds,this.calendarSystem.arrayToMarker(r)}subtract(e,n){let r=this.calendarSystem.markerToArray(e);return r[0]-=n.years,r[1]-=n.months,r[2]-=n.days,r[6]-=n.milliseconds,this.calendarSystem.arrayToMarker(r)}addYears(e,n){let r=this.calendarSystem.markerToArray(e);return r[0]+=n,this.calendarSystem.arrayToMarker(r)}addMonths(e,n){let r=this.calendarSystem.markerToArray(e);return r[1]+=n,this.calendarSystem.arrayToMarker(r)}diffWholeYears(e,n){let{calendarSystem:r}=this;return L(e)===L(n)&&r.getMarkerDay(e)===r.getMarkerDay(n)&&r.getMarkerMonth(e)===r.getMarkerMonth(n)?r.getMarkerYear(n)-r.getMarkerYear(e):null}diffWholeMonths(e,n){let{calendarSystem:r}=this;return L(e)===L(n)&&r.getMarkerDay(e)===r.getMarkerDay(n)?r.getMarkerMonth(n)-r.getMarkerMonth(e)+(r.getMarkerYear(n)-r.getMarkerYear(e))*12:null}greatestWholeUnit(e,n){let r=this.diffWholeYears(e,n);return r!==null?{unit:"year",value:r}:(r=this.diffWholeMonths(e,n),r!==null?{unit:"month",value:r}:(r=Li(e,n),r!==null?{unit:"week",value:r}:(r=Fe(e,n),r!==null?{unit:"day",value:r}:(r=Fi(e,n),Je(r)?{unit:"hour",value:r}:(r=Vi(e,n),Je(r)?{unit:"minute",value:r}:(r=ji(e,n),Je(r)?{unit:"second",value:r}:{unit:"millisecond",value:n.valueOf()-e.valueOf()}))))))}countDurationsBetween(e,n,r){let i;return r.years&&(i=this.diffWholeYears(e,n),i!==null)?i/Hi(r):r.months&&(i=this.diffWholeMonths(e,n),i!==null)?i/Pi(r):r.days&&(i=Fe(e,n),i!==null)?i/ie(r):(n.valueOf()-e.valueOf())/ge(r)}startOf(e,n){return n==="year"?this.startOfYear(e):n==="month"?this.startOfMonth(e):n==="week"?this.startOfWeek(e):n==="day"?w(e):n==="hour"?Wi(e):n==="minute"?Qi(e):n==="second"?Gi(e):null}startOfYear(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])}startOfMonth(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])}startOfWeek(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])}computeWeekNumber(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):qi(e,this.weekDow,this.weekDoy)}format(e,n,r={}){return n.format({marker:e,timeZoneOffset:r.forcedTzo!=null?r.forcedTzo:this.offsetForMarker(e)},this)}formatRange(e,n,r,i={}){return i.isEndExclusive&&(n=J(n,-1)),r.formatRange({marker:e,timeZoneOffset:i.forcedStartTzo!=null?i.forcedStartTzo:this.offsetForMarker(e)},{marker:n,timeZoneOffset:i.forcedEndTzo!=null?i.forcedEndTzo:this.offsetForMarker(n)},this,i.defaultSeparator)}formatIso(e,n={}){let r=null;return n.omitTimeZoneOffset||(n.forcedTzo!=null?r=n.forcedTzo:r=this.offsetForMarker(e)),Yi(e,r,n.omitTime)}timestampToMarker(e){return this.timeZone==="local"?O(nn(new Date(e))):this.timeZone==="UTC"||!this.namedTimeZoneImpl?new Date(e):O(this.namedTimeZoneImpl.timestampToArray(e))}offsetForMarker(e){return this.timeZone==="local"?-rn(z(e)).getTimezoneOffset():this.timeZone==="UTC"?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(z(e)):null}toDate(e,n){return this.timeZone==="local"?rn(z(e)):this.timeZone==="UTC"?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-this.namedTimeZoneImpl.offsetForArray(z(e))*1e3*60):new Date(e.valueOf()-(n||0))}}class _e{constructor(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}setIconOverride(e){let n,r;if(typeof e=="object"&&e){n=Object.assign({},this.iconClasses);for(r in e)n[r]=this.applyIconOverridePrefix(e[r]);this.iconClasses=n}else e===!1&&(this.iconClasses={})}applyIconOverridePrefix(e){let n=this.iconOverridePrefix;return n&&e.indexOf(n)!==0&&(e=n+e),e}getClass(e){return this.classes[e]||""}getIconClass(e,n){let r;return n&&this.rtlIconClasses?r=this.rtlIconClasses[e]||this.iconClasses[e]:r=this.iconClasses[e],r?`${this.baseIconClass} ${r}`:""}getCustomButtonIconClass(e){let n;return this.iconOverrideCustomButtonOption&&(n=e[this.iconOverrideCustomButtonOption],n)?`${this.baseIconClass} ${this.applyIconOverridePrefix(n)}`:""}}_e.prototype.classes={};_e.prototype.iconClasses={};_e.prototype.baseIconClass="";_e.prototype.iconOverridePrefix="";function Le(t){t();let e=A.debounceRendering,n=[];function r(i){n.push(i)}for(A.debounceRendering=r,he(g(Es,{}),document.createElement("div"));n.length;)n.shift()();A.debounceRendering=e}class Es extends x{render(){return g("div",{})}componentDidMount(){this.setState({})}}function dr(t){let e=Jr(t),n=e.Provider;return e.Provider=function(){let r=!this.getChildContext,i=n.apply(this,arguments);if(r){let s=[];this.shouldComponentUpdate=a=>{this.props.value!==a.value&&s.forEach(l=>{l.context=a.value,l.forceUpdate()})},this.sub=a=>{s.push(a);let l=a.componentWillUnmount;a.componentWillUnmount=()=>{s.splice(s.indexOf(a),1),l&&l.call(a)}}}return i},e}class Cs{constructor(e,n,r,i){this.execFunc=e,this.emitter=n,this.scrollTime=r,this.scrollTimeReset=i,this.handleScrollRequest=s=>{this.queuedRequest=Object.assign({},this.queuedRequest||{},s),this.drain()},n.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}detach(){this.emitter.off("_scrollRequest",this.handleScrollRequest)}update(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()}fireInitialScroll(){this.handleScrollRequest({time:this.scrollTime})}drain(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)}}const ee=dr({});function Ds(t,e,n,r,i,s,a,l,o,u,c,p,f){return{dateEnv:i,options:n,pluginHooks:a,emitter:u,dispatch:l,getCurrentData:o,calendarApi:c,viewSpec:t,viewApi:e,dateProfileGenerator:r,theme:s,isRtl:n.direction==="rtl",addResizeHandler(d){u.on("_resize",d)},removeResizeHandler(d){u.off("_resize",d)},createScrollResponder(d){return new Cs(d,u,y(n.scrollTime),n.scrollTimeReset)},registerInteractiveComponent:p,unregisterInteractiveComponent:f}}class te extends x{shouldComponentUpdate(e,n){return this.debug&&console.log(ut(e,this.props),ut(n,this.state)),!et(this.props,e,this.propEquality)||!et(this.state,n,this.stateEquality)}safeSetState(e){et(this.state,Object.assign(Object.assign({},this.state),e),this.stateEquality)||this.setState(e)}}te.addPropsEquality=ws;te.addStateEquality=Ss;te.contextType=ee;te.prototype.propEquality={};te.prototype.stateEquality={};class S extends te{}S.contextType=ee;function ws(t){let e=Object.create(this.prototype.propEquality);Object.assign(e,t),this.prototype.propEquality=e}function Ss(t){let e=Object.create(this.prototype.stateEquality);Object.assign(e,t),this.prototype.stateEquality=e}function ne(t,e){typeof t=="function"?t(e):t&&(t.current=e)}class Dt extends S{constructor(){super(...arguments),this.id=K(),this.queuedDomNodes=[],this.currentDomNodes=[],this.handleEl=e=>{const{options:n}=this.context,{generatorName:r}=this.props;(!n.customRenderingReplaces||!dt(r,n))&&this.updateElRef(e)},this.updateElRef=e=>{this.props.elRef&&ne(this.props.elRef,e)}}render(){const{props:e,context:n}=this,{options:r}=n,{customGenerator:i,defaultGenerator:s,renderProps:a}=e,l=fr(e,[],this.handleEl);let o=!1,u,c=[],p;if(i!=null){const f=typeof i=="function"?i(a,g):i;if(f===!0)o=!0;else{const d=f&&typeof f=="object";d&&"html"in f?l.dangerouslySetInnerHTML={__html:f.html}:d&&"domNodes"in f?c=Array.prototype.slice.call(f.domNodes):(d?xn(f):typeof f!="function")?u=f:p=f}}else o=!dt(e.generatorName,r);return o&&s&&(u=s(a)),this.queuedDomNodes=c,this.currentGeneratorMeta=p,g(e.elTag,l,u)}componentDidMount(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentDidUpdate(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentWillUnmount(){this.triggerCustomRendering(!1)}triggerCustomRendering(e){var n;const{props:r,context:i}=this,{handleCustomRendering:s,customRenderingMetaMap:a}=i.options;if(s){const l=(n=this.currentGeneratorMeta)!==null&&n!==void 0?n:a==null?void 0:a[r.generatorName];l&&s(Object.assign(Object.assign({id:this.id,isActive:e,containerEl:this.base,reportNewContainerEl:this.updateElRef,generatorMeta:l},r),{elClasses:(r.elClasses||[]).filter(Rs)}))}}applyQueueudDomNodes(){const{queuedDomNodes:e,currentDomNodes:n}=this,r=this.base;if(!Q(e,n)){n.forEach(bi);for(let i of e)r.appendChild(i);this.currentDomNodes=e}}}Dt.addPropsEquality({elClasses:Q,elStyle:G,elAttrs:hs,renderProps:G});function dt(t,e){var n;return!!(e.handleCustomRendering&&t&&(!((n=e.customRenderingMetaMap)===null||n===void 0)&&n[t]))}function fr(t,e,n){const r=Object.assign(Object.assign({},t.elAttrs),{ref:n});return(t.elClasses||e)&&(r.className=(t.elClasses||[]).concat(e||[]).concat(r.className||[]).filter(Boolean).join(" ")),t.elStyle&&(r.style=t.elStyle),r}function Rs(t){return!!t}const hr=dr(0);class j extends x{constructor(){super(...arguments),this.InnerContent=Ts.bind(void 0,this),this.handleEl=e=>{this.el=e,this.props.elRef&&(ne(this.props.elRef,e),e&&this.didMountMisfire&&this.componentDidMount())}}render(){const{props:e}=this,n=ks(e.classNameGenerator,e.renderProps);if(e.children){const r=fr(e,n,this.handleEl),i=e.children(this.InnerContent,e.renderProps,r);return e.elTag?g(e.elTag,r,i):i}else return g(Dt,Object.assign(Object.assign({},e),{elRef:this.handleEl,elTag:e.elTag||"div",elClasses:(e.elClasses||[]).concat(n),renderId:this.context}))}componentDidMount(){var e,n;this.el?(n=(e=this.props).didMount)===null||n===void 0||n.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el})):this.didMountMisfire=!0}componentWillUnmount(){var e,n;(n=(e=this.props).willUnmount)===null||n===void 0||n.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el}))}}j.contextType=hr;function Ts(t,e){const n=t.props;return g(Dt,Object.assign({renderProps:n.renderProps,generatorName:n.generatorName,customGenerator:n.customGenerator,defaultGenerator:n.defaultGenerator,renderId:t.context},e))}function ks(t,e){const n=typeof t=="function"?t(e):t||[];return typeof n=="string"?[n]:n}class Lo extends S{render(){let{props:e,context:n}=this,{options:r}=n,i={view:n.viewApi};return g(j,Object.assign({},e,{elTag:e.elTag||"div",elClasses:[...pr(e.viewSpec),...e.elClasses||[]],renderProps:i,classNameGenerator:r.viewClassNames,generatorName:void 0,didMount:r.viewDidMount,willUnmount:r.viewWillUnmount}),()=>e.children)}}function pr(t){return[`fc-${t.type}-view`,"fc-view"]}function Is(t,e){let n=null,r=null;return t.start&&(n=e.createMarker(t.start)),t.end&&(r=e.createMarker(t.end)),!n&&!r||n&&r&&r<n?null:{start:n,end:r}}function dn(t,e){let n=[],{start:r}=e,i,s;for(t.sort(Ms),i=0;i<t.length;i+=1)s=t[i],s.start>r&&n.push({start:r,end:s.start}),s.end>r&&(r=s.end);return r<e.end&&n.push({start:r,end:e.end}),n}function Ms(t,e){return t.start.valueOf()-e.start.valueOf()}function me(t,e){let{start:n,end:r}=t,i=null;return e.start!==null&&(n===null?n=e.start:n=new Date(Math.max(n.valueOf(),e.start.valueOf()))),e.end!=null&&(r===null?r=e.end:r=new Date(Math.min(r.valueOf(),e.end.valueOf()))),(n===null||r===null||n<r)&&(i={start:n,end:r}),i}function Os(t,e){return(t.end===null||e.start===null||t.end>e.start)&&(t.start===null||e.end===null||t.start<e.end)}function W(t,e){return(t.start===null||e>=t.start)&&(t.end===null||e<t.end)}function Ns(t,e){return e.start!=null&&t<e.start?e.start:e.end!=null&&t>=e.end?new Date(e.end.valueOf()-1):t}function gr(t){let e=Math.floor(le(t.start,t.end))||1,n=w(t.start),r=B(n,e);return{start:n,end:r}}function mr(t,e=y(0)){let n=null,r=null;if(t.end){r=w(t.end);let i=t.end.valueOf()-r.valueOf();i&&i>=ge(e)&&(r=B(r,1))}return t.start&&(n=w(t.start),r&&r<=n&&(r=B(n,1))),{start:n,end:r}}function Re(t,e,n,r){return r==="year"?y(n.diffWholeYears(t,e),"year"):r==="month"?y(n.diffWholeMonths(t,e),"month"):zi(t,e)}function xs(t,e){switch(e.type){case"CHANGE_DATE":return e.dateMarker;default:return t}}function Bs(t,e){let n=t.initialDate;return n!=null?e.createMarker(n):ye(t.now,e)}function ye(t,e){return typeof t=="function"&&(t=t()),t==null?e.createNowMarker():e.createMarker(t)}class Hs{constructor(e){this.props=e,this.nowDate=ye(e.nowInput,e.dateEnv),this.initHiddenDays()}buildPrev(e,n,r){let{dateEnv:i}=this.props,s=i.subtract(i.startOf(n,e.currentRangeUnit),e.dateIncrement);return this.build(s,-1,r)}buildNext(e,n,r){let{dateEnv:i}=this.props,s=i.add(i.startOf(n,e.currentRangeUnit),e.dateIncrement);return this.build(s,1,r)}build(e,n,r=!0){let{props:i}=this,s,a,l,o,u,c;return s=this.buildValidRange(),s=this.trimHiddenDays(s),r&&(e=Ns(e,s)),a=this.buildCurrentRangeInfo(e,n),l=/^(year|month|week|day)$/.test(a.unit),o=this.buildRenderRange(this.trimHiddenDays(a.range),a.unit,l),o=this.trimHiddenDays(o),u=o,i.showNonCurrentDates||(u=me(u,a.range)),u=this.adjustActiveRange(u),u=me(u,s),c=Os(a.range,s),W(o,e)||(e=o.start),{currentDate:e,validRange:s,currentRange:a.range,currentRangeUnit:a.unit,isRangeAllDay:l,activeRange:u,renderRange:o,slotMinTime:i.slotMinTime,slotMaxTime:i.slotMaxTime,isValid:c,dateIncrement:this.buildDateIncrement(a.duration)}}buildValidRange(){let e=this.props.validRangeInput,n=typeof e=="function"?e.call(this.props.calendarApi,this.nowDate):e;return this.refineRange(n)||{start:null,end:null}}buildCurrentRangeInfo(e,n){let{props:r}=this,i=null,s=null,a=null,l;return r.duration?(i=r.duration,s=r.durationUnit,a=this.buildRangeFromDuration(e,n,i,s)):(l=this.props.dayCount)?(s="day",a=this.buildRangeFromDayCount(e,n,l)):(a=this.buildCustomVisibleRange(e))?s=r.dateEnv.greatestWholeUnit(a.start,a.end).unit:(i=this.getFallbackDuration(),s=ct(i).unit,a=this.buildRangeFromDuration(e,n,i,s)),{duration:i,unit:s,range:a}}getFallbackDuration(){return y({day:1})}adjustActiveRange(e){let{dateEnv:n,usesMinMaxTime:r,slotMinTime:i,slotMaxTime:s}=this.props,{start:a,end:l}=e;return r&&(ie(i)<0&&(a=w(a),a=n.add(a,i)),ie(s)>1&&(l=w(l),l=B(l,-1),l=n.add(l,s))),{start:a,end:l}}buildRangeFromDuration(e,n,r,i){let{dateEnv:s,dateAlignment:a}=this.props,l,o,u;if(!a){let{dateIncrement:p}=this.props;p&&ge(p)<ge(r)?a=ct(p).unit:a=i}ie(r)<=1&&this.isHiddenDay(l)&&(l=this.skipHiddenDays(l,n),l=w(l));function c(){l=s.startOf(e,a),o=s.add(l,r),u={start:l,end:o}}return c(),this.trimHiddenDays(u)||(e=this.skipHiddenDays(e,n),c()),u}buildRangeFromDayCount(e,n,r){let{dateEnv:i,dateAlignment:s}=this.props,a=0,l=e,o;s&&(l=i.startOf(l,s)),l=w(l),l=this.skipHiddenDays(l,n),o=l;do o=B(o,1),this.isHiddenDay(o)||(a+=1);while(a<r);return{start:l,end:o}}buildCustomVisibleRange(e){let{props:n}=this,r=n.visibleRangeInput,i=typeof r=="function"?r.call(n.calendarApi,n.dateEnv.toDate(e)):r,s=this.refineRange(i);return s&&(s.start==null||s.end==null)?null:s}buildRenderRange(e,n,r){return e}buildDateIncrement(e){let{dateIncrement:n}=this.props,r;return n||((r=this.props.dateAlignment)?y(1,r):e||y({days:1}))}refineRange(e){if(e){let n=Is(e,this.props.dateEnv);return n&&(n=mr(n)),n}return null}initHiddenDays(){let e=this.props.hiddenDays||[],n=[],r=0,i;for(this.props.weekends===!1&&e.push(0,6),i=0;i<7;i+=1)(n[i]=e.indexOf(i)!==-1)||(r+=1);if(!r)throw new Error("invalid hiddenDays");this.isHiddenDayHash=n}trimHiddenDays(e){let{start:n,end:r}=e;return n&&(n=this.skipHiddenDays(n)),r&&(r=this.skipHiddenDays(r,-1,!0)),n==null||r==null||n<r?{start:n,end:r}:null}isHiddenDay(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]}skipHiddenDays(e,n=1,r=!1){for(;this.isHiddenDayHash[(e.getUTCDay()+(r?n:0)+7)%7];)e=B(e,n);return e}}function wt(t,e,n,r){return{instanceId:K(),defId:t,range:e,forcedStartTzo:n??null,forcedEndTzo:r??null}}function Ps(t,e,n,r){for(let i=0;i<r.length;i+=1){let s=r[i].parse(t,n);if(s){let{allDay:a}=t;return a==null&&(a=e,a==null&&(a=s.allDayGuess,a==null&&(a=!1))),{allDay:a,duration:s.duration,typeData:s.typeData,typeId:i}}}return null}function Ee(t,e,n){let{dateEnv:r,pluginHooks:i,options:s}=n,{defs:a,instances:l}=t;l=ae(l,o=>!a[o.defId].recurringDef);for(let o in a){let u=a[o];if(u.recurringDef){let{duration:c}=u.recurringDef;c||(c=u.allDay?s.defaultAllDayEventDuration:s.defaultTimedEventDuration);let p=Us(u,c,e,r,i.recurringTypes);for(let f of p){let d=wt(o,{start:f,end:r.add(f,c)});l[d.instanceId]=d}}}return{defs:a,instances:l}}function Us(t,e,n,r,i){let a=i[t.recurringDef.typeId].expand(t.recurringDef.typeData,{start:r.subtract(n.start,e),end:n.end},r);return t.allDay&&(a=a.map(w)),a}const xe={id:String,groupId:String,title:String,url:String,interactive:Boolean},vr={start:h,end:h,date:h,allDay:Boolean},Fs=Object.assign(Object.assign(Object.assign({},xe),vr),{extendedProps:h});function Ar(t,e,n,r,i=St(n),s,a){let{refined:l,extra:o}=br(t,n,i),u=js(e,n),c=Ps(l,u,n.dateEnv,n.pluginHooks.recurringTypes);if(c){let f=ft(l,o,e?e.sourceId:"",c.allDay,!!c.duration,n,s);return f.recurringDef={typeId:c.typeId,typeData:c.typeData,duration:c.duration},{def:f,instance:null}}let p=Vs(l,u,n,r);if(p){let f=ft(l,o,e?e.sourceId:"",p.allDay,p.hasEnd,n,s),d=wt(f.defId,p.range,p.forcedStartTzo,p.forcedEndTzo);return a&&f.publicId&&a[f.publicId]&&(d.instanceId=a[f.publicId]),{def:f,instance:d}}return null}function br(t,e,n=St(e)){return yt(t,n)}function St(t){return Object.assign(Object.assign(Object.assign({},We),Fs),t.pluginHooks.eventRefiners)}function ft(t,e,n,r,i,s,a){let l={title:t.title||"",groupId:t.groupId||"",publicId:t.id||"",url:t.url||"",recurringDef:null,defId:(a&&t.id?a[t.id]:"")||K(),sourceId:n,allDay:r,hasEnd:i,interactive:t.interactive,ui:Qe(t,s),extendedProps:Object.assign(Object.assign({},t.extendedProps||{}),e)};for(let o of s.pluginHooks.eventDefMemberAdders)Object.assign(l,o(t));return Object.freeze(l.ui.classNames),Object.freeze(l.extendedProps),l}function Vs(t,e,n,r){let{allDay:i}=t,s,a=null,l=!1,o,u=null,c=t.start!=null?t.start:t.date;if(s=n.dateEnv.createMarkerMeta(c),s)a=s.marker;else if(!r)return null;return t.end!=null&&(o=n.dateEnv.createMarkerMeta(t.end)),i==null&&(e!=null?i=e:i=(!s||s.isTimeUnspecified)&&(!o||o.isTimeUnspecified)),i&&a&&(a=w(a)),o&&(u=o.marker,i&&(u=w(u)),a&&u<=a&&(u=null)),u?l=!0:r||(l=n.options.forceEventDuration||!1,u=n.dateEnv.add(a,i?n.options.defaultAllDayEventDuration:n.options.defaultTimedEventDuration)),{allDay:i,hasEnd:l,range:{start:a,end:u},forcedStartTzo:s?s.forcedTzo:null,forcedEndTzo:o?o.forcedTzo:null}}function js(t,e){let n=null;return t&&(n=t.defaultAllDay),n==null&&(n=e.options.defaultAllDay),n}function ve(t,e,n,r,i,s){let a=X(),l=St(n);for(let o of t){let u=Ar(o,e,n,r,l,i,s);u&&ht(u,a)}return a}function ht(t,e=X()){return e.defs[t.def.defId]=t.def,t.instance&&(e.instances[t.instance.instanceId]=t.instance),e}function zs(t,e){let n=t.instances[e];if(n){let r=t.defs[n.defId],i=Tt(t,s=>Ls(r,s));return i.defs[r.defId]=r,i.instances[n.instanceId]=n,i}return X()}function Ls(t,e){return!!(t.groupId&&t.groupId===e.groupId)}function X(){return{defs:{},instances:{}}}function Rt(t,e){return{defs:Object.assign(Object.assign({},t.defs),e.defs),instances:Object.assign(Object.assign({},t.instances),e.instances)}}function Tt(t,e){let n=ae(t.defs,e),r=ae(t.instances,i=>n[i.defId]);return{defs:n,instances:r}}function Ws(t,e){let{defs:n,instances:r}=t,i={},s={};for(let a in n)e.defs[a]||(i[a]=n[a]);for(let a in r)!e.instances[a]&&i[r[a].defId]&&(s[a]=r[a]);return{defs:i,instances:s}}function Qs(t,e){return Array.isArray(t)?ve(t,null,e,!0):typeof t=="object"&&t?ve([t],null,e,!0):t!=null?String(t):null}function fn(t){return Array.isArray(t)?t:typeof t=="string"?t.split(/\s+/):[]}const We={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:h,overlap:h,allow:h,className:fn,classNames:fn,color:String,backgroundColor:String,borderColor:String,textColor:String},Gs={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function Qe(t,e){let n=Qs(t.constraint,e);return{display:t.display||null,startEditable:t.startEditable!=null?t.startEditable:t.editable,durationEditable:t.durationEditable!=null?t.durationEditable:t.editable,constraints:n!=null?[n]:[],overlap:t.overlap!=null?t.overlap:null,allows:t.allow!=null?[t.allow]:[],backgroundColor:t.backgroundColor||t.color||"",borderColor:t.borderColor||t.color||"",textColor:t.textColor||"",classNames:(t.className||[]).concat(t.classNames||[])}}function qs(t){return t.reduce(Zs,Gs)}function Zs(t,e){return{display:e.display!=null?e.display:t.display,startEditable:e.startEditable!=null?e.startEditable:t.startEditable,durationEditable:e.durationEditable!=null?e.durationEditable:t.durationEditable,constraints:t.constraints.concat(e.constraints),overlap:typeof e.overlap=="boolean"?e.overlap:t.overlap,allows:t.allows.concat(e.allows),backgroundColor:e.backgroundColor||t.backgroundColor,borderColor:e.borderColor||t.borderColor,textColor:e.textColor||t.textColor,classNames:t.classNames.concat(e.classNames)}}const Ys={id:String,defaultAllDay:Boolean,url:String,format:String,events:h,eventDataTransform:h,success:h,failure:h};function _r(t,e,n=yr(e)){let r;if(typeof t=="string"?r={url:t}:typeof t=="function"||Array.isArray(t)?r={events:t}:typeof t=="object"&&t&&(r=t),r){let{refined:i,extra:s}=yt(r,n),a=$s(i,e);if(a)return{_raw:t,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:K(),sourceDefId:a.sourceDefId,meta:a.meta,ui:Qe(i,e),extendedProps:s}}return null}function yr(t){return Object.assign(Object.assign(Object.assign({},We),Ys),t.pluginHooks.eventSourceRefiners)}function $s(t,e){let n=e.pluginHooks.eventSourceDefs;for(let r=n.length-1;r>=0;r-=1){let s=n[r].parseMeta(t);if(s)return{sourceDefId:r,meta:s}}return null}function Js(t,e,n,r,i){switch(e.type){case"RECEIVE_EVENTS":return Xs(t,n[e.sourceId],e.fetchId,e.fetchRange,e.rawEvents,i);case"RESET_RAW_EVENTS":return Ks(t,n[e.sourceId],e.rawEvents,r.activeRange,i);case"ADD_EVENTS":return ea(t,e.eventStore,r?r.activeRange:null,i);case"RESET_EVENTS":return e.eventStore;case"MERGE_EVENTS":return Rt(t,e.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return r?Ee(t,r.activeRange,i):t;case"REMOVE_EVENTS":return Ws(t,e.eventStore);case"REMOVE_EVENT_SOURCE":return Cr(t,e.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return Tt(t,s=>!s.sourceId);case"REMOVE_ALL_EVENTS":return X();default:return t}}function Xs(t,e,n,r,i,s){if(e&&n===e.latestFetchId){let a=ve(Er(i,e,s),e,s);return r&&(a=Ee(a,r,s)),Rt(Cr(t,e.sourceId),a)}return t}function Ks(t,e,n,r,i){const{defIdMap:s,instanceIdMap:a}=ta(t);let l=ve(Er(n,e,i),e,i,!1,s,a);return Ee(l,r,i)}function Er(t,e,n){let r=n.options.eventDataTransform,i=e?e.eventDataTransform:null;return i&&(t=hn(t,i)),r&&(t=hn(t,r)),t}function hn(t,e){let n;if(!e)n=t;else{n=[];for(let r of t){let i=e(r);i?n.push(i):i==null&&n.push(r)}}return n}function ea(t,e,n,r){return n&&(e=Ee(e,n,r)),Rt(t,e)}function pn(t,e,n){let{defs:r}=t,i=be(t.instances,s=>r[s.defId].allDay?s:Object.assign(Object.assign({},s),{range:{start:n.createMarker(e.toDate(s.range.start,s.forcedStartTzo)),end:n.createMarker(e.toDate(s.range.end,s.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:s.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:s.forcedEndTzo}));return{defs:r,instances:i}}function Cr(t,e){return Tt(t,n=>n.sourceId!==e)}function ta(t){const{defs:e,instances:n}=t,r={},i={};for(let s in e){const a=e[s],{publicId:l}=a;l&&(r[l]=s)}for(let s in n){const a=n[s],l=e[a.defId],{publicId:o}=l;o&&(i[o]=s)}return{defIdMap:r,instanceIdMap:i}}class na{constructor(){this.handlers={},this.thisContext=null}setThisContext(e){this.thisContext=e}setOptions(e){this.options=e}on(e,n){ra(this.handlers,e,n)}off(e,n){ia(this.handlers,e,n)}trigger(e,...n){let r=this.handlers[e]||[],i=this.options&&this.options[e],s=[].concat(i||[],r);for(let a of s)a.apply(this.thisContext,n)}hasHandlers(e){return!!(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])}}function ra(t,e,n){(t[e]||(t[e]=[])).push(n)}function ia(t,e,n){n?t[e]&&(t[e]=t[e].filter(r=>r!==n)):delete t[e]}const sa={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function aa(t,e){return ve(la(t),null,e)}function la(t){let e;return t===!0?e=[{}]:Array.isArray(t)?e=t.filter(n=>n.daysOfWeek):typeof t=="object"&&t?e=[t]:e=[],e=e.map(n=>Object.assign(Object.assign({},sa),n)),e}function oa(t,e,n){n.emitter.trigger("select",Object.assign(Object.assign({},ua(t,n)),{jsEvent:e?e.origEvent:null,view:n.viewApi||n.calendarApi.view}))}function ca(t,e){e.emitter.trigger("unselect",{jsEvent:t?t.origEvent:null,view:e.viewApi||e.calendarApi.view})}function ua(t,e){let n={};for(let r of e.pluginHooks.dateSpanTransforms)Object.assign(n,r(t,e));return Object.assign(n,Da(t,e.dateEnv)),n}function gn(t,e,n){let{dateEnv:r,options:i}=n,s=e;return t?(s=w(s),s=r.add(s,i.defaultAllDayEventDuration)):s=r.add(s,i.defaultTimedEventDuration),s}function da(t,e,n,r){let i=wr(t.defs,e),s=X();for(let a in t.defs){let l=t.defs[a];s.defs[a]=fa(l,i[a],n,r)}for(let a in t.instances){let l=t.instances[a],o=s.defs[l.defId];s.instances[a]=ha(l,o,i[l.defId],n,r)}return s}function fa(t,e,n,r){let i=n.standardProps||{};i.hasEnd==null&&e.durationEditable&&(n.startDelta||n.endDelta)&&(i.hasEnd=!0);let s=Object.assign(Object.assign(Object.assign({},t),i),{ui:Object.assign(Object.assign({},t.ui),i.ui)});n.extendedProps&&(s.extendedProps=Object.assign(Object.assign({},s.extendedProps),n.extendedProps));for(let a of r.pluginHooks.eventDefMutationAppliers)a(s,n,r);return!s.hasEnd&&r.options.forceEventDuration&&(s.hasEnd=!0),s}function ha(t,e,n,r,i){let{dateEnv:s}=i,a=r.standardProps&&r.standardProps.allDay===!0,l=r.standardProps&&r.standardProps.hasEnd===!1,o=Object.assign({},t);return a&&(o.range=gr(o.range)),r.datesDelta&&n.startEditable&&(o.range={start:s.add(o.range.start,r.datesDelta),end:s.add(o.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(o.range={start:s.add(o.range.start,r.startDelta),end:o.range.end}),r.endDelta&&n.durationEditable&&(o.range={start:o.range.start,end:s.add(o.range.end,r.endDelta)}),l&&(o.range={start:o.range.start,end:gn(e.allDay,o.range.start,i)}),e.allDay&&(o.range={start:w(o.range.start),end:w(o.range.end)}),o.range.end<o.range.start&&(o.range.end=gn(e.allDay,o.range.start,i)),o}class re{constructor(e,n){this.context=e,this.internalEventSource=n}remove(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})}refetch(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})}get id(){return this.internalEventSource.publicId}get url(){return this.internalEventSource.meta.url}get format(){return this.internalEventSource.meta.format}}class H{constructor(e,n,r){this._context=e,this._def=n,this._instance=r||null}setProp(e,n){if(e in vr)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if(e==="id")n=xe[e](n),this.mutate({standardProps:{publicId:n}});else if(e in xe)n=xe[e](n),this.mutate({standardProps:{[e]:n}});else if(e in We){let r=We[e](n);e==="color"?r={backgroundColor:n,borderColor:n}:e==="editable"?r={startEditable:n,durationEditable:n}:r={[e]:n},this.mutate({standardProps:{ui:r}})}else console.warn(`Could not set prop '${e}'. Use setExtendedProp instead.`)}setExtendedProp(e,n){this.mutate({extendedProps:{[e]:n}})}setStart(e,n={}){let{dateEnv:r}=this._context,i=r.createMarker(e);if(i&&this._instance){let s=this._instance.range,a=Re(s.start,i,r,n.granularity);n.maintainDuration?this.mutate({datesDelta:a}):this.mutate({startDelta:a})}}setEnd(e,n={}){let{dateEnv:r}=this._context,i;if(!(e!=null&&(i=r.createMarker(e),!i))&&this._instance)if(i){let s=Re(this._instance.range.end,i,r,n.granularity);this.mutate({endDelta:s})}else this.mutate({standardProps:{hasEnd:!1}})}setDates(e,n,r={}){let{dateEnv:i}=this._context,s={allDay:r.allDay},a=i.createMarker(e),l;if(a&&!(n!=null&&(l=i.createMarker(n),!l))&&this._instance){let o=this._instance.range;r.allDay===!0&&(o=gr(o));let u=Re(o.start,a,i,r.granularity);if(l){let c=Re(o.end,l,i,r.granularity);xi(u,c)?this.mutate({datesDelta:u,standardProps:s}):this.mutate({startDelta:u,endDelta:c,standardProps:s})}else s.hasEnd=!1,this.mutate({datesDelta:u,standardProps:s})}}moveStart(e){let n=y(e);n&&this.mutate({startDelta:n})}moveEnd(e){let n=y(e);n&&this.mutate({endDelta:n})}moveDates(e){let n=y(e);n&&this.mutate({datesDelta:n})}setAllDay(e,n={}){let r={allDay:e},{maintainDuration:i}=n;i==null&&(i=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(r.hasEnd=i),this.mutate({standardProps:r})}formatRange(e){let{dateEnv:n}=this._context,r=this._instance,i=I(e);return this._def.hasEnd?n.formatRange(r.range.start,r.range.end,i,{forcedStartTzo:r.forcedStartTzo,forcedEndTzo:r.forcedEndTzo}):n.format(r.range.start,i,{forcedTzo:r.forcedStartTzo})}mutate(e){let n=this._instance;if(n){let r=this._def,i=this._context,{eventStore:s}=i.getCurrentData(),a=zs(s,n.instanceId);a=da(a,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},e,i);let o=new H(i,r,n);this._def=a.defs[r.defId],this._instance=a.instances[n.instanceId],i.dispatch({type:"MERGE_EVENTS",eventStore:a}),i.emitter.trigger("eventChange",{oldEvent:o,event:this,relatedEvents:kt(a,i,n),revert(){i.dispatch({type:"RESET_EVENTS",eventStore:s})}})}}remove(){let e=this._context,n=Dr(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:n}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert(){e.dispatch({type:"MERGE_EVENTS",eventStore:n})}})}get source(){let{sourceId:e}=this._def;return e?new re(this._context,this._context.getCurrentData().eventSources[e]):null}get start(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null}get end(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null}get startStr(){let e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""}get endStr(){let e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""}get id(){return this._def.publicId}get groupId(){return this._def.groupId}get allDay(){return this._def.allDay}get title(){return this._def.title}get url(){return this._def.url}get display(){return this._def.ui.display||"auto"}get startEditable(){return this._def.ui.startEditable}get durationEditable(){return this._def.ui.durationEditable}get constraint(){return this._def.ui.constraints[0]||null}get overlap(){return this._def.ui.overlap}get allow(){return this._def.ui.allows[0]||null}get backgroundColor(){return this._def.ui.backgroundColor}get borderColor(){return this._def.ui.borderColor}get textColor(){return this._def.ui.textColor}get classNames(){return this._def.ui.classNames}get extendedProps(){return this._def.extendedProps}toPlainObject(e={}){let n=this._def,{ui:r}=n,{startStr:i,endStr:s}=this,a={allDay:n.allDay};return n.title&&(a.title=n.title),i&&(a.start=i),s&&(a.end=s),n.publicId&&(a.id=n.publicId),n.groupId&&(a.groupId=n.groupId),n.url&&(a.url=n.url),r.display&&r.display!=="auto"&&(a.display=r.display),e.collapseColor&&r.backgroundColor&&r.backgroundColor===r.borderColor?a.color=r.backgroundColor:(r.backgroundColor&&(a.backgroundColor=r.backgroundColor),r.borderColor&&(a.borderColor=r.borderColor)),r.textColor&&(a.textColor=r.textColor),r.classNames.length&&(a.classNames=r.classNames),Object.keys(n.extendedProps).length&&(e.collapseExtendedProps?Object.assign(a,n.extendedProps):a.extendedProps=n.extendedProps),a}toJSON(){return this.toPlainObject()}}function Dr(t){let e=t._def,n=t._instance;return{defs:{[e.defId]:e},instances:n?{[n.instanceId]:n}:{}}}function kt(t,e,n){let{defs:r,instances:i}=t,s=[],a=n?n.instanceId:"";for(let l in i){let o=i[l],u=r[o.defId];o.instanceId!==a&&s.push(new H(e,u,o))}return s}function mn(t,e,n,r){let i={},s={},a={},l=[],o=[],u=wr(t.defs,e);for(let c in t.defs){let p=t.defs[c];u[p.defId].display==="inverse-background"&&(p.groupId?(i[p.groupId]=[],a[p.groupId]||(a[p.groupId]=p)):s[c]=[])}for(let c in t.instances){let p=t.instances[c],f=t.defs[p.defId],d=u[f.defId],m=p.range,_=!f.allDay&&r?mr(m,r):m,v=me(_,n);v&&(d.display==="inverse-background"?f.groupId?i[f.groupId].push(v):s[p.defId].push(v):d.display!=="none"&&(d.display==="background"?l:o).push({def:f,ui:d,instance:p,range:v,isStart:_.start&&_.start.valueOf()===v.start.valueOf(),isEnd:_.end&&_.end.valueOf()===v.end.valueOf()}))}for(let c in i){let p=i[c],f=dn(p,n);for(let d of f){let m=a[c],_=u[m.defId];l.push({def:m,ui:_,instance:null,range:d,isStart:!1,isEnd:!1})}}for(let c in s){let p=s[c],f=dn(p,n);for(let d of f)l.push({def:t.defs[c],ui:u[c],instance:null,range:d,isStart:!1,isEnd:!1})}return{bg:l,fg:o}}function vn(t,e){t.fcSeg=e}function pt(t){return t.fcSeg||t.parentNode.fcSeg||null}function wr(t,e){return be(t,n=>Sr(n,e))}function Sr(t,e){let n=[];return e[""]&&n.push(e[""]),e[t.defId]&&n.push(e[t.defId]),n.push(t.ui),qs(n)}function Wo(t,e){let n=t.map(pa);return n.sort((r,i)=>Ti(r,i,e)),n.map(r=>r._seg)}function pa(t){let{eventRange:e}=t,n=e.def,r=e.instance?e.instance.range:e.range,i=r.start?r.start.valueOf():0,s=r.end?r.end.valueOf():0;return Object.assign(Object.assign(Object.assign({},n.extendedProps),n),{id:n.publicId,start:i,end:s,duration:s-i,allDay:Number(n.allDay),_seg:t})}function ga(t,e){let{pluginHooks:n}=e,r=n.isDraggableTransformers,{def:i,ui:s}=t.eventRange,a=s.startEditable;for(let l of r)a=l(a,i,s,e);return a}function ma(t,e){return t.isStart&&t.eventRange.ui.durationEditable&&e.options.eventResizableFromStart}function va(t,e){return t.isEnd&&t.eventRange.ui.durationEditable}function Aa(t,e,n,r,i,s,a){let{dateEnv:l,options:o}=n,{displayEventTime:u,displayEventEnd:c}=o,p=t.eventRange.def,f=t.eventRange.instance;u==null&&(u=r!==!1),c==null&&(c=i!==!1);let d=f.range.start,m=f.range.end,_=s||t.start||t.eventRange.range.start,v=a||t.end||t.eventRange.range.end,b=w(d).valueOf()===w(_).valueOf(),C=w(J(m,-1)).valueOf()===w(J(v,-1)).valueOf();return u&&!p.allDay&&(b||C)?(_=b?d:_,v=C?m:v,c&&p.hasEnd?l.formatRange(_,v,e,{forcedStartTzo:s?null:f.forcedStartTzo,forcedEndTzo:a?null:f.forcedEndTzo}):l.format(_,e,{forcedTzo:s?null:f.forcedStartTzo})):""}function Qo(t,e,n){let r=t.eventRange.range;return{isPast:r.end<=(n||e.start),isFuture:r.start>=(n||e.end),isToday:e&&W(e,r.start)}}function ba(t){let e=["fc-event"];return t.isMirror&&e.push("fc-event-mirror"),t.isDraggable&&e.push("fc-event-draggable"),(t.isStartResizable||t.isEndResizable)&&e.push("fc-event-resizable"),t.isDragging&&e.push("fc-event-dragging"),t.isResizing&&e.push("fc-event-resizing"),t.isSelected&&e.push("fc-event-selected"),t.isStart&&e.push("fc-event-start"),t.isEnd&&e.push("fc-event-end"),t.isPast&&e.push("fc-event-past"),t.isToday&&e.push("fc-event-today"),t.isFuture&&e.push("fc-event-future"),e}function Go(t){return t.instance?t.instance.instanceId:`${t.def.defId}:${t.range.start.toISOString()}`}function _a(t,e){let{def:n,instance:r}=t.eventRange,{url:i}=n;if(i)return{href:i};let{emitter:s,options:a}=e,{eventInteractive:l}=a;return l==null&&(l=n.interactive,l==null&&(l=!!s.hasHandlers("eventClick"))),l?ar(o=>{s.trigger("eventClick",{el:o.target,event:new H(e,n,r),jsEvent:o,view:e.viewApi})}):{}}const ya={start:h,end:h,allDay:Boolean};function Ea(t,e,n){let r=Ca(t,e),{range:i}=r;if(!i.start)return null;if(!i.end){if(n==null)return null;i.end=e.add(i.start,n)}return r}function Ca(t,e){let{refined:n,extra:r}=yt(t,ya),i=n.start?e.createMarkerMeta(n.start):null,s=n.end?e.createMarkerMeta(n.end):null,{allDay:a}=n;return a==null&&(a=i&&i.isTimeUnspecified&&(!s||s.isTimeUnspecified)),Object.assign({range:{start:i?i.marker:null,end:s?s.marker:null},allDay:a},r)}function Da(t,e){return Object.assign(Object.assign({},Tr(t.range,e,t.allDay)),{allDay:t.allDay})}function Rr(t,e,n){return Object.assign(Object.assign({},Tr(t,e,n)),{timeZone:e.timeZone})}function Tr(t,e,n){return{start:e.toDate(t.start),end:e.toDate(t.end),startStr:e.formatIso(t.start,{omitTime:n}),endStr:e.formatIso(t.end,{omitTime:n})}}function wa(t,e,n){let r=br({editable:!1},n),i=ft(r.refined,r.extra,"",t.allDay,!0,n);return{def:i,ui:Sr(i,e),instance:wt(i.defId,t.range),range:t.range,isStart:!0,isEnd:!0}}function Sa(t,e,n){let r=!1,i=function(l){r||(r=!0,e(l))},s=function(l){r||(r=!0,n(l))},a=t(i,s);a&&typeof a.then=="function"&&a.then(i,s)}class An extends Error{constructor(e,n){super(e),this.response=n}}function Ra(t,e,n){t=t.toUpperCase();const r={method:t};return t==="GET"?e+=(e.indexOf("?")===-1?"?":"&")+new URLSearchParams(n):(r.body=new URLSearchParams(n),r.headers={"Content-Type":"application/x-www-form-urlencoded"}),fetch(e,r).then(i=>{if(i.ok)return i.json().then(s=>[s,i],()=>{throw new An("Failure parsing JSON",i)});throw new An("Request failed",i)})}let tt;function kr(){return tt==null&&(tt=Ta()),tt}function Ta(){if(typeof document>"u")return!0;let t=document.createElement("div");t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.innerHTML="<table><tr><td><div></div></td></tr></table>",t.querySelector("table").style.height="100px",t.querySelector("div").style.height="100%",document.body.appendChild(t);let n=t.querySelector("div").offsetHeight>0;return document.body.removeChild(t),n}class ka extends S{constructor(){super(...arguments),this.state={forPrint:!1},this.handleBeforePrint=()=>{Le(()=>{this.setState({forPrint:!0})})},this.handleAfterPrint=()=>{Le(()=>{this.setState({forPrint:!1})})}}render(){let{props:e}=this,{options:n}=e,{forPrint:r}=this.state,i=r||n.height==="auto"||n.contentHeight==="auto",s=!i&&n.height!=null?n.height:"",a=["fc",r?"fc-media-print":"fc-media-screen",`fc-direction-${n.direction}`,e.theme.getClass("root")];return kr()||a.push("fc-liquid-hack"),e.children(a,s,i,r)}componentDidMount(){let{emitter:e}=this.props;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)}componentWillUnmount(){let{emitter:e}=this.props;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)}}class Ir{constructor(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}destroy(){}}function Ia(t,e){return{component:t,el:e.el,useEventCenter:e.useEventCenter!=null?e.useEventCenter:!0,isHitComboAllowed:e.isHitComboAllowed||null}}const bn={};class Ma{getCurrentData(){return this.currentDataManager.getCurrentData()}dispatch(e){this.currentDataManager.dispatch(e)}get view(){return this.getCurrentData().viewApi}batchRendering(e){e()}updateSize(){this.trigger("_resize",!0)}setOption(e,n){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:n})}getOption(e){return this.currentDataManager.currentCalendarOptionsInput[e]}getAvailableLocaleCodes(){return Object.keys(this.getCurrentData().availableRawLocales)}on(e,n){let{currentDataManager:r}=this;r.currentCalendarOptionsRefiners[e]?r.emitter.on(e,n):console.warn(`Unknown listener name '${e}'`)}off(e,n){this.currentDataManager.emitter.off(e,n)}trigger(e,...n){this.currentDataManager.emitter.trigger(e,...n)}changeView(e,n){this.batchRendering(()=>{if(this.unselect(),n)if(n.start&&n.end)this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),this.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:n});else{let{dateEnv:r}=this.getCurrentData();this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:r.createMarker(n)})}else this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})})}zoomTo(e,n){let r=this.getCurrentData(),i;n=n||"day",i=r.viewSpecs[n]||this.getUnitViewSpec(n),this.unselect(),i?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:i.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})}getUnitViewSpec(e){let{viewSpecs:n,toolbarConfig:r}=this.getCurrentData(),i=[].concat(r.header?r.header.viewsWithButtons:[],r.footer?r.footer.viewsWithButtons:[]),s,a;for(let l in n)i.push(l);for(s=0;s<i.length;s+=1)if(a=n[i[s]],a&&a.singleUnit===e)return a;return null}prev(){this.unselect(),this.dispatch({type:"PREV"})}next(){this.unselect(),this.dispatch({type:"NEXT"})}prevYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})}nextYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})}today(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:ye(e.calendarOptions.now,e.dateEnv)})}gotoDate(e){let n=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:n.dateEnv.createMarker(e)})}incrementDate(e){let n=this.getCurrentData(),r=y(e);r&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:n.dateEnv.add(n.currentDate,r)}))}getDate(){let e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)}formatDate(e,n){let{dateEnv:r}=this.getCurrentData();return r.format(r.createMarker(e),I(n))}formatRange(e,n,r){let{dateEnv:i}=this.getCurrentData();return i.formatRange(i.createMarker(e),i.createMarker(n),I(r),r)}formatIso(e,n){let{dateEnv:r}=this.getCurrentData();return r.formatIso(r.createMarker(e),{omitTime:n})}select(e,n){let r;n==null?e.start!=null?r=e:r={start:e,end:null}:r={start:e,end:n};let i=this.getCurrentData(),s=Ea(r,i.dateEnv,y({days:1}));s&&(this.dispatch({type:"SELECT_DATES",selection:s}),oa(s,null,i))}unselect(e){let n=this.getCurrentData();n.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),ca(e,n))}addEvent(e,n){if(e instanceof H){let a=e._def,l=e._instance;return this.getCurrentData().eventStore.defs[a.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:ht({def:a,instance:l})}),this.triggerEventAdd(e)),e}let r=this.getCurrentData(),i;if(n instanceof re)i=n.internalEventSource;else if(typeof n=="boolean")n&&([i]=Ct(r.eventSources));else if(n!=null){let a=this.getEventSourceById(n);if(!a)return console.warn(`Could not find an event source with ID "${n}"`),null;i=a.internalEventSource}let s=Ar(e,i,r,!1);if(s){let a=new H(r,s.def,s.def.recurringDef?null:s.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:ht(s)}),this.triggerEventAdd(a),a}return null}triggerEventAdd(e){let{emitter:n}=this.getCurrentData();n.trigger("eventAdd",{event:e,relatedEvents:[],revert:()=>{this.dispatch({type:"REMOVE_EVENTS",eventStore:Dr(e)})}})}getEventById(e){let n=this.getCurrentData(),{defs:r,instances:i}=n.eventStore;e=String(e);for(let s in r){let a=r[s];if(a.publicId===e){if(a.recurringDef)return new H(n,a,null);for(let l in i){let o=i[l];if(o.defId===a.defId)return new H(n,a,o)}}}return null}getEvents(){let e=this.getCurrentData();return kt(e.eventStore,e)}removeAllEvents(){this.dispatch({type:"REMOVE_ALL_EVENTS"})}getEventSources(){let e=this.getCurrentData(),n=e.eventSources,r=[];for(let i in n)r.push(new re(e,n[i]));return r}getEventSourceById(e){let n=this.getCurrentData(),r=n.eventSources;e=String(e);for(let i in r)if(r[i].publicId===e)return new re(n,r[i]);return null}addEventSource(e){let n=this.getCurrentData();if(e instanceof re)return n.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;let r=_r(e,n);return r?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[r]}),new re(n,r)):null}removeAllEventSources(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})}refetchEvents(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})}scrollToTime(e){let n=y(e);n&&this.trigger("_scrollRequest",{time:n})}}function Oa(t,e){let n={left:Math.max(t.left,e.left),right:Math.min(t.right,e.right),top:Math.max(t.top,e.top),bottom:Math.min(t.bottom,e.bottom)};return n.left<n.right&&n.top<n.bottom?n:!1}function Mr(t,e,n,r){return{dow:t.getUTCDay(),isDisabled:!!(r&&!W(r.activeRange,t)),isOther:!!(r&&!W(r.currentRange,t)),isToday:!!(e&&W(e,t)),isPast:!!(n?t<n:e&&t<e.start),isFuture:!!(n?t>n:e&&t>=e.end)}}function It(t,e){let n=["fc-day",`fc-day-${Ui[t.dow]}`];return t.isDisabled?n.push("fc-day-disabled"):(t.isToday&&(n.push("fc-day-today"),n.push(e.getClass("today"))),t.isPast&&n.push("fc-day-past"),t.isFuture&&n.push("fc-day-future"),t.isOther&&n.push("fc-day-other")),n}const Na=I({year:"numeric",month:"long",day:"numeric"}),xa=I({week:"long"});function Ba(t,e,n="day",r=!0){const{dateEnv:i,options:s,calendarApi:a}=t;let l=i.format(e,n==="week"?xa:Na);if(s.navLinks){let o=i.toDate(e);const u=c=>{let p=n==="day"?s.navLinkDayClick:n==="week"?s.navLinkWeekClick:null;typeof p=="function"?p.call(a,i.toDate(e),c):(typeof p=="string"&&(n=p),a.zoomTo(e,n))};return Object.assign({title:ue(s.navLinkHint,[l,o],l),"data-navlink":""},r?sr(u):{onClick:u})}return{"aria-label":l}}let nt;function Ha(){return nt||(nt=Pa()),nt}function Pa(){let t=document.createElement("div");t.style.overflow="scroll",t.style.position="absolute",t.style.top="-9999px",t.style.left="-9999px",document.body.appendChild(t);let e=Ua(t);return document.body.removeChild(t),e}function Ua(t){return{x:t.offsetHeight-t.clientHeight,y:t.offsetWidth-t.clientWidth}}function Fa(t){let e=Va(t),n=t.getBoundingClientRect();for(let r of e){let i=Oa(n,r.getBoundingClientRect());if(i)n=i;else return null}return n}function Va(t){let e=[];for(;t instanceof HTMLElement;){let n=window.getComputedStyle(t);if(n.position==="fixed")break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&e.push(t),t=t.parentNode}return e}class qo{constructor(e,n,r,i){this.els=n;let s=this.originClientRect=e.getBoundingClientRect();r&&this.buildElHorizontals(s.left),i&&this.buildElVerticals(s.top)}buildElHorizontals(e){let n=[],r=[];for(let i of this.els){let s=i.getBoundingClientRect();n.push(s.left-e),r.push(s.right-e)}this.lefts=n,this.rights=r}buildElVerticals(e){let n=[],r=[];for(let i of this.els){let s=i.getBoundingClientRect();n.push(s.top-e),r.push(s.bottom-e)}this.tops=n,this.bottoms=r}leftToIndex(e){let{lefts:n,rights:r}=this,i=n.length,s;for(s=0;s<i;s+=1)if(e>=n[s]&&e<r[s])return s}topToIndex(e){let{tops:n,bottoms:r}=this,i=n.length,s;for(s=0;s<i;s+=1)if(e>=n[s]&&e<r[s])return s}getWidth(e){return this.rights[e]-this.lefts[e]}getHeight(e){return this.bottoms[e]-this.tops[e]}similarTo(e){return Te(this.tops||[],e.tops||[])&&Te(this.bottoms||[],e.bottoms||[])&&Te(this.lefts||[],e.lefts||[])&&Te(this.rights||[],e.rights||[])}}function Te(t,e){const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(Math.round(t[r])!==Math.round(e[r]))return!1;return!0}class ja extends S{constructor(){super(...arguments),this.uid=K()}prepareHits(){}queryHit(e,n,r,i){return null}isValidSegDownEl(e){return!this.props.eventDrag&&!this.props.eventResize&&!F(e,".fc-event-mirror")}isValidDateDownEl(e){return!F(e,".fc-event:not(.fc-bg-event)")&&!F(e,".fc-more-link")&&!F(e,"a[data-navlink]")&&!F(e,".fc-popover")}}class Zo{constructor(e=n=>n.thickness||1){this.getEntryThickness=e,this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}addSegs(e){let n=[];for(let r of e)this.insertEntry(r,n);return n}insertEntry(e,n){let r=this.findInsertion(e);this.isInsertionValid(r,e)?this.insertEntryAt(e,r):this.handleInvalidInsertion(r,e,n)}isInsertionValid(e,n){return(this.maxCoord===-1||e.levelCoord+this.getEntryThickness(n)<=this.maxCoord)&&(this.maxStackCnt===-1||e.stackCnt<this.maxStackCnt)}handleInvalidInsertion(e,n,r){if(this.allowReslicing&&e.touchingEntry){const i=Object.assign(Object.assign({},n),{span:za(n.span,e.touchingEntry.span)});r.push(i),this.splitEntry(n,e.touchingEntry,r)}else r.push(n)}splitEntry(e,n,r){let i=e.span,s=n.span;i.start<s.start&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:i.start,end:s.start}},r),i.end>s.end&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:s.end,end:i.end}},r)}insertEntryAt(e,n){let{entriesByLevel:r,levelCoords:i}=this;n.lateral===-1?(rt(i,n.level,n.levelCoord),rt(r,n.level,[e])):rt(r[n.level],n.lateral,e),this.stackCnts[yn(e)]=n.stackCnt}findInsertion(e){let{levelCoords:n,entriesByLevel:r,strictOrder:i,stackCnts:s}=this,a=n.length,l=0,o=-1,u=-1,c=null,p=0;for(let m=0;m<a;m+=1){const _=n[m];if(!i&&_>=l+this.getEntryThickness(e))break;let v=r[m],b,C=En(v,e.span.start,_n),k=C[0]+C[1];for(;(b=v[k])&&b.span.start<e.span.end;){let R=_+this.getEntryThickness(b);R>l&&(l=R,c=b,o=m,u=k),R===l&&(p=Math.max(p,s[yn(b)]+1)),k+=1}}let f=0;if(c)for(f=o+1;f<a&&n[f]<l;)f+=1;let d=-1;return f<a&&n[f]===l&&(d=En(r[f],e.span.end,_n)[0]),{touchingLevel:o,touchingLateral:u,touchingEntry:c,stackCnt:p,levelCoord:l,level:f,lateral:d}}toRects(){let{entriesByLevel:e,levelCoords:n}=this,r=e.length,i=[];for(let s=0;s<r;s+=1){let a=e[s],l=n[s];for(let o of a)i.push(Object.assign(Object.assign({},o),{thickness:this.getEntryThickness(o),levelCoord:l}))}return i}}function _n(t){return t.span.end}function yn(t){return t.index+":"+t.span.start}function za(t,e){let n=Math.max(t.start,e.start),r=Math.min(t.end,e.end);return n<r?{start:n,end:r}:null}function rt(t,e,n){t.splice(e,0,n)}function En(t,e,n){let r=0,i=t.length;if(!i||e<n(t[r]))return[0,0];if(e>n(t[i-1]))return[i,0];for(;r<i;){let s=Math.floor(r+(i-r)/2),a=n(t[s]);if(e<a)i=s;else if(e>a)r=s+1;else return[s,1]}return[r,0]}function La(t,e){return!t||e>10?I({weekday:"short"}):e>1?I({weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}):I({weekday:"long"})}const Or="fc-col-header-cell";function Nr(t){return t.text}class Wa extends S{render(){let{dateEnv:e,options:n,theme:r,viewApi:i}=this.context,{props:s}=this,{date:a,dateProfile:l}=s,o=Mr(a,s.todayRange,null,l),u=[Or].concat(It(o,r)),c=e.format(a,s.dayHeaderFormat),p=!o.isDisabled&&s.colCnt>1?Ba(this.context,a):{},f=Object.assign(Object.assign(Object.assign({date:e.toDate(a),view:i},s.extraRenderProps),{text:c}),o);return g(j,{elTag:"th",elClasses:u,elAttrs:Object.assign({role:"columnheader",colSpan:s.colSpan,"data-date":o.isDisabled?void 0:or(a)},s.extraDataAttrs),renderProps:f,generatorName:"dayHeaderContent",customGenerator:n.dayHeaderContent,defaultGenerator:Nr,classNameGenerator:n.dayHeaderClassNames,didMount:n.dayHeaderDidMount,willUnmount:n.dayHeaderWillUnmount},d=>g("div",{className:"fc-scrollgrid-sync-inner"},!o.isDisabled&&g(d,{elTag:"a",elAttrs:p,elClasses:["fc-col-header-cell-cushion",s.isSticky&&"fc-sticky"]})))}}const Qa=I({weekday:"long"});class Ga extends S{render(){let{props:e}=this,{dateEnv:n,theme:r,viewApi:i,options:s}=this.context,a=B(new Date(2592e5),e.dow),l={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},o=n.format(a,e.dayHeaderFormat),u=Object.assign(Object.assign(Object.assign(Object.assign({date:a},l),{view:i}),e.extraRenderProps),{text:o});return g(j,{elTag:"th",elClasses:[Or,...It(l,r),...e.extraClassNames||[]],elAttrs:Object.assign({role:"columnheader",colSpan:e.colSpan},e.extraDataAttrs),renderProps:u,generatorName:"dayHeaderContent",customGenerator:s.dayHeaderContent,defaultGenerator:Nr,classNameGenerator:s.dayHeaderClassNames,didMount:s.dayHeaderDidMount,willUnmount:s.dayHeaderWillUnmount},c=>g("div",{className:"fc-scrollgrid-sync-inner"},g(c,{elTag:"a",elClasses:["fc-col-header-cell-cushion",e.isSticky&&"fc-sticky"],elAttrs:{"aria-label":n.format(a,Qa)}})))}}class xr extends x{constructor(e,n){super(e,n),this.initialNowDate=ye(n.options.now,n.dateEnv),this.initialNowQueriedMs=new Date().valueOf(),this.state=this.computeTiming().currentState}render(){let{props:e,state:n}=this;return e.children(n.nowDate,n.todayRange)}componentDidMount(){this.setTimeout()}componentDidUpdate(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())}componentWillUnmount(){this.clearTimeout()}computeTiming(){let{props:e,context:n}=this,r=J(this.initialNowDate,new Date().valueOf()-this.initialNowQueriedMs),i=n.dateEnv.startOf(r,e.unit),s=n.dateEnv.add(i,y(1,e.unit)),a=s.valueOf()-r.valueOf();return a=Math.min(1e3*60*60*24,a),{currentState:{nowDate:i,todayRange:Cn(i)},nextState:{nowDate:s,todayRange:Cn(s)},waitMs:a}}setTimeout(){let{nextState:e,waitMs:n}=this.computeTiming();this.timeoutId=setTimeout(()=>{this.setState(e,()=>{this.setTimeout()})},n)}clearTimeout(){this.timeoutId&&clearTimeout(this.timeoutId)}}xr.contextType=ee;function Cn(t){let e=w(t),n=B(e,1);return{start:e,end:n}}class Yo extends S{constructor(){super(...arguments),this.createDayHeaderFormatter=D(qa)}render(){let{context:e}=this,{dates:n,dateProfile:r,datesRepDistinctDays:i,renderIntro:s}=this.props,a=this.createDayHeaderFormatter(e.options.dayHeaderFormat,i,n.length);return g(xr,{unit:"day"},(l,o)=>g("tr",{role:"row"},s&&s("day"),n.map(u=>i?g(Wa,{key:u.toISOString(),date:u,dateProfile:r,todayRange:o,colCnt:n.length,dayHeaderFormat:a}):g(Ga,{key:u.getUTCDay(),dow:u.getUTCDay(),dayHeaderFormat:a}))))}}function qa(t,e,n){return t||La(e,n)}class $o{constructor(e,n){let r=e.start,{end:i}=e,s=[],a=[],l=-1;for(;r<i;)n.isHiddenDay(r)?s.push(l+.5):(l+=1,s.push(l),a.push(r)),r=B(r,1);this.dates=a,this.indices=s,this.cnt=a.length}sliceRange(e){let n=this.getDateDayIndex(e.start),r=this.getDateDayIndex(B(e.end,-1)),i=Math.max(0,n),s=Math.min(this.cnt-1,r);return i=Math.ceil(i),s=Math.floor(s),i<=s?{firstIndex:i,lastIndex:s,isStart:n===i,isEnd:r===s}:null}getDateDayIndex(e){let{indices:n}=this,r=Math.floor(le(this.dates[0],e));return r<0?n[0]-1:r>=n.length?n[n.length-1]+1:n[r]}}class Jo{constructor(e,n){let{dates:r}=e,i,s,a;if(n){for(s=r[0].getUTCDay(),i=1;i<r.length&&r[i].getUTCDay()!==s;i+=1);a=Math.ceil(r.length/i)}else a=1,i=r.length;this.rowCnt=a,this.colCnt=i,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}buildCells(){let e=[];for(let n=0;n<this.rowCnt;n+=1){let r=[];for(let i=0;i<this.colCnt;i+=1)r.push(this.buildCell(n,i));e.push(r)}return e}buildCell(e,n){let r=this.daySeries.dates[e*this.colCnt+n];return{key:r.toISOString(),date:r}}buildHeaderDates(){let e=[];for(let n=0;n<this.colCnt;n+=1)e.push(this.cells[0][n].date);return e}sliceRange(e){let{colCnt:n}=this,r=this.daySeries.sliceRange(e),i=[];if(r){let{firstIndex:s,lastIndex:a}=r,l=s;for(;l<=a;){let o=Math.floor(l/n),u=Math.min((o+1)*n,a+1);i.push({row:o,firstCol:l%n,lastCol:(u-1)%n,isStart:r.isStart&&l===s,isEnd:r.isEnd&&u-1===a}),l=u}}return i}}class Xo{constructor(){this.sliceBusinessHours=D(this._sliceBusinessHours),this.sliceDateSelection=D(this._sliceDateSpan),this.sliceEventStore=D(this._sliceEventStore),this.sliceEventDrag=D(this._sliceInteraction),this.sliceEventResize=D(this._sliceInteraction),this.forceDayIfListItem=!1}sliceProps(e,n,r,i,...s){let{eventUiBases:a}=e,l=this.sliceEventStore(e.eventStore,a,n,r,...s);return{dateSelectionSegs:this.sliceDateSelection(e.dateSelection,n,r,a,i,...s),businessHourSegs:this.sliceBusinessHours(e.businessHours,n,r,i,...s),fgEventSegs:l.fg,bgEventSegs:l.bg,eventDrag:this.sliceEventDrag(e.eventDrag,a,n,r,...s),eventResize:this.sliceEventResize(e.eventResize,a,n,r,...s),eventSelection:e.eventSelection}}sliceNowDate(e,n,r,i,...s){return this._sliceDateSpan({range:{start:e,end:J(e,1)},allDay:!1},n,r,{},i,...s)}_sliceBusinessHours(e,n,r,i,...s){return e?this._sliceEventStore(Ee(e,ke(n,!!r),i),{},n,r,...s).bg:[]}_sliceEventStore(e,n,r,i,...s){if(e){let a=mn(e,n,ke(r,!!i),i);return{bg:this.sliceEventRanges(a.bg,s),fg:this.sliceEventRanges(a.fg,s)}}return{bg:[],fg:[]}}_sliceInteraction(e,n,r,i,...s){if(!e)return null;let a=mn(e.mutatedEvents,n,ke(r,!!i),i);return{segs:this.sliceEventRanges(a.fg,s),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}}_sliceDateSpan(e,n,r,i,s,...a){if(!e)return[];let l=ke(n,!!r),o=me(e.range,l);if(o){e=Object.assign(Object.assign({},e),{range:o});let u=wa(e,i,s),c=this.sliceRange(e.range,...a);for(let p of c)p.eventRange=u;return c}return[]}sliceEventRanges(e,n){let r=[];for(let i of e)r.push(...this.sliceEventRange(i,n));return r}sliceEventRange(e,n){let r=e.range;this.forceDayIfListItem&&e.ui.display==="list-item"&&(r={start:r.start,end:B(r.start,1)});let i=this.sliceRange(r,...n);for(let s of i)s.eventRange=e,s.isStart=e.isStart&&s.isStart,s.isEnd=e.isEnd&&s.isEnd;return i}}function ke(t,e){let n=t.activeRange;return e?n:{start:J(n.start,t.slotMinTime.milliseconds),end:J(n.end,t.slotMaxTime.milliseconds-864e5)}}const Ie=/^(visible|hidden)$/;class Za extends S{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,ne(this.props.elRef,e)}}render(){let{props:e}=this,{liquid:n,liquidIsAbsolute:r}=e,i=n&&r,s=["fc-scroller"];return n&&(r?s.push("fc-scroller-liquid-absolute"):s.push("fc-scroller-liquid")),g("div",{ref:this.handleEl,className:s.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:i&&-(e.overcomeLeft||0)||"",right:i&&-(e.overcomeRight||0)||"",bottom:i&&-(e.overcomeBottom||0)||"",marginLeft:!i&&-(e.overcomeLeft||0)||"",marginRight:!i&&-(e.overcomeRight||0)||"",marginBottom:!i&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)}needsXScrolling(){if(Ie.test(this.props.overflowX))return!1;let{el:e}=this,n=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),{children:r}=e;for(let i=0;i<r.length;i+=1)if(r[i].getBoundingClientRect().width>n)return!0;return!1}needsYScrolling(){if(Ie.test(this.props.overflowY))return!1;let{el:e}=this,n=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),{children:r}=e;for(let i=0;i<r.length;i+=1)if(r[i].getBoundingClientRect().height>n)return!0;return!1}getXScrollbarWidth(){return Ie.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight}getYScrollbarWidth(){return Ie.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth}}class Dn{constructor(e){this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=(n,r)=>{let{depths:i,currentMap:s}=this,a=!1,l=!1;n!==null?(a=r in s,s[r]=n,i[r]=(i[r]||0)+1,l=!0):(i[r]-=1,i[r]||(delete s[r],delete this.callbackMap[r],a=!0)),this.masterCallback&&(a&&this.masterCallback(null,String(r)),l&&this.masterCallback(n,String(r)))}}createRef(e){let n=this.callbackMap[e];return n||(n=this.callbackMap[e]=r=>{this.handleValue(r,String(e))}),n}collect(e,n,r){return gs(this.currentMap,e,n,r)}getAll(){return Ct(this.currentMap)}}function Ya(t){let e=yi(t,".fc-scrollgrid-shrink"),n=0;for(let r of e)n=Math.max(n,Mi(r));return Math.ceil(n)}function Br(t,e){return t.liquid&&e.liquid}function $a(t,e){return e.maxHeight!=null||Br(t,e)}function Ja(t,e,n,r){let{expandRows:i}=n;return typeof e.content=="function"?e.content(n):g("table",{role:"presentation",className:[e.tableClassName,t.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:i?n.clientHeight:""}},n.tableColGroupNode,g(r?"thead":"tbody",{role:"presentation"},typeof e.rowContent=="function"?e.rowContent(n):e.rowContent))}function Xa(t,e){return Q(t,e,G)}function Ka(t,e){let n=[];for(let r of t){let i=r.span||1;for(let s=0;s<i;s+=1)n.push(g("col",{style:{width:r.width==="shrink"?el(e):r.width||"",minWidth:r.minWidth||""}}))}return g("colgroup",{},...n)}function el(t){return t??4}function tl(t){for(let e of t)if(e.width==="shrink")return!0;return!1}function nl(t,e){let n=["fc-scrollgrid",e.theme.getClass("table")];return t&&n.push("fc-scrollgrid-liquid"),n}function rl(t,e){let n=["fc-scrollgrid-section",`fc-scrollgrid-section-${t.type}`,t.className];return e&&t.liquid&&t.maxHeight==null&&n.push("fc-scrollgrid-section-liquid"),t.isSticky&&n.push("fc-scrollgrid-section-sticky"),n}function Ko(t){return g("div",{className:"fc-scrollgrid-sticky-shim",style:{width:t.clientWidth,minWidth:t.tableMinWidth}})}function ec(t){let{stickyHeaderDates:e}=t;return(e==null||e==="auto")&&(e=t.height==="auto"||t.viewHeight==="auto"),e}function tc(t){let{stickyFooterScrollbar:e}=t;return(e==null||e==="auto")&&(e=t.height==="auto"||t.viewHeight==="auto"),e}class il extends S{constructor(){super(...arguments),this.processCols=D(e=>e,Xa),this.renderMicroColGroup=D(Ka),this.scrollerRefs=new Dn,this.scrollerElRefs=new Dn(this._handleScrollerEl.bind(this)),this.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},this.handleSizing=()=>{this.safeSetState(Object.assign({shrinkWidth:this.computeShrinkWidth()},this.computeScrollerDims()))}}render(){let{props:e,state:n,context:r}=this,i=e.sections||[],s=this.processCols(e.cols),a=this.renderMicroColGroup(s,n.shrinkWidth),l=nl(e.liquid,r);e.collapsibleWidth&&l.push("fc-scrollgrid-collapsible");let o=i.length,u=0,c,p=[],f=[],d=[];for(;u<o&&(c=i[u]).type==="header";)p.push(this.renderSection(c,a,!0)),u+=1;for(;u<o&&(c=i[u]).type==="body";)f.push(this.renderSection(c,a,!1)),u+=1;for(;u<o&&(c=i[u]).type==="footer";)d.push(this.renderSection(c,a,!0)),u+=1;let m=!kr();const _={role:"rowgroup"};return g("table",{role:"grid",className:l.join(" "),style:{height:e.height}},!!(!m&&p.length)&&g("thead",_,...p),!!(!m&&f.length)&&g("tbody",_,...f),!!(!m&&d.length)&&g("tfoot",_,...d),m&&g("tbody",_,...p,...f,...d))}renderSection(e,n,r){return"outerContent"in e?g(P,{key:e.key},e.outerContent):g("tr",{key:e.key,role:"presentation",className:rl(e,this.props.liquid).join(" ")},this.renderChunkTd(e,n,e.chunk,r))}renderChunkTd(e,n,r,i){if("outerContent"in r)return r.outerContent;let{props:s}=this,{forceYScrollbars:a,scrollerClientWidths:l,scrollerClientHeights:o}=this.state,u=$a(s,e),c=Br(s,e),p=s.liquid?a?"scroll":u?"auto":"hidden":"visible",f=e.key,d=Ja(e,r,{tableColGroupNode:n,tableMinWidth:"",clientWidth:!s.collapsibleWidth&&l[f]!==void 0?l[f]:null,clientHeight:o[f]!==void 0?o[f]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:()=>{}},i);return g(i?"th":"td",{ref:r.elRef,role:"presentation"},g("div",{className:`fc-scroller-harness${c?" fc-scroller-harness-liquid":""}`},g(Za,{ref:this.scrollerRefs.createRef(f),elRef:this.scrollerElRefs.createRef(f),overflowY:p,overflowX:s.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:c,liquidIsAbsolute:!0},d)))}_handleScrollerEl(e,n){let r=sl(this.props.sections,n);r&&ne(r.chunk.scrollerElRef,e)}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(){this.handleSizing()}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}computeShrinkWidth(){return tl(this.props.cols)?Ya(this.scrollerElRefs.getAll()):0}computeScrollerDims(){let e=Ha(),{scrollerRefs:n,scrollerElRefs:r}=this,i=!1,s={},a={};for(let l in n.currentMap){let o=n.currentMap[l];if(o&&o.needsYScrolling()){i=!0;break}}for(let l of this.props.sections){let o=l.key,u=r.currentMap[o];if(u){let c=u.parentNode;s[o]=Math.floor(c.getBoundingClientRect().width-(i?e.y:0)),a[o]=Math.floor(c.getBoundingClientRect().height)}}return{forceYScrollbars:i,scrollerClientWidths:s,scrollerClientHeights:a}}}il.addStateEquality({scrollerClientWidths:G,scrollerClientHeights:G});function sl(t,e){for(let n of t)if(n.key===e)return n;return null}class Hr extends S{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,e&&vn(e,this.props.seg)}}render(){const{props:e,context:n}=this,{options:r}=n,{seg:i}=e,{eventRange:s}=i,{ui:a}=s,l={event:new H(n,s.def,s.instance),view:n.viewApi,timeText:e.timeText,textColor:a.textColor,backgroundColor:a.backgroundColor,borderColor:a.borderColor,isDraggable:!e.disableDragging&&ga(i,n),isStartResizable:!e.disableResizing&&ma(i,n),isEndResizable:!e.disableResizing&&va(i),isMirror:!!(e.isDragging||e.isResizing||e.isDateSelecting),isStart:!!i.isStart,isEnd:!!i.isEnd,isPast:!!e.isPast,isFuture:!!e.isFuture,isToday:!!e.isToday,isSelected:!!e.isSelected,isDragging:!!e.isDragging,isResizing:!!e.isResizing};return g(j,Object.assign({},e,{elRef:this.handleEl,elClasses:[...ba(l),...i.eventRange.ui.classNames,...e.elClasses||[]],renderProps:l,generatorName:"eventContent",customGenerator:r.eventContent,defaultGenerator:e.defaultGenerator,classNameGenerator:r.eventClassNames,didMount:r.eventDidMount,willUnmount:r.eventWillUnmount}))}componentDidUpdate(e){this.el&&this.props.seg!==e.seg&&vn(this.el,this.props.seg)}}class nc extends S{render(){let{props:e,context:n}=this,{options:r}=n,{seg:i}=e,{ui:s}=i.eventRange,a=r.eventTimeFormat||e.defaultTimeFormat,l=Aa(i,a,n,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return g(Hr,Object.assign({},e,{elTag:"a",elStyle:{borderColor:s.borderColor,backgroundColor:s.backgroundColor},elAttrs:_a(i,n),defaultGenerator:al,timeText:l}),(o,u)=>g(P,null,g(o,{elTag:"div",elClasses:["fc-event-main"],elStyle:{color:u.textColor}}),!!u.isStartResizable&&g("div",{className:"fc-event-resizer fc-event-resizer-start"}),!!u.isEndResizable&&g("div",{className:"fc-event-resizer fc-event-resizer-end"})))}}function al(t){return g("div",{className:"fc-event-main-frame"},t.timeText&&g("div",{className:"fc-event-time"},t.timeText),g("div",{className:"fc-event-title-container"},g("div",{className:"fc-event-title fc-sticky"},t.event.title||g(P,null," "))))}const ll=I({day:"numeric"});class ol extends S{constructor(){super(...arguments),this.refineRenderProps=Ne(ul)}render(){let{props:e,context:n}=this,{options:r}=n,i=this.refineRenderProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,isMonthStart:e.isMonthStart||!1,showDayNumber:e.showDayNumber,extraRenderProps:e.extraRenderProps,viewApi:n.viewApi,dateEnv:n.dateEnv,monthStartFormat:r.monthStartFormat});return g(j,Object.assign({},e,{elClasses:[...It(i,n.theme),...e.elClasses||[]],elAttrs:Object.assign(Object.assign({},e.elAttrs),i.isDisabled?{}:{"data-date":or(e.date)}),renderProps:i,generatorName:"dayCellContent",customGenerator:r.dayCellContent,defaultGenerator:e.defaultGenerator,classNameGenerator:i.isDisabled?void 0:r.dayCellClassNames,didMount:r.dayCellDidMount,willUnmount:r.dayCellWillUnmount}))}}function cl(t){return!!(t.dayCellContent||dt("dayCellContent",t))}function ul(t){let{date:e,dateEnv:n,dateProfile:r,isMonthStart:i}=t,s=Mr(e,t.todayRange,null,r),a=t.showDayNumber?n.format(e,i?t.monthStartFormat:ll):"";return Object.assign(Object.assign(Object.assign({date:n.toDate(e),view:t.viewApi},s),{isMonthStart:i,dayNumberText:a}),t.extraRenderProps)}class rc extends S{render(){let{props:e}=this,{seg:n}=e;return g(Hr,{elTag:"div",elClasses:["fc-bg-event"],elStyle:{backgroundColor:n.eventRange.ui.backgroundColor},defaultGenerator:dl,seg:n,timeText:"",isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,disableDragging:!0,disableResizing:!0})}}function dl(t){let{title:e}=t.event;return e&&g("div",{className:"fc-event-title"},t.event.title)}function ic(t){return g("div",{className:`fc-${t}`})}const sc=t=>g(ee.Consumer,null,e=>{let{dateEnv:n,options:r}=e,{date:i}=t,s=r.weekNumberFormat||t.defaultFormat,a=n.computeWeekNumber(i),l=n.format(i,s);return g(j,Object.assign({},t,{renderProps:{num:a,text:l,date:i},generatorName:"weekNumberContent",customGenerator:r.weekNumberContent,defaultGenerator:fl,classNameGenerator:r.weekNumberClassNames,didMount:r.weekNumberDidMount,willUnmount:r.weekNumberWillUnmount}))});function fl(t){return t.text}const it=10;class hl extends S{constructor(){super(...arguments),this.state={titleId:bt()},this.handleRootEl=e=>{this.rootEl=e,this.props.elRef&&ne(this.props.elRef,e)},this.handleDocumentMouseDown=e=>{const n=Di(e);this.rootEl.contains(n)||this.handleCloseClick()},this.handleDocumentKeyDown=e=>{e.key==="Escape"&&this.handleCloseClick()},this.handleCloseClick=()=>{let{onClose:e}=this.props;e&&e()}}render(){let{theme:e,options:n}=this.context,{props:r,state:i}=this,s=["fc-popover",e.getClass("popover")].concat(r.extraClassNames||[]);return si(g("div",Object.assign({},r.extraAttrs,{id:r.id,className:s.join(" "),"aria-labelledby":i.titleId,ref:this.handleRootEl}),g("div",{className:"fc-popover-header "+e.getClass("popoverHeader")},g("span",{className:"fc-popover-title",id:i.titleId},r.title),g("span",{className:"fc-popover-close "+e.getIconClass("close"),title:n.closeHint,onClick:this.handleCloseClick})),g("div",{className:"fc-popover-body "+e.getClass("popoverContent")},r.children)),r.parentEl)}componentDidMount(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()}componentWillUnmount(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)}updateSize(){let{isRtl:e}=this.context,{alignmentEl:n,alignGridTop:r}=this.props,{rootEl:i}=this,s=Fa(n);if(s){let a=i.getBoundingClientRect(),l=r?F(n,".fc-scrollgrid").getBoundingClientRect().top:s.top,o=e?s.right-a.width:s.left;l=Math.max(l,it),o=Math.min(o,document.documentElement.clientWidth-it-a.width),o=Math.max(o,it);let u=i.offsetParent.getBoundingClientRect();Ci(i,{top:l-u.top,left:o-u.left})}}}class pl extends ja{constructor(){super(...arguments),this.handleRootEl=e=>{this.rootEl=e,e?this.context.registerInteractiveComponent(this,{el:e,useEventCenter:!1}):this.context.unregisterInteractiveComponent(this)}}render(){let{options:e,dateEnv:n}=this.context,{props:r}=this,{startDate:i,todayRange:s,dateProfile:a}=r,l=n.format(i,e.dayPopoverFormat);return g(ol,{elRef:this.handleRootEl,date:i,dateProfile:a,todayRange:s},(o,u,c)=>g(hl,{elRef:c.ref,id:r.id,title:l,extraClassNames:["fc-more-popover"].concat(c.className||[]),extraAttrs:c,parentEl:r.parentEl,alignmentEl:r.alignmentEl,alignGridTop:r.alignGridTop,onClose:r.onClose},cl(e)&&g(o,{elTag:"div",elClasses:["fc-more-popover-misc"]}),r.children))}queryHit(e,n,r,i){let{rootEl:s,props:a}=this;return e>=0&&e<r&&n>=0&&n<i?{dateProfile:a.dateProfile,dateSpan:Object.assign({allDay:!a.forceTimed,range:{start:a.startDate,end:a.endDate}},a.extraDateSpan),dayEl:s,rect:{left:0,top:0,right:r,bottom:i},layer:1}:null}}class ac extends S{constructor(){super(...arguments),this.state={isPopoverOpen:!1,popoverId:bt()},this.handleLinkEl=e=>{this.linkEl=e,this.props.elRef&&ne(this.props.elRef,e)},this.handleClick=e=>{let{props:n,context:r}=this,{moreLinkClick:i}=r.options,s=wn(n).start;function a(l){let{def:o,instance:u,range:c}=l.eventRange;return{event:new H(r,o,u),start:r.dateEnv.toDate(c.start),end:r.dateEnv.toDate(c.end),isStart:l.isStart,isEnd:l.isEnd}}typeof i=="function"&&(i=i({date:s,allDay:!!n.allDayDate,allSegs:n.allSegs.map(a),hiddenSegs:n.hiddenSegs.map(a),jsEvent:e,view:r.viewApi})),!i||i==="popover"?this.setState({isPopoverOpen:!0}):typeof i=="string"&&r.calendarApi.zoomTo(s,i)},this.handlePopoverClose=()=>{this.setState({isPopoverOpen:!1})}}render(){let{props:e,state:n}=this;return g(ee.Consumer,null,r=>{let{viewApi:i,options:s,calendarApi:a}=r,{moreLinkText:l}=s,{moreCnt:o}=e,u=wn(e),c=typeof l=="function"?l.call(a,o):`+${o} ${l}`,p=ue(s.moreLinkHint,[o],c),f={num:o,shortText:`+${o}`,text:c,view:i};return g(P,null,!!e.moreCnt&&g(j,{elTag:e.elTag||"a",elRef:this.handleLinkEl,elClasses:[...e.elClasses||[],"fc-more-link"],elStyle:e.elStyle,elAttrs:Object.assign(Object.assign(Object.assign({},e.elAttrs),sr(this.handleClick)),{title:p,"aria-expanded":n.isPopoverOpen,"aria-controls":n.isPopoverOpen?n.popoverId:""}),renderProps:f,generatorName:"moreLinkContent",customGenerator:s.moreLinkContent,defaultGenerator:e.defaultGenerator||gl,classNameGenerator:s.moreLinkClassNames,didMount:s.moreLinkDidMount,willUnmount:s.moreLinkWillUnmount},e.children),n.isPopoverOpen&&g(pl,{id:n.popoverId,startDate:u.start,endDate:u.end,dateProfile:e.dateProfile,todayRange:e.todayRange,extraDateSpan:e.extraDateSpan,parentEl:this.parentEl,alignmentEl:e.alignmentElRef?e.alignmentElRef.current:this.linkEl,alignGridTop:e.alignGridTop,forceTimed:e.forceTimed,onClose:this.handlePopoverClose},e.popoverContent()))})}componentDidMount(){this.updateParentEl()}componentDidUpdate(){this.updateParentEl()}updateParentEl(){this.linkEl&&(this.parentEl=F(this.linkEl,".fc-view-harness"))}}function gl(t){return t.text}function wn(t){if(t.allDayDate)return{start:t.allDayDate,end:B(t.allDayDate,1)};let{hiddenSegs:e}=t;return{start:ml(e),end:Al(e)}}function ml(t){return t.reduce(vl).eventRange.range.start}function vl(t,e){return t.eventRange.range.start<e.eventRange.range.start?t:e}function Al(t){return t.reduce(bl).eventRange.range.end}function bl(t,e){return t.eventRange.range.end>e.eventRange.range.end?t:e}class _l{constructor(){this.handlers=[]}set(e){this.currentValue=e;for(let n of this.handlers)n(e)}subscribe(e){this.handlers.push(e),this.currentValue!==void 0&&e(this.currentValue)}}class lc extends _l{constructor(){super(...arguments),this.map=new Map}handle(e){const{map:n}=this;let r=!1;e.isActive?(n.set(e.id,e),r=!0):n.has(e.id)&&(n.delete(e.id),r=!0),r&&this.set(n)}}const yl=[],Pr={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},Ur=Object.assign(Object.assign({},Pr),{buttonHints:{prev:"Previous $0",next:"Next $0",today(t,e){return e==="day"?"Today":`This ${t}`}},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint(t){return`Show ${t} more event${t===1?"":"s"}`}});function El(t){let e=t.length>0?t[0].code:"en",n=yl.concat(t),r={en:Ur};for(let i of n)r[i.code]=i;return{map:r,defaultCode:e}}function Fr(t,e){return typeof t=="object"&&!Array.isArray(t)?Vr(t.code,[t.code],t):Cl(t,e)}function Cl(t,e){let n=[].concat(t||[]),r=Dl(n,e)||Ur;return Vr(t,n,r)}function Dl(t,e){for(let n=0;n<t.length;n+=1){let r=t[n].toLocaleLowerCase().split("-");for(let i=r.length;i>0;i-=1){let s=r.slice(0,i).join("-");if(e[s])return e[s]}}return null}function Vr(t,e,n){let r=Et([Pr,n],["buttonText"]);delete r.code;let{week:i}=r;return delete r.week,{codeArg:t,codes:e,week:i,simpleNumberFormat:new Intl.NumberFormat(t),options:r}}function oe(t){return{id:K(),name:t.name,premiumReleaseDate:t.premiumReleaseDate?new Date(t.premiumReleaseDate):void 0,deps:t.deps||[],reducers:t.reducers||[],isLoadingFuncs:t.isLoadingFuncs||[],contextInit:[].concat(t.contextInit||[]),eventRefiners:t.eventRefiners||{},eventDefMemberAdders:t.eventDefMemberAdders||[],eventSourceRefiners:t.eventSourceRefiners||{},isDraggableTransformers:t.isDraggableTransformers||[],eventDragMutationMassagers:t.eventDragMutationMassagers||[],eventDefMutationAppliers:t.eventDefMutationAppliers||[],dateSelectionTransformers:t.dateSelectionTransformers||[],datePointTransforms:t.datePointTransforms||[],dateSpanTransforms:t.dateSpanTransforms||[],views:t.views||{},viewPropsTransformers:t.viewPropsTransformers||[],isPropsValid:t.isPropsValid||null,externalDefTransforms:t.externalDefTransforms||[],viewContainerAppends:t.viewContainerAppends||[],eventDropTransformers:t.eventDropTransformers||[],componentInteractions:t.componentInteractions||[],calendarInteractions:t.calendarInteractions||[],themeClasses:t.themeClasses||{},eventSourceDefs:t.eventSourceDefs||[],cmdFormatter:t.cmdFormatter,recurringTypes:t.recurringTypes||[],namedTimeZonedImpl:t.namedTimeZonedImpl,initialView:t.initialView||"",elementDraggingImpl:t.elementDraggingImpl,optionChangeHandlers:t.optionChangeHandlers||{},scrollGridImpl:t.scrollGridImpl||null,listenerRefiners:t.listenerRefiners||{},optionRefiners:t.optionRefiners||{},propSetHandlers:t.propSetHandlers||{}}}function wl(t,e){let n={},r={premiumReleaseDate:void 0,reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(s){for(let a of s){const l=a.name,o=n[l];o===void 0?(n[l]=a.id,i(a.deps),r=Rl(r,a)):o!==a.id&&console.warn(`Duplicate plugin '${l}'`)}}return t&&i(t),i(e),r}function Sl(){let t=[],e=[],n;return(r,i)=>((!n||!Q(r,t)||!Q(i,e))&&(n=wl(r,i)),t=r,e=i,n)}function Rl(t,e){return{premiumReleaseDate:Tl(t.premiumReleaseDate,e.premiumReleaseDate),reducers:t.reducers.concat(e.reducers),isLoadingFuncs:t.isLoadingFuncs.concat(e.isLoadingFuncs),contextInit:t.contextInit.concat(e.contextInit),eventRefiners:Object.assign(Object.assign({},t.eventRefiners),e.eventRefiners),eventDefMemberAdders:t.eventDefMemberAdders.concat(e.eventDefMemberAdders),eventSourceRefiners:Object.assign(Object.assign({},t.eventSourceRefiners),e.eventSourceRefiners),isDraggableTransformers:t.isDraggableTransformers.concat(e.isDraggableTransformers),eventDragMutationMassagers:t.eventDragMutationMassagers.concat(e.eventDragMutationMassagers),eventDefMutationAppliers:t.eventDefMutationAppliers.concat(e.eventDefMutationAppliers),dateSelectionTransformers:t.dateSelectionTransformers.concat(e.dateSelectionTransformers),datePointTransforms:t.datePointTransforms.concat(e.datePointTransforms),dateSpanTransforms:t.dateSpanTransforms.concat(e.dateSpanTransforms),views:Object.assign(Object.assign({},t.views),e.views),viewPropsTransformers:t.viewPropsTransformers.concat(e.viewPropsTransformers),isPropsValid:e.isPropsValid||t.isPropsValid,externalDefTransforms:t.externalDefTransforms.concat(e.externalDefTransforms),viewContainerAppends:t.viewContainerAppends.concat(e.viewContainerAppends),eventDropTransformers:t.eventDropTransformers.concat(e.eventDropTransformers),calendarInteractions:t.calendarInteractions.concat(e.calendarInteractions),componentInteractions:t.componentInteractions.concat(e.componentInteractions),themeClasses:Object.assign(Object.assign({},t.themeClasses),e.themeClasses),eventSourceDefs:t.eventSourceDefs.concat(e.eventSourceDefs),cmdFormatter:e.cmdFormatter||t.cmdFormatter,recurringTypes:t.recurringTypes.concat(e.recurringTypes),namedTimeZonedImpl:e.namedTimeZonedImpl||t.namedTimeZonedImpl,initialView:t.initialView||e.initialView,elementDraggingImpl:t.elementDraggingImpl||e.elementDraggingImpl,optionChangeHandlers:Object.assign(Object.assign({},t.optionChangeHandlers),e.optionChangeHandlers),scrollGridImpl:e.scrollGridImpl||t.scrollGridImpl,listenerRefiners:Object.assign(Object.assign({},t.listenerRefiners),e.listenerRefiners),optionRefiners:Object.assign(Object.assign({},t.optionRefiners),e.optionRefiners),propSetHandlers:Object.assign(Object.assign({},t.propSetHandlers),e.propSetHandlers)}}function Tl(t,e){return t===void 0?e:e===void 0?t:new Date(Math.max(t.valueOf(),e.valueOf()))}class q extends _e{}q.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"};q.prototype.baseIconClass="fc-icon";q.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"};q.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"};q.prototype.iconOverrideOption="buttonIcons";q.prototype.iconOverrideCustomButtonOption="icon";q.prototype.iconOverridePrefix="fc-icon-";function kl(t,e){let n={},r;for(r in t)gt(r,n,t,e);for(r in e)gt(r,n,t,e);return n}function gt(t,e,n,r){if(e[t])return e[t];let i=Il(t,e,n,r);return i&&(e[t]=i),i}function Il(t,e,n,r){let i=n[t],s=r[t],a=c=>i&&i[c]!==null?i[c]:s&&s[c]!==null?s[c]:null,l=a("component"),o=a("superType"),u=null;if(o){if(o===t)throw new Error("Can't have a custom view type that references itself");u=gt(o,e,n,r)}return!l&&u&&(l=u.component),l?{type:t,component:l,defaults:Object.assign(Object.assign({},u?u.defaults:{}),i?i.rawOptions:{}),overrides:Object.assign(Object.assign({},u?u.overrides:{}),s?s.rawOptions:{})}:null}function Sn(t){return be(t,Ml)}function Ml(t){let e=typeof t=="function"?{component:t}:t,{component:n}=e;return e.content?n=Rn(e):n&&!(n.prototype instanceof S)&&(n=Rn(Object.assign(Object.assign({},e),{content:n}))),{superType:e.type,component:n,rawOptions:e}}function Rn(t){return e=>g(ee.Consumer,null,n=>g(j,{elTag:"div",elClasses:pr(n.viewSpec),renderProps:Object.assign(Object.assign({},e),{nextDayThreshold:n.options.nextDayThreshold}),generatorName:void 0,customGenerator:t.content,classNameGenerator:t.classNames,didMount:t.didMount,willUnmount:t.willUnmount}))}function Ol(t,e,n,r){let i=Sn(t),s=Sn(e.views),a=kl(i,s);return be(a,l=>Nl(l,s,e,n,r))}function Nl(t,e,n,r,i){let s=t.overrides.duration||t.defaults.duration||r.duration||n.duration,a=null,l="",o="",u={};if(s&&(a=xl(s),a)){let f=ct(a);l=f.unit,f.value===1&&(o=l,u=e[l]?e[l].rawOptions:{})}let c=f=>{let d=f.buttonText||{},m=t.defaults.buttonTextKey;return m!=null&&d[m]!=null?d[m]:d[t.type]!=null?d[t.type]:d[o]!=null?d[o]:null},p=f=>{let d=f.buttonHints||{},m=t.defaults.buttonTextKey;return m!=null&&d[m]!=null?d[m]:d[t.type]!=null?d[t.type]:d[o]!=null?d[o]:null};return{type:t.type,component:t.component,duration:a,durationUnit:l,singleUnit:o,optionDefaults:t.defaults,optionOverrides:Object.assign(Object.assign({},u),t.overrides),buttonTextOverride:c(r)||c(n)||t.overrides.buttonText,buttonTextDefault:c(i)||t.defaults.buttonText||c(de)||t.type,buttonTitleOverride:p(r)||p(n)||t.overrides.buttonHint,buttonTitleDefault:p(i)||t.defaults.buttonHint||p(de)}}let Tn={};function xl(t){let e=JSON.stringify(t),n=Tn[e];return n===void 0&&(n=y(t),Tn[e]=n),n}function Bl(t,e){switch(e.type){case"CHANGE_VIEW_TYPE":t=e.viewType}return t}function Hl(t,e){switch(e.type){case"SET_OPTION":return Object.assign(Object.assign({},t),{[e.optionName]:e.rawOptionValue});default:return t}}function Pl(t,e,n,r){let i;switch(e.type){case"CHANGE_VIEW_TYPE":return r.build(e.dateMarker||n);case"CHANGE_DATE":return r.build(e.dateMarker);case"PREV":if(i=r.buildPrev(t,n),i.isValid)return i;break;case"NEXT":if(i=r.buildNext(t,n),i.isValid)return i;break}return t}function Ul(t,e,n){let r=e?e.activeRange:null;return zr({},Ql(t,n),r,n)}function Fl(t,e,n,r){let i=n?n.activeRange:null;switch(e.type){case"ADD_EVENT_SOURCES":return zr(t,e.sources,i,r);case"REMOVE_EVENT_SOURCE":return jl(t,e.sourceId);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return n?Lr(t,i,r):t;case"FETCH_EVENT_SOURCES":return Mt(t,e.sourceIds?cr(e.sourceIds):Wr(t,r),i,e.isRefetch||!1,r);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return Wl(t,e.sourceId,e.fetchId,e.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return t}}function Vl(t,e,n){let r=e?e.activeRange:null;return Mt(t,Wr(t,n),r,!0,n)}function jr(t){for(let e in t)if(t[e].isFetching)return!0;return!1}function zr(t,e,n,r){let i={};for(let s of e)i[s.sourceId]=s;return n&&(i=Lr(i,n,r)),Object.assign(Object.assign({},t),i)}function jl(t,e){return ae(t,n=>n.sourceId!==e)}function Lr(t,e,n){return Mt(t,ae(t,r=>zl(r,e,n)),e,!1,n)}function zl(t,e,n){return Qr(t,n)?!n.options.lazyFetching||!t.fetchRange||t.isFetching||e.start<t.fetchRange.start||e.end>t.fetchRange.end:!t.latestFetchId}function Mt(t,e,n,r,i){let s={};for(let a in t){let l=t[a];e[a]?s[a]=Ll(l,n,r,i):s[a]=l}return s}function Ll(t,e,n,r){let{options:i,calendarApi:s}=r,a=r.pluginHooks.eventSourceDefs[t.sourceDefId],l=K();return a.fetch({eventSource:t,range:e,isRefetch:n,context:r},o=>{let{rawEvents:u}=o;i.eventSourceSuccess&&(u=i.eventSourceSuccess.call(s,u,o.response)||u),t.success&&(u=t.success.call(s,u,o.response)||u),r.dispatch({type:"RECEIVE_EVENTS",sourceId:t.sourceId,fetchId:l,fetchRange:e,rawEvents:u})},o=>{let u=!1;i.eventSourceFailure&&(i.eventSourceFailure.call(s,o),u=!0),t.failure&&(t.failure(o),u=!0),u||console.warn(o.message,o),r.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:t.sourceId,fetchId:l,fetchRange:e,error:o})}),Object.assign(Object.assign({},t),{isFetching:!0,latestFetchId:l})}function Wl(t,e,n,r){let i=t[e];return i&&n===i.latestFetchId?Object.assign(Object.assign({},t),{[e]:Object.assign(Object.assign({},i),{isFetching:!1,fetchRange:r})}):t}function Wr(t,e){return ae(t,n=>Qr(n,e))}function Ql(t,e){let n=yr(e),r=[].concat(t.eventSources||[]),i=[];t.initialEvents&&r.unshift(t.initialEvents),t.events&&r.unshift(t.events);for(let s of r){let a=_r(s,e,n);a&&i.push(a)}return i}function Qr(t,e){return!e.pluginHooks.eventSourceDefs[t.sourceDefId].ignoreRange}function Gl(t,e){switch(e.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return e.selection;default:return t}}function ql(t,e){switch(e.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return e.eventInstanceId;default:return t}}function Zl(t,e){let n;switch(e.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return n=e.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return t}}function Yl(t,e){let n;switch(e.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return n=e.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return t}}function $l(t,e,n,r,i){let s=t.headerToolbar?kn(t.headerToolbar,t,e,n,r,i):null,a=t.footerToolbar?kn(t.footerToolbar,t,e,n,r,i):null;return{header:s,footer:a}}function kn(t,e,n,r,i,s){let a={},l=[],o=!1;for(let u in t){let c=t[u],p=Jl(c,e,n,r,i,s);a[u]=p.widgets,l.push(...p.viewsWithButtons),o=o||p.hasTitle}return{sectionWidgets:a,viewsWithButtons:l,hasTitle:o}}function Jl(t,e,n,r,i,s){let a=e.direction==="rtl",l=e.customButtons||{},o=n.buttonText||{},u=e.buttonText||{},c=n.buttonHints||{},p=e.buttonHints||{},f=t?t.split(" "):[],d=[],m=!1;return{widgets:f.map(v=>v.split(",").map(b=>{if(b==="title")return m=!0,{buttonName:b};let C,k,R,U,M,N;if(C=l[b])R=E=>{C.click&&C.click.call(E.target,E,E.target)},(U=r.getCustomButtonIconClass(C))||(U=r.getIconClass(b,a))||(M=C.text),N=C.hint||C.text;else if(k=i[b]){d.push(b),R=()=>{s.changeView(b)},(M=k.buttonTextOverride)||(U=r.getIconClass(b,a))||(M=k.buttonTextDefault);let E=k.buttonTextOverride||k.buttonTextDefault;N=ue(k.buttonTitleOverride||k.buttonTitleDefault||e.viewHint,[E,b],E)}else if(s[b])if(R=()=>{s[b]()},(M=o[b])||(U=r.getIconClass(b,a))||(M=u[b]),b==="prevYear"||b==="nextYear"){let E=b==="prevYear"?"prev":"next";N=ue(c[E]||p[E],[u.year||"year","year"],u[b])}else N=E=>ue(c[b]||p[b],[u[E]||E,E],u[b]);return{buttonName:b,buttonClick:R,buttonIcon:U,buttonText:M,buttonHint:N}})),viewsWithButtons:d,hasTitle:m}}class Xl{constructor(e,n,r){this.type=e,this.getCurrentData=n,this.dateEnv=r}get calendar(){return this.getCurrentData().calendarApi}get title(){return this.getCurrentData().viewTitle}get activeStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)}get activeEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)}get currentStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)}get currentEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)}getOption(e){return this.getCurrentData().options[e]}}let Kl={ignoreRange:!0,parseMeta(t){return Array.isArray(t.events)?t.events:null},fetch(t,e){e({rawEvents:t.eventSource.meta})}};const eo=oe({name:"array-event-source",eventSourceDefs:[Kl]});let to={parseMeta(t){return typeof t.events=="function"?t.events:null},fetch(t,e,n){const{dateEnv:r}=t.context,i=t.eventSource.meta;Sa(i.bind(null,Rr(t.range,r)),s=>e({rawEvents:s}),n)}};const no=oe({name:"func-event-source",eventSourceDefs:[to]}),ro={method:String,extraParams:h,startParam:String,endParam:String,timeZoneParam:String};let io={parseMeta(t){return t.url&&(t.format==="json"||!t.format)?{url:t.url,format:"json",method:(t.method||"GET").toUpperCase(),extraParams:t.extraParams,startParam:t.startParam,endParam:t.endParam,timeZoneParam:t.timeZoneParam}:null},fetch(t,e,n){const{meta:r}=t.eventSource,i=ao(r,t.range,t.context);Ra(r.method,r.url,i).then(([s,a])=>{e({rawEvents:s,response:a})},n)}};const so=oe({name:"json-event-source",eventSourceRefiners:ro,eventSourceDefs:[io]});function ao(t,e,n){let{dateEnv:r,options:i}=n,s,a,l,o,u={};return s=t.startParam,s==null&&(s=i.startParam),a=t.endParam,a==null&&(a=i.endParam),l=t.timeZoneParam,l==null&&(l=i.timeZoneParam),typeof t.extraParams=="function"?o=t.extraParams():o=t.extraParams||{},Object.assign(u,o),u[s]=r.formatIso(e.start),u[a]=r.formatIso(e.end),r.timeZone!=="local"&&(u[l]=r.timeZone),u}const lo={daysOfWeek:h,startTime:y,endTime:y,duration:y,startRecur:h,endRecur:h};let oo={parse(t,e){if(t.daysOfWeek||t.startTime||t.endTime||t.startRecur||t.endRecur){let n={daysOfWeek:t.daysOfWeek||null,startTime:t.startTime||null,endTime:t.endTime||null,startRecur:t.startRecur?e.createMarker(t.startRecur):null,endRecur:t.endRecur?e.createMarker(t.endRecur):null},r;return t.duration&&(r=t.duration),!r&&t.startTime&&t.endTime&&(r=Bi(t.endTime,t.startTime)),{allDayGuess:!t.startTime&&!t.endTime,duration:r,typeData:n}}return null},expand(t,e,n){let r=me(e,{start:t.startRecur,end:t.endRecur});return r?uo(t.daysOfWeek,t.startTime,r,n):[]}};const co=oe({name:"simple-recurring-event",recurringTypes:[oo],eventRefiners:lo});function uo(t,e,n,r){let i=t?cr(t):null,s=w(n.start),a=n.end,l=[];for(;s<a;){let o;(!i||i[s.getUTCDay()])&&(e?o=r.add(s,e):o=s,l.push(o)),s=B(s,1)}return l}const fo=oe({name:"change-handler",optionChangeHandlers:{events(t,e){In([t],e)},eventSources:In}});function In(t,e){let n=Ct(e.getCurrentData().eventSources);if(n.length===1&&t.length===1&&Array.isArray(n[0]._raw)&&Array.isArray(t[0])){e.dispatch({type:"RESET_RAW_EVENTS",sourceId:n[0].sourceId,rawEvents:t[0]});return}let r=[];for(let i of t){let s=!1;for(let a=0;a<n.length;a+=1)if(n[a]._raw===i){n.splice(a,1),s=!0;break}s||r.push(i)}for(let i of n)e.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:i.sourceId});for(let i of r)e.calendarApi.addEventSource(i)}function ho(t,e){e.emitter.trigger("datesSet",Object.assign(Object.assign({},Rr(t.activeRange,e.dateEnv)),{view:e.viewApi}))}function po(t,e){let{emitter:n}=e;n.hasHandlers("eventsSet")&&n.trigger("eventsSet",kt(t,e))}const go=[eo,no,so,co,fo,oe({name:"misc",isLoadingFuncs:[t=>jr(t.eventSources)],propSetHandlers:{dateProfile:ho,eventStore:po}})];class mo{constructor(e,n){this.runTaskOption=e,this.drainedOption=n,this.queue=[],this.delayedRunner=new At(this.drain.bind(this))}request(e,n){this.queue.push(e),this.delayedRunner.request(n)}pause(e){this.delayedRunner.pause(e)}resume(e,n){this.delayedRunner.resume(e,n)}drain(){let{queue:e}=this;for(;e.length;){let n=[],r;for(;r=e.shift();)this.runTask(r),n.push(r);this.drained(n)}}runTask(e){this.runTaskOption&&this.runTaskOption(e)}drained(e){this.drainedOption&&this.drainedOption(e)}}function vo(t,e,n){let r;return/^(year|month)$/.test(t.currentRangeUnit)?r=t.currentRange:r=t.activeRange,n.formatRange(r.start,r.end,I(e.titleFormat||Ao(t)),{isEndExclusive:t.isRangeAllDay,defaultSeparator:e.titleRangeSeparator})}function Ao(t){let{currentRangeUnit:e}=t;if(e==="year")return{year:"numeric"};if(e==="month")return{year:"numeric",month:"long"};let n=Fe(t.currentRange.start,t.currentRange.end);return n!==null&&n>1?{year:"numeric",month:"short",day:"numeric"}:{year:"numeric",month:"long",day:"numeric"}}class bo{constructor(e){this.computeCurrentViewData=D(this._computeCurrentViewData),this.organizeRawLocales=D(El),this.buildLocale=D(Fr),this.buildPluginHooks=Sl(),this.buildDateEnv=D(_o),this.buildTheme=D(yo),this.parseToolbars=D($l),this.buildViewSpecs=D(Ol),this.buildDateProfileGenerator=Ne(Eo),this.buildViewApi=D(Co),this.buildViewUiProps=Ne(So),this.buildEventUiBySource=D(Do,G),this.buildEventUiBases=D(wo),this.parseContextBusinessHours=Ne(Ro),this.buildTitle=D(vo),this.emitter=new na,this.actionRunner=new mo(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.optionsForRefining=[],this.optionsForHandling=[],this.getCurrentData=()=>this.data,this.dispatch=f=>{this.actionRunner.request(f)},this.props=e,this.actionRunner.pause();let n={},r=this.computeOptionsData(e.optionOverrides,n,e.calendarApi),i=r.calendarOptions.initialView||r.pluginHooks.initialView,s=this.computeCurrentViewData(i,r,e.optionOverrides,n);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(s.options);let a=Bs(r.calendarOptions,r.dateEnv),l=s.dateProfileGenerator.build(a);W(l.activeRange,a)||(a=l.currentRange.start);let o={dateEnv:r.dateEnv,options:r.calendarOptions,pluginHooks:r.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData};for(let f of r.pluginHooks.contextInit)f(o);let u=Ul(r.calendarOptions,l,o),c={dynamicOptionOverrides:n,currentViewType:i,currentDate:a,dateProfile:l,businessHours:this.parseContextBusinessHours(o),eventSources:u,eventUiBases:{},eventStore:X(),renderableEventStore:X(),dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(o).selectionConfig},p=Object.assign(Object.assign({},o),c);for(let f of r.pluginHooks.reducers)Object.assign(c,f(null,null,p));st(c,o)&&this.emitter.trigger("loading",!0),this.state=c,this.updateData(),this.actionRunner.resume()}resetOptions(e,n){let{props:r}=this;n===void 0?r.optionOverrides=e:(r.optionOverrides=Object.assign(Object.assign({},r.optionOverrides||{}),e),this.optionsForRefining.push(...n)),(n===void 0||n.length)&&this.actionRunner.request({type:"NOTHING"})}_handleAction(e){let{props:n,state:r,emitter:i}=this,s=Hl(r.dynamicOptionOverrides,e),a=this.computeOptionsData(n.optionOverrides,s,n.calendarApi),l=Bl(r.currentViewType,e),o=this.computeCurrentViewData(l,a,n.optionOverrides,s);n.calendarApi.currentDataManager=this,i.setThisContext(n.calendarApi),i.setOptions(o.options);let u={dateEnv:a.dateEnv,options:a.calendarOptions,pluginHooks:a.pluginHooks,calendarApi:n.calendarApi,dispatch:this.dispatch,emitter:i,getCurrentData:this.getCurrentData},{currentDate:c,dateProfile:p}=r;this.data&&this.data.dateProfileGenerator!==o.dateProfileGenerator&&(p=o.dateProfileGenerator.build(c)),c=xs(c,e),p=Pl(p,e,c,o.dateProfileGenerator),(e.type==="PREV"||e.type==="NEXT"||!W(p.currentRange,c))&&(c=p.currentRange.start);let f=Fl(r.eventSources,e,p,u),d=Js(r.eventStore,e,f,p,u),_=jr(f)&&!o.options.progressiveEventRendering&&r.renderableEventStore||d,{eventUiSingleBase:v,selectionConfig:b}=this.buildViewUiProps(u),C=this.buildEventUiBySource(f),k=this.buildEventUiBases(_.defs,v,C),R={dynamicOptionOverrides:s,currentViewType:l,currentDate:c,dateProfile:p,eventSources:f,eventStore:d,renderableEventStore:_,selectionConfig:b,eventUiBases:k,businessHours:this.parseContextBusinessHours(u),dateSelection:Gl(r.dateSelection,e),eventSelection:ql(r.eventSelection,e),eventDrag:Zl(r.eventDrag,e),eventResize:Yl(r.eventResize,e)},U=Object.assign(Object.assign({},u),R);for(let E of a.pluginHooks.reducers)Object.assign(R,E(r,e,U));let M=st(r,u),N=st(R,u);!M&&N?i.trigger("loading",!0):M&&!N&&i.trigger("loading",!1),this.state=R,n.onAction&&n.onAction(e)}updateData(){let{props:e,state:n}=this,r=this.data,i=this.computeOptionsData(e.optionOverrides,n.dynamicOptionOverrides,e.calendarApi),s=this.computeCurrentViewData(n.currentViewType,i,e.optionOverrides,n.dynamicOptionOverrides),a=this.data=Object.assign(Object.assign(Object.assign({viewTitle:this.buildTitle(n.dateProfile,s.options,i.dateEnv),calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},i),s),n),l=i.pluginHooks.optionChangeHandlers,o=r&&r.calendarOptions,u=i.calendarOptions;if(o&&o!==u){o.timeZone!==u.timeZone&&(n.eventSources=a.eventSources=Vl(a.eventSources,n.dateProfile,a),n.eventStore=a.eventStore=pn(a.eventStore,r.dateEnv,a.dateEnv),n.renderableEventStore=a.renderableEventStore=pn(a.renderableEventStore,r.dateEnv,a.dateEnv));for(let c in l)(this.optionsForHandling.indexOf(c)!==-1||o[c]!==u[c])&&l[c](u[c],a)}this.optionsForHandling=[],e.onData&&e.onData(a)}computeOptionsData(e,n,r){if(!this.optionsForRefining.length&&e===this.stableOptionOverrides&&n===this.stableDynamicOptionOverrides)return this.stableCalendarOptionsData;let{refinedOptions:i,pluginHooks:s,localeDefaults:a,availableLocaleData:l,extra:o}=this.processRawCalendarOptions(e,n);Mn(o);let u=this.buildDateEnv(i.timeZone,i.locale,i.weekNumberCalculation,i.firstDay,i.weekText,s,l,i.defaultRangeSeparator),c=this.buildViewSpecs(s.views,this.stableOptionOverrides,this.stableDynamicOptionOverrides,a),p=this.buildTheme(i,s),f=this.parseToolbars(i,this.stableOptionOverrides,p,c,r);return this.stableCalendarOptionsData={calendarOptions:i,pluginHooks:s,dateEnv:u,viewSpecs:c,theme:p,toolbarConfig:f,localeDefaults:a,availableRawLocales:l.map}}processRawCalendarOptions(e,n){let{locales:r,locale:i}=Ke([de,e,n]),s=this.organizeRawLocales(r),a=s.map,l=this.buildLocale(i||s.defaultCode,a).options,o=this.buildPluginHooks(e.plugins||[],go),u=this.currentCalendarOptionsRefiners=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},on),cn),un),o.listenerRefiners),o.optionRefiners),c={},p=Ke([de,l,e,n]),f={},d=this.currentCalendarOptionsInput,m=this.currentCalendarOptionsRefined,_=!1;for(let v in p)this.optionsForRefining.indexOf(v)===-1&&(p[v]===d[v]||Z[v]&&v in d&&Z[v](d[v],p[v]))?f[v]=m[v]:u[v]?(f[v]=u[v](p[v]),_=!0):c[v]=d[v];return _&&(this.currentCalendarOptionsInput=p,this.currentCalendarOptionsRefined=f,this.stableOptionOverrides=e,this.stableDynamicOptionOverrides=n),this.optionsForHandling.push(...this.optionsForRefining),this.optionsForRefining=[],{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:o,availableLocaleData:s,localeDefaults:l,extra:c}}_computeCurrentViewData(e,n,r,i){let s=n.viewSpecs[e];if(!s)throw new Error(`viewType "${e}" is not available. Please make sure you've loaded all neccessary plugins`);let{refinedOptions:a,extra:l}=this.processRawViewOptions(s,n.pluginHooks,n.localeDefaults,r,i);Mn(l);let o=this.buildDateProfileGenerator({dateProfileGeneratorClass:s.optionDefaults.dateProfileGeneratorClass,duration:s.duration,durationUnit:s.durationUnit,usesMinMaxTime:s.optionDefaults.usesMinMaxTime,dateEnv:n.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:a.slotMinTime,slotMaxTime:a.slotMaxTime,showNonCurrentDates:a.showNonCurrentDates,dayCount:a.dayCount,dateAlignment:a.dateAlignment,dateIncrement:a.dateIncrement,hiddenDays:a.hiddenDays,weekends:a.weekends,nowInput:a.now,validRangeInput:a.validRange,visibleRangeInput:a.visibleRange,fixedWeekCount:a.fixedWeekCount}),u=this.buildViewApi(e,this.getCurrentData,n.dateEnv);return{viewSpec:s,options:a,dateProfileGenerator:o,viewApi:u}}processRawViewOptions(e,n,r,i,s){let a=Ke([de,e.optionDefaults,r,i,e.optionOverrides,s]),l=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},on),cn),un),ds),n.listenerRefiners),n.optionRefiners),o={},u=this.currentViewOptionsInput,c=this.currentViewOptionsRefined,p=!1,f={};for(let d in a)a[d]===u[d]||Z[d]&&Z[d](a[d],u[d])?o[d]=c[d]:(a[d]===this.currentCalendarOptionsInput[d]||Z[d]&&Z[d](a[d],this.currentCalendarOptionsInput[d])?d in this.currentCalendarOptionsRefined&&(o[d]=this.currentCalendarOptionsRefined[d]):l[d]?o[d]=l[d](a[d]):f[d]=a[d],p=!0);return p&&(this.currentViewOptionsInput=a,this.currentViewOptionsRefined=o),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:f}}}function _o(t,e,n,r,i,s,a,l){let o=Fr(e||a.defaultCode,a.map);return new ys({calendarSystem:"gregory",timeZone:t,namedTimeZoneImpl:s.namedTimeZonedImpl,locale:o,weekNumberCalculation:n,firstDay:r,weekText:i,cmdFormatter:s.cmdFormatter,defaultSeparator:l})}function yo(t,e){let n=e.themeClasses[t.themeSystem]||q;return new n(t)}function Eo(t){let e=t.dateProfileGeneratorClass||Hs;return new e(t)}function Co(t,e,n){return new Xl(t,e,n)}function Do(t){return be(t,e=>e.ui)}function wo(t,e,n){let r={"":e};for(let i in t){let s=t[i];s.sourceId&&n[s.sourceId]&&(r[i]=n[s.sourceId])}return r}function So(t){let{options:e}=t;return{eventUiSingleBase:Qe({display:e.eventDisplay,editable:e.editable,startEditable:e.eventStartEditable,durationEditable:e.eventDurationEditable,constraint:e.eventConstraint,overlap:typeof e.eventOverlap=="boolean"?e.eventOverlap:void 0,allow:e.eventAllow,backgroundColor:e.eventBackgroundColor,borderColor:e.eventBorderColor,textColor:e.eventTextColor,color:e.eventColor},t),selectionConfig:Qe({constraint:e.selectConstraint,overlap:typeof e.selectOverlap=="boolean"?e.selectOverlap:void 0,allow:e.selectAllow},t)}}function st(t,e){for(let n of e.pluginHooks.isLoadingFuncs)if(n(t))return!0;return!1}function Ro(t){return aa(t.options.businessHours,t)}function Mn(t,e){for(let n in t)console.warn(`Unknown option '${n}'`+(e?` for view '${e}'`:""))}class To extends S{render(){let e=this.props.widgetGroups.map(n=>this.renderWidgetGroup(n));return g("div",{className:"fc-toolbar-chunk"},...e)}renderWidgetGroup(e){let{props:n}=this,{theme:r}=this.context,i=[],s=!0;for(let a of e){let{buttonName:l,buttonClick:o,buttonText:u,buttonIcon:c,buttonHint:p}=a;if(l==="title")s=!1,i.push(g("h2",{className:"fc-toolbar-title",id:n.titleId},n.title));else{let f=l===n.activeButton,d=!n.isTodayEnabled&&l==="today"||!n.isPrevEnabled&&l==="prev"||!n.isNextEnabled&&l==="next",m=[`fc-${l}-button`,r.getClass("button")];f&&m.push(r.getClass("buttonActive")),i.push(g("button",{type:"button",title:typeof p=="function"?p(n.navUnit):p,disabled:d,"aria-pressed":f,className:m.join(" "),onClick:o},u||(c?g("span",{className:c,role:"img"}):"")))}}if(i.length>1){let a=s&&r.getClass("buttonGroup")||"";return g("div",{className:a},...i)}return i[0]}}class On extends S{render(){let{model:e,extraClassName:n}=this.props,r=!1,i,s,a=e.sectionWidgets,l=a.center;return a.left?(r=!0,i=a.left):i=a.start,a.right?(r=!0,s=a.right):s=a.end,g("div",{className:[n||"","fc-toolbar",r?"fc-toolbar-ltr":""].join(" ")},this.renderSection("start",i||[]),this.renderSection("center",l||[]),this.renderSection("end",s||[]))}renderSection(e,n){let{props:r}=this;return g(To,{key:e,widgetGroups:n,title:r.title,navUnit:r.navUnit,activeButton:r.activeButton,isTodayEnabled:r.isTodayEnabled,isPrevEnabled:r.isPrevEnabled,isNextEnabled:r.isNextEnabled,titleId:r.titleId})}}class ko extends S{constructor(){super(...arguments),this.state={availableWidth:null},this.handleEl=e=>{this.el=e,ne(this.props.elRef,e),this.updateAvailableWidth()},this.handleResize=()=>{this.updateAvailableWidth()}}render(){let{props:e,state:n}=this,{aspectRatio:r}=e,i=["fc-view-harness",r||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],s="",a="";return r?n.availableWidth!==null?s=n.availableWidth/r:a=`${1/r*100}%`:s=e.height||"",g("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:i.join(" "),style:{height:s,paddingBottom:a}},e.children)}componentDidMount(){this.context.addResizeHandler(this.handleResize)}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}updateAvailableWidth(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})}}class Io extends Ir{constructor(e){super(e),this.handleSegClick=(n,r)=>{let{component:i}=this,{context:s}=i,a=pt(r);if(a&&i.isValidSegDownEl(n.target)){let l=F(n.target,".fc-event-forced-url"),o=l?l.querySelector("a[href]").href:"";s.emitter.trigger("eventClick",{el:r,event:new H(i.context,a.eventRange.def,a.eventRange.instance),jsEvent:n,view:s.viewApi}),o&&!n.defaultPrevented&&(window.location.href=o)}},this.destroy=ir(e.el,"click",".fc-event",this.handleSegClick)}}class Mo extends Ir{constructor(e){super(e),this.handleEventElRemove=n=>{n===this.currentSegEl&&this.handleSegLeave(null,this.currentSegEl)},this.handleSegEnter=(n,r)=>{pt(r)&&(this.currentSegEl=r,this.triggerEvent("eventMouseEnter",n,r))},this.handleSegLeave=(n,r)=>{this.currentSegEl&&(this.currentSegEl=null,this.triggerEvent("eventMouseLeave",n,r))},this.removeHoverListeners=Si(e.el,".fc-event",this.handleSegEnter,this.handleSegLeave)}destroy(){this.removeHoverListeners()}triggerEvent(e,n,r){let{component:i}=this,{context:s}=i,a=pt(r);(!n||i.isValidSegDownEl(n.target))&&s.emitter.trigger(e,{el:r,event:new H(s,a.eventRange.def,a.eventRange.instance),jsEvent:n,view:s.viewApi})}}class Oo extends te{constructor(){super(...arguments),this.buildViewContext=D(Ds),this.buildViewPropTransformers=D(xo),this.buildToolbarProps=D(No),this.headerRef=Nt(),this.footerRef=Nt(),this.interactionsStore={},this.state={viewLabelId:bt()},this.registerInteractiveComponent=(e,n)=>{let r=Ia(e,n),a=[Io,Mo].concat(this.props.pluginHooks.componentInteractions).map(l=>new l(r));this.interactionsStore[e.uid]=a,bn[e.uid]=r},this.unregisterInteractiveComponent=e=>{let n=this.interactionsStore[e.uid];if(n){for(let r of n)r.destroy();delete this.interactionsStore[e.uid]}delete bn[e.uid]},this.resizeRunner=new At(()=>{this.props.emitter.trigger("_resize",!0),this.props.emitter.trigger("windowResize",{view:this.props.viewApi})}),this.handleWindowResize=e=>{let{options:n}=this.props;n.handleWindowResize&&e.target===window&&this.resizeRunner.request(n.windowResizeDelay)}}render(){let{props:e}=this,{toolbarConfig:n,options:r}=e,i=this.buildToolbarProps(e.viewSpec,e.dateProfile,e.dateProfileGenerator,e.currentDate,ye(e.options.now,e.dateEnv),e.viewTitle),s=!1,a="",l;e.isHeightAuto||e.forPrint?a="":r.height!=null?s=!0:r.contentHeight!=null?a=r.contentHeight:l=Math.max(r.aspectRatio,.5);let o=this.buildViewContext(e.viewSpec,e.viewApi,e.options,e.dateProfileGenerator,e.dateEnv,e.theme,e.pluginHooks,e.dispatch,e.getCurrentData,e.emitter,e.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),u=n.header&&n.header.hasTitle?this.state.viewLabelId:void 0;return g(ee.Provider,{value:o},n.header&&g(On,Object.assign({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.header,titleId:u},i)),g(ko,{liquid:s,height:a,aspectRatio:l,labeledById:u},this.renderView(e),this.buildAppendContent()),n.footer&&g(On,Object.assign({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footer,titleId:""},i)))}componentDidMount(){let{props:e}=this;this.calendarInteractions=e.pluginHooks.calendarInteractions.map(r=>new r(e)),window.addEventListener("resize",this.handleWindowResize);let{propSetHandlers:n}=e.pluginHooks;for(let r in n)n[r](e[r],e)}componentDidUpdate(e){let{props:n}=this,{propSetHandlers:r}=n.pluginHooks;for(let i in r)n[i]!==e[i]&&r[i](n[i],n)}componentWillUnmount(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(let e of this.calendarInteractions)e.destroy();this.props.emitter.trigger("_unmount")}buildAppendContent(){let{props:e}=this,n=e.pluginHooks.viewContainerAppends.map(r=>r(e));return g(P,{},...n)}renderView(e){let{pluginHooks:n}=e,{viewSpec:r}=e,i={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},s=this.buildViewPropTransformers(n.viewPropsTransformers);for(let l of s)Object.assign(i,l.transform(i,e));let a=r.component;return g(a,Object.assign({},i))}}function No(t,e,n,r,i,s){let a=n.build(i,void 0,!1),l=n.buildPrev(e,r,!1),o=n.buildNext(e,r,!1);return{title:s,activeButton:t.type,navUnit:t.singleUnit,isTodayEnabled:a.isValid&&!W(e.currentRange,i),isPrevEnabled:l.isValid,isNextEnabled:o.isValid}}function xo(t){return t.map(e=>new e)}class oc extends Ma{constructor(e,n={}){super(),this.isRendering=!1,this.isRendered=!1,this.currentClassNames=[],this.customContentRenderId=0,this.handleAction=r=>{switch(r.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":this.renderRunner.tryDrain()}},this.handleData=r=>{this.currentData=r,this.renderRunner.request(r.calendarOptions.rerenderDelay)},this.handleRenderRequest=()=>{if(this.isRendering){this.isRendered=!0;let{currentData:r}=this;Le(()=>{he(g(ka,{options:r.calendarOptions,theme:r.theme,emitter:r.emitter},(i,s,a,l)=>(this.setClassNames(i),this.setHeight(s),g(hr.Provider,{value:this.customContentRenderId},g(Oo,Object.assign({isHeightAuto:a,forPrint:l},r))))),this.el)})}else this.isRendered&&(this.isRendered=!1,he(null,this.el),this.setClassNames([]),this.setHeight(""))},pi(e),this.el=e,this.renderRunner=new At(this.handleRenderRequest),new bo({optionOverrides:n,calendarApi:this,onAction:this.handleAction,onData:this.handleData})}render(){let e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()}destroy(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())}updateSize(){Le(()=>{super.updateSize()})}batchRendering(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")}pauseRendering(){this.renderRunner.pause("pauseRendering")}resumeRendering(){this.renderRunner.resume("pauseRendering",!0)}resetOptions(e,n){this.currentDataManager.resetOptions(e,n)}setClassNames(e){if(!Q(e,this.currentClassNames)){let{classList:n}=this.el;for(let r of this.currentClassNames)n.remove(r);for(let r of e)n.add(r);this.currentClassNames=e}}setHeight(e){rr(this.el,"height",e)}}export{x as $,Ba as A,rc as B,cl as C,Yo as D,Hr as E,ol as F,J as G,me as H,Zo as I,yn as J,za as K,zo as L,ac as M,or as N,Xo as O,qo as P,xr as Q,Dn as R,il as S,oe as T,lc as U,Lo as V,sc as W,oc as X,he as Y,Fo as Z,P as _,$o as a,Ho as a0,Po as a1,Zn as a2,Ue as a3,Bo as a4,Uo as a5,Jo as b,I as c,Nt as d,Hs as e,Vo as f,B as g,jo as h,G as i,hi as j,ja as k,ec as l,D as m,tc as n,Qo as o,ic as p,Go as q,Ko as r,Wo as s,S as t,nc as u,Aa as v,_a as w,bt as x,g as y,ne as z};
