import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const WriterNotesModal = ({ notes, setModalClose }) => {
  return (
    <div className="z-999999 fixed inset-0 overflow-y-auto bg-black bg-opacity-80">
      <div className="flex min-h-screen items-center justify-center px-4 py-8">
        <div className="shadow-default w-full max-w-xl transform rounded border border-stroke bg-boxdark transition-all">
          {/* Modal Header */}
          <div className="flex items-center justify-between border-b border-stroke px-6 py-4">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon
                icon="fa-solid fa-envelope"
                className="text-xl text-primary"
              />
              <h3 className="text-xl font-medium text-white">
                Notes Emailed to Writer
              </h3>
            </div>
            <button
              onClick={() => setModalClose(false)}
              className="hover:text-primary"
            >
              <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
            </button>
          </div>

          {/* Notes Content */}
          <div className="max-h-[60vh] overflow-y-auto p-6">
            {notes ? (
              <div className="rounded border border-stroke bg-boxdark-2 p-4">
                <div className="prose prose-invert max-w-none">
                  <div className="whitespace-pre-wrap text-white">{notes}</div>
                </div>
              </div>
            ) : (
              // Empty State
              <div className="flex flex-col items-center justify-center py-8">
                <div className="mb-3 rounded-full bg-boxdark p-3">
                  <FontAwesomeIcon
                    icon="fa-solid fa-envelope-open"
                    className="h-6 w-6 text-bodydark2"
                  />
                </div>
                <p className="text-sm text-bodydark2">
                  No notes have been sent to the writer
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-stroke px-6 py-4">
            <button
              onClick={() => setModalClose(false)}
              className="flex w-full items-center justify-center rounded-sm bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
            >
              <FontAwesomeIcon icon="fa-solid fa-xmark" className="mr-2" />
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WriterNotesModal;
