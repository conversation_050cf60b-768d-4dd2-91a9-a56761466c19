import {
  Document,
  Font,
  Image,
  Page,
  Text,
  View,
  StyleSheet,
} from "@react-pdf/renderer";
import { Component } from "react";
import { createTw } from "react-pdf-tailwind";
import moment from "moment";

// Create custom styles
const styles = StyleSheet.create({
  page: {
    padding: 20,
    backgroundColor: "white",
  },
  header: {
    marginBottom: 20,
    padding: 20,
    backgroundColor: "#000",
    color: "white",
  },
  logoContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    height: 120,
    marginBottom: 10,
  },
  logo: {
    maxWidth: 300,
    maxHeight: 100,
    objectFit: "contain",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    color: "white",
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 5,
  },
  row: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    paddingVertical: 8,
  },
  tableHeader: {
    backgroundColor: "#000",
    color: "white",
    padding: 6,
    paddingVertical: 8,
    fontWeight: "bold",
    fontSize: 8,
    textAlign: "center",
  },
  tableCell: {
    padding: 4,
    fontSize: 8,
    textAlign: "center",
  },
  totalsContainer: {
    marginTop: 20,
    alignSelf: "flex-end",
    width: "40%",
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 5,
  },
  totalLabel: {
    fontSize: 12,
  },
  totalValue: {
    fontSize: 12,
    textAlign: "right",
  },
  totalBold: {
    fontWeight: "bold",
  },
  footer: {
    position: "absolute",
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: "center",
    fontSize: 10,
    color: "grey",
  },
  discountHighlight: {
    backgroundColor: "#f8f8f8",
  },
});

const tw = createTw({
  theme: {
    extend: {
      colors: {
        custom: "cornflowerblue",
      },
    },
  },
});

Font.registerHyphenationCallback((word) => {
  // Return entire word as unique part
  return [word];
});

class InvoiceQuotePDF extends Component {
  shouldComponentUpdate(nextProps, nextState) {
    return nextProps.data !== this.props.data;
  }

  render() {
    const { data, userDetails, companyInfo } = this.props;

    // Add safety checks
    if (!data || !data.invoiceData) {
      return (
        <Document>
          <Page size="A4" style={styles.page}>
            <Text>No data available</Text>
          </Page>
        </Document>
      );
    }

    // Determine which company details to use (manager, main member, or user)
    const companyDetails =
      companyInfo?.company?.manager ||
      companyInfo?.company?.main_member ||
      userDetails ||
      {};

    const calculateSubtotal = (items) => {
      if (!Array.isArray(items)) return 0;
      return items.reduce((sum, item) => {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;

        // Apply discount to price
        const discountedPrice = price - (price * discount) / 100;
        return sum + discountedPrice * quantity;
      }, 0);
    };

    const calculateDeposit = (subtotal) => {
      if (data.invoiceData.depositPercentage > 0) {
        return (subtotal * data.invoiceData.depositPercentage) / 100;
      }
      return data.invoiceData.depositAmount || 0;
    };

    const subtotal = calculateSubtotal(data.invoiceData.items);
    const deposit = calculateDeposit(subtotal);
    const balance = subtotal - deposit;

    const clientName = data.selectedClientId
      ? data.clientDetails?.program
      : data.newClientData?.program;

    const clientEmail = data.selectedClientId
      ? data.clientDetails?.email
      : data.newClientData?.email;

    // Get company logo from the correct property
    const companyLogo =
      companyDetails?.company_logo || companyDetails?.license_company_logo;

    return (
      <Document>
        <Page size="A4" orientation="landscape" style={styles.page}>
          {/* Header with Company Logo */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              {companyLogo ? (
                <Image style={styles.logo} src={companyLogo} />
              ) : (
                <Text style={styles.headerTitle}>
                  {companyDetails?.company_name || "COMPANY NAME"}
                </Text>
              )}
            </View>
            <Text style={styles.headerTitle}>
              {data.invoiceData.isQuote ? "QUOTE" : "INVOICE"}
            </Text>
          </View>

          {/* Company and Quote/Invoice Info */}
          <View style={tw("flex flex-row justify-between mb-6")}>
            <View style={tw("w-1/2")}>
              <Text style={styles.sectionTitle}>From:</Text>
              <Text style={tw("text-sm font-bold")}>
                {companyDetails?.company_name != null
                  ? companyDetails?.company_name
                  : "Company Name"}
              </Text>
              <Text style={tw("text-sm")}>
                {companyDetails?.address || "Address"}
              </Text>
              <Text style={tw("text-sm")}>
                {companyDetails?.phone || "Phone"}
              </Text>
              <Text style={tw("text-sm")}>
                {companyDetails?.email ||
                  companyDetails?.office_email ||
                  "Email"}
              </Text>
            </View>
            <View style={tw("w-1/2 text-right")}>
              <Text style={styles.sectionTitle}>
                {data.invoiceData.isQuote
                  ? "Quote Details:"
                  : "Invoice Details:"}
              </Text>
              <Text style={tw("text-sm")}>
                <Text style={tw("font-bold")}>Number: </Text>
                {data.invoiceData.invoiceNumber || "N/A"}
              </Text>
              <Text style={tw("text-sm")}>
                <Text style={tw("font-bold")}>Date: </Text>
                {moment(data.invoiceDates?.invoiceDate || new Date()).format(
                  "MM/DD/YYYY"
                )}
              </Text>
              <Text style={tw("text-sm")}>
                <Text style={tw("font-bold")}>Due Date: </Text>
                {data.invoiceDates?.invoiceDueDate
                  ? moment(data.invoiceDates.invoiceDueDate).format(
                      "MM/DD/YYYY"
                    )
                  : "N/A"}
              </Text>
            </View>
          </View>

          {/* Client Info */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Bill To:</Text>
            <Text style={tw("text-sm font-bold")}>
              {clientName || "Client Name"}
            </Text>
            <Text style={tw("text-sm")}>{clientEmail || "Client Email"}</Text>
          </View>

          {/* Items Table */}
          <View style={styles.section}>
            {/* Table Header */}
            <View style={tw("flex flex-row bg-black")}>
              <Text style={[styles.tableHeader, tw("w-[12%]")]}>Mix Date</Text>
              <Text style={[styles.tableHeader, tw("w-[12%]")]}>Producer</Text>
              <Text style={[styles.tableHeader, tw("w-[10%]")]}>Mix Type</Text>
              <Text style={[styles.tableHeader, tw("w-[12%]")]}>Team Name</Text>
              <Text style={[styles.tableHeader, tw("w-[10%]")]}>Division</Text>
              <Text style={[styles.tableHeader, tw("w-[8%]")]}>Survey Due</Text>
              <Text style={[styles.tableHeader, tw("w-[8%]")]}>Sub Due</Text>
              <Text style={[styles.tableHeader, tw("w-[8%]")]}>Est. Comp</Text>
              <Text style={[styles.tableHeader, tw("w-[7%]")]}>Price</Text>
              <Text style={[styles.tableHeader, tw("w-[7%]")]}>Discount</Text>
              <Text style={[styles.tableHeader, tw("w-[6%]")]}>Amount</Text>
            </View>

            {/* Table Rows */}
            {data.invoiceData.items?.map((item, index) => {
              const price = parseFloat(item.price) || 0;
              const quantity = parseInt(item.quantity) || 1;
              const discount = parseFloat(item.discount) || 0;
              const discountedPrice = price - (price * discount) / 100;
              const amount = discountedPrice * quantity;

              // Special row handling
              if (item.isSpecial || item.isSpecialRow) {
                return (
                  <View
                    key={index}
                    style={[
                      tw("flex flex-row border-b"),
                      styles.discountHighlight,
                    ]}
                  >
                    <Text style={[styles.tableCell, tw("w-[12%]")]}>-</Text>
                    <Text style={[styles.tableCell, tw("w-[12%]")]}>
                      {item.producer || "-"}
                    </Text>
                    <Text style={[styles.tableCell, tw("w-[10%]")]}>
                      Special
                    </Text>
                    <Text style={[styles.tableCell, tw("w-[12%]")]}>
                      {item.name || "Additional Charge"}
                    </Text>
                    <Text style={[styles.tableCell, tw("w-[10%]")]}>-</Text>
                    <Text style={[styles.tableCell, tw("w-[8%]")]}>-</Text>
                    <Text style={[styles.tableCell, tw("w-[8%]")]}>-</Text>
                    <Text style={[styles.tableCell, tw("w-[8%]")]}>-</Text>
                    <Text style={[styles.tableCell, tw("w-[7%]")]}>
                      ${price.toFixed(2)}
                    </Text>
                    <Text style={[styles.tableCell, tw("w-[7%]")]}>
                      {discount > 0 ? `${discount}%` : "-"}
                    </Text>
                    <Text style={[styles.tableCell, tw("w-[6%]")]}>
                      ${amount.toFixed(2)}
                    </Text>
                  </View>
                );
              }

              // Regular row
              return (
                <View key={index} style={tw("flex flex-row border-b")}>
                  <Text style={[styles.tableCell, tw("w-[12%]")]}>
                    {item.mixDate
                      ? moment(item.mixDate).format("MM/DD/YY")
                      : "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[12%]")]}>
                    {item.producer ||
                      (companyDetails?.first_name && companyDetails?.last_name
                        ? `${companyDetails.first_name} ${companyDetails.last_name}`
                        : "-")}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[10%]")]}>
                    {item.mixType || "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[12%]")]}>
                    {item.teamName || "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[10%]")]}>
                    {item.division || "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[8%]")]}>
                    {item.musicSurveyDue
                      ? moment(item.musicSurveyDue).format("MM/DD/YY")
                      : "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[8%]")]}>
                    {item.routineSubmissionDue
                      ? moment(item.routineSubmissionDue).format("MM/DD/YY")
                      : "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[8%]")]}>
                    {item.estimatedCompletion
                      ? moment(item.estimatedCompletion).format("MM/DD/YY")
                      : "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[7%]")]}>
                    ${price.toFixed(2)}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[7%]")]}>
                    {discount > 0 ? `${discount}%` : "-"}
                  </Text>
                  <Text style={[styles.tableCell, tw("w-[6%]")]}>
                    ${amount.toFixed(2)}
                  </Text>
                </View>
              );
            })}
          </View>

          {/* Totals */}
          <View style={styles.totalsContainer}>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Subtotal:</Text>
              <Text style={styles.totalValue}>${subtotal.toFixed(2)}</Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>
                Deposit{" "}
                {data.invoiceData.depositPercentage > 0
                  ? `(${data.invoiceData.depositPercentage}%)`
                  : ""}
                :
              </Text>
              <Text style={styles.totalValue}>${deposit.toFixed(2)}</Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={[styles.totalLabel, styles.totalBold]}>
                Balance Due:
              </Text>
              <Text style={[styles.totalValue, styles.totalBold]}>
                ${balance.toFixed(2)}
              </Text>
            </View>
          </View>

          {/* Notes */}
          {data.invoiceData.notes && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Notes:</Text>
              <Text style={tw("text-sm")}>{data.invoiceData.notes}</Text>
            </View>
          )}

          {/* Terms and Conditions */}
          {/* {data.invoiceData.termsAndConditions && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Terms and Conditions:</Text>
              <Text
                dangerouslySetInnerHTML={{
                  __html: data.invoiceData.termsAndConditions?.replace(
                    /\n/g,
                    "<br> "
                  ),
                }}
                style={tw("text-sm")}
              ></Text>
            </View>
          )} */}

          {/* Footer */}
          <View style={styles.footer}>
            <Text>
              {data.invoiceData.isQuote
                ? "This is a quote for the services described above, subject to the noted terms and conditions."
                : "Thank you for your business. Payment is due by the date specified above."}
            </Text>
          </View>
        </Page>
      </Document>
    );
  }
}

export default InvoiceQuotePDF;
