// src/Pages/manager/ManagerSettingsPage.jsx
import React, { useContext, useState, useEffect } from "react";
import { useStripe, useElements, CardElement } from "@stripe/react-stripe-js";
import { GlobalContext, showToast } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import CustomSelect2 from "Components/CustomSelect2";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import {
  getUserDetailsByIdAPI,
  updateUserDetailsAPI,
} from "Src/services/userService";
import { uploadS3FilesAPI } from "Src/services/workOrderService";
import LogoUpload from "Components/logoUpload";

const ManagerSettingsPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const stripe = useStripe();
  const elements = useElements();

  // State for member form
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phone, setPhone] = useState("");
  const [amount, setAmount] = useState(100);
  const [paymentMethod] = useState("card"); // Fixed to card only
  const [paymentError, setPaymentError] = useState(null);

  // State for tab management
  const [activeTab, setActiveTab] = useState("members");

  // State for member list
  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(false);

  // State for project settings
  const [depositPercent, setDepositPercent] = useState(50);
  const [contractAgreement, setContractAgreement] = useState("");
  // eslint-disable-next-line no-unused-vars
  const [editorInstance, setEditorInstance] = useState(null);
  const [surveySettings, setSurveySettings] = useState({
    weeks: 8,
    day: "Monday",
  });
  const [routineSettings, setRoutineSettings] = useState({
    weeks: 1,
    day: "Monday",
  });
  const [deliverySettings, setDeliverySettings] = useState({
    weeks: 1,
    day: "Friday",
  });

  // State for general settings
  const [companyName, setCompanyName] = useState("");
  const [companyAddress, setCompanyAddress] = useState("");
  const [companyPhone, setCompanyPhone] = useState("");
  const [officeEmail, setOfficeEmail] = useState("");
  const [companyLogoUrl, setCompanyLogoUrl] = useState("");
  const [licenseLogoUrl, setLicenseLogoUrl] = useState("");

  // SunEditor button list
  const buttonList = {
    complex: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["removeFormat"],
      ["fontColor", "hiliteColor"],
      ["indent", "outdent"],
      ["align", "horizontalRule", "list", "table"],
      ["link"],
      ["fullScreen", "showBlocks", "codeView"],
      ["preview"],
    ],
  };

  // Get SunEditor instance
  const getSunEditorInstance = (sunEditor) => {
    setEditorInstance(sunEditor);
  };

  // Update project settings
  const updateProjectSettings = async () => {
    try {
      const userId = localStorage.getItem("user");

      const payload = {
        id: parseInt(userId),
        deposit_percent: depositPercent,
        contract_agreement: contractAgreement,
        survey: JSON.stringify(surveySettings),
        routine_submission_date: JSON.stringify(routineSettings),
        estimated_delivery: JSON.stringify(deliverySettings),
        company_logo: companyLogoUrl,
        license_company_logo: licenseLogoUrl,
        office_email: officeEmail,
      };

      const res = await updateUserDetailsAPI(payload);

      if (!res.error) {
        showToast(
          globalDispatch,
          "Project settings updated successfully",
          4000
        );
      } else {
        showToast(
          globalDispatch,
          res.message || "Failed to update project settings",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error updating project settings:", error);
      showToast(
        globalDispatch,
        "Failed to update project settings",
        4000,
        "error"
      );
    }
  };

  // Fetch project settings
  const fetchProjectSettings = async () => {
    try {
      const userId = localStorage.getItem("user");

      const response = await getUserDetailsByIdAPI(userId);

      if (!response.error && response.model) {
        // Set deposit percentage
        setDepositPercent(response.model.deposit_percent || 50);

        // Set contract agreement
        setContractAgreement(response.model.contract_agreement || "");

        // Set general settings
        setCompanyName(response.model.company_name || "");
        setCompanyAddress(response.model.company_address || "");
        setCompanyPhone(response.model.phone || "");
        setOfficeEmail(response.model.office_email || "");
        setCompanyLogoUrl(response.model.company_logo || "");
        setLicenseLogoUrl(response.model.license_company_logo || "");

        // Parse JSON strings if they exist, otherwise use defaults
        const surveyData = response.model.survey
          ? response.model.survey
          : { weeks: 8, day: "Monday" };
        const routineData = response.model.routine_submission_date
          ? response.model.routine_submission_date
          : { weeks: 1, day: "Monday" };
        const deliveryData = response.model.estimated_delivery
          ? response.model.estimated_delivery
          : { weeks: 1, day: "Friday" };

        console.log(
          surveyData,
          routineData,
          deliveryData,
          response.model.estimated_delivery
        );

        setSurveySettings(surveyData);
        setRoutineSettings(routineData);
        setDeliverySettings(deliveryData);
      }
    } catch (error) {
      console.error("Error fetching project settings:", error);
    }
  };

  // Handle company logo upload
  const handleCompanyLogoUpload = async (formData) => {
    try {
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        let attachmentsArr = JSON.parse(result.attachments);
        setCompanyLogoUrl(attachmentsArr[0]);
        showToast(globalDispatch, "Company logo uploaded successfully", 4000);
      } else {
        showToast(
          globalDispatch,
          "Failed to upload company logo",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error uploading company logo:", error);
      showToast(globalDispatch, "Failed to upload company logo", 4000, "error");
    }
  };

  // Handle license company logo upload
  const handleLicenseLogoUpload = async (formData) => {
    try {
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        let attachmentsArr = JSON.parse(result.attachments);
        setLicenseLogoUrl(attachmentsArr[0]);
        showToast(
          globalDispatch,
          "License company logo uploaded successfully",
          4000
        );
      } else {
        showToast(
          globalDispatch,
          "Failed to upload license company logo",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error uploading license company logo:", error);
      showToast(
        globalDispatch,
        "Failed to upload license company logo",
        4000,
        "error"
      );
    }
  };

  // Update general settings
  const updateGeneralSettings = async () => {
    try {
      const userId = localStorage.getItem("user");

      const payload = {
        id: parseInt(userId),
        company_name: companyName,
        company_address: companyAddress,
        phone: companyPhone,
        office_email: officeEmail,
        company_logo: companyLogoUrl,
        license_company_logo: licenseLogoUrl,
      };

      const res = await updateUserDetailsAPI(payload);

      if (!res.error) {
        showToast(
          globalDispatch,
          "General settings updated successfully",
          4000
        );
      } else {
        showToast(
          globalDispatch,
          res.message || "Failed to update general settings",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error updating general settings:", error);
      showToast(
        globalDispatch,
        "Failed to update general settings",
        4000,
        "error"
      );
    }
  };

  // Fetch members on component mount
  useEffect(() => {
    fetchMembers();
    fetchProjectSettings();

    // Set the path in global context
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "manager-settings",
      },
    });
  }, [globalDispatch]);

  // Function to fetch members
  const fetchMembers = async () => {
    setLoading(true);
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      if (!response.error && response.company && response.company.members) {
        // The API returns members in company.members array
        setMembers(response.company.members);
      } else {
        console.error(
          "Error fetching members:",
          response.message || "No members found in response"
        );
      }
    } catch (error) {
      console.error("Error fetching members:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddMember = async () => {
    if (!stripe || !elements) {
      setPaymentError("Stripe has not been initialized");
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setPaymentError("Card element not found");
      return;
    }

    try {
      const sdk = new MkdSDK();

      // Step 1: Create member with payment info for card payment
      const createMemberResponse = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/member/create",
        {
          email,
          first_name: firstName,
          last_name: lastName,
          phone,
          payment_info: {
            amount: amount,
            method: "card",
          },
        },
        "POST"
      );

      console.log("Create member response:", createMemberResponse);

      if (createMemberResponse.error) {
        throw new Error(
          createMemberResponse.message || "Failed to create member"
        );
      }

      // Check if we have the required client_secret and payment_id
      if (!createMemberResponse.client_secret) {
        throw new Error("Missing client_secret in the response");
      }

      // if (!createMemberResponse.payment_id) {
      //   throw new Error("Missing payment_id in the response");
      // }

      const client_secret = createMemberResponse.client_secret;
      // const payment_id = createMemberResponse?.payment_id; // Unused
      const payment_intent_id = createMemberResponse?.payment_intent_id;
      // const user_id = createMemberResponse.user_id; // Unused

      // if (!user_id) {
      //   throw new Error("Missing user ID in the response");
      // }

      console.log("Confirming payment with client_secret:", client_secret);

      // Step 2: Confirm card payment with Stripe
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        client_secret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              email,
              name: `${firstName} ${lastName}`,
              phone,
            },
          },
        }
      );

      if (error) {
        console.error("Stripe error:", error);
        setPaymentError(error.message);
        return;
      }

      if (!paymentIntent || paymentIntent.status !== "succeeded") {
        throw new Error("Payment confirmation failed");
      }

      // Step 3: Confirm payment with backend
      const confirmResponse = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/user/company/member/payment/confirm/${payment_intent_id}`,
        [],
        "POST"
      );

      if (confirmResponse.error) {
        throw new Error(confirmResponse.message || "Failed to confirm payment");
      }

      showToast(globalDispatch, "Member added successfully", 4000);

      // Reset form fields
      setEmail("");
      setFirstName("");
      setLastName("");
      setPhone("");
      setAmount(100);
      setPaymentError(null);

      // Refresh member list
      fetchMembers();
    } catch (error) {
      console.error("Error adding member:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to add member",
        4000,
        "error"
      );
    }
  };

  return (
    <div className="max-w-screen mx-auto p-4 md:p-6 2xl:p-10">
      <div className="mb-6">
        <h2 className="text-title-md2 font-semibold text-white">
          Manager Settings
        </h2>
      </div>

      <div className="rounded border border-strokedark bg-boxdark">
        {/* Tab Navigation */}
        <div className="flex flex-wrap border-b border-strokedark">
          <button
            onClick={() => setActiveTab("members")}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === "members"
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Members
          </button>

          <button
            onClick={() => setActiveTab("projects")}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === "projects"
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Projects
          </button>

          <button
            onClick={() => setActiveTab("settings")}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === "settings"
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            General Settings
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === "members" && (
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white">
                Add New Member
              </h3>
              <p className="mt-1 text-sm text-gray-400">
                Add a new member to your company with payment information
              </p>
            </div>

            <form className="max-w-2xl space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-white">Email</label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter email"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-white">First Name</label>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-white">Last Name</label>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter last name"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-white">Phone</label>
                  <input
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-white">
                    Payment Amount
                  </label>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter payment amount"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-white">
                    Payment Method
                  </label>
                  <input
                    type="text"
                    value="card"
                    disabled
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white opacity-70"
                  />
                </div>
              </div>

              {paymentMethod === "card" && (
                <div>
                  <label className="mb-2 block text-white">Card Details</label>
                  <div className="rounded border border-stroke bg-transparent p-4">
                    <CardElement
                      options={{
                        style: {
                          base: {
                            fontSize: "16px",
                            color: "#ffffff",
                            "::placeholder": {
                              color: "#aab7c4",
                            },
                          },
                          invalid: {
                            color: "#dc3545",
                          },
                        },
                      }}
                    />
                  </div>
                  {paymentError && (
                    <p className="mt-2 text-sm text-danger">{paymentError}</p>
                  )}
                </div>
              )}

              <button
                type="button"
                onClick={handleAddMember}
                className="rounded-md bg-primary px-6 py-2 text-white hover:bg-opacity-90"
              >
                Add Member
              </button>
            </form>

            <div className="mt-10">
              <h3 className="mb-4 text-xl font-semibold text-white">
                Member List
              </h3>

              {loading ? (
                <p className="text-white">Loading members...</p>
              ) : members.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full table-auto">
                    <thead>
                      <tr className="bg-meta-4">
                        <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                          Name
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                          Email
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                          Phone
                        </th>
                        {/* <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                          Status
                        </th> */}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-strokedark">
                      {members.map((member, index) => (
                        <tr key={index} className="hover:bg-meta-4/30">
                          <td className="whitespace-nowrap px-4 py-3 text-sm text-white">
                            {member.first_name} {member.last_name}
                          </td>
                          <td className="whitespace-nowrap px-4 py-3 text-sm text-white">
                            {member.email}
                          </td>
                          <td className="whitespace-nowrap px-4 py-3 text-sm text-white">
                            {member.phone || "N/A"}
                          </td>
                          {/* <td className="px-4 py-3 text-sm whitespace-nowrap">
                            <span
                              className={`rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                member.status === 1
                                  ? "bg-success/10 text-success"
                                  : "bg-danger/10 text-danger"
                              }`}
                            >
                              {member.status === 1 ? "Active" : "Inactive"}
                            </span>
                          </td> */}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-white">No members found.</p>
              )}
            </div>
          </div>
        )}

        {activeTab === "projects" && (
          <div className="p-6">
            <div className="mb-6">
              <h4 className="text-xl font-semibold text-white">
                Project Settings
              </h4>
              <p className="mt-1 text-sm text-gray-400">
                Set default values for new projects and invoices
              </p>
            </div>

            <div className="max-w-2xl space-y-6">
              <div className="w-full">
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Default Deposit Percentage
                </label>
                <div className="flex w-full max-w-[200px] items-center">
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={depositPercent}
                    onChange={(e) => setDepositPercent(Number(e.target.value))}
                    className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                  />
                  <span className="ml-2 text-white">%</span>
                </div>
              </div>

              <div className="w-full">
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Contract Agreement
                </label>
                <div className="flex flex-col gap-4">
                  <SunEditor
                    setContents={contractAgreement}
                    onChange={(content) => setContractAgreement(content)}
                    getSunEditorInstance={getSunEditorInstance}
                    setOptions={{
                      buttonList: buttonList.complex,
                      height: 200,
                      width: "100%",
                    }}
                  />
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Survey Timing
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="1"
                        value={surveySettings.weeks}
                        onChange={(e) =>
                          setSurveySettings((prev) => ({
                            ...prev,
                            weeks: Number(e.target.value),
                          }))
                        }
                        className="w-20 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                      />
                      <span className="ml-2 text-white">weeks before</span>
                    </div>
                    <CustomSelect2
                      value={surveySettings.day}
                      onChange={(value) =>
                        setSurveySettings((prev) => ({ ...prev, day: value }))
                      }
                      className="w-32 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-primary focus:outline-none"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </CustomSelect2>
                  </div>
                </div>

                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Routine Submission
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="1"
                        value={routineSettings.weeks}
                        onChange={(e) =>
                          setRoutineSettings((prev) => ({
                            ...prev,
                            weeks: Number(e.target.value),
                          }))
                        }
                        className="w-20 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                      />
                      <span className="ml-2 text-white">weeks before</span>
                    </div>
                    <CustomSelect2
                      value={routineSettings.day}
                      onChange={(value) =>
                        setRoutineSettings((prev) => ({ ...prev, day: value }))
                      }
                      className="w-32 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-primary focus:outline-none"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </CustomSelect2>
                  </div>
                </div>

                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Estimated Delivery
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="1"
                        value={deliverySettings.weeks}
                        onChange={(e) =>
                          setDeliverySettings((prev) => ({
                            ...prev,
                            weeks: Number(e.target.value),
                          }))
                        }
                        className="w-20 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                      />
                      <span className="ml-2 text-white">weeks after</span>
                    </div>
                    <CustomSelect2
                      value={deliverySettings.day}
                      onChange={(value) =>
                        setDeliverySettings((prev) => ({ ...prev, day: value }))
                      }
                      className="w-32 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-primary focus:outline-none"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </CustomSelect2>
                  </div>
                </div>
              </div>

              <button
                onClick={updateProjectSettings}
                className="mt-6 inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
              >
                Save Project Settings
              </button>
            </div>
          </div>
        )}

        {activeTab === "settings" && (
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white">
                General Settings
              </h3>
              <p className="mt-1 text-sm text-gray-400">
                Configure general settings for your manager account
              </p>
            </div>

            <div className="max-w-2xl space-y-6">
              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Company Name
                </label>
                <input
                  type="text"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Company Address
                </label>
                <input
                  type="text"
                  value={companyAddress}
                  onChange={(e) => setCompanyAddress(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={companyPhone}
                  onChange={(e) => setCompanyPhone(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Office Email
                </label>
                <input
                  type="email"
                  value={officeEmail}
                  onChange={(e) => setOfficeEmail(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Company Logo
                  </label>
                  {companyLogoUrl && (
                    <div className="mb-4">
                      <img
                        src={companyLogoUrl}
                        alt="Company Logo"
                        className="h-24 w-auto object-contain"
                      />
                    </div>
                  )}
                  <LogoUpload
                    maxFileSize={2}
                    setFileUpload={handleCompanyLogoUpload}
                    transparent={true}
                  />
                </div>

                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    License Company Logo
                  </label>
                  {licenseLogoUrl && (
                    <div className="mb-4">
                      <img
                        src={licenseLogoUrl}
                        alt="License Company Logo"
                        className="h-24 w-auto object-contain"
                      />
                    </div>
                  )}
                  <LogoUpload
                    maxFileSize={2}
                    setFileUpload={handleLicenseLogoUpload}
                    transparent={true}
                  />
                </div>
              </div>

              <button
                onClick={updateGeneralSettings}
                className="mt-6 inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
              >
                Save General Settings
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManagerSettingsPage;
