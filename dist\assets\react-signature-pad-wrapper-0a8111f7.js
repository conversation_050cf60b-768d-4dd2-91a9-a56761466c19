import{r as b}from"./vendor-3aca5368.js";import{p as w}from"./@fortawesome/react-fontawesome-8ac6acae.js";/*!
 * Signature Pad v5.0.4 | https://github.com/szimek/signature_pad
 * (c) 2024 <PERSON><PERSON><PERSON> | Released under the MIT license
 */class y{constructor(t,e,i,n){if(isNaN(t)||isNaN(e))throw new Error(`Point is invalid: (${t}, ${e})`);this.x=+t,this.y=+e,this.pressure=i||0,this.time=n||Date.now()}distanceTo(t){return Math.sqrt(Math.pow(this.x-t.x,2)+Math.pow(this.y-t.y,2))}equals(t){return this.x===t.x&&this.y===t.y&&this.pressure===t.pressure&&this.time===t.time}velocityFrom(t){return this.time!==t.time?this.distanceTo(t)/(this.time-t.time):0}}class W{static fromPoints(t,e){const i=this.calculateControlPoints(t[0],t[1],t[2]).c2,n=this.calculateControlPoints(t[1],t[2],t[3]).c1;return new W(t[1],i,n,t[2],e.start,e.end)}static calculateControlPoints(t,e,i){const n=t.x-e.x,o=t.y-e.y,s=e.x-i.x,h=e.y-i.y,a={x:(t.x+e.x)/2,y:(t.y+e.y)/2},r={x:(e.x+i.x)/2,y:(e.y+i.y)/2},c=Math.sqrt(n*n+o*o),d=Math.sqrt(s*s+h*h),g=a.x-r.x,f=a.y-r.y,l=c+d==0?0:d/(c+d),m={x:r.x+g*l,y:r.y+f*l},p=e.x-m.x,_=e.y-m.y;return{c1:new y(a.x+p,a.y+_),c2:new y(r.x+p,r.y+_)}}constructor(t,e,i,n,o,s){this.startPoint=t,this.control2=e,this.control1=i,this.endPoint=n,this.startWidth=o,this.endWidth=s}length(){let e=0,i,n;for(let o=0;o<=10;o+=1){const s=o/10,h=this.point(s,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),a=this.point(s,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(o>0){const r=h-i,c=a-n;e+=Math.sqrt(r*r+c*c)}i=h,n=a}return e}point(t,e,i,n,o){return e*(1-t)*(1-t)*(1-t)+3*i*(1-t)*(1-t)*t+3*n*(1-t)*t*t+o*t*t*t}}class T{constructor(){try{this._et=new EventTarget}catch{this._et=document}}addEventListener(t,e,i){this._et.addEventListener(t,e,i)}dispatchEvent(t){return this._et.dispatchEvent(t)}removeEventListener(t,e,i){this._et.removeEventListener(t,e,i)}}function D(u,t=250){let e=0,i=null,n,o,s;const h=()=>{e=Date.now(),i=null,n=u.apply(o,s),i||(o=null,s=[])};return function(...r){const c=Date.now(),d=t-(c-e);return o=this,s=r,d<=0||d>t?(i&&(clearTimeout(i),i=null),e=c,n=u.apply(o,s),i||(o=null,s=[])):i||(i=window.setTimeout(h,d)),n}}class P extends T{constructor(t,e={}){var i,n,o;super(),this.canvas=t,this._drawingStroke=!1,this._isEmpty=!0,this._lastPoints=[],this._data=[],this._lastVelocity=0,this._lastWidth=0,this._handleMouseDown=s=>{!this._isLeftButtonPressed(s,!0)||this._drawingStroke||this._strokeBegin(this._pointerEventToSignatureEvent(s))},this._handleMouseMove=s=>{if(!this._isLeftButtonPressed(s,!0)||!this._drawingStroke){this._strokeEnd(this._pointerEventToSignatureEvent(s),!1);return}this._strokeMoveUpdate(this._pointerEventToSignatureEvent(s))},this._handleMouseUp=s=>{this._isLeftButtonPressed(s)||this._strokeEnd(this._pointerEventToSignatureEvent(s))},this._handleTouchStart=s=>{s.targetTouches.length!==1||this._drawingStroke||(s.cancelable&&s.preventDefault(),this._strokeBegin(this._touchEventToSignatureEvent(s)))},this._handleTouchMove=s=>{if(s.targetTouches.length===1){if(s.cancelable&&s.preventDefault(),!this._drawingStroke){this._strokeEnd(this._touchEventToSignatureEvent(s),!1);return}this._strokeMoveUpdate(this._touchEventToSignatureEvent(s))}},this._handleTouchEnd=s=>{s.targetTouches.length===0&&(s.cancelable&&s.preventDefault(),this.canvas.removeEventListener("touchmove",this._handleTouchMove),this._strokeEnd(this._touchEventToSignatureEvent(s)))},this._handlePointerDown=s=>{!s.isPrimary||!this._isLeftButtonPressed(s)||this._drawingStroke||(s.preventDefault(),this._strokeBegin(this._pointerEventToSignatureEvent(s)))},this._handlePointerMove=s=>{if(s.isPrimary){if(!this._isLeftButtonPressed(s,!0)||!this._drawingStroke){this._strokeEnd(this._pointerEventToSignatureEvent(s),!1);return}s.preventDefault(),this._strokeMoveUpdate(this._pointerEventToSignatureEvent(s))}},this._handlePointerUp=s=>{!s.isPrimary||this._isLeftButtonPressed(s)||(s.preventDefault(),this._strokeEnd(this._pointerEventToSignatureEvent(s)))},this.velocityFilterWeight=e.velocityFilterWeight||.7,this.minWidth=e.minWidth||.5,this.maxWidth=e.maxWidth||2.5,this.throttle=(i=e.throttle)!==null&&i!==void 0?i:16,this.minDistance=(n=e.minDistance)!==null&&n!==void 0?n:5,this.dotSize=e.dotSize||0,this.penColor=e.penColor||"black",this.backgroundColor=e.backgroundColor||"rgba(0,0,0,0)",this.compositeOperation=e.compositeOperation||"source-over",this.canvasContextOptions=(o=e.canvasContextOptions)!==null&&o!==void 0?o:{},this._strokeMoveUpdate=this.throttle?D(P.prototype._strokeUpdate,this.throttle):P.prototype._strokeUpdate,this._ctx=t.getContext("2d",this.canvasContextOptions),this.clear(),this.on()}clear(){const{_ctx:t,canvas:e}=this;t.fillStyle=this.backgroundColor,t.clearRect(0,0,e.width,e.height),t.fillRect(0,0,e.width,e.height),this._data=[],this._reset(this._getPointGroupOptions()),this._isEmpty=!0}fromDataURL(t,e={}){return new Promise((i,n)=>{const o=new Image,s=e.ratio||window.devicePixelRatio||1,h=e.width||this.canvas.width/s,a=e.height||this.canvas.height/s,r=e.xOffset||0,c=e.yOffset||0;this._reset(this._getPointGroupOptions()),o.onload=()=>{this._ctx.drawImage(o,r,c,h,a),i()},o.onerror=d=>{n(d)},o.crossOrigin="anonymous",o.src=t,this._isEmpty=!1})}toDataURL(t="image/png",e){switch(t){case"image/svg+xml":return typeof e!="object"&&(e=void 0),`data:image/svg+xml;base64,${btoa(this.toSVG(e))}`;default:return typeof e!="number"&&(e=void 0),this.canvas.toDataURL(t,e)}}on(){this.canvas.style.touchAction="none",this.canvas.style.msTouchAction="none",this.canvas.style.userSelect="none";const t=/Macintosh/.test(navigator.userAgent)&&"ontouchstart"in document;window.PointerEvent&&!t?this._handlePointerEvents():(this._handleMouseEvents(),"ontouchstart"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction="auto",this.canvas.style.msTouchAction="auto",this.canvas.style.userSelect="auto",this.canvas.removeEventListener("pointerdown",this._handlePointerDown),this.canvas.removeEventListener("mousedown",this._handleMouseDown),this.canvas.removeEventListener("touchstart",this._handleTouchStart),this._removeMoveUpEventListeners()}_getListenerFunctions(){var t;const e=window.document===this.canvas.ownerDocument?window:(t=this.canvas.ownerDocument.defaultView)!==null&&t!==void 0?t:this.canvas.ownerDocument;return{addEventListener:e.addEventListener.bind(e),removeEventListener:e.removeEventListener.bind(e)}}_removeMoveUpEventListeners(){const{removeEventListener:t}=this._getListenerFunctions();t("pointermove",this._handlePointerMove),t("pointerup",this._handlePointerUp),t("mousemove",this._handleMouseMove),t("mouseup",this._handleMouseUp),t("touchmove",this._handleTouchMove),t("touchend",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(t,{clear:e=!0}={}){e&&this.clear(),this._fromData(t,this._drawCurve.bind(this),this._drawDot.bind(this)),this._data=this._data.concat(t)}toData(){return this._data}_isLeftButtonPressed(t,e){return e?t.buttons===1:(t.buttons&1)===1}_pointerEventToSignatureEvent(t){return{event:t,type:t.type,x:t.clientX,y:t.clientY,pressure:"pressure"in t?t.pressure:0}}_touchEventToSignatureEvent(t){const e=t.changedTouches[0];return{event:t,type:t.type,x:e.clientX,y:e.clientY,pressure:e.force}}_getPointGroupOptions(t){return{penColor:t&&"penColor"in t?t.penColor:this.penColor,dotSize:t&&"dotSize"in t?t.dotSize:this.dotSize,minWidth:t&&"minWidth"in t?t.minWidth:this.minWidth,maxWidth:t&&"maxWidth"in t?t.maxWidth:this.maxWidth,velocityFilterWeight:t&&"velocityFilterWeight"in t?t.velocityFilterWeight:this.velocityFilterWeight,compositeOperation:t&&"compositeOperation"in t?t.compositeOperation:this.compositeOperation}}_strokeBegin(t){if(!this.dispatchEvent(new CustomEvent("beginStroke",{detail:t,cancelable:!0})))return;const{addEventListener:i}=this._getListenerFunctions();switch(t.event.type){case"mousedown":i("mousemove",this._handleMouseMove),i("mouseup",this._handleMouseUp);break;case"touchstart":i("touchmove",this._handleTouchMove),i("touchend",this._handleTouchEnd);break;case"pointerdown":i("pointermove",this._handlePointerMove),i("pointerup",this._handlePointerUp);break}this._drawingStroke=!0;const n=this._getPointGroupOptions(),o=Object.assign(Object.assign({},n),{points:[]});this._data.push(o),this._reset(n),this._strokeUpdate(t)}_strokeUpdate(t){if(!this._drawingStroke)return;if(this._data.length===0){this._strokeBegin(t);return}this.dispatchEvent(new CustomEvent("beforeUpdateStroke",{detail:t}));const e=this._createPoint(t.x,t.y,t.pressure),i=this._data[this._data.length-1],n=i.points,o=n.length>0&&n[n.length-1],s=o?e.distanceTo(o)<=this.minDistance:!1,h=this._getPointGroupOptions(i);if(!o||!(o&&s)){const a=this._addPoint(e,h);o?a&&this._drawCurve(a,h):this._drawDot(e,h),n.push({time:e.time,x:e.x,y:e.y,pressure:e.pressure})}this.dispatchEvent(new CustomEvent("afterUpdateStroke",{detail:t}))}_strokeEnd(t,e=!0){this._removeMoveUpEventListeners(),this._drawingStroke&&(e&&this._strokeUpdate(t),this._drawingStroke=!1,this.dispatchEvent(new CustomEvent("endStroke",{detail:t})))}_handlePointerEvents(){this._drawingStroke=!1,this.canvas.addEventListener("pointerdown",this._handlePointerDown)}_handleMouseEvents(){this._drawingStroke=!1,this.canvas.addEventListener("mousedown",this._handleMouseDown)}_handleTouchEvents(){this.canvas.addEventListener("touchstart",this._handleTouchStart)}_reset(t){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(t.minWidth+t.maxWidth)/2,this._ctx.fillStyle=t.penColor,this._ctx.globalCompositeOperation=t.compositeOperation}_createPoint(t,e,i){const n=this.canvas.getBoundingClientRect();return new y(t-n.left,e-n.top,i,new Date().getTime())}_addPoint(t,e){const{_lastPoints:i}=this;if(i.push(t),i.length>2){i.length===3&&i.unshift(i[0]);const n=this._calculateCurveWidths(i[1],i[2],e),o=W.fromPoints(i,n);return i.shift(),o}return null}_calculateCurveWidths(t,e,i){const n=i.velocityFilterWeight*e.velocityFrom(t)+(1-i.velocityFilterWeight)*this._lastVelocity,o=this._strokeWidth(n,i),s={end:o,start:this._lastWidth};return this._lastVelocity=n,this._lastWidth=o,s}_strokeWidth(t,e){return Math.max(e.maxWidth/(t+1),e.minWidth)}_drawCurveSegment(t,e,i){const n=this._ctx;n.moveTo(t,e),n.arc(t,e,i,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve(t,e){const i=this._ctx,n=t.endWidth-t.startWidth,o=Math.ceil(t.length())*2;i.beginPath(),i.fillStyle=e.penColor;for(let s=0;s<o;s+=1){const h=s/o,a=h*h,r=a*h,c=1-h,d=c*c,g=d*c;let f=g*t.startPoint.x;f+=3*d*h*t.control1.x,f+=3*c*a*t.control2.x,f+=r*t.endPoint.x;let l=g*t.startPoint.y;l+=3*d*h*t.control1.y,l+=3*c*a*t.control2.y,l+=r*t.endPoint.y;const m=Math.min(t.startWidth+r*n,e.maxWidth);this._drawCurveSegment(f,l,m)}i.closePath(),i.fill()}_drawDot(t,e){const i=this._ctx,n=e.dotSize>0?e.dotSize:(e.minWidth+e.maxWidth)/2;i.beginPath(),this._drawCurveSegment(t.x,t.y,n),i.closePath(),i.fillStyle=e.penColor,i.fill()}_fromData(t,e,i){for(const n of t){const{points:o}=n,s=this._getPointGroupOptions(n);if(o.length>1)for(let h=0;h<o.length;h+=1){const a=o[h],r=new y(a.x,a.y,a.pressure,a.time);h===0&&this._reset(s);const c=this._addPoint(r,s);c&&e(c,s)}else this._reset(s),i(o[0],s)}}toSVG({includeBackgroundColor:t=!1}={}){const e=this._data,i=Math.max(window.devicePixelRatio||1,1),n=0,o=0,s=this.canvas.width/i,h=this.canvas.height/i,a=document.createElementNS("http://www.w3.org/2000/svg","svg");if(a.setAttribute("xmlns","http://www.w3.org/2000/svg"),a.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),a.setAttribute("viewBox",`${n} ${o} ${s} ${h}`),a.setAttribute("width",s.toString()),a.setAttribute("height",h.toString()),t&&this.backgroundColor){const r=document.createElement("rect");r.setAttribute("width","100%"),r.setAttribute("height","100%"),r.setAttribute("fill",this.backgroundColor),a.appendChild(r)}return this._fromData(e,(r,{penColor:c})=>{const d=document.createElement("path");if(!isNaN(r.control1.x)&&!isNaN(r.control1.y)&&!isNaN(r.control2.x)&&!isNaN(r.control2.y)){const g=`M ${r.startPoint.x.toFixed(3)},${r.startPoint.y.toFixed(3)} C ${r.control1.x.toFixed(3)},${r.control1.y.toFixed(3)} ${r.control2.x.toFixed(3)},${r.control2.y.toFixed(3)} ${r.endPoint.x.toFixed(3)},${r.endPoint.y.toFixed(3)}`;d.setAttribute("d",g),d.setAttribute("stroke-width",(r.endWidth*2.25).toFixed(3)),d.setAttribute("stroke",c),d.setAttribute("fill","none"),d.setAttribute("stroke-linecap","round"),a.appendChild(d)}},(r,{penColor:c,dotSize:d,minWidth:g,maxWidth:f})=>{const l=document.createElement("circle"),m=d>0?d:(g+f)/2;l.setAttribute("r",m.toString()),l.setAttribute("cx",r.x.toString()),l.setAttribute("cy",r.y.toString()),l.setAttribute("fill",c),a.appendChild(l)}),a.outerHTML}}function M(u,t,e){var i=e||{},n=i.noTrailing,o=n===void 0?!1:n,s=i.noLeading,h=s===void 0?!1:s,a=i.debounceMode,r=a===void 0?void 0:a,c,d=!1,g=0;function f(){c&&clearTimeout(c)}function l(p){var _=p||{},v=_.upcomingOnly,E=v===void 0?!1:v;f(),d=!E}function m(){for(var p=arguments.length,_=new Array(p),v=0;v<p;v++)_[v]=arguments[v];var E=this,k=Date.now()-g;if(d)return;function x(){g=Date.now(),t.apply(E,_)}function C(){c=void 0}!h&&r&&!c&&x(),f(),r===void 0&&k>u?h?(g=Date.now(),o||(c=setTimeout(r?C:x,u))):x():o!==!0&&(c=setTimeout(r?C:x,r===void 0?u-k:u))}return m.cancel=l,m}function L(u,t,e){var i=e||{},n=i.atBegin,o=n===void 0?!1:n;return M(u,t,{debounceMode:o!==!1})}class S extends b.PureComponent{constructor(t){super(t),Object.defineProperty(this,"canvasRef",{enumerable:!0,configurable:!0,writable:!0,value:b.createRef()}),Object.defineProperty(this,"signaturePad",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"callResizeHandler",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.state={canvasWidth:0,canvasHeight:0},this.callResizeHandler=L(this.props.debounceInterval,this.handleResize.bind(this))}componentDidMount(){const t=this.canvasRef.current;t&&(this.props.width&&this.props.height||(t.style.width="100%",window.addEventListener("resize",this.callResizeHandler)),this.signaturePad=new P(t,this.props.options),this.scaleCanvas(t))}componentWillUnmount(){this.props.width&&this.props.height||window.removeEventListener("resize",this.callResizeHandler),this.signaturePad.off()}get instance(){return this.signaturePad}get canvas(){return this.canvasRef}set dotSize(t){this.signaturePad.dotSize=t}get dotSize(){return this.signaturePad.dotSize}set minWidth(t){this.signaturePad.minWidth=t}get minWidth(){return this.signaturePad.minWidth}set maxWidth(t){this.signaturePad.maxWidth=t}get maxWidth(){return this.signaturePad.maxWidth}set throttle(t){this.signaturePad.throttle=t}get throttle(){return this.signaturePad.throttle}set backgroundColor(t){this.signaturePad.backgroundColor=t}get backgroundColor(){return this.signaturePad.backgroundColor}set penColor(t){this.signaturePad.penColor=t}get penColor(){return this.signaturePad.penColor}set velocityFilterWeight(t){this.signaturePad.velocityFilterWeight=t}get velocityFilterWeight(){return this.signaturePad.velocityFilterWeight}isEmpty(){return this.signaturePad.isEmpty()}clear(){this.signaturePad.clear()}fromDataURL(t,e={}){this.signaturePad.fromDataURL(t,e)}toDataURL(t,e){return this.signaturePad.toDataURL(t,e)}toSVG(t){return this.signaturePad.toSVG(t)}fromData(t){this.signaturePad.fromData(t)}toData(){return this.signaturePad.toData()}off(){this.signaturePad.off()}on(){this.signaturePad.on()}handleResize(){const t=this.canvasRef.current;t&&this.scaleCanvas(t)}scaleCanvas(t){const e=Math.max(window.devicePixelRatio||1,1),i=(this.props.width||t.offsetWidth)*e,n=(this.props.height||t.offsetHeight)*e,{canvasWidth:o,canvasHeight:s}=this.state;if(i===o&&n===s)return;let h;this.props.redrawOnResize&&this.signaturePad&&!this.signaturePad.isEmpty()&&(h=this.signaturePad.toDataURL()),t.width=i,t.height=n,this.setState({canvasWidth:i,canvasHeight:n});const a=t.getContext("2d");a&&a.scale(e,e),h?this.signaturePad.fromDataURL(h):this.signaturePad&&this.signaturePad.clear()}render(){const{canvasProps:t}=this.props;return b.createElement("canvas",Object.assign({"data-testid":"canvas-element",ref:this.canvasRef},t))}}Object.defineProperty(S,"displayName",{enumerable:!0,configurable:!0,writable:!0,value:"react-signature-pad-wrapper"}),Object.defineProperty(S,"propTypes",{enumerable:!0,configurable:!0,writable:!0,value:{width:w.number,height:w.number,options:w.object,canvasProps:w.object,redrawOnResize:w.bool.isRequired,debounceInterval:w.number.isRequired}}),Object.defineProperty(S,"defaultProps",{enumerable:!0,configurable:!0,writable:!0,value:{redrawOnResize:!1,debounceInterval:150}});export{S as r};
