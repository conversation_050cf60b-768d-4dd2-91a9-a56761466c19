import React, { memo, useState } from "react";

import { SearchableDropdown } from "Components/SearchableDropdown";
import { StringCaser } from "Utils/utils";

const FilterJoinDropdown = ({
  columnData = null,
  option = null,
  setOptionValue = null,
}) => {
  return (
    <>
      {["user"].includes(columnData?.join) ? (
        <SearchableDropdown
          table="user"
          className="flex w-full flex-col items-start "
          uniqueKey={"id"}
          displaySeparator={"-"}
          label={StringCaser(columnData?.accessor, {
            casetype: "capitalize",
            separator: " ",
          })}
          display={[columnData?.accessor]}
          placeholder={columnData?.accessor}
          filter={[`role,cs,user`, `is_company,eq,1`]}
          onSelect={(data, clear) => {
            if (clear) {
              setOptionValue("value", "", option?.uid);
            } else {
              setOptionValue("value", data?.id, option?.uid);
            }
          }}
          value={option?.value}
          // errors={errors}
          // disabled={disableCustomer}
        />
      ) : null}
      {/* {["division"].includes(columnData?.join) ? (
        <SearchableDropdown
          table="division"
          display={[columnData?.accessor]}
          displaySeparator={"-"}
          label={""}
          uniqueKey={"id"}
          className="flex flex-col items-start border border-ll"
          placeholder={columnData?.accessor}
          onSelect={(data, clear) => {
            if (clear) {
              setOptionValue("value", "", option?.uid);
            } else {
              setOptionValue("value", data?.id, option?.uid);
            }
          }}
          value={option?.value}
          // errors={errors}
          // disabled={disableCustomer}
        />
      ) : null} */}
      {/* {["campaign"].includes(columnData?.join) ? (
        <SearchableDropdown
          table="campaign"
          display={[columnData?.accessor]}
          displaySeparator={"-"}
          label={""}
          uniqueKey={"id"}
          className=""
          placeholder={columnData?.accessor}
          onSelect={(data, clear) => {
            if (clear) {
              setOptionValue("value", "", option?.uid);
            } else {
              setOptionValue("value", data?.id, option?.uid);
            }
          }}
          value={option?.value}
          // errors={errors}
          // disabled={disableCustomer}
        />
      ) : null} */}
      {[
        "warehouse",
        "warehouse_location",
        "location_type",
        "campaign",
        "division",
        "rate_card",
      ].includes(columnData?.join) ? (
        <SearchableDropdown
          className="flex w-full flex-col items-start "
          uniqueKey={"id"}
          displaySeparator={"-"}
          table={columnData?.join}
          label={StringCaser(columnData?.accessor, {
            casetype: "capitalize",
            separator: " ",
          })}
          display={[columnData?.accessor]}
          placeholder={columnData?.accessor}
          onSelect={(data, clear) => {
            if (clear) {
              setOptionValue("value", "", option?.uid);
            } else {
              setOptionValue("value", data?.id, option?.uid);
            }
          }}
          value={option?.value}
          // errors={errors}
          // disabled={disableCustomer}
        />
      ) : null}
    </>
  );
};

export default memo(FilterJoinDropdown);
