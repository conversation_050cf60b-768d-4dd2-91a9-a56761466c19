import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import CustomSelect2 from "Components/CustomSelect2";

const ModifyWriterModal = ({
  writers,
  selectedWriterId,
  setModalClose,
  setFormSubmit,
}) => {
  const [writerCost, setWriterCost] = React.useState(0);
  const [localSelectedWriterId, setLocalSelectedWriterId] =
    React.useState(selectedWriterId);

  const handleWriterChange = (value) => {
    if (value) {
      setLocalSelectedWriterId(value);
      if (writers) {
        const selectedWriter = writers.filter(
          (writer) => writer.id === Number(value)
        );
        setWriterCost(selectedWriter[0].writer_cost);
      }
    } else {
      setLocalSelectedWriterId(null);
      setWriterCost(0);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      writer_id: localSelectedWriterId,
      writer_cost: writerCost,
    };
    setFormSubmit(payload);
    setModalClose(false);
  };

  React.useEffect(() => {
    if (writers && writers.length > 0) {
      const selectedWriter = writers.filter(
        (writer) => writer.id === Number(selectedWriterId)
      );
      setWriterCost(selectedWriter[0].writer_cost);
    }
  }, [writers]);

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center">
      <div
        className="fixed inset-0 backdrop-blur-sm bg-black/50"
        onClick={() => setModalClose(false)}
      />
      <div className="w-full max-w-xl rounded border transition-all transform shadow-default border-strokedark bg-boxdark">
        {/* Modal Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-stroke">
          <div className="flex gap-3 items-center">
            <FontAwesomeIcon
              icon="fa-solid fa-pen-nib"
              className="text-xl text-primary"
            />
            <h3 className="text-xl font-medium text-white">Update Writer</h3>
          </div>
          <button
            onClick={() => setModalClose(false)}
            className="hover:text-primary"
          >
            <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
          </button>
        </div>

        <div className="px-6 py-4">
          <form className="space-y-4">
            <div className="flex flex-col">
              <label
                className="mb-2.5 block text-sm font-medium text-white"
                htmlFor="writer"
              >
                Writer
              </label>
              <CustomSelect2
                className="w-full rounded border border-form-strokedark bg-boxdark-2 px-4 py-2.5 text-sm text-bodydark2 focus:border-primary focus-visible:outline-none"
                name="writer"
                id="writer"
                value={localSelectedWriterId}
                defaultValue={selectedWriterId}
                onChange={(value) => {
                  handleWriterChange(value);
                }}
              >
                <option value="">Select Writer</option>
                {writers &&
                  writers.length > 0 &&
                  writers?.map((writer) => (
                    <option key={writer.id} value={writer.id}>
                      {writer.name}
                    </option>
                  ))}
              </CustomSelect2>
            </div>

            <div className="flex flex-col">
              <label
                className="mb-2.5 block text-sm font-medium text-white"
                htmlFor="writer_cost"
              >
                Cost (default)
              </label>
              <input
                type="number"
                id="writer_cost"
                className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-sm text-bodydark2 focus:border-primary focus-visible:outline-none"
                placeholder="Enter cost"
                value={writerCost}
                min="0"
                onChange={(e) => setWriterCost(e.target.value)}
              />
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-stroke">
          <div className="flex gap-2">
            <button
              onClick={(e) => handleSubmit(e)}
              className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-primary hover:bg-opacity-90"
            >
              Save Changes
            </button>
            <button
              onClick={() => setModalClose(false)}
              className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-danger hover:bg-opacity-90"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModifyWriterModal;
