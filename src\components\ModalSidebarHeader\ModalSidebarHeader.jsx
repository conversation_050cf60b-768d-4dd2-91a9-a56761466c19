import React, { memo } from "react";
import AddButton from "Components/AddButton";
import { LazyLoad } from "Components/LazyLoad";

const ModalSidebarHeader = ({
  onToggleModal,
  onSaveTrigger,
  cancelText = "Cancel",
}) => {
  return (
    <LazyLoad className="flex w-full justify-end gap-3">
      <div className="flex w-full justify-end gap-3">
        {onToggleModal && (
          <button type="button" onClick={onToggleModal}>
            {cancelText}
          </button>
        )}
        {onSaveTrigger && (
          <AddButton onClick={onSaveTrigger} showPlus={false}>
            Save
          </AddButton>
        )}
      </div>
    </LazyLoad>
  );
};

export default memo(ModalSidebarHeader);
