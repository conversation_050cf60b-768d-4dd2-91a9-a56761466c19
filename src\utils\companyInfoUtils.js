import MkdSDK from "./MkdSDK";

/**
 * Fetches company information and determines settings based on user role
 * @param {string} userId - Current user ID
 * @returns {Promise<Object>} - Company info with role-specific settings
 */
export const getCompanyInfoWithSettings = async (userId = null) => {
  try {
    const sdk = new MkdSDK();
    const result = await sdk.callRawAPI(
      "/v3/api/custom/equality_record/user/company/info",
      {},
      "GET"
    );

    if (result.error) {
      throw new Error(result.message || "Failed to fetch company info");
    }

    // If no userId provided, get from localStorage
    if (!userId) {
      userId = localStorage.getItem("user");
    }

    // Initialize settings object with defaults
    const settings = {
      companyLogo: null,
      companyName: null,
      estimatedCompletionDays: 14, // Default value
      defaultDepositPercentage: 50, // Default value
      termsAndConditions: "",
      isManager: false,
      managerId: null,
      members: result.members || [],
      managers: result.managers || [],
      producers: result.members?.filter(member => member.role === "producer") || [],
    };

    // Set company-wide settings
    settings.companyLogo = result.company_logo || null;
    settings.companyName = result.company_name || null;
    settings.termsAndConditions = result.terms_and_conditions || "";

    // Check if current user is a manager
    const isManager = result.managers?.some(manager => manager.id.toString() === userId.toString());
    settings.isManager = isManager;

    if (isManager) {
      // Use manager's settings
      const managerData = result.managers.find(manager => manager.id.toString() === userId.toString());
      if (managerData) {
        settings.companyLogo = managerData.company_logo || settings.companyLogo;
        settings.companyName = managerData.company_name || settings.companyName;
        settings.estimatedCompletionDays = managerData.estimated_completion_days || settings.estimatedCompletionDays;
        settings.defaultDepositPercentage = managerData.default_deposit_percentage || settings.defaultDepositPercentage;
        settings.termsAndConditions = managerData.terms_and_conditions || settings.termsAndConditions;
        settings.managerId = managerData.id;
      }
    } else {
      // Check if user has a manager
      const userMember = result.members?.find(member => member.id.toString() === userId.toString());
      if (userMember && userMember.manager_id) {
        // User has a manager, use manager's settings
        const userManager = result.managers?.find(manager => manager.id.toString() === userMember.manager_id.toString());
        if (userManager) {
          settings.companyLogo = userManager.company_logo || settings.companyLogo;
          settings.companyName = userManager.company_name || settings.companyName;
          settings.estimatedCompletionDays = userManager.estimated_completion_days || settings.estimatedCompletionDays;
          settings.defaultDepositPercentage = userManager.default_deposit_percentage || settings.defaultDepositPercentage;
          settings.termsAndConditions = userManager.terms_and_conditions || settings.termsAndConditions;
          settings.managerId = userManager.id;
        }
      } else {
        // Use main member settings (already set above)
      }
    }

    // Calculate default dates based on estimated completion days
    const today = new Date();
    
    // Music survey due (typically 2 weeks before mix date)
    const musicSurveyDue = new Date(today);
    musicSurveyDue.setDate(today.getDate() - 14);
    settings.defaultMusicSurveyDue = musicSurveyDue.toISOString().split('T')[0];
    
    // Routine submission due (typically same as mix date)
    settings.defaultRoutineSubmissionDue = today.toISOString().split('T')[0];
    
    // Estimated completion date
    const estimatedCompletion = new Date(today);
    estimatedCompletion.setDate(today.getDate() + settings.estimatedCompletionDays);
    settings.defaultEstimatedCompletion = estimatedCompletion.toISOString().split('T')[0];

    return {
      ...result,
      settings
    };
  } catch (error) {
    console.error("Error in getCompanyInfoWithSettings:", error);
    return {
      error: true,
      message: error.message || "Failed to process company information",
      settings: {
        companyLogo: null,
        companyName: null,
        estimatedCompletionDays: 14,
        defaultDepositPercentage: 50,
        termsAndConditions: "",
        isManager: false,
        managerId: null,
        members: [],
        managers: [],
        producers: []
      }
    };
  }
};

/**
 * Maps producer IDs to their names using company info
 * @param {Array} producerIds - Array of producer IDs
 * @param {Object} companyInfo - Company info object from getCompanyInfoWithSettings
 * @returns {Array} - Array of producer names
 */
export const mapProducerIdsToNames = (producerIds, companyInfo) => {
  if (!producerIds || !companyInfo || !companyInfo.members) {
    return [];
  }

  return producerIds.map(id => {
    const producer = companyInfo.members.find(member => member.id === parseInt(id));
    return producer ? producer.name : "Unknown";
  });
};

/**
 * Gets mix seasons and types for a specific producer
 * @param {number} producerId - Producer ID
 * @param {Object} companyInfo - Company info object from getCompanyInfoWithSettings
 * @returns {Object} - Object containing mix seasons and types for the producer
 */
export const getProducerMixOptions = (producerId, companyInfo) => {
  if (!producerId || !companyInfo || !companyInfo.members) {
    return { mixSeasons: [], mixTypes: [] };
  }

  const producer = companyInfo.members.find(member => member.id === parseInt(producerId));
  
  if (!producer) {
    return { mixSeasons: [], mixTypes: [] };
  }

  return {
    mixSeasons: producer.mix_seasons || [],
    mixTypes: producer.mix_types || []
  };
};

export default {
  getCompanyInfoWithSettings,
  mapProducerIdsToNames,
  getProducerMixOptions
};
