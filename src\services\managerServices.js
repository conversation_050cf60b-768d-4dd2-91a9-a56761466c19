import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const retrieveAllProjectManagerAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/project/manager/retrieve_all_multi_filter`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllForProjectCalendarForManager = async (
  page,
  limit,
  filter
) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/project/calendar/manager/retrieve_all`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllForClientForManager = async (page, limit, filter) => {
  try {
    const payload = {
      page,
      limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/client/manager/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllForMixTypeForManager = async (page, limit, filter) => {
  try {
    const payload = {
      page,
      limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/mix_type/manager/retrieve_all_multi_filter`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllForMixSeasonForManager = async (
  page,
  limit,
  filter
) => {
  try {
    const payload = {
      page,
      limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/mix_season/manager/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllMembersForManager = async (id) => {
  try {
    // const payload = {
    //   page: page,
    //   limit: limit,
    //   filter: filter,
    // };
    const uri = `/v3/api/custom/equality_record/user/manager/all_members/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllMembersForManagerPortal = async (id) => {
  try {
    // const payload = {
    //   page: page,
    //   limit: limit,
    //   filter: filter,
    // };
    const uri = `/v3/api/custom/equality_record/manager_permission/retrieve_all`;
    const res = await sdk.callRawAPI(uri, [], 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllPermissionsForManager = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/manager_permission/retrieve_all`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getOnePermissionsForManager = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/manager_permission/view_admin/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};
