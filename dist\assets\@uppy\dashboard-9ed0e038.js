import{g as Is,a as Es,p as Ot,U as lt,b as As,i as ks}from"./core-5d4a6f29.js";import{y as o,Z as it,_ as re,$ as ve,a0 as be,a1 as st,a2 as Oi,a3 as te,d as Ds,a4 as Ni,a5 as ct}from"../@fullcalendar/core-ff88745d.js";import{g as ut}from"../vendor-3aca5368.js";import{n as zt,r as Bs}from"./aws-s3-5653d8cf.js";var Gt=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function Os(i,e){return!!(i===e||Gt(i)&&Gt(e))}function Ns(i,e){if(i.length!==e.length)return!1;for(var t=0;t<i.length;t++)if(!Os(i[t],e[t]))return!1;return!0}function Qt(i,e){e===void 0&&(e=Ns);var t=null;function s(){for(var n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];if(t&&t.lastThis===this&&e(n,t.lastArgs))return t.lastResult;var r=i.apply(this,n);return t={lastResult:r,lastArgs:n,lastThis:this},r}return s.clear=function(){t=null},s}var _i={exports:{}};(function(i){var e=Object.prototype.hasOwnProperty,t="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(t=!1));function n(u,d,h){this.fn=u,this.context=d,this.once=h||!1}function a(u,d,h,c,p){if(typeof h!="function")throw new TypeError("The listener must be a function");var f=new n(h,c||u,p),m=t?t+d:d;return u._events[m]?u._events[m].fn?u._events[m]=[u._events[m],f]:u._events[m].push(f):(u._events[m]=f,u._eventsCount++),u}function r(u,d){--u._eventsCount===0?u._events=new s:delete u._events[d]}function l(){this._events=new s,this._eventsCount=0}l.prototype.eventNames=function(){var d=[],h,c;if(this._eventsCount===0)return d;for(c in h=this._events)e.call(h,c)&&d.push(t?c.slice(1):c);return Object.getOwnPropertySymbols?d.concat(Object.getOwnPropertySymbols(h)):d},l.prototype.listeners=function(d){var h=t?t+d:d,c=this._events[h];if(!c)return[];if(c.fn)return[c.fn];for(var p=0,f=c.length,m=new Array(f);p<f;p++)m[p]=c[p].fn;return m},l.prototype.listenerCount=function(d){var h=t?t+d:d,c=this._events[h];return c?c.fn?1:c.length:0},l.prototype.emit=function(d,h,c,p,f,m){var b=t?t+d:d;if(!this._events[b])return!1;var g=this._events[b],v=arguments.length,P,w;if(g.fn){switch(g.once&&this.removeListener(d,g.fn,void 0,!0),v){case 1:return g.fn.call(g.context),!0;case 2:return g.fn.call(g.context,h),!0;case 3:return g.fn.call(g.context,h,c),!0;case 4:return g.fn.call(g.context,h,c,p),!0;case 5:return g.fn.call(g.context,h,c,p,f),!0;case 6:return g.fn.call(g.context,h,c,p,f,m),!0}for(w=1,P=new Array(v-1);w<v;w++)P[w-1]=arguments[w];g.fn.apply(g.context,P)}else{var S=g.length,T;for(w=0;w<S;w++)switch(g[w].once&&this.removeListener(d,g[w].fn,void 0,!0),v){case 1:g[w].fn.call(g[w].context);break;case 2:g[w].fn.call(g[w].context,h);break;case 3:g[w].fn.call(g[w].context,h,c);break;case 4:g[w].fn.call(g[w].context,h,c,p);break;default:if(!P)for(T=1,P=new Array(v-1);T<v;T++)P[T-1]=arguments[T];g[w].fn.apply(g[w].context,P)}}return!0},l.prototype.on=function(d,h,c){return a(this,d,h,c,!1)},l.prototype.once=function(d,h,c){return a(this,d,h,c,!0)},l.prototype.removeListener=function(d,h,c,p){var f=t?t+d:d;if(!this._events[f])return this;if(!h)return r(this,f),this;var m=this._events[f];if(m.fn)m.fn===h&&(!p||m.once)&&(!c||m.context===c)&&r(this,f);else{for(var b=0,g=[],v=m.length;b<v;b++)(m[b].fn!==h||p&&!m[b].once||c&&m[b].context!==c)&&g.push(m[b]);g.length?this._events[f]=g.length===1?g[0]:g:r(this,f)}return this},l.prototype.removeAllListeners=function(d){var h;return d?(h=t?t+d:d,this._events[h]&&r(this,h)):(this._events=new s,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=t,l.EventEmitter=l,i.exports=l})(_i);var _s=_i.exports;const xs=ut(_s);class xi extends Error{constructor(e){super(e),this.name="TimeoutError"}}let Rs=class extends Error{constructor(e){super(),this.name="AbortError",this.message=e}};const Kt=i=>globalThis.DOMException===void 0?new Rs(i):new DOMException(i),Xt=i=>{const e=i.reason===void 0?Kt("This operation was aborted."):i.reason;return e instanceof Error?e:Kt(e)};function Us(i,e,t,s){let n;const a=new Promise((r,l)=>{if(typeof e!="number"||Math.sign(e)!==1)throw new TypeError(`Expected \`milliseconds\` to be a positive number, got \`${e}\``);if(e===Number.POSITIVE_INFINITY){r(i);return}if(s={customTimers:{setTimeout,clearTimeout},...s},s.signal){const{signal:u}=s;u.aborted&&l(Xt(u)),u.addEventListener("abort",()=>{l(Xt(u))})}n=s.customTimers.setTimeout.call(void 0,()=>{if(typeof t=="function"){try{r(t())}catch(h){l(h)}return}const u=typeof t=="string"?t:`Promise timed out after ${e} milliseconds`,d=t instanceof Error?t:new xi(u);typeof i.cancel=="function"&&i.cancel(),l(d)},e),(async()=>{try{r(await i)}catch(u){l(u)}finally{s.customTimers.clearTimeout.call(void 0,n)}})()});return a.clear=()=>{clearTimeout(n),n=void 0},a}function Ls(i,e,t){let s=0,n=i.length;for(;n>0;){const a=Math.trunc(n/2);let r=s+a;t(i[r],e)<=0?(s=++r,n-=a+1):n=a}return s}var ue=globalThis&&globalThis.__classPrivateFieldGet||function(i,e,t,s){if(t==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?i!==e||!s:!e.has(i))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?s:t==="a"?s.call(i):s?s.value:e.get(i)},Z;class Ms{constructor(){Z.set(this,[])}enqueue(e,t){t={priority:0,...t};const s={priority:t.priority,run:e};if(this.size&&ue(this,Z,"f")[this.size-1].priority>=t.priority){ue(this,Z,"f").push(s);return}const n=Ls(ue(this,Z,"f"),s,(a,r)=>r.priority-a.priority);ue(this,Z,"f").splice(n,0,s)}dequeue(){const e=ue(this,Z,"f").shift();return e==null?void 0:e.run}filter(e){return ue(this,Z,"f").filter(t=>t.priority===e.priority).map(t=>t.run)}get size(){return ue(this,Z,"f").length}}Z=new WeakMap;var E=globalThis&&globalThis.__classPrivateFieldSet||function(i,e,t,s,n){if(s==="m")throw new TypeError("Private method is not writable");if(s==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?i!==e||!n:!e.has(i))throw new TypeError("Cannot write private member to an object whose class did not declare it");return s==="a"?n.call(i,t):n?n.value=t:e.set(i,t),t},y=globalThis&&globalThis.__classPrivateFieldGet||function(i,e,t,s){if(t==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?i!==e||!s:!e.has(i))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?s:t==="a"?s.call(i):s?s.value:e.get(i)},k,Oe,Ne,se,nt,_e,Ye,V,ke,L,Je,z,xe,ie,Ze,Yt,Jt,Ri,Zt,ei,et,pt,ft,rt,Ui,tt;class Li extends Error{}class zs extends xs{constructor(e){var t,s,n,a;if(super(),k.add(this),Oe.set(this,void 0),Ne.set(this,void 0),se.set(this,0),nt.set(this,void 0),_e.set(this,void 0),Ye.set(this,0),V.set(this,void 0),ke.set(this,void 0),L.set(this,void 0),Je.set(this,void 0),z.set(this,0),xe.set(this,void 0),ie.set(this,void 0),Ze.set(this,void 0),Object.defineProperty(this,"timeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),e={carryoverConcurrencyCount:!1,intervalCap:Number.POSITIVE_INFINITY,interval:0,concurrency:Number.POSITIVE_INFINITY,autoStart:!0,queueClass:Ms,...e},!(typeof e.intervalCap=="number"&&e.intervalCap>=1))throw new TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${(s=(t=e.intervalCap)===null||t===void 0?void 0:t.toString())!==null&&s!==void 0?s:""}\` (${typeof e.intervalCap})`);if(e.interval===void 0||!(Number.isFinite(e.interval)&&e.interval>=0))throw new TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${(a=(n=e.interval)===null||n===void 0?void 0:n.toString())!==null&&a!==void 0?a:""}\` (${typeof e.interval})`);E(this,Oe,e.carryoverConcurrencyCount,"f"),E(this,Ne,e.intervalCap===Number.POSITIVE_INFINITY||e.interval===0,"f"),E(this,nt,e.intervalCap,"f"),E(this,_e,e.interval,"f"),E(this,L,new e.queueClass,"f"),E(this,Je,e.queueClass,"f"),this.concurrency=e.concurrency,this.timeout=e.timeout,E(this,Ze,e.throwOnTimeout===!0,"f"),E(this,ie,e.autoStart===!1,"f")}get concurrency(){return y(this,xe,"f")}set concurrency(e){if(!(typeof e=="number"&&e>=1))throw new TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);E(this,xe,e,"f"),y(this,k,"m",rt).call(this)}async add(e,t={}){return t={timeout:this.timeout,throwOnTimeout:y(this,Ze,"f"),...t},new Promise((s,n)=>{y(this,L,"f").enqueue(async()=>{var a,r,l;E(this,z,(r=y(this,z,"f"),r++,r),"f"),E(this,se,(l=y(this,se,"f"),l++,l),"f");try{if(!((a=t.signal)===null||a===void 0)&&a.aborted)throw new Li("The task was aborted.");let u=e({signal:t.signal});t.timeout&&(u=Us(Promise.resolve(u),t.timeout)),t.signal&&(u=Promise.race([u,y(this,k,"m",Ui).call(this,t.signal)]));const d=await u;s(d),this.emit("completed",d)}catch(u){if(u instanceof xi&&!t.throwOnTimeout){s();return}n(u),this.emit("error",u)}finally{y(this,k,"m",Ri).call(this)}},t),this.emit("add"),y(this,k,"m",et).call(this)})}async addAll(e,t){return Promise.all(e.map(async s=>this.add(s,t)))}start(){return y(this,ie,"f")?(E(this,ie,!1,"f"),y(this,k,"m",rt).call(this),this):this}pause(){E(this,ie,!0,"f")}clear(){E(this,L,new(y(this,Je,"f")),"f")}async onEmpty(){y(this,L,"f").size!==0&&await y(this,k,"m",tt).call(this,"empty")}async onSizeLessThan(e){y(this,L,"f").size<e||await y(this,k,"m",tt).call(this,"next",()=>y(this,L,"f").size<e)}async onIdle(){y(this,z,"f")===0&&y(this,L,"f").size===0||await y(this,k,"m",tt).call(this,"idle")}get size(){return y(this,L,"f").size}sizeBy(e){return y(this,L,"f").filter(e).length}get pending(){return y(this,z,"f")}get isPaused(){return y(this,ie,"f")}}Oe=new WeakMap,Ne=new WeakMap,se=new WeakMap,nt=new WeakMap,_e=new WeakMap,Ye=new WeakMap,V=new WeakMap,ke=new WeakMap,L=new WeakMap,Je=new WeakMap,z=new WeakMap,xe=new WeakMap,ie=new WeakMap,Ze=new WeakMap,k=new WeakSet,Yt=function(){return y(this,Ne,"f")||y(this,se,"f")<y(this,nt,"f")},Jt=function(){return y(this,z,"f")<y(this,xe,"f")},Ri=function(){var e;E(this,z,(e=y(this,z,"f"),e--,e),"f"),y(this,k,"m",et).call(this),this.emit("next")},Zt=function(){y(this,k,"m",ft).call(this),y(this,k,"m",pt).call(this),E(this,ke,void 0,"f")},ei=function(){const e=Date.now();if(y(this,V,"f")===void 0){const t=y(this,Ye,"f")-e;if(t<0)E(this,se,y(this,Oe,"f")?y(this,z,"f"):0,"f");else return y(this,ke,"f")===void 0&&E(this,ke,setTimeout(()=>{y(this,k,"m",Zt).call(this)},t),"f"),!0}return!1},et=function(){if(y(this,L,"f").size===0)return y(this,V,"f")&&clearInterval(y(this,V,"f")),E(this,V,void 0,"f"),this.emit("empty"),y(this,z,"f")===0&&this.emit("idle"),!1;if(!y(this,ie,"f")){const e=!y(this,k,"a",ei);if(y(this,k,"a",Yt)&&y(this,k,"a",Jt)){const t=y(this,L,"f").dequeue();return t?(this.emit("active"),t(),e&&y(this,k,"m",pt).call(this),!0):!1}}return!1},pt=function(){y(this,Ne,"f")||y(this,V,"f")!==void 0||(E(this,V,setInterval(()=>{y(this,k,"m",ft).call(this)},y(this,_e,"f")),"f"),E(this,Ye,Date.now()+y(this,_e,"f"),"f"))},ft=function(){y(this,se,"f")===0&&y(this,z,"f")===0&&y(this,V,"f")&&(clearInterval(y(this,V,"f")),E(this,V,void 0,"f")),E(this,se,y(this,Oe,"f")?y(this,z,"f"):0,"f"),y(this,k,"m",rt).call(this)},rt=function(){for(;y(this,k,"m",et).call(this););},Ui=async function(e){return new Promise((t,s)=>{e.addEventListener("abort",()=>{s(new Li("The task was aborted."))},{once:!0})})},tt=async function(e,t){return new Promise(s=>{const n=()=>{t&&!t()||(this.off(e,n),s())};this.on(e,n)})};function $s(){return o("svg",{width:"26",height:"26",viewBox:"0 0 26 26",xmlns:"http://www.w3.org/2000/svg"},o("g",{fill:"none","fill-rule":"evenodd"},o("circle",{fill:"#FFF",cx:"13",cy:"13",r:"13"}),o("path",{d:"M21.64 13.205c0-.639-.057-1.252-.164-1.841H13v3.481h4.844a4.14 4.14 0 01-1.796 2.716v2.259h2.908c1.702-1.567 2.684-3.875 2.684-6.615z",fill:"#4285F4","fill-rule":"nonzero"}),o("path",{d:"M13 22c2.43 0 4.467-.806 5.956-2.18l-2.908-2.259c-.806.54-1.837.86-3.048.86-2.344 0-4.328-1.584-5.036-3.711H4.957v2.332A8.997 8.997 0 0013 22z",fill:"#34A853","fill-rule":"nonzero"}),o("path",{d:"M7.964 14.71A5.41 5.41 0 017.682 13c0-.593.102-1.17.282-1.71V8.958H4.957A8.996 8.996 0 004 13c0 1.452.348 2.827.957 4.042l3.007-2.332z",fill:"#FBBC05","fill-rule":"nonzero"}),o("path",{d:"M13 7.58c1.321 0 2.508.454 3.44 1.345l2.582-2.58C17.463 4.891 15.426 4 13 4a8.997 8.997 0 00-8.043 4.958l3.007 2.332C8.672 9.163 10.656 7.58 13 7.58z",fill:"#EA4335","fill-rule":"nonzero"}),o("path",{d:"M4 4h18v18H4z"})))}function Hs(i){let{pluginName:e,i18n:t,onAuth:s}=i;const n=e==="Google Drive",a=it(r=>{r.preventDefault(),s()},[s]);return o("form",{onSubmit:a},n?o("button",{type:"submit",className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Provider-authBtn uppy-Provider-btn-google","data-uppy-super-focusable":!0},o($s,null),t("signInWithGoogle")):o("button",{type:"submit",className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Provider-authBtn","data-uppy-super-focusable":!0},t("authenticateWith",{pluginName:e})))}const Vs=i=>{let{pluginName:e,i18n:t,onAuth:s}=i;return o(Hs,{pluginName:e,i18n:t,onAuth:s})};function qs(i){const{loading:e,pluginName:t,pluginIcon:s,i18n:n,handleAuth:a,renderForm:r=Vs}=i;return o("div",{className:"uppy-Provider-auth"},o("div",{className:"uppy-Provider-authIcon"},s()),o("div",{className:"uppy-Provider-authTitle"},n("authenticateWithTitle",{pluginName:t})),o("div",{className:"uppy-Provider-authForm"},r({pluginName:t,i18n:n,loading:e,onAuth:a})))}function Ws(i){let{i18n:e,logout:t,username:s}=i;return o(re,null,o("span",{className:"uppy-ProviderBrowser-user",key:"username"},s),o("button",{type:"button",onClick:t,className:"uppy-u-reset uppy-c-btn uppy-ProviderBrowser-userLogout",key:"logout"},e("logOut")))}const js=i=>{const{getFolder:e,title:t,isLast:s}=i;return o(re,null,o("button",{type:"button",className:"uppy-u-reset uppy-c-btn",onClick:e},t),s?"":" / ")};function Gs(i){const{getFolder:e,title:t,breadcrumbsIcon:s,breadcrumbs:n}=i;return o("div",{className:"uppy-Provider-breadcrumbs"},o("div",{className:"uppy-Provider-breadcrumbsIcon"},s),n.map((a,r)=>o(js,{key:a.id,getFolder:()=>e(a.requestPath,a.name),title:r===0?t:a.name,isLast:r+1===n.length})))}function Qs(i){return o(re,null,i.showBreadcrumbs&&o(Gs,{getFolder:i.getFolder,breadcrumbs:i.breadcrumbs,breadcrumbsIcon:i.pluginIcon&&i.pluginIcon(),title:i.title}),o(Ws,{logout:i.logout,username:i.username,i18n:i.i18n}))}var Mi={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(i){(function(){var e={}.hasOwnProperty;function t(){for(var a="",r=0;r<arguments.length;r++){var l=arguments[r];l&&(a=n(a,s(l)))}return a}function s(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return t.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var r="";for(var l in a)e.call(a,l)&&a[l]&&(r=n(r,l));return r}function n(a,r){return r?a?a+" "+r:a+r:a}i.exports?(t.default=t,i.exports=t):window.classNames=t})()})(Mi);var Ks=Mi.exports;const x=ut(Ks);function zi(i){return{...i,type:i.mimeType,extension:i.name?Is(i.name).extension:null}}function Nt(){return Nt=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},Nt.apply(this,arguments)}const Xs={position:"relative",width:"100%",minHeight:"100%"},Ys={position:"absolute",top:0,left:0,width:"100%",overflow:"visible"};class $i extends ve{constructor(e){super(e),this.handleScroll=()=>{this.setState({offset:this.base.scrollTop})},this.handleResize=()=>{this.resize()},this.focusElement=null,this.state={offset:0,height:0}}componentDidMount(){this.resize(),window.addEventListener("resize",this.handleResize)}componentWillUpdate(){this.base.contains(document.activeElement)&&(this.focusElement=document.activeElement)}componentDidUpdate(){this.focusElement&&this.focusElement.parentNode&&document.activeElement!==this.focusElement&&this.focusElement.focus(),this.focusElement=null,this.resize()}componentWillUnmount(){window.removeEventListener("resize",this.handleResize)}resize(){const{height:e}=this.state;e!==this.base.offsetHeight&&this.setState({height:this.base.offsetHeight})}render(e){let{data:t,rowHeight:s,renderRow:n,overscanCount:a=10,...r}=e;const{offset:l,height:u}=this.state;let d=Math.floor(l/s),h=Math.floor(u/s);a&&(d=Math.max(0,d-d%a),h+=a);const c=d+h+4,p=t.slice(d,c),f={...Xs,height:t.length*s},m={...Ys,top:d*s};return o("div",Nt({onScroll:this.handleScroll},r),o("div",{role:"presentation",style:f},o("div",{role:"presentation",style:m},p.map(n))))}}function Hi(i){const{search:e,searchOnInput:t,searchTerm:s,showButton:n,inputLabel:a,clearSearchLabel:r,buttonLabel:l,clearSearch:u,inputClassName:d,buttonCSSClassName:h}=i,[c,p]=be(s??""),f=it(v=>{v.preventDefault(),e(c)},[e,c]),m=it(v=>{const P=v.target.value;p(P),t&&e(P)},[p,t,e]),b=()=>{p(""),u&&u()},[g]=be(()=>{const v=document.createElement("form");return v.setAttribute("tabindex","-1"),v.id=zt(),v});return st(()=>(document.body.appendChild(g),g.addEventListener("submit",f),()=>{g.removeEventListener("submit",f),document.body.removeChild(g)}),[g,f]),o(re,null,o("input",{className:`uppy-u-reset ${d}`,type:"search","aria-label":a,placeholder:a,value:c,onInput:m,form:g.id,"data-uppy-super-focusable":!0}),!n&&o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-ProviderBrowser-searchFilterIcon",width:"12",height:"12",viewBox:"0 0 12 12"},o("path",{d:"M8.638 7.99l3.172 3.172a.492.492 0 1 1-.697.697L7.91 8.656a4.977 4.977 0 0 1-2.983.983C2.206 9.639 0 7.481 0 4.819 0 2.158 2.206 0 4.927 0c2.721 0 4.927 2.158 4.927 4.82a4.74 4.74 0 0 1-1.216 3.17zm-3.71.685c2.176 0 3.94-1.726 3.94-3.856 0-2.129-1.764-3.855-3.94-3.855C2.75.964.984 2.69.984 4.819c0 2.13 1.765 3.856 3.942 3.856z"})),!n&&c&&o("button",{className:"uppy-u-reset uppy-ProviderBrowser-searchFilterReset",type:"button","aria-label":r,title:r,onClick:b},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",viewBox:"0 0 19 19"},o("path",{d:"M17.318 17.232L9.94 9.854 9.586 9.5l-.354.354-7.378 7.378h.707l-.62-.62v.706L9.318 9.94l.354-.354-.354-.354L1.94 1.854v.707l.62-.62h-.706l7.378 7.378.354.354.354-.354 7.378-7.378h-.707l.622.62v-.706L9.854 9.232l-.354.354.354.354 7.378 7.378.708-.707-7.38-7.378v.708l7.38-7.38.353-.353-.353-.353-.622-.622-.353-.353-.354.352-7.378 7.38h.708L2.56 1.23 2.208.88l-.353.353-.622.62-.353.355.352.353 7.38 7.38v-.708l-7.38 7.38-.353.353.352.353.622.622.353.353.354-.353 7.38-7.38h-.708l7.38 7.38z"}))),n&&o("button",{className:`uppy-u-reset uppy-c-btn uppy-c-btn-primary ${h}`,type:"submit",form:g.id},l))}function Js(i){let{cancel:e,done:t,i18n:s,selected:n}=i;return o("div",{className:"uppy-ProviderBrowser-footer"},o("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary",onClick:t,type:"button"},s("selectX",{smart_count:n})),o("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-link",onClick:e,type:"button"},s("cancel")))}function Zs(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:11,height:14.5,viewBox:"0 0 44 58"},o("path",{d:"M27.437.517a1 1 0 0 0-.094.03H4.25C2.037.548.217 2.368.217 4.58v48.405c0 2.212 1.82 4.03 4.03 4.03H39.03c2.21 0 4.03-1.818 4.03-4.03V15.61a1 1 0 0 0-.03-.28 1 1 0 0 0 0-.093 1 1 0 0 0-.03-.032 1 1 0 0 0 0-.03 1 1 0 0 0-.032-.063 1 1 0 0 0-.03-.063 1 1 0 0 0-.032 0 1 1 0 0 0-.03-.063 1 1 0 0 0-.032-.03 1 1 0 0 0-.03-.063 1 1 0 0 0-.063-.062l-14.593-14a1 1 0 0 0-.062-.062A1 1 0 0 0 28 .708a1 1 0 0 0-.374-.157 1 1 0 0 0-.156 0 1 1 0 0 0-.03-.03l-.003-.003zM4.25 2.547h22.218v9.97c0 2.21 1.82 4.03 4.03 4.03h10.564v36.438a2.02 2.02 0 0 1-2.032 2.032H4.25c-1.13 0-2.032-.9-2.032-2.032V4.58c0-1.13.902-2.032 2.03-2.032zm24.218 1.345l10.375 9.937.75.718H30.5c-1.13 0-2.032-.9-2.032-2.03V3.89z"}))}function en(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",style:{minWidth:16,marginRight:3},viewBox:"0 0 276.157 276.157"},o("path",{d:"M273.08 101.378c-3.3-4.65-8.86-7.32-15.254-7.32h-24.34V67.59c0-10.2-8.3-18.5-18.5-18.5h-85.322c-3.63 0-9.295-2.875-11.436-5.805l-6.386-8.735c-4.982-6.814-15.104-11.954-23.546-11.954H58.73c-9.292 0-18.638 6.608-21.737 15.372l-2.033 5.752c-.958 2.71-4.72 5.37-7.596 5.37H18.5C8.3 49.09 0 57.39 0 67.59v167.07c0 .886.16 1.73.443 2.52.152 3.306 1.18 6.424 3.053 9.064 3.3 4.652 8.86 7.32 15.255 7.32h188.487c11.395 0 23.27-8.425 27.035-19.18l40.677-116.188c2.11-6.035 1.43-12.164-1.87-16.816zM18.5 64.088h8.864c9.295 0 18.64-6.607 21.738-15.37l2.032-5.75c.96-2.712 4.722-5.373 7.597-5.373h29.565c3.63 0 9.295 2.876 11.437 5.806l6.386 8.735c4.982 6.815 15.104 11.954 23.546 11.954h85.322c1.898 0 3.5 1.602 3.5 3.5v26.47H69.34c-11.395 0-23.27 8.423-27.035 19.178L15 191.23V67.59c0-1.898 1.603-3.5 3.5-3.5zm242.29 49.15l-40.676 116.188c-1.674 4.78-7.812 9.135-12.877 9.135H18.75c-1.447 0-2.576-.372-3.02-.997-.442-.625-.422-1.814.057-3.18l40.677-116.19c1.674-4.78 7.812-9.134 12.877-9.134h188.487c1.448 0 2.577.372 3.02.997.443.625.423 1.814-.056 3.18z"}))}function tn(){return o("svg",{"aria-hidden":"true",focusable:"false",style:{width:16,marginRight:4},viewBox:"0 0 58 58"},o("path",{d:"M36.537 28.156l-11-7a1.005 1.005 0 0 0-1.02-.033C24.2 21.3 24 21.635 24 22v14a1 1 0 0 0 1.537.844l11-7a1.002 1.002 0 0 0 0-1.688zM26 34.18V23.82L34.137 29 26 34.18z"}),o("path",{d:"M57 6H1a1 1 0 0 0-1 1v44a1 1 0 0 0 1 1h56a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1zM10 28H2v-9h8v9zm-8 2h8v9H2v-9zm10 10V8h34v42H12V40zm44-12h-8v-9h8v9zm-8 2h8v9h-8v-9zm8-22v9h-8V8h8zM2 8h8v9H2V8zm0 42v-9h8v9H2zm54 0h-8v-9h8v9z"}))}function sn(i){const{itemIconString:e}=i;if(e===null)return null;switch(e){case"file":return o(Zs,null);case"folder":return o(en,null);case"video":return o(tn,null);default:{const{alt:t}=i;return o("img",{src:e,alt:t,referrerPolicy:"no-referrer",loading:"lazy",width:16,height:16})}}}function ti(i){const{className:e,isDisabled:t,restrictionError:s,isChecked:n,title:a,itemIconEl:r,showTitles:l,toggleCheckbox:u,recordShiftKeyPress:d,id:h,children:c}=i,p=x("uppy-u-reset","uppy-ProviderBrowserItem-checkbox","uppy-ProviderBrowserItem-checkbox--grid",{"uppy-ProviderBrowserItem-checkbox--is-checked":n});return o("li",{className:e,title:t?s==null?void 0:s.message:void 0},o("input",{type:"checkbox",className:p,onChange:u,onKeyDown:d,onMouseDown:d,name:"listitem",id:h,checked:n,disabled:t,"data-uppy-super-focusable":!0}),o("label",{htmlFor:h,"aria-label":a,className:"uppy-u-reset uppy-ProviderBrowserItem-inner"},r,l&&a,c))}function nn(i){const{className:e,isDisabled:t,restrictionError:s,isCheckboxDisabled:n,isChecked:a,toggleCheckbox:r,recordShiftKeyPress:l,type:u,id:d,itemIconEl:h,title:c,handleFolderClick:p,showTitles:f,i18n:m}=i;return o("li",{className:e,title:t?s==null?void 0:s.message:void 0},n?null:o("input",{type:"checkbox",className:`uppy-u-reset uppy-ProviderBrowserItem-checkbox ${a?"uppy-ProviderBrowserItem-checkbox--is-checked":""}`,onChange:r,onKeyDown:l,onMouseDown:l,name:"listitem",id:d,checked:a,"aria-label":u==="file"?null:m("allFilesFromFolderNamed",{name:c}),disabled:t,"data-uppy-super-focusable":!0}),u==="file"?o("label",{htmlFor:d,className:"uppy-u-reset uppy-ProviderBrowserItem-inner"},o("div",{className:"uppy-ProviderBrowserItem-iconWrap"},h),f&&c):o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-ProviderBrowserItem-inner",onClick:p,"aria-label":m("openFolderNamed",{name:c})},o("div",{className:"uppy-ProviderBrowserItem-iconWrap"},h),f&&c?o("span",null,c):m("unnamed")))}function Re(){return Re=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},Re.apply(this,arguments)}function ii(i){const{author:e,getItemIcon:t,isChecked:s,isDisabled:n,viewType:a}=i,r=t(),l=x("uppy-ProviderBrowserItem",{"uppy-ProviderBrowserItem--selected":s},{"uppy-ProviderBrowserItem--disabled":n},{"uppy-ProviderBrowserItem--noPreview":r==="video"}),u=o(sn,{itemIconString:r});switch(a){case"grid":return o(ti,Re({},i,{className:l,itemIconEl:u}));case"list":return o(nn,Re({},i,{className:l,itemIconEl:u}));case"unsplash":return o(ti,Re({},i,{className:l,itemIconEl:u}),o("a",{href:`${e.url}?utm_source=Companion&utm_medium=referral`,target:"_blank",rel:"noopener noreferrer",className:"uppy-ProviderBrowserItem-author",tabIndex:-1},e.name));default:throw new Error(`There is no such type ${a}`)}}const rn="shared-with-me";function si(i){const{currentSelection:e,uppyFiles:t,viewType:s,isChecked:n,toggleCheckbox:a,recordShiftKeyPress:r,showTitles:l,i18n:u,validateRestrictions:d,getNextFolder:h,f:c}=i;if(c.isFolder)return ii({showTitles:l,viewType:s,i18n:u,id:c.id,title:c.name,getItemIcon:()=>c.icon,isChecked:n(c),toggleCheckbox:f=>a(f,c),recordShiftKeyPress:r,type:"folder",isDisabled:!1,isCheckboxDisabled:c.id===rn,handleFolderClick:()=>h(c)});const p=d(zi(c),[...t,...e]);return ii({id:c.id,title:c.name,author:c.author,getItemIcon:()=>s==="grid"&&c.thumbnail?c.thumbnail:c.icon,isChecked:n(c),toggleCheckbox:f=>a(f,c),isCheckboxDisabled:!1,recordShiftKeyPress:r,showTitles:l,viewType:s,i18n:u,type:"file",isDisabled:!!p&&!n(c),restrictionError:p})}function Vi(i){const{currentSelection:e,folders:t,files:s,uppyFiles:n,viewType:a,headerComponent:r,showBreadcrumbs:l,isChecked:u,toggleCheckbox:d,recordShiftKeyPress:h,handleScroll:c,showTitles:p,i18n:f,validateRestrictions:m,isLoading:b,showSearchFilter:g,search:v,searchTerm:P,clearSearch:w,searchOnInput:S,searchInputLabel:T,clearSearchLabel:R,getNextFolder:I,cancel:D,done:N,noResultsLabel:me,virtualList:Pe}=i,ae=e.length,oe=Oi(()=>[...t,...s],[t,s]);return o("div",{className:x("uppy-ProviderBrowser",`uppy-ProviderBrowser-viewType--${a}`)},r&&o("div",{className:"uppy-ProviderBrowser-header"},o("div",{className:x("uppy-ProviderBrowser-headerBar",!l&&"uppy-ProviderBrowser-headerBar--simple")},r)),g&&o("div",{class:"uppy-ProviderBrowser-searchFilter"},o(Hi,{search:v,searchTerm:P,clearSearch:w,inputLabel:T,clearSearchLabel:R,inputClassName:"uppy-ProviderBrowser-searchFilterInput",searchOnInput:S})),(()=>b?o("div",{className:"uppy-Provider-loading"},o("span",null,typeof b=="string"?b:f("loading"))):!t.length&&!s.length?o("div",{className:"uppy-Provider-empty"},me):Pe?o("div",{className:"uppy-ProviderBrowser-body"},o("ul",{className:"uppy-ProviderBrowser-list"},o($i,{data:oe,renderRow:Q=>o(si,{currentSelection:e,uppyFiles:n,viewType:a,isChecked:u,toggleCheckbox:d,recordShiftKeyPress:h,showTitles:p,i18n:f,validateRestrictions:m,getNextFolder:I,f:Q}),rowHeight:31}))):o("div",{className:"uppy-ProviderBrowser-body"},o("ul",{className:"uppy-ProviderBrowser-list",onScroll:c,role:"listbox",tabIndex:-1},oe.map(Q=>o(si,{currentSelection:e,uppyFiles:n,viewType:a,isChecked:u,toggleCheckbox:d,recordShiftKeyPress:h,showTitles:p,i18n:f,validateRestrictions:m,getNextFolder:I,f:Q})))))(),ae>0&&o(Js,{selected:ae,done:N,cancel:D,i18n:f}))}class at extends ve{componentWillUnmount(){const{onUnmount:e}=this.props;e()}render(){const{children:e}=this.props;return te(e)[0]}}class qi{constructor(e,t){this.filterItems=s=>{const n=this.plugin.getPluginState();return!n.filterInput||n.filterInput===""?s:s.filter(a=>a.name.toLowerCase().indexOf(n.filterInput.toLowerCase())!==-1)},this.recordShiftKeyPress=s=>{this.isShiftKeyPressed=s.shiftKey},this.isChecked=s=>{const{currentSelection:n}=this.plugin.getPluginState();return n.some(a=>a.id===s.id)},this.plugin=e,this.provider=t.provider,this.opts=t,this.isHandlingScroll=!1,this.preFirstRender=this.preFirstRender.bind(this),this.handleError=this.handleError.bind(this),this.clearSelection=this.clearSelection.bind(this),this.cancelPicking=this.cancelPicking.bind(this)}preFirstRender(){this.plugin.setPluginState({didFirstRender:!0}),this.plugin.onFirstRender()}shouldHandleScroll(e){const{scrollHeight:t,scrollTop:s,offsetHeight:n}=e.target;return t-(s+n)<50&&!this.isHandlingScroll}clearSelection(){this.plugin.setPluginState({currentSelection:[],filterInput:""})}cancelPicking(){this.clearSelection();const e=this.plugin.uppy.getPlugin("Dashboard");e&&e.hideAllPanels()}handleError(e){var t;const{uppy:s}=this.plugin,n=s.i18n("companionError");s.log(e.toString()),!(e.isAuthError||((t=e.cause)==null?void 0:t.name)==="AbortError")&&s.info({message:n,details:e.toString()},"error",5e3)}registerRequestClient(){this.requestClientId=this.provider.provider,this.plugin.uppy.registerRequestClient(this.requestClientId,this.provider)}getTagFile(e){const t={id:e.id,source:this.plugin.id,name:e.name||e.id,type:e.mimeType,isRemote:!0,data:e,meta:{},body:{fileId:e.id},remote:{companionUrl:this.plugin.opts.companionUrl,url:`${this.provider.fileUrl(e.requestPath)}`,body:{fileId:e.id},providerName:this.provider.name,provider:this.provider.provider,requestClientId:this.requestClientId}};return e.thumbnail&&(t.preview=e.thumbnail),e.author&&(e.author.name!=null&&(t.meta.authorName=String(e.author.name)),e.author.url&&(t.meta.authorUrl=e.author.url)),e.relDirPath!=null&&(t.meta.relativePath=e.relDirPath?`${e.relDirPath}/${t.name}`:null),e.absDirPath!=null&&(t.meta.absolutePath=e.absDirPath?`/${e.absDirPath}/${t.name}`:`/${t.name}`),t}toggleCheckbox(e,t){e.stopPropagation(),e.preventDefault(),e.currentTarget.focus();const{folders:s,files:n}=this.plugin.getPluginState(),a=this.filterItems(s.concat(n));if(this.lastCheckbox&&this.isShiftKeyPressed){const{currentSelection:l}=this.plugin.getPluginState(),u=a.indexOf(this.lastCheckbox),d=a.indexOf(t),h=u<d?a.slice(u,d+1):a.slice(d,u+1),c=[];for(const p of h){const{uppy:f}=this.plugin,m=f.validateRestrictions(zi(p),[...f.getFiles(),...c]);m?f.info({message:m.message},"error",f.opts.infoTimeout):c.push(p)}this.plugin.setPluginState({currentSelection:[...new Set([...l,...c])]});return}this.lastCheckbox=t;const{currentSelection:r}=this.plugin.getPluginState();this.isChecked(t)?this.plugin.setPluginState({currentSelection:r.filter(l=>l.id!==t.id)}):this.plugin.setPluginState({currentSelection:r.concat([t])})}setLoading(e){this.plugin.setPluginState({loading:e})}}function U(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var an=0;function $e(i){return"__private_"+an+++"_"+i}const on={version:"3.13.0"};function ln(i){return i.slice(1).map(e=>e.name).join("/")}function _t(i,e){return i?`${i}/${e}`:e}function Wi(){return o("svg",{"aria-hidden":"true",focusable:"false",width:"30",height:"30",viewBox:"0 0 30 30"},o("path",{d:"M15 30c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15zm4.258-12.676v6.846h-8.426v-6.846H5.204l9.82-12.364 9.82 12.364H19.26z"}))}const un={viewType:"list",showTitles:!0,showFilter:!0,showBreadcrumbs:!0,loadAllFiles:!1,virtualList:!1};var pe=$e("abortController"),H=$e("withAbort"),Me=$e("list"),Se=$e("listFilesAndFolders"),Ue=$e("recursivelyListAllFiles");class dn extends qi{constructor(e,t){super(e,{...un,...t}),Object.defineProperty(this,Ue,{value:fn}),Object.defineProperty(this,Se,{value:pn}),Object.defineProperty(this,Me,{value:cn}),Object.defineProperty(this,H,{value:hn}),Object.defineProperty(this,pe,{writable:!0,value:void 0}),this.filterQuery=this.filterQuery.bind(this),this.clearFilter=this.clearFilter.bind(this),this.getFolder=this.getFolder.bind(this),this.getNextFolder=this.getNextFolder.bind(this),this.logout=this.logout.bind(this),this.handleAuth=this.handleAuth.bind(this),this.handleScroll=this.handleScroll.bind(this),this.donePicking=this.donePicking.bind(this),this.render=this.render.bind(this),this.plugin.setPluginState({authenticated:void 0,files:[],folders:[],breadcrumbs:[],filterInput:"",isSearchVisible:!1,currentSelection:[]}),this.registerRequestClient()}tearDown(){}async getFolder(e,t){this.setLoading(!0);try{await U(this,H)[H](async s=>{this.lastCheckbox=void 0;let{breadcrumbs:n}=this.plugin.getPluginState();const a=n.findIndex(u=>e===u.requestPath);a!==-1?n=n.slice(0,a+1):n=[...n,{requestPath:e,name:t}],this.nextPagePath=e;let r=[],l=[];do{const{files:u,folders:d}=await U(this,Se)[Se]({breadcrumbs:n,signal:s});r=r.concat(u),l=l.concat(d),this.setLoading(this.plugin.uppy.i18n("loadedXFiles",{numFiles:r.length+l.length}))}while(this.opts.loadAllFiles&&this.nextPagePath);this.plugin.setPluginState({folders:l,files:r,breadcrumbs:n,filterInput:""})})}catch(s){if((s==null?void 0:s.name)==="UserFacingApiError"){this.plugin.uppy.info({message:this.plugin.uppy.i18n(s.message)},"warning",5e3);return}this.handleError(s)}finally{this.setLoading(!1)}}getNextFolder(e){this.getFolder(e.requestPath,e.name),this.lastCheckbox=void 0}async logout(){try{await U(this,H)[H](async e=>{const t=await this.provider.logout({signal:e});if(t.ok){if(!t.revoked){const n=this.plugin.uppy.i18n("companionUnauthorizeHint",{provider:this.plugin.title,url:t.manual_revoke_url});this.plugin.uppy.info(n,"info",7e3)}const s={authenticated:!1,files:[],folders:[],breadcrumbs:[],filterInput:""};this.plugin.setPluginState(s)}})}catch(e){this.handleError(e)}}filterQuery(e){this.plugin.setPluginState({filterInput:e})}clearFilter(){this.plugin.setPluginState({filterInput:""})}async handleAuth(e){try{await U(this,H)[H](async t=>{this.setLoading(!0),await this.provider.login({authFormData:e,signal:t}),this.plugin.setPluginState({authenticated:!0}),this.preFirstRender()})}catch(t){if(t.name==="UserFacingApiError"){this.plugin.uppy.info({message:this.plugin.uppy.i18n(t.message)},"warning",5e3);return}this.plugin.uppy.log(`login failed: ${t.message}`)}finally{this.setLoading(!1)}}async handleScroll(e){if(this.shouldHandleScroll(e)&&this.nextPagePath){this.isHandlingScroll=!0;try{await U(this,H)[H](async t=>{const{files:s,folders:n,breadcrumbs:a}=this.plugin.getPluginState(),{files:r,folders:l}=await U(this,Se)[Se]({breadcrumbs:a,signal:t}),u=s.concat(r),d=n.concat(l);this.plugin.setPluginState({folders:d,files:u})})}catch(t){this.handleError(t)}finally{this.isHandlingScroll=!1}}}async donePicking(){this.setLoading(!0);try{await U(this,H)[H](async e=>{const{currentSelection:t}=this.plugin.getPluginState(),s=[],n=[];for(const a of t){const{requestPath:r}=a,l=u=>({...u,relDirPath:u.absDirPath.replace(a.absDirPath,"").replace(/^\//,"")});if(a.isFolder){let u=!0,d=0;const h=new zs({concurrency:6}),c=f=>{for(const m of f){const b=this.getTagFile(m),g=Es(b,this.plugin.uppy.getID());this.plugin.uppy.checkIfFileAlreadyExists(g)||(n.push(l(m)),d++,this.setLoading(this.plugin.uppy.i18n("addedNumFiles",{numFiles:d}))),u=!1}};await U(this,Ue)[Ue]({requestPath:r,absDirPath:_t(a.absDirPath,a.name),relDirPath:a.name,queue:h,onFiles:c,signal:e}),await h.onIdle();let p;u?p=this.plugin.uppy.i18n("emptyFolderAdded"):d===0?p=this.plugin.uppy.i18n("folderAlreadyAdded",{folder:a.name}):p=this.plugin.uppy.i18n("folderAdded",{smart_count:d,folder:a.name}),s.push(p)}else n.push(l(a))}this.plugin.uppy.log("Adding files from a remote provider"),this.plugin.uppy.addFiles(n.map(a=>this.getTagFile(a,this.requestClientId))),this.plugin.setPluginState({filterInput:""}),s.forEach(a=>this.plugin.uppy.info(a)),this.clearSelection()})}catch(e){this.handleError(e)}finally{this.setLoading(!1)}}render(e,t){var s=this;t===void 0&&(t={});const{authenticated:n,didFirstRender:a}=this.plugin.getPluginState(),{i18n:r}=this.plugin.uppy;a||this.preFirstRender();const l={...this.opts,...t},{files:u,folders:d,filterInput:h,loading:c,currentSelection:p}=this.plugin.getPluginState(),{isChecked:f,recordShiftKeyPress:m,filterItems:b}=this,g=h!=="",v=this.plugin.icon||Wi,P={showBreadcrumbs:l.showBreadcrumbs,getFolder:this.getFolder,breadcrumbs:this.plugin.getPluginState().breadcrumbs,pluginIcon:v,title:this.plugin.title,logout:this.logout,username:this.username,i18n:r},w={isChecked:f,toggleCheckbox:this.toggleCheckbox.bind(this),recordShiftKeyPress:m,currentSelection:p,files:g?b(u):u,folders:g?b(d):d,getNextFolder:this.getNextFolder,getFolder:this.getFolder,loadAllFiles:this.opts.loadAllFiles,virtualList:this.opts.virtualList,showSearchFilter:l.showFilter,search:this.filterQuery,clearSearch:this.clearFilter,searchTerm:h,searchOnInput:!0,searchInputLabel:r("filter"),clearSearchLabel:r("resetFilter"),noResultsLabel:r("noFilesFound"),logout:this.logout,handleScroll:this.handleScroll,done:this.donePicking,cancel:this.cancelPicking,headerComponent:o(Qs,P),title:this.plugin.title,viewType:l.viewType,showTitles:l.showTitles,showBreadcrumbs:l.showBreadcrumbs,pluginIcon:v,i18n:this.plugin.uppy.i18n,uppyFiles:this.plugin.uppy.getFiles(),validateRestrictions:function(){return s.plugin.uppy.validateRestrictions(...arguments)},isLoading:c};return n===!1?o(at,{onUnmount:this.clearSelection},o(qs,{pluginName:this.plugin.title,pluginIcon:v,handleAuth:this.handleAuth,i18n:this.plugin.uppy.i18nArray,renderForm:this.opts.renderAuthForm,loading:c})):o(at,{onUnmount:this.clearSelection},o(Vi,w))}}async function hn(i){var e;(e=U(this,pe)[pe])==null||e.abort();const t=new AbortController;U(this,pe)[pe]=t;const s=()=>{t.abort(),this.clearSelection()};try{this.plugin.uppy.on("dashboard:close-panel",s),this.plugin.uppy.on("cancel-all",s),await i(t.signal)}finally{this.plugin.uppy.off("dashboard:close-panel",s),this.plugin.uppy.off("cancel-all",s),U(this,pe)[pe]=void 0}}async function cn(i){let{requestPath:e,absDirPath:t,signal:s}=i;const{username:n,nextPagePath:a,items:r}=await this.provider.list(e,{signal:s});return this.username=n||this.username,{items:r.map(l=>({...l,absDirPath:t})),nextPagePath:a}}async function pn(i){let{breadcrumbs:e,signal:t}=i;const s=ln(e),{items:n,nextPagePath:a}=await U(this,Me)[Me]({requestPath:this.nextPagePath,absDirPath:s,signal:t});this.nextPagePath=a;const r=[],l=[];return n.forEach(u=>{u.isFolder?l.push(u):r.push(u)}),{files:r,folders:l}}async function fn(i){let{requestPath:e,absDirPath:t,relDirPath:s,queue:n,onFiles:a,signal:r}=i,l=e;for(;l;){const u=await U(this,Me)[Me]({requestPath:l,absDirPath:t,signal:r});l=u.nextPagePath;const d=u.items.filter(p=>!p.isFolder),h=u.items.filter(p=>p.isFolder);a(d);const c=h.map(async p=>n.add(async()=>U(this,Ue)[Ue]({requestPath:p.requestPath,absDirPath:_t(t,p.name),relDirPath:_t(s,p.name),queue:n,onFiles:a,signal:r})));await Promise.all(c)}}dn.VERSION=on.version;function ni(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var mn=0;function gn(i){return"__private_"+mn+++"_"+i}const yn={version:"3.13.0"},ri={isInputMode:!0,files:[],folders:[],breadcrumbs:[],filterInput:"",currentSelection:[],searchTerm:null},bn={viewType:"grid",showTitles:!0,showFilter:!0,showBreadcrumbs:!0};var Ce=gn("updateFilesAndInputMode");class vn extends qi{constructor(e,t){super(e,{...bn,...t}),Object.defineProperty(this,Ce,{value:wn}),this.nextPageQuery=null,this.search=this.search.bind(this),this.clearSearch=this.clearSearch.bind(this),this.resetPluginState=this.resetPluginState.bind(this),this.handleScroll=this.handleScroll.bind(this),this.donePicking=this.donePicking.bind(this),this.render=this.render.bind(this),this.plugin.setPluginState(ri),this.registerRequestClient()}tearDown(){}resetPluginState(){this.plugin.setPluginState(ri)}async search(e){const{searchTerm:t}=this.plugin.getPluginState();if(!(e&&e===t)){this.setLoading(!0);try{const s=await this.provider.search(e);ni(this,Ce)[Ce](s,[])}catch(s){this.handleError(s)}finally{this.setLoading(!1)}}}clearSearch(){this.plugin.setPluginState({currentSelection:[],files:[],searchTerm:null})}async handleScroll(e){const t=this.nextPageQuery||null;if(this.shouldHandleScroll(e)&&t){this.isHandlingScroll=!0;try{const{files:s,searchTerm:n}=this.plugin.getPluginState(),a=await this.provider.search(n,t);ni(this,Ce)[Ce](a,s)}catch(s){this.handleError(s)}finally{this.isHandlingScroll=!1}}}donePicking(){const{currentSelection:e}=this.plugin.getPluginState();this.plugin.uppy.log("Adding remote search provider files"),this.plugin.uppy.addFiles(e.map(t=>this.getTagFile(t))),this.resetPluginState()}render(e,t){var s=this;t===void 0&&(t={});const{didFirstRender:n,isInputMode:a,searchTerm:r}=this.plugin.getPluginState(),{i18n:l}=this.plugin.uppy;n||this.preFirstRender();const u={...this.opts,...t},{files:d,folders:h,filterInput:c,loading:p,currentSelection:f}=this.plugin.getPluginState(),{isChecked:m,filterItems:b,recordShiftKeyPress:g}=this,v=c!=="",P={isChecked:m,toggleCheckbox:this.toggleCheckbox.bind(this),recordShiftKeyPress:g,currentSelection:f,files:v?b(d):d,folders:v?b(h):h,handleScroll:this.handleScroll,done:this.donePicking,cancel:this.cancelPicking,showSearchFilter:u.showFilter,search:this.search,clearSearch:this.clearSearch,searchTerm:r,searchOnInput:!1,searchInputLabel:l("search"),clearSearchLabel:l("resetSearch"),noResultsLabel:l("noSearchResults"),title:this.plugin.title,viewType:u.viewType,showTitles:u.showTitles,showFilter:u.showFilter,isLoading:p,showBreadcrumbs:u.showBreadcrumbs,pluginIcon:this.plugin.icon,i18n:l,uppyFiles:this.plugin.uppy.getFiles(),validateRestrictions:function(){return s.plugin.uppy.validateRestrictions(...arguments)}};return a?o(at,{onUnmount:this.resetPluginState},o("div",{className:"uppy-SearchProvider"},o(Hi,{search:this.search,inputLabel:l("enterTextToSearch"),buttonLabel:l("searchImages"),inputClassName:"uppy-c-textInput uppy-SearchProvider-input",buttonCSSClassName:"uppy-SearchProvider-searchButton",showButton:!0}))):o(at,{onUnmount:this.resetPluginState},o(Vi,P))}}function wn(i,e){this.nextPageQuery=i.nextPageQuery,i.items.forEach(t=>{e.push(t)}),this.plugin.setPluginState({currentSelection:[],isInputMode:!1,files:e,searchTerm:i.searchedFor})}vn.VERSION=yn.version;function ai(i,e,t,s){return t===0||i===e?i:s===0?e:i+(e-i)*2**(-s/t)}const X={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete"};function mt(i){const e=[];let t="indeterminate",s;for(const{progress:a}of Object.values(i)){const{preprocess:r,postprocess:l}=a;s==null&&(r||l)&&({mode:t,message:s}=r||l),(r==null?void 0:r.mode)==="determinate"&&e.push(r.value),(l==null?void 0:l.mode)==="determinate"&&e.push(l.value)}const n=e.reduce((a,r)=>a+r/e.length,0);return{mode:t,message:s,value:n}}function Pn(i){const e=Math.floor(i/3600)%24,t=Math.floor(i/60)%60,s=Math.floor(i%60);return{hours:e,minutes:t,seconds:s}}function Fn(i){const e=Pn(i),t=e.hours===0?"":`${e.hours}h`,s=e.minutes===0?"":`${e.hours===0?e.minutes:` ${e.minutes.toString(10).padStart(2,"0")}`}m`,n=e.hours!==0?"":`${e.minutes===0?e.seconds:` ${e.seconds.toString(10).padStart(2,"0")}`}s`;return`${t}${s}${n}`}const Sn="·",oi=()=>` ${Sn} `;function Cn(i){const{newFiles:e,isUploadStarted:t,recoveredState:s,i18n:n,uploadState:a,isSomeGhost:r,startUpload:l}=i,u=x("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--upload",{"uppy-c-btn-primary":a===X.STATE_WAITING},{"uppy-StatusBar-actionBtn--disabled":r}),d=e&&t&&!s?n("uploadXNewFiles",{smart_count:e}):n("uploadXFiles",{smart_count:e});return o("button",{type:"button",className:u,"aria-label":n("uploadXFiles",{smart_count:e}),onClick:l,disabled:r,"data-uppy-super-focusable":!0},d)}function Tn(i){const{i18n:e,uppy:t}=i;return o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--retry","aria-label":e("retryUpload"),onClick:()=>t.retryAll().catch(()=>{}),"data-uppy-super-focusable":!0,"data-cy":"retry"},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"8",height:"10",viewBox:"0 0 8 10"},o("path",{d:"M4 2.408a2.75 2.75 0 1 0 2.75 2.75.626.626 0 0 1 1.25.018v.023a4 4 0 1 1-4-4.041V.25a.25.25 0 0 1 .389-.208l2.299 1.533a.25.25 0 0 1 0 .416l-2.3 1.533A.25.25 0 0 1 4 3.316v-.908z"})),e("retry"))}function In(i){const{i18n:e,uppy:t}=i;return o("button",{type:"button",className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",title:e("cancel"),"aria-label":e("cancel"),onClick:()=>t.cancelAll(),"data-cy":"cancel","data-uppy-super-focusable":!0},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},o("g",{fill:"none",fillRule:"evenodd"},o("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),o("path",{fill:"#FFF",d:"M9.283 8l2.567 2.567-1.283 1.283L8 9.283 5.433 11.85 4.15 10.567 6.717 8 4.15 5.433 5.433 4.15 8 6.717l2.567-2.567 1.283 1.283z"}))))}function En(i){const{isAllPaused:e,i18n:t,isAllComplete:s,resumableUploads:n,uppy:a}=i,r=t(e?"resume":"pause");function l(){if(!s){if(!n){a.cancelAll();return}if(e){a.resumeAll();return}a.pauseAll()}}return o("button",{title:r,"aria-label":r,className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",type:"button",onClick:l,"data-cy":"togglePauseResume","data-uppy-super-focusable":!0},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},o("g",{fill:"none",fillRule:"evenodd"},o("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),o("path",{fill:"#FFF",d:e?"M6 4.25L11.5 8 6 11.75z":"M5 4.5h2v7H5v-7zm4 0h2v7H9v-7z"}))))}function An(i){const{i18n:e,doneButtonHandler:t}=i;return o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--done",onClick:t,"data-uppy-super-focusable":!0},e("done"))}function ji(){return o("svg",{className:"uppy-StatusBar-spinner","aria-hidden":"true",focusable:"false",width:"14",height:"14"},o("path",{d:"M13.983 6.547c-.12-2.509-1.64-4.893-3.939-5.936-2.48-1.127-5.488-.656-7.556 1.094C.524 3.367-.398 6.048.162 8.562c.556 2.495 2.46 4.52 4.94 5.183 2.932.784 5.61-.602 7.256-3.015-1.493 1.993-3.745 3.309-6.298 2.868-2.514-.434-4.578-2.349-5.153-4.84a6.226 6.226 0 0 1 2.98-6.778C6.34.586 9.74 1.1 11.373 3.493c.407.596.693 1.282.842 1.988.127.598.073 1.197.161 1.794.078.525.543 1.257 1.15.864.525-.341.49-1.05.456-1.592-.007-.15.02.3 0 0",fillRule:"evenodd"}))}function kn(i){const{progress:e}=i,{value:t,mode:s,message:n}=e,a="·";return o("div",{className:"uppy-StatusBar-content"},o(ji,null),s==="determinate"?`${Math.round(t*100)}% ${a} `:"",n)}function Dn(i){const{numUploads:e,complete:t,totalUploadedSize:s,totalSize:n,totalETA:a,i18n:r}=i,l=e>1;return o("div",{className:"uppy-StatusBar-statusSecondary"},l&&r("filesUploadedOfTotal",{complete:t,smart_count:e}),o("span",{className:"uppy-StatusBar-additionalInfo"},l&&oi(),r("dataUploadedOfTotal",{complete:Ot(s),total:Ot(n)}),oi(),r("xTimeLeft",{time:Fn(a)})))}function Gi(i){const{i18n:e,complete:t,numUploads:s}=i;return o("div",{className:"uppy-StatusBar-statusSecondary"},e("filesUploadedOfTotal",{complete:t,smart_count:s}))}function Bn(i){const{i18n:e,newFiles:t,startUpload:s}=i,n=x("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--uploadNewlyAdded");return o("div",{className:"uppy-StatusBar-statusSecondary"},o("div",{className:"uppy-StatusBar-statusSecondaryHint"},e("xMoreFilesAdded",{smart_count:t})),o("button",{type:"button",className:n,"aria-label":e("uploadXFiles",{smart_count:t}),onClick:s},e("upload")))}function On(i){const{i18n:e,supportsUploadProgress:t,totalProgress:s,showProgressDetails:n,isUploadStarted:a,isAllComplete:r,isAllPaused:l,newFiles:u,numUploads:d,complete:h,totalUploadedSize:c,totalSize:p,totalETA:f,startUpload:m}=i,b=u&&a;if(!a||r)return null;const g=e(l?"paused":"uploading");function v(){return!l&&!b&&n?t?o(Dn,{numUploads:d,complete:h,totalUploadedSize:c,totalSize:p,totalETA:f,i18n:e}):o(Gi,{i18n:e,complete:h,numUploads:d}):null}return o("div",{className:"uppy-StatusBar-content","aria-label":g,title:g},l?null:o(ji,null),o("div",{className:"uppy-StatusBar-status"},o("div",{className:"uppy-StatusBar-statusPrimary"},t?`${g}: ${s}%`:g),v(),b?o(Bn,{i18n:e,newFiles:u,startUpload:m}):null))}function Nn(i){const{i18n:e}=i;return o("div",{className:"uppy-StatusBar-content",role:"status",title:e("complete")},o("div",{className:"uppy-StatusBar-status"},o("div",{className:"uppy-StatusBar-statusPrimary"},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"15",height:"11",viewBox:"0 0 15 11"},o("path",{d:"M.414 5.843L1.627 4.63l3.472 3.472L13.202 0l1.212 1.213L5.1 10.528z"})),e("complete"))))}function _n(i){const{error:e,i18n:t,complete:s,numUploads:n}=i;function a(){const r=`${t("uploadFailed")} 

 ${e}`;alert(r)}return o("div",{className:"uppy-StatusBar-content",title:t("uploadFailed")},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"11",height:"11",viewBox:"0 0 11 11"},o("path",{d:"M4.278 5.5L0 1.222 1.222 0 5.5 4.278 9.778 0 11 1.222 6.722 5.5 11 9.778 9.778 11 5.5 6.722 1.222 11 0 9.778z"})),o("div",{className:"uppy-StatusBar-status"},o("div",{className:"uppy-StatusBar-statusPrimary"},t("uploadFailed"),o("button",{className:"uppy-u-reset uppy-StatusBar-details","aria-label":t("showErrorDetails"),"data-microtip-position":"top-right","data-microtip-size":"medium",onClick:a,type:"button"},"?")),o(Gi,{i18n:t,complete:s,numUploads:n})))}const{STATE_ERROR:li,STATE_WAITING:ui,STATE_PREPROCESSING:gt,STATE_UPLOADING:Ve,STATE_POSTPROCESSING:yt,STATE_COMPLETE:qe}=X;function Qi(i){const{newFiles:e,allowNewUpload:t,isUploadInProgress:s,isAllPaused:n,resumableUploads:a,error:r,hideUploadButton:l,hidePauseResumeButton:u,hideCancelButton:d,hideRetryButton:h,recoveredState:c,uploadState:p,totalProgress:f,files:m,supportsUploadProgress:b,hideAfterFinish:g,isSomeGhost:v,doneButtonHandler:P,isUploadStarted:w,i18n:S,startUpload:T,uppy:R,isAllComplete:I,showProgressDetails:D,numUploads:N,complete:me,totalSize:Pe,totalETA:ae,totalUploadedSize:oe}=i;function Q(){switch(p){case yt:case gt:{const He=mt(m);return He.mode==="determinate"?He.value*100:f}case li:return null;case Ve:return b?f:null;default:return f}}function le(){switch(p){case yt:case gt:{const{mode:He}=mt(m);return He==="indeterminate"}case Ve:return!b;default:return!1}}function dt(){if(c)return!1;switch(p){case ui:return l||e===0;case qe:return g;default:return!1}}const Fe=Q(),bs=dt(),ht=Fe??100,vs=!r&&e&&!s&&!n&&t&&!l,ws=!d&&p!==ui&&p!==qe,Ps=a&&!u&&p===Ve,Fs=r&&!I&&!h,Ss=P&&p===qe,Cs=x("uppy-StatusBar-progress",{"is-indeterminate":le()}),Ts=x("uppy-StatusBar",`is-${p}`,{"has-ghosts":v});return o("div",{className:Ts,"aria-hidden":bs},o("div",{className:Cs,style:{width:`${ht}%`},role:"progressbar","aria-label":`${ht}%`,"aria-valuetext":`${ht}%`,"aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":Fe}),(()=>{switch(p){case gt:case yt:return o(kn,{progress:mt(m)});case qe:return o(Nn,{i18n:S});case li:return o(_n,{error:r,i18n:S,numUploads:N,complete:me});case Ve:return o(On,{i18n:S,supportsUploadProgress:b,totalProgress:f,showProgressDetails:D,isUploadStarted:w,isAllComplete:I,isAllPaused:n,newFiles:e,numUploads:N,complete:me,totalUploadedSize:oe,totalSize:Pe,totalETA:ae,startUpload:T});default:return null}})(),o("div",{className:"uppy-StatusBar-actions"},c||vs?o(Cn,{newFiles:e,isUploadStarted:w,recoveredState:c,i18n:S,isSomeGhost:v,startUpload:T,uploadState:p}):null,Fs?o(Tn,{i18n:S,uppy:R}):null,Ps?o(En,{isAllPaused:n,i18n:S,isAllComplete:I,resumableUploads:a,uppy:R}):null,ws?o(In,{i18n:S,uppy:R}):null,Ss?o(An,{i18n:S,doneButtonHandler:P}):null))}Qi.defaultProps={doneButtonHandler:void 0,hideAfterFinish:!1,hideCancelButton:!1,hidePauseResumeButton:!1,hideRetryButton:!1,hideUploadButton:void 0,showProgressDetails:void 0};const xn={strings:{uploading:"Uploading",complete:"Complete",uploadFailed:"Upload failed",paused:"Paused",retry:"Retry",cancel:"Cancel",pause:"Pause",resume:"Resume",done:"Done",filesUploadedOfTotal:{0:"%{complete} of %{smart_count} file uploaded",1:"%{complete} of %{smart_count} files uploaded"},dataUploadedOfTotal:"%{complete} of %{total}",xTimeLeft:"%{time} left",uploadXFiles:{0:"Upload %{smart_count} file",1:"Upload %{smart_count} files"},uploadXNewFiles:{0:"Upload +%{smart_count} file",1:"Upload +%{smart_count} files"},upload:"Upload",retryUpload:"Retry upload",xMoreFilesAdded:{0:"%{smart_count} more file added",1:"%{smart_count} more files added"},showErrorDetails:"Show error details"}};function A(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var Rn=0;function we(i){return"__private_"+Rn+++"_"+i}const Un={version:"3.3.3"},Ln=2e3,Mn=2e3;function zn(i,e,t,s){if(i)return X.STATE_ERROR;if(e)return X.STATE_COMPLETE;if(t)return X.STATE_WAITING;let n=X.STATE_WAITING;const a=Object.keys(s);for(let r=0;r<a.length;r++){const{progress:l}=s[a[r]];if(l.uploadStarted&&!l.uploadComplete)return X.STATE_UPLOADING;l.preprocess&&(n=X.STATE_PREPROCESSING),l.postprocess&&n!==X.STATE_PREPROCESSING&&(n=X.STATE_POSTPROCESSING)}return n}const $n={target:"body",hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideCancelButton:!1,showProgressDetails:!1,hideAfterFinish:!0,doneButtonHandler:null};var $=we("lastUpdateTime"),q=we("previousUploadedBytes"),ee=we("previousSpeed"),M=we("previousETA"),bt=we("computeSmoothETA"),Te=we("onUploadStart");class Ki extends lt{constructor(e,t){super(e,{...$n,...t}),Object.defineProperty(this,bt,{value:Hn}),Object.defineProperty(this,$,{writable:!0,value:void 0}),Object.defineProperty(this,q,{writable:!0,value:void 0}),Object.defineProperty(this,ee,{writable:!0,value:void 0}),Object.defineProperty(this,M,{writable:!0,value:void 0}),this.startUpload=()=>this.uppy.upload().catch(()=>{}),Object.defineProperty(this,Te,{writable:!0,value:()=>{const{recoveredState:s}=this.uppy.getState();if(A(this,ee)[ee]=null,A(this,M)[M]=null,s){A(this,q)[q]=Object.values(s.files).reduce((n,a)=>{let{progress:r}=a;return n+r.bytesUploaded},0),this.uppy.emit("restore-confirmed");return}A(this,$)[$]=performance.now(),A(this,q)[q]=0}}),this.id=this.opts.id||"StatusBar",this.title="StatusBar",this.type="progressindicator",this.defaultLocale=xn,this.i18nInit(),this.render=this.render.bind(this),this.install=this.install.bind(this)}render(e){const{capabilities:t,files:s,allowNewUpload:n,totalProgress:a,error:r,recoveredState:l}=e,{newFiles:u,startedFiles:d,completeFiles:h,isUploadStarted:c,isAllComplete:p,isAllErrored:f,isAllPaused:m,isUploadInProgress:b,isSomeGhost:g}=this.uppy.getObjectOfFilesPerState(),v=l?Object.values(s):u,P=!!t.resumableUploads,w=t.uploadProgress!==!1;let S=0,T=0;d.forEach(I=>{S+=I.progress.bytesTotal||0,T+=I.progress.bytesUploaded||0});const R=A(this,bt)[bt]({uploaded:T,total:S,remaining:S-T});return Qi({error:r,uploadState:zn(r,p,l,e.files||{}),allowNewUpload:n,totalProgress:a,totalSize:S,totalUploadedSize:T,isAllComplete:!1,isAllPaused:m,isAllErrored:f,isUploadStarted:c,isUploadInProgress:b,isSomeGhost:g,recoveredState:l,complete:h.length,newFiles:v.length,numUploads:d.length,totalETA:R,files:s,i18n:this.i18n,uppy:this.uppy,startUpload:this.startUpload,doneButtonHandler:this.opts.doneButtonHandler,resumableUploads:P,supportsUploadProgress:w,showProgressDetails:this.opts.showProgressDetails,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,hideAfterFinish:this.opts.hideAfterFinish,isTargetDOMEl:this.isTargetDOMEl})}onMount(){const e=this.el;As(e)||(e.dir="ltr")}install(){const{target:e}=this.opts;e&&this.mount(e,this),this.uppy.on("upload",A(this,Te)[Te]),A(this,$)[$]=performance.now(),A(this,q)[q]=this.uppy.getFiles().reduce((t,s)=>t+s.progress.bytesUploaded,0)}uninstall(){this.unmount(),this.uppy.off("upload",A(this,Te)[Te])}}function Hn(i){var e,t;if(i.total===0||i.remaining===0)return 0;(t=(e=A(this,$))[$])!=null||(e[$]=performance.now());const s=performance.now()-A(this,$)[$];if(s===0){var n;return Math.round(((n=A(this,M)[M])!=null?n:0)/100)/10}const a=i.uploaded-A(this,q)[q];if(A(this,q)[q]=i.uploaded,a<=0){var r;return Math.round(((r=A(this,M)[M])!=null?r:0)/100)/10}const l=a/s,u=A(this,ee)[ee]==null?l:ai(l,A(this,ee)[ee],Ln,s);A(this,ee)[ee]=u;const d=i.remaining/u,h=Math.max(A(this,M)[M]-s,0),c=A(this,M)[M]==null?d:ai(d,h,Mn,s);return A(this,M)[M]=c,A(this,$)[$]=performance.now(),Math.round(c/100)/10}Ki.VERSION=Un.version;const di=300;class Vn extends ve{constructor(){super(...arguments),this.ref=Ds()}componentWillEnter(e){this.ref.current.style.opacity="1",this.ref.current.style.transform="none",setTimeout(e,di)}componentWillLeave(e){this.ref.current.style.opacity="0",this.ref.current.style.transform="translateY(350%)",setTimeout(e,di)}render(){const{children:e}=this.props;return o("div",{className:"uppy-Informer-animated",ref:this.ref},e)}}function qn(i,e){return Object.assign(i,e)}function Wn(i,e){var t;return(t=i==null?void 0:i.key)!=null?t:e}function jn(i,e){const t=i._ptgLinkedRefs||(i._ptgLinkedRefs={});return t[e]||(t[e]=s=>{i.refs[e]=s})}function Ie(i){const e={};for(let t=0;t<i.length;t++)if(i[t]!=null){const s=Wn(i[t],t.toString(36));e[s]=i[t]}return e}function Gn(i,e){i=i||{},e=e||{};const t=r=>e.hasOwnProperty(r)?e[r]:i[r],s={};let n=[];for(const r in i)e.hasOwnProperty(r)?n.length&&(s[r]=n,n=[]):n.push(r);const a={};for(const r in e){if(s.hasOwnProperty(r))for(let l=0;l<s[r].length;l++){const u=s[r][l];a[s[r][l]]=t(u)}a[r]=t(r)}for(let r=0;r<n.length;r++)a[n[r]]=t(n[r]);return a}const Qn=i=>i;class Xi extends ve{constructor(e,t){super(e,t),this.refs={},this.state={children:Ie(te(te(this.props.children))||[])},this.performAppear=this.performAppear.bind(this),this.performEnter=this.performEnter.bind(this),this.performLeave=this.performLeave.bind(this)}componentWillMount(){this.currentlyTransitioningKeys={},this.keysToAbortLeave=[],this.keysToEnter=[],this.keysToLeave=[]}componentDidMount(){const e=this.state.children;for(const t in e)e[t]&&this.performAppear(t)}componentWillReceiveProps(e){const t=Ie(te(e.children)||[]),s=this.state.children;this.setState(a=>({children:Gn(a.children,t)}));let n;for(n in t)if(t.hasOwnProperty(n)){const a=s&&s.hasOwnProperty(n);t[n]&&a&&this.currentlyTransitioningKeys[n]?(this.keysToEnter.push(n),this.keysToAbortLeave.push(n)):t[n]&&!a&&!this.currentlyTransitioningKeys[n]&&this.keysToEnter.push(n)}for(n in s)if(s.hasOwnProperty(n)){const a=t&&t.hasOwnProperty(n);s[n]&&!a&&!this.currentlyTransitioningKeys[n]&&this.keysToLeave.push(n)}}componentDidUpdate(){const{keysToEnter:e}=this;this.keysToEnter=[],e.forEach(this.performEnter);const{keysToLeave:t}=this;this.keysToLeave=[],t.forEach(this.performLeave)}_finishAbort(e){const t=this.keysToAbortLeave.indexOf(e);t!==-1&&this.keysToAbortLeave.splice(t,1)}performAppear(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillAppear?t.componentWillAppear(this._handleDoneAppearing.bind(this,e)):this._handleDoneAppearing(e)}_handleDoneAppearing(e){const t=this.refs[e];t!=null&&t.componentDidAppear&&t.componentDidAppear(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=Ie(te(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performEnter(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillEnter?t.componentWillEnter(this._handleDoneEntering.bind(this,e)):this._handleDoneEntering(e)}_handleDoneEntering(e){const t=this.refs[e];t!=null&&t.componentDidEnter&&t.componentDidEnter(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=Ie(te(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performLeave(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;this.currentlyTransitioningKeys[e]=!0;const s=this.refs[e];s!=null&&s.componentWillLeave?s.componentWillLeave(this._handleDoneLeaving.bind(this,e)):this._handleDoneLeaving(e)}_handleDoneLeaving(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;const s=this.refs[e];s!=null&&s.componentDidLeave&&s.componentDidLeave(),delete this.currentlyTransitioningKeys[e];const n=Ie(te(this.props.children)||[]);if(n&&n.hasOwnProperty(e))this.performEnter(e);else{const a=qn({},this.state.children);delete a[e],this.setState({children:a})}}render(e,t){let{childFactory:s,transitionLeave:n,transitionName:a,transitionAppear:r,transitionEnter:l,transitionLeaveTimeout:u,transitionEnterTimeout:d,transitionAppearTimeout:h,component:c,...p}=e,{children:f}=t;const m=Object.entries(f).map(b=>{let[g,v]=b;if(!v)return;const P=jn(this,g);return Ni(s(v),{ref:P,key:g})}).filter(Boolean);return o(c,p,m)}}Xi.defaultProps={component:"span",childFactory:Qn};const Kn={version:"3.1.0"};class Yi extends lt{constructor(e,t){super(e,t),this.render=s=>o("div",{className:"uppy uppy-Informer"},o(Xi,null,s.info.map(n=>o(Vn,{key:n.message},o("p",{role:"alert"},n.message," ",n.details&&o("span",{"aria-label":n.details,"data-microtip-position":"top-left","data-microtip-size":"medium",role:"tooltip",onClick:()=>alert(`${n.message} 

 ${n.details}`)},"?")))))),this.type="progressindicator",this.id=this.opts.id||"Informer",this.title="Informer"}install(){const{target:e}=this.opts;e&&this.mount(e,this)}}Yi.VERSION=Kn.version;const Xn=/^data:([^/]+\/[^,;]+(?:[^,]*?))(;base64)?,([\s\S]*)$/;function Yn(i,e,t){var s,n;const a=Xn.exec(i),r=(s=(n=e.mimeType)!=null?n:a==null?void 0:a[1])!=null?s:"plain/text";let l;if((a==null?void 0:a[2])!=null){const u=atob(decodeURIComponent(a[3])),d=new Uint8Array(u.length);for(let h=0;h<u.length;h++)d[h]=u.charCodeAt(h);l=[d]}else(a==null?void 0:a[3])!=null&&(l=[decodeURIComponent(a[3])]);return t?new File(l,e.name||"",{type:r}):new Blob(l,{type:r})}function hi(i){return i.startsWith("blob:")}function ci(i){return i?/^[^/]+\/(jpe?g|gif|png|svg|svg\+xml|bmp|webp|avif)$/.test(i):!1}function F(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}var Ji=typeof self<"u"?self:global;const ze=typeof navigator<"u",Jn=ze&&typeof HTMLImageElement>"u",pi=!(typeof global>"u"||typeof process>"u"||!process.versions||!process.versions.node),Zi=Ji.Buffer,es=!!Zi,Zn=i=>i!==void 0;function ts(i){return i===void 0||(i instanceof Map?i.size===0:Object.values(i).filter(Zn).length===0)}function O(i){let e=new Error(i);throw delete e.stack,e}function fi(i){let e=function(t){let s=0;return t.ifd0.enabled&&(s+=1024),t.exif.enabled&&(s+=2048),t.makerNote&&(s+=2048),t.userComment&&(s+=1024),t.gps.enabled&&(s+=512),t.interop.enabled&&(s+=100),t.ifd1.enabled&&(s+=1024),s+2048}(i);return i.jfif.enabled&&(e+=50),i.xmp.enabled&&(e+=2e4),i.iptc.enabled&&(e+=14e3),i.icc.enabled&&(e+=6e3),e}const vt=i=>String.fromCharCode.apply(null,i),mi=typeof TextDecoder<"u"?new TextDecoder("utf-8"):void 0;class j{static from(e,t){return e instanceof this&&e.le===t?e:new j(e,void 0,void 0,t)}constructor(e,t=0,s,n){if(typeof n=="boolean"&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),e===0)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){s===void 0&&(s=e.byteLength-t);let a=new DataView(e,t,s);this._swapDataView(a)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof j){s===void 0&&(s=e.byteLength-t),(t+=e.byteOffset)+s>e.byteOffset+e.byteLength&&O("Creating view outside of available memory in ArrayBuffer");let a=new DataView(e.buffer,t,s);this._swapDataView(a)}else if(typeof e=="number"){let a=new DataView(new ArrayBuffer(e));this._swapDataView(a)}else O("Invalid input argument for BufferView: "+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,s=j){return e instanceof DataView||e instanceof j?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||O("BufferView.set(): Invalid data argument."),this.toUint8().set(e,t),new s(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new j(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return n=this.getUint8Array(e,t),mi?mi.decode(n):es?Buffer.from(n).toString("utf8"):decodeURIComponent(escape(vt(n)));var n}getLatin1String(e=0,t=this.byteLength){let s=this.getUint8Array(e,t);return vt(s)}getUnicodeString(e=0,t=this.byteLength){const s=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)s.push(this.getUint16(e+n));return vt(s)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,s){switch(t){case 1:return this.getUint8(e,s);case 2:return this.getUint16(e,s);case 4:return this.getUint32(e,s);case 8:return this.getUint64&&this.getUint64(e,s)}}getUint(e,t,s){switch(t){case 8:return this.getUint8(e,s);case 16:return this.getUint16(e,s);case 32:return this.getUint32(e,s);case 64:return this.getUint64&&this.getUint64(e,s)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function xt(i,e){O(`${i} '${e}' was not loaded, try using full build of exifr.`)}class $t extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||xt(this.kind,e),t&&(e in t||function(s,n){O(`Unknown ${s} '${n}'.`)}(this.kind,e),t[e].enabled||xt(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var is=new $t("file parser"),W=new $t("segment parser"),Ht=new $t("file reader");let er=Ji.fetch;function gi(i,e){return(t=i).startsWith("data:")||t.length>1e4?Ut(i,e,"base64"):pi&&i.includes("://")?Rt(i,e,"url",yi):pi?Ut(i,e,"fs"):ze?Rt(i,e,"url",yi):void O("Invalid input argument");var t}async function Rt(i,e,t,s){return Ht.has(t)?Ut(i,e,t):s?async function(n,a){let r=await a(n);return new j(r)}(i,s):void O(`Parser ${t} is not loaded`)}async function Ut(i,e,t){let s=new(Ht.get(t))(i,e);return await s.read(),s}const yi=i=>er(i).then(e=>e.arrayBuffer()),Lt=i=>new Promise((e,t)=>{let s=new FileReader;s.onloadend=()=>e(s.result||new ArrayBuffer),s.onerror=t,s.readAsArrayBuffer(i)}),Vt=new Map,tr=new Map,ir=new Map,We=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],ss=["jfif","xmp","icc","iptc","ihdr"],Mt=["tiff",...ss],B=["ifd0","ifd1","exif","gps","interop"],je=[...Mt,...B],Ge=["makerNote","userComment"],ns=["translateKeys","translateValues","reviveValues","multiSegment"],Qe=[...ns,"sanitize","mergeOutput","silentErrors"];class rs{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class Ee extends rs{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,s,n){if(super(),F(this,"enabled",!1),F(this,"skip",new Set),F(this,"pick",new Set),F(this,"deps",new Set),F(this,"translateKeys",!1),F(this,"translateValues",!1),F(this,"reviveValues",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=B.includes(e),this.canBeFiltered&&(this.dict=Vt.get(e)),s!==void 0)if(Array.isArray(s))this.parse=this.enabled=!0,this.canBeFiltered&&s.length>0&&this.translateTagSet(s,this.pick);else if(typeof s=="object"){if(this.enabled=!0,this.parse=s.parse!==!1,this.canBeFiltered){let{pick:a,skip:r}=s;a&&a.length>0&&this.translateTagSet(a,this.pick),r&&r.length>0&&this.translateTagSet(r,this.skip)}this.applyInheritables(s)}else s===!0||s===!1?this.parse=this.enabled=s:O(`Invalid options argument: ${s}`)}applyInheritables(e){let t,s;for(t of ns)s=e[t],s!==void 0&&(this[t]=s)}translateTagSet(e,t){if(this.dict){let s,n,{tagKeys:a,tagValues:r}=this.dict;for(s of e)typeof s=="string"?(n=r.indexOf(s),n===-1&&(n=a.indexOf(Number(s))),n!==-1&&t.add(Number(a[n]))):t.add(s)}else for(let s of e)t.add(s)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,ot(this.pick,this.deps)):this.enabled&&this.pick.size>0&&ot(this.pick,this.deps)}}var _={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},bi=new Map;class qt extends rs{static useCached(e){let t=bi.get(e);return t!==void 0||(t=new this(e),bi.set(e,t)),t}constructor(e){super(),e===!0?this.setupFromTrue():e===void 0?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):typeof e=="object"?this.setupFromObject(e):O(`Invalid options argument ${e}`),this.firstChunkSize===void 0&&(this.firstChunkSize=ze?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of We)this[e]=_[e];for(e of Qe)this[e]=_[e];for(e of Ge)this[e]=_[e];for(e of je)this[e]=new Ee(e,_[e],void 0,this)}setupFromTrue(){let e;for(e of We)this[e]=_[e];for(e of Qe)this[e]=_[e];for(e of Ge)this[e]=!0;for(e of je)this[e]=new Ee(e,!0,void 0,this)}setupFromArray(e){let t;for(t of We)this[t]=_[t];for(t of Qe)this[t]=_[t];for(t of Ge)this[t]=_[t];for(t of je)this[t]=new Ee(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,B)}setupFromObject(e){let t;for(t of(B.ifd0=B.ifd0||B.image,B.ifd1=B.ifd1||B.thumbnail,Object.assign(this,e),We))this[t]=wt(e[t],_[t]);for(t of Qe)this[t]=wt(e[t],_[t]);for(t of Ge)this[t]=wt(e[t],_[t]);for(t of Mt)this[t]=new Ee(t,_[t],e[t],this);for(t of B)this[t]=new Ee(t,_[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,B,je),e.tiff===!0?this.batchEnableWithBool(B,!0):e.tiff===!1?this.batchEnableWithUserValue(B,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,B):typeof e.tiff=="object"&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,B)}batchEnableWithBool(e,t){for(let s of e)this[s].enabled=t}batchEnableWithUserValue(e,t){for(let s of e){let n=t[s];this[s].enabled=n!==!1&&n!==void 0}}setupGlobalFilters(e,t,s,n=s){if(e&&e.length){for(let r of n)this[r].enabled=!1;let a=vi(e,s);for(let[r,l]of a)ot(this[r].pick,l),this[r].enabled=!0}else if(t&&t.length){let a=vi(t,s);for(let[r,l]of a)ot(this[r].skip,l)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:s,iptc:n,icc:a}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),s.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),a.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:s,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),s.needed&&e.deps.add(34853),this.tiff.enabled=B.some(a=>this[a].enabled===!0)||this.makerNote||this.userComment;for(let a of B)this[a].finalizeFilters()}get onlyTiff(){return!ss.map(e=>this[e].enabled).some(e=>e===!0)&&this.tiff.enabled}checkLoadedPlugins(){for(let e of Mt)this[e].enabled&&!W.has(e)&&xt("segment parser",e)}}function vi(i,e){let t,s,n,a,r=[];for(n of e){for(a of(t=Vt.get(n),s=[],t))(i.includes(a[0])||i.includes(a[1]))&&s.push(a[0]);s.length&&r.push([n,s])}return r}function wt(i,e){return i!==void 0?i:e!==void 0?e:void 0}function ot(i,e){for(let t of e)i.add(t)}F(qt,"default",_);class sr{constructor(e){F(this,"parsers",{}),F(this,"output",{}),F(this,"errors",[]),F(this,"pushToErrors",t=>this.errors.push(t)),this.options=qt.useCached(e)}async read(e){this.file=await function(t,s){return typeof t=="string"?gi(t,s):ze&&!Jn&&t instanceof HTMLImageElement?gi(t.src,s):t instanceof Uint8Array||t instanceof ArrayBuffer||t instanceof DataView?new j(t):ze&&t instanceof Blob?Rt(t,s,"blob",Lt):void O("Invalid input argument")}(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[s,n]of is)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[s]=!0;this.file.close&&this.file.close(),O("Unknown file format")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),ts(s=e)?void 0:s;var s}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map(async s=>{let n=await s.parse();s.assignToOutput(e,n)});this.options.silentErrors&&(t=t.map(s=>s.catch(this.pushToErrors))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,s=W.get("tiff",e);var n;if(t.tiff?n={start:0,type:"tiff"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment("tiff")),n===void 0)return;let a=await this.fileParser.ensureSegmentChunk(n),r=this.parsers.tiff=new s(a,e,t),l=await r.extractThumbnail();return t.close&&t.close(),l}}class ye{static findPosition(e,t){let s=e.getUint16(t+2)+2,n=typeof this.headerLength=="function"?this.headerLength(e,t,s):this.headerLength,a=t+n,r=s-n;return{offset:t,length:s,headerLength:n,start:a,size:r,end:a+r}}static parse(e,t={}){return new this(e,new qt({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof j?e:new j(e)}constructor(e,t={},s){F(this,"errors",[]),F(this,"raw",new Map),F(this,"handleError",n=>{if(!this.options.silentErrors)throw n;this.errors.push(n.message)}),this.chunk=this.normalizeInput(e),this.file=s,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let s=ir.get(t),n=tr.get(t),a=Vt.get(t),r=this.options[t],l=r.reviveValues&&!!s,u=r.translateValues&&!!n,d=r.translateKeys&&!!a,h={};for(let[c,p]of e)l&&s.has(c)?p=s.get(c)(p):u&&n.has(c)&&(p=this.translateValue(p,n.get(c))),d&&a.has(c)&&(c=a.get(c)||c),h[c]=p;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,s){if(this.globalOptions.mergeOutput)return Object.assign(e,s);e[t]?Object.assign(e[t],s):e[t]=s}}F(ye,"headerLength",4),F(ye,"type",void 0),F(ye,"multiSegment",!1),F(ye,"canHandle",()=>!1);function nr(i){return i===192||i===194||i===196||i===219||i===221||i===218||i===254}function rr(i){return i>=224&&i<=239}function ar(i,e,t){for(let[s,n]of W)if(n.canHandle(i,e,t))return s}class wi extends class{constructor(e,t,s){F(this,"errors",[]),F(this,"ensureSegmentChunk",async n=>{let a=n.start,r=n.size||65536;if(this.file.chunked)if(this.file.available(a,r))n.chunk=this.file.subarray(a,r);else try{n.chunk=await this.file.readChunk(a,r)}catch(l){O(`Couldn't read segment: ${JSON.stringify(n)}. ${l.message}`)}else this.file.byteLength>a+r?n.chunk=this.file.subarray(a,r):n.size===void 0?n.chunk=this.file.subarray(a):O("Segment unreachable: "+JSON.stringify(n));return n.chunk}),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=s}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let s=new(W.get(e))(t,this.options,this.file);return this.parsers[e]=s}createParsers(e){for(let t of e){let{type:s,chunk:n}=t,a=this.options[s];if(a&&a.enabled){let r=this.parsers[s];r&&r.append||r||this.createParser(s,n)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}{constructor(...e){super(...e),F(this,"appSegments",[]),F(this,"jpegSegments",[]),F(this,"unknownSegments",[])}static canHandle(e,t){return t===65496}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){e===!0?(this.findAll=!0,this.wanted=new Set(W.keyList())):(e=e===void 0?W.keyList().filter(t=>this.options[t].enabled):e.filter(t=>this.options[t].enabled&&W.has(t)),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:s,findAll:n,wanted:a,remaining:r}=this;if(!n&&this.file.chunked&&(n=Array.from(a).some(l=>{let u=W.get(l),d=this.options[l];return u.multiSegment&&d.multiSegment}),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,s.byteLength),!this.options.onlyTiff&&s.chunked){let l=!1;for(;r.size>0&&!l&&(s.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:u}=s,d=this.appSegments.some(h=>!this.file.available(h.offset||h.start,h.length||h.size));if(l=e>u&&!d?!await s.readNextChunk(e):!await s.readNextChunk(u),(e=this.findAppSegmentsInRange(e,s.byteLength))===void 0)return}}}findAppSegmentsInRange(e,t){t-=2;let s,n,a,r,l,u,{file:d,findAll:h,wanted:c,remaining:p,options:f}=this;for(;e<t;e++)if(d.getUint8(e)===255){if(s=d.getUint8(e+1),rr(s)){if(n=d.getUint16(e+2),a=ar(d,e,n),a&&c.has(a)&&(r=W.get(a),l=r.findPosition(d,e),u=f[a],l.type=a,this.appSegments.push(l),!h&&(r.multiSegment&&u.multiSegment?(this.unfinishedMultiSegment=l.chunkNumber<l.chunkCount,this.unfinishedMultiSegment||p.delete(a)):p.delete(a),p.size===0)))break;f.recordUnknownSegments&&(l=ye.findPosition(d,e),l.marker=s,this.unknownSegments.push(l)),e+=n+1}else if(nr(s)){if(n=d.getUint16(e+2),s===218&&f.stopAfterSos!==!1)return;f.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:s}),e+=n+1}}return e}mergeMultiSegments(){if(!this.appSegments.some(t=>t.multiSegment))return;let e=function(t,s){let n,a,r,l=new Map;for(let u=0;u<t.length;u++)n=t[u],a=n[s],l.has(a)?r=l.get(a):l.set(a,r=[]),r.push(n);return Array.from(l)}(this.appSegments,"type");this.mergedAppSegments=e.map(([t,s])=>{let n=W.get(t,this.options);return n.handleMultiSegments?{type:t,chunk:n.handleMultiSegments(s)}:s[0]})}getSegment(e){return this.appSegments.find(t=>t.type===e)}async getOrFindSegment(e){let t=this.getSegment(e);return t===void 0&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}F(wi,"type","jpeg"),is.set("jpeg",wi);const or=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class lr extends ye{parseHeader(){var e=this.chunk.getUint16();e===18761?this.le=!0:e===19789&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,s=new Map){let{pick:n,skip:a}=this.options[t];n=new Set(n);let r=n.size>0,l=a.size===0,u=this.chunk.getUint16(e);e+=2;for(let d=0;d<u;d++){let h=this.chunk.getUint16(e);if(r){if(n.has(h)&&(s.set(h,this.parseTag(e,h,t)),n.delete(h),n.size===0))break}else!l&&a.has(h)||s.set(h,this.parseTag(e,h,t));e+=12}return s}parseTag(e,t,s){let{chunk:n}=this,a=n.getUint16(e+2),r=n.getUint32(e+4),l=or[a];if(l*r<=4?e+=8:e=n.getUint32(e+8),(a<1||a>13)&&O(`Invalid TIFF value type. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${a}, offset ${e}`),e>n.byteLength&&O(`Invalid TIFF value offset. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${a}, offset ${e} is outside of chunk size ${n.byteLength}`),a===1)return n.getUint8Array(e,r);if(a===2)return(u=function(d){for(;d.endsWith("\0");)d=d.slice(0,-1);return d}(u=n.getString(e,r)).trim())===""?void 0:u;var u;if(a===7)return n.getUint8Array(e,r);if(r===1)return this.parseTagValue(a,e);{let d=new(function(c){switch(c){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(a))(r),h=l;for(let c=0;c<r;c++)d[c]=this.parseTagValue(a,e),e+=h;return d}}parseTagValue(e,t){let{chunk:s}=this;switch(e){case 1:return s.getUint8(t);case 3:return s.getUint16(t);case 4:return s.getUint32(t);case 5:return s.getUint32(t)/s.getUint32(t+4);case 6:return s.getInt8(t);case 8:return s.getInt16(t);case 9:return s.getInt32(t);case 10:return s.getInt32(t)/s.getInt32(t+4);case 11:return s.getFloat(t);case 12:return s.getDouble(t);case 13:return s.getUint32(t);default:O(`Invalid tiff type ${e}`)}}}class Pt extends lr{static canHandle(e,t){return e.getUint8(t+1)===225&&e.getUint32(t+4)===1165519206&&e.getUint16(t+8)===0}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse("parseExifBlock"),e.gps.enabled&&await this.safeParse("parseGpsBlock"),e.interop.enabled&&await this.safeParse("parseInteropBlock"),e.ifd1.enabled&&await this.safeParse("parseThumbnailBlock"),this.createOutput()}safeParse(e){let t=this[e]();return t.catch!==void 0&&(t=t.catch(this.handleError)),t}findIfd0Offset(){this.ifd0Offset===void 0&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(this.ifd1Offset===void 0){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let s=new Map;return this[t]=s,this.parseTags(e,t,s),s}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&O("Malformed EXIF data"),!e.chunked&&this.ifd0Offset>e.byteLength&&O(`IFD0 offset points to outside of file.
this.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,fi(this.options));let t=this.parseBlock(this.ifd0Offset,"ifd0");return t.size!==0?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif||(this.ifd0||await this.parseIfd0Block(),this.exifOffset===void 0))return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,fi(this.options));let e=this.parseBlock(this.exifOffset,"exif");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let s=e.get(t);s&&s.length===1&&e.set(t,s[0])}async parseGpsBlock(){if(this.gps||(this.ifd0||await this.parseIfd0Block(),this.gpsOffset===void 0))return;let e=this.parseBlock(this.gpsOffset,"gps");return e&&e.has(2)&&e.has(4)&&(e.set("latitude",Pi(...e.get(2),e.get(1))),e.set("longitude",Pi(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),this.interopOffset!==void 0||this.exif||await this.parseExifBlock(),this.interopOffset!==void 0))return this.parseBlock(this.interopOffset,"interop")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,"ifd1"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),this.ifd1===void 0)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,s,n={};for(t of B)if(e=this[t],!ts(e))if(s=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(t==="ifd1")continue;Object.assign(n,s)}else n[t]=s;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[s,n]of Object.entries(t))this.assignObjectToOutput(e,s,n)}}function Pi(i,e,t,s){var n=i+e/60+t/3600;return s!=="S"&&s!=="W"||(n*=-1),n}F(Pt,"type","tiff"),F(Pt,"headerLength",10),W.set("tiff",Pt);const Wt={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};Object.assign({},Wt,{firstChunkSize:4e4,gps:[1,2,3,4]});Object.assign({},Wt,{tiff:!1,ifd1:!0,mergeOutput:!1});const ur=Object.assign({},Wt,{firstChunkSize:4e4,ifd0:[274]});async function dr(i){let e=new sr(ur);await e.read(i);let t=await e.parse();if(t&&t.ifd0)return t.ifd0[274]}const hr=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let De=!0,Be=!0;if(typeof navigator=="object"){let i=navigator.userAgent;if(i.includes("iPad")||i.includes("iPhone")){let e=i.match(/OS (\d+)_(\d+)/);if(e){let[,t,s]=e;De=Number(t)+.1*Number(s)<13.4,Be=!1}}else if(i.includes("OS X 10")){let[,e]=i.match(/OS X 10[_.](\d+)/);De=Be=Number(e)<15}if(i.includes("Chrome/")){let[,e]=i.match(/Chrome\/(\d+)/);De=Be=Number(e)<81}else if(i.includes("Firefox/")){let[,e]=i.match(/Firefox\/(\d+)/);De=Be=Number(e)<77}}async function cr(i){let e=await dr(i);return Object.assign({canvas:De,css:Be},hr[e])}class pr extends j{constructor(...e){super(...e),F(this,"ranges",new fr),this.byteLength!==0&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,s){if(e===0&&this.byteLength===0&&s){let n=new DataView(s.buffer||s,s.byteOffset,s.byteLength);this._swapDataView(n)}else{let n=e+t;if(n>this.byteLength){let{dataView:a}=this._extend(n);this._swapDataView(a)}}}_extend(e){let t;t=es?Zi.allocUnsafe(e):new Uint8Array(e);let s=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:s}}subarray(e,t,s=!1){return t=t||this._lengthToEnd(e),s&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,s=!1){s&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class fr{constructor(){F(this,"list",[])}get length(){return this.list.length}add(e,t,s=0){let n=e+t,a=this.list.filter(r=>Fi(e,r.offset,n)||Fi(e,r.end,n));if(a.length>0){e=Math.min(e,...a.map(l=>l.offset)),n=Math.max(n,...a.map(l=>l.end)),t=n-e;let r=a.shift();r.offset=e,r.length=t,r.end=n,this.list=this.list.filter(l=>!a.includes(l))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let s=e+t;return this.list.some(n=>n.offset<=e&&s<=n.end)}}function Fi(i,e,t){return i<=e&&e<=t}class mr extends pr{constructor(e,t){super(0),F(this,"chunksRead",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,s=await this.readChunk(e,t);return!!s&&s.byteLength===t}async readChunk(e,t){if(this.chunksRead++,(t=this.safeWrapAddress(e,t))!==0)return this._readChunk(e,t)}safeWrapAddress(e,t){return this.size!==void 0&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(this.ranges.list.length!==0)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return this.size!==void 0&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}Ht.set("blob",class extends mr{async readWhole(){this.chunked=!1;let i=await Lt(this.input);this._swapArrayBuffer(i)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(i,e){let t=e?i+e:void 0,s=this.input.slice(i,t),n=await Lt(s);return this.set(n,i,!0)}});const gr={strings:{generatingThumbnails:"Generating thumbnails..."}},yr={version:"3.1.0"};function br(i,e,t){try{i.getContext("2d").getImageData(0,0,1,1)}catch(s){if(s.code===18)return Promise.reject(new Error("cannot read image, probably an svg with external resources"))}return i.toBlob?new Promise(s=>{i.toBlob(s,e,t)}).then(s=>{if(s===null)throw new Error("cannot read image, probably an svg with external resources");return s}):Promise.resolve().then(()=>Yn(i.toDataURL(e,t),{})).then(s=>{if(s===null)throw new Error("could not extract blob, probably an old browser");return s})}function vr(i,e){let t=i.width,s=i.height;(e.deg===90||e.deg===270)&&(t=i.height,s=i.width);const n=document.createElement("canvas");n.width=t,n.height=s;const a=n.getContext("2d");return a.translate(t/2,s/2),e.canvas&&(a.rotate(e.rad),a.scale(e.scaleX,e.scaleY)),a.drawImage(i,-i.width/2,-i.height/2,i.width,i.height),n}function wr(i){const e=i.width/i.height,t=5e6,s=4096;let n=Math.floor(Math.sqrt(t*e)),a=Math.floor(t/Math.sqrt(t*e));if(n>s&&(n=s,a=Math.round(n/e)),a>s&&(a=s,n=Math.round(e*a)),i.width>n){const r=document.createElement("canvas");return r.width=n,r.height=a,r.getContext("2d").drawImage(i,0,0,n,a),r}return i}const Pr={thumbnailWidth:null,thumbnailHeight:null,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,lazy:!1};class as extends lt{constructor(e,t){if(super(e,{...Pr,...t}),this.onFileAdded=s=>{!s.preview&&s.data&&ci(s.type)&&!s.isRemote&&this.addToQueue(s.id)},this.onCancelRequest=s=>{const n=this.queue.indexOf(s.id);n!==-1&&this.queue.splice(n,1)},this.onFileRemoved=s=>{const n=this.queue.indexOf(s.id);n!==-1&&this.queue.splice(n,1),s.preview&&hi(s.preview)&&URL.revokeObjectURL(s.preview)},this.onRestored=()=>{this.uppy.getFiles().filter(n=>n.isRestored).forEach(n=>{(!n.preview||hi(n.preview))&&this.addToQueue(n.id)})},this.onAllFilesRemoved=()=>{this.queue=[]},this.waitUntilAllProcessed=s=>{s.forEach(a=>{const r=this.uppy.getFile(a);this.uppy.emit("preprocess-progress",r,{mode:"indeterminate",message:this.i18n("generatingThumbnails")})});const n=()=>{s.forEach(a=>{const r=this.uppy.getFile(a);this.uppy.emit("preprocess-complete",r)})};return new Promise(a=>{this.queueProcessing?this.uppy.once("thumbnail:all-generated",()=>{n(),a()}):(n(),a())})},this.type="modifier",this.id=this.opts.id||"ThumbnailGenerator",this.title="Thumbnail Generator",this.queue=[],this.queueProcessing=!1,this.defaultThumbnailDimension=200,this.thumbnailType=this.opts.thumbnailType,this.defaultLocale=gr,this.i18nInit(),this.opts.lazy&&this.opts.waitForThumbnailsBeforeUpload)throw new Error("ThumbnailGenerator: The `lazy` and `waitForThumbnailsBeforeUpload` options are mutually exclusive. Please ensure at most one of them is set to `true`.")}createThumbnail(e,t,s){const n=URL.createObjectURL(e.data),a=new Promise((l,u)=>{const d=new Image;d.src=n,d.addEventListener("load",()=>{URL.revokeObjectURL(n),l(d)}),d.addEventListener("error",h=>{URL.revokeObjectURL(n),u(h.error||new Error("Could not create thumbnail"))})}),r=cr(e.data).catch(()=>1);return Promise.all([a,r]).then(l=>{let[u,d]=l;const h=this.getProportionalDimensions(u,t,s,d.deg),c=vr(u,d),p=this.resizeImage(c,h.width,h.height);return br(p,this.thumbnailType,80)}).then(l=>URL.createObjectURL(l))}getProportionalDimensions(e,t,s,n){let a=e.width/e.height;return(n===90||n===270)&&(a=e.height/e.width),t!=null?{width:t,height:Math.round(t/a)}:s!=null?{width:Math.round(s*a),height:s}:{width:this.defaultThumbnailDimension,height:Math.round(this.defaultThumbnailDimension/a)}}resizeImage(e,t,s){let n=wr(e),a=Math.ceil(Math.log2(n.width/t));a<1&&(a=1);let r=t*2**(a-1),l=s*2**(a-1);const u=2;for(;a--;){const d=document.createElement("canvas");d.width=r,d.height=l,d.getContext("2d").drawImage(n,0,0,r,l),n=d,r=Math.round(r/u),l=Math.round(l/u)}return n}setPreviewURL(e,t){this.uppy.setFileState(e,{preview:t})}addToQueue(e){this.queue.push(e),this.queueProcessing===!1&&this.processQueue()}processQueue(){if(this.queueProcessing=!0,this.queue.length>0){const e=this.uppy.getFile(this.queue.shift());return e?this.requestThumbnail(e).catch(()=>{}).then(()=>this.processQueue()):(this.uppy.log("[ThumbnailGenerator] file was removed before a thumbnail could be generated, but not removed from the queue. This is probably a bug","error"),Promise.resolve())}return this.queueProcessing=!1,this.uppy.log("[ThumbnailGenerator] Emptied thumbnail queue"),this.uppy.emit("thumbnail:all-generated"),Promise.resolve()}requestThumbnail(e){return ci(e.type)&&!e.isRemote?this.createThumbnail(e,this.opts.thumbnailWidth,this.opts.thumbnailHeight).then(t=>{this.setPreviewURL(e.id,t),this.uppy.log(`[ThumbnailGenerator] Generated thumbnail for ${e.id}`),this.uppy.emit("thumbnail:generated",this.uppy.getFile(e.id),t)}).catch(t=>{this.uppy.log(`[ThumbnailGenerator] Failed thumbnail for ${e.id}:`,"warning"),this.uppy.log(t,"warning"),this.uppy.emit("thumbnail:error",this.uppy.getFile(e.id),t)}):Promise.resolve()}install(){this.uppy.on("file-removed",this.onFileRemoved),this.uppy.on("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("thumbnail:cancel",this.onCancelRequest)):(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("file-added",this.onFileAdded),this.uppy.on("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.addPreProcessor(this.waitUntilAllProcessed)}uninstall(){this.uppy.off("file-removed",this.onFileRemoved),this.uppy.off("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("thumbnail:cancel",this.onCancelRequest)):(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("file-added",this.onFileAdded),this.uppy.off("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.removePreProcessor(this.waitUntilAllProcessed)}}as.VERSION=yr.version;function Si(i){if(typeof i=="string"){const e=document.querySelectorAll(i);return e.length===0?null:Array.from(e)}return typeof i=="object"&&ks(i)?[i]:null}const Le=Array.from;function os(i,e,t,s){let{onSuccess:n}=s;i.readEntries(a=>{const r=[...e,...a];a.length?queueMicrotask(()=>{os(i,r,t,{onSuccess:n})}):n(r)},a=>{t(a),n(e)})}function ls(i,e){return i==null?i:{kind:i.isFile?"file":i.isDirectory?"directory":void 0,name:i.name,getFile(){return new Promise((t,s)=>i.file(t,s))},async*values(){const t=i.createReader();yield*await new Promise(n=>{os(t,[],e,{onSuccess:a=>n(a.map(r=>ls(r,e)))})})},isSameEntry:void 0}}function us(i,e,t){try{return t===void 0&&(t=void 0),async function*(){const s=()=>`${e}/${i.name}`;if(i.kind==="file"){const n=await i.getFile();n!=null?(n.relativePath=e?s():null,yield n):t!=null&&(yield t)}else if(i.kind==="directory")for await(const n of i.values())yield*us(n,e?s():i.name);else t!=null&&(yield t)}()}catch(s){return Promise.reject(s)}}async function*Fr(i,e){const t=await Promise.all(Array.from(i.items,async s=>{var n;let a;const r=()=>typeof s.getAsEntry=="function"?s.getAsEntry():s.webkitGetAsEntry();return(n=a)!=null||(a=ls(r(),e)),{fileSystemHandle:a,lastResortFile:s.getAsFile()}}));for(const{lastResortFile:s,fileSystemHandle:n}of t)if(n!=null)try{yield*us(n,"",s)}catch(a){s!=null?yield s:e(a)}else s!=null&&(yield s)}function Sr(i){const e=Le(i.files);return Promise.resolve(e)}async function Cr(i,e){var t;const s=(t=e==null?void 0:e.logDropError)!=null?t:Function.prototype;try{const n=[];for await(const a of Fr(i,s))n.push(a);return n}catch{return Sr(i)}}const ds=['a[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','area[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])',"input:not([disabled]):not([inert]):not([aria-hidden])","select:not([disabled]):not([inert]):not([aria-hidden])","textarea:not([disabled]):not([inert]):not([aria-hidden])","button:not([disabled]):not([inert]):not([aria-hidden])",'iframe:not([tabindex^="-"]):not([inert]):not([aria-hidden])','object:not([tabindex^="-"]):not([inert]):not([aria-hidden])','embed:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[contenteditable]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[tabindex]:not([tabindex^="-"]):not([inert]):not([aria-hidden])'];function hs(i,e){if(e){const t=i.querySelector(`[data-uppy-paneltype="${e}"]`);if(t)return t}return i}function Ci(i,e){const t=e[0];t&&(t.focus(),i.preventDefault())}function Tr(i,e){const t=e[e.length-1];t&&(t.focus(),i.preventDefault())}function Ir(i){return i.contains(document.activeElement)}function cs(i,e,t){const s=hs(t,e),n=Le(s.querySelectorAll(ds)),a=n.indexOf(document.activeElement);Ir(s)?i.shiftKey&&a===0?Tr(i,n):!i.shiftKey&&a===n.length-1&&Ci(i,n):Ci(i,n)}function Er(i,e,t){e===null||cs(i,e,t)}var Ar=Bs();const kr=ut(Ar);function Dr(){let i=!1;return kr((t,s)=>{const n=hs(t,s),a=n.contains(document.activeElement);if(a&&i)return;const r=n.querySelector("[data-uppy-super-focusable]");if(!(a&&!r))if(r)r.focus({preventScroll:!0}),i=!0;else{const l=n.querySelector(ds);l==null||l.focus({preventScroll:!0}),i=!1}},260)}function Br(){const i=document.body;return!(!("draggable"in i)||!("ondragstart"in i&&"ondrop"in i)||!("FormData"in window)||!("FileReader"in window))}var Or=function(e,t){if(e===t)return!0;for(var s in e)if(!(s in t))return!1;for(var s in t)if(e[s]!==t[s])return!1;return!0};const Nr=ut(Or);function _r(){return o("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},o("g",{fill:"#686DE0",fillRule:"evenodd"},o("path",{d:"M5 7v10h15V7H5zm0-1h15a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1z",fillRule:"nonzero"}),o("path",{d:"M6.35 17.172l4.994-5.026a.5.5 0 0 1 .707 0l2.16 2.16 3.505-3.505a.5.5 0 0 1 .707 0l2.336 2.31-.707.72-1.983-1.97-3.505 3.505a.5.5 0 0 1-.707 0l-2.16-2.159-3.938 3.939-1.409.026z",fillRule:"nonzero"}),o("circle",{cx:"7.5",cy:"9.5",r:"1.5"})))}function xr(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M9.5 18.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V7.25a.5.5 0 0 1 .379-.485l9-2.25A.5.5 0 0 1 18.5 5v11.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V8.67l-8 2v7.97zm8-11v-2l-8 2v2l8-2zM7 19.64c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1zm9-2c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1z",fill:"#049BCF",fillRule:"nonzero"}))}function Rr(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M16 11.834l4.486-2.691A1 1 0 0 1 22 10v6a1 1 0 0 1-1.514.857L16 14.167V17a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v2.834zM15 9H5v8h10V9zm1 4l5 3v-6l-5 3z",fill:"#19AF67",fillRule:"nonzero"}))}function Ur(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M9.766 8.295c-.691-1.843-.539-3.401.747-3.726 1.643-.414 2.505.938 2.39 3.299-.039.79-.194 1.662-.537 3.148.324.49.66.967 1.055 1.51.17.231.382.488.629.757 1.866-.128 3.653.114 4.918.655 1.487.635 2.192 1.685 1.614 2.84-.566 1.133-1.839 1.084-3.416.249-1.141-.604-2.457-1.634-3.51-2.707a13.467 13.467 0 0 0-2.238.426c-1.392 4.051-4.534 6.453-5.707 4.572-.986-1.58 1.38-4.206 4.914-5.375.097-.322.185-.656.264-1.001.08-.353.306-1.31.407-1.737-.678-1.059-1.2-2.031-1.53-2.91zm2.098 4.87c-.033.144-.068.287-.104.427l.033-.01-.012.038a14.065 14.065 0 0 1 1.02-.197l-.032-.033.052-.004a7.902 7.902 0 0 1-.208-.271c-.197-.27-.38-.526-.555-.775l-.006.028-.002-.003c-.076.323-.148.632-.186.8zm5.77 2.978c1.143.605 1.832.632 2.054.187.26-.519-.087-1.034-1.113-1.473-.911-.39-2.175-.608-3.55-.608.845.766 1.787 1.459 2.609 1.894zM6.559 18.789c.14.223.693.16 1.425-.413.827-.648 1.61-1.747 2.208-3.206-2.563 1.064-4.102 2.867-3.633 3.62zm5.345-10.97c.088-1.793-.351-2.48-1.146-2.28-.473.119-.564 1.05-.056 2.405.213.566.52 1.188.908 1.859.18-.858.268-1.453.294-1.984z",fill:"#E2514A",fillRule:"nonzero"}))}function Lr(){return o("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M10.45 2.05h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V2.55a.5.5 0 0 1 .5-.5zm2.05 1.024h1.05a.5.5 0 0 1 .5.5V3.6a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5v-.001zM10.45 0h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V.5a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 3.074h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 1.024h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm-2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-1.656 3.074l-.82 5.946c.52.302 1.174.458 1.976.458.803 0 1.455-.156 1.975-.458l-.82-5.946h-2.311zm0-1.025h2.312c.512 0 .946.378 1.015.885l.82 5.946c.056.412-.142.817-.501 1.026-.686.398-1.515.597-2.49.597-.974 0-1.804-.199-2.49-.597a1.025 1.025 0 0 1-.5-1.026l.819-5.946c.07-.507.503-.885 1.015-.885zm.545 6.6a.5.5 0 0 1-.397-.561l.143-.999a.5.5 0 0 1 .495-.429h.74a.5.5 0 0 1 .495.43l.143.998a.5.5 0 0 1-.397.561c-.404.08-.819.08-1.222 0z",fill:"#00C469",fillRule:"nonzero"}))}function Mr(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("g",{fill:"#A7AFB7",fillRule:"nonzero"},o("path",{d:"M5.5 22a.5.5 0 0 1-.5-.5v-18a.5.5 0 0 1 .5-.5h10.719a.5.5 0 0 1 .367.16l3.281 3.556a.5.5 0 0 1 .133.339V21.5a.5.5 0 0 1-.5.5h-14zm.5-1h13V7.25L16 4H6v17z"}),o("path",{d:"M15 4v3a1 1 0 0 0 1 1h3V7h-3V4h-1z"})))}function zr(){return o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},o("path",{d:"M4.5 7h13a.5.5 0 1 1 0 1h-13a.5.5 0 0 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h10a.5.5 0 1 1 0 1h-10a.5.5 0 1 1 0-1z",fill:"#5A5E69",fillRule:"nonzero"}))}function jt(i){const e={color:"#838999",icon:Mr()};if(!i)return e;const t=i.split("/")[0],s=i.split("/")[1];return t==="text"?{color:"#5a5e69",icon:zr()}:t==="image"?{color:"#686de0",icon:_r()}:t==="audio"?{color:"#068dbb",icon:xr()}:t==="video"?{color:"#19af67",icon:Rr()}:t==="application"&&s==="pdf"?{color:"#e25149",icon:Ur()}:t==="application"&&["zip","x-7z-compressed","x-zip-compressed","x-rar-compressed","x-tar","x-gzip","x-apple-diskimage"].indexOf(s)!==-1?{color:"#00C469",icon:Lr()}:e}function ps(i){const{file:e}=i;if(e.preview)return o("img",{className:"uppy-Dashboard-Item-previewImg",alt:e.name,src:e.preview});const{color:t,icon:s}=jt(e.type);return o("div",{className:"uppy-Dashboard-Item-previewIconWrap"},o("span",{className:"uppy-Dashboard-Item-previewIcon",style:{color:t}},s),o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-Dashboard-Item-previewIconBg",width:"58",height:"76",viewBox:"0 0 58 76"},o("rect",{fill:"#FFF",width:"58",height:"76",rx:"3",fillRule:"evenodd"})))}const $r=(i,e)=>(typeof e=="function"?e():e).filter(n=>n.id===i)[0].name;function fs(i){const{file:e,toggleFileCard:t,i18n:s,metaFields:n}=i,{missingRequiredMetaFields:a}=e;if(!(a!=null&&a.length))return null;const r=a.map(l=>$r(l,n)).join(", ");return o("div",{className:"uppy-Dashboard-Item-errorMessage"},s("missingRequiredMetaFields",{smart_count:a.length,fields:r})," ",o("button",{type:"button",class:"uppy-u-reset uppy-Dashboard-Item-errorMessageBtn",onClick:()=>t(!0,e.id)},s("editFile")))}function Hr(i){const{file:e,i18n:t,toggleFileCard:s,metaFields:n,showLinkToFileUploadResult:a}=i,r="rgba(255, 255, 255, 0.5)",l=e.preview?r:jt(e.type).color;return o("div",{className:"uppy-Dashboard-Item-previewInnerWrap",style:{backgroundColor:l}},a&&e.uploadURL&&o("a",{className:"uppy-Dashboard-Item-previewLink",href:e.uploadURL,rel:"noreferrer noopener",target:"_blank","aria-label":e.meta.name},o("span",{hidden:!0},e.meta.name)),o(ps,{file:e}),o(fs,{file:e,i18n:t,toggleFileCard:s,metaFields:n}))}function Vr(i){if(!i.isUploaded){if(i.error&&!i.hideRetryButton){i.uppy.retryUpload(i.file.id);return}i.resumableUploads&&!i.hidePauseResumeButton?i.uppy.pauseResume(i.file.id):i.individualCancellation&&!i.hideCancelButton&&i.uppy.removeFile(i.file.id)}}function Ti(i){return i.isUploaded?i.i18n("uploadComplete"):i.error?i.i18n("retryUpload"):i.resumableUploads?i.file.isPaused?i.i18n("resumeUpload"):i.i18n("pauseUpload"):i.individualCancellation?i.i18n("cancelUpload"):""}function Ft(i){return o("div",{className:"uppy-Dashboard-Item-progress"},o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-progressIndicator",type:"button","aria-label":Ti(i),title:Ti(i),onClick:()=>Vr(i)},i.children))}function Ke(i){let{children:e}=i;return o("svg",{"aria-hidden":"true",focusable:"false",width:"70",height:"70",viewBox:"0 0 36 36",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--circle"},e)}function St(i){let{progress:e}=i;const t=2*Math.PI*15;return o("g",null,o("circle",{className:"uppy-Dashboard-Item-progressIcon--bg",r:"15",cx:"18",cy:"18","stroke-width":"2",fill:"none"}),o("circle",{className:"uppy-Dashboard-Item-progressIcon--progress",r:"15",cx:"18",cy:"18",transform:"rotate(-90, 18, 18)",fill:"none","stroke-width":"2","stroke-dasharray":t,"stroke-dashoffset":t-t/100*e}))}function qr(i){if(!i.file.progress.uploadStarted)return null;if(i.isUploaded)return o("div",{className:"uppy-Dashboard-Item-progress"},o("div",{className:"uppy-Dashboard-Item-progressIndicator"},o(Ke,null,o("circle",{r:"15",cx:"18",cy:"18",fill:"#1bb240"}),o("polygon",{className:"uppy-Dashboard-Item-progressIcon--check",transform:"translate(2, 3)",points:"14 22.5 7 15.2457065 8.99985857 13.1732815 14 18.3547104 22.9729883 9 25 11.1005634"}))));if(!i.recoveredState)return i.error&&!i.hideRetryButton?o(Ft,i,o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--retry",width:"28",height:"31",viewBox:"0 0 16 19"},o("path",{d:"M16 11a8 8 0 1 1-8-8v2a6 6 0 1 0 6 6h2z"}),o("path",{d:"M7.9 3H10v2H7.9z"}),o("path",{d:"M8.536.5l3.535 3.536-1.414 1.414L7.12 1.914z"}),o("path",{d:"M10.657 2.621l1.414 1.415L8.536 7.57 7.12 6.157z"}))):i.resumableUploads&&!i.hidePauseResumeButton?o(Ft,i,o(Ke,null,o(St,{progress:i.file.progress.percentage}),i.file.isPaused?o("polygon",{className:"uppy-Dashboard-Item-progressIcon--play",transform:"translate(3, 3)",points:"12 20 12 10 20 15"}):o("g",{className:"uppy-Dashboard-Item-progressIcon--pause",transform:"translate(14.5, 13)"},o("rect",{x:"0",y:"0",width:"2",height:"10",rx:"0"}),o("rect",{x:"5",y:"0",width:"2",height:"10",rx:"0"})))):!i.resumableUploads&&i.individualCancellation&&!i.hideCancelButton?o(Ft,i,o(Ke,null,o(St,{progress:i.file.progress.percentage}),o("polygon",{className:"cancel",transform:"translate(2, 2)",points:"19.8856516 11.0625 16 14.9481516 12.1019737 11.0625 11.0625 12.1143484 14.9481516 16 11.0625 19.8980263 12.1019737 20.9375 16 17.0518484 19.8856516 20.9375 20.9375 19.8980263 17.0518484 16 20.9375 12"}))):o("div",{className:"uppy-Dashboard-Item-progress"},o("div",{className:"uppy-Dashboard-Item-progressIndicator"},o(Ke,null,o(St,{progress:i.file.progress.percentage}))))}const Ct="...";function ms(i,e){if(e===0)return"";if(i.length<=e)return i;if(e<=Ct.length+1)return`${i.slice(0,e-1)}…`;const t=e-Ct.length,s=Math.ceil(t/2),n=Math.floor(t/2);return i.slice(0,s)+Ct+i.slice(-n)}const Wr=i=>{const{author:e,name:t}=i.file.meta;function s(){return i.isSingleFile&&i.containerHeight>=350?90:i.containerWidth<=352?35:i.containerWidth<=576?60:e?20:30}return o("div",{className:"uppy-Dashboard-Item-name",title:t},ms(t,s()))},jr=i=>{var e;const{author:t}=i.file.meta,s=(e=i.file.remote)==null?void 0:e.providerName,n="·";return t?o("div",{className:"uppy-Dashboard-Item-author"},o("a",{href:`${t.url}?utm_source=Companion&utm_medium=referral`,target:"_blank",rel:"noopener noreferrer"},ms(t.name,13)),s?o(re,null,` ${n} `,s,` ${n} `):null):null},Gr=i=>i.file.size&&o("div",{className:"uppy-Dashboard-Item-statusSize"},Ot(i.file.size)),Qr=i=>i.file.isGhost&&o("span",null," • ",o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-reSelect",type:"button",onClick:i.toggleAddFilesPanel},i.i18n("reSelect"))),Kr=i=>{let{file:e,onClick:t}=i;return e.error?o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-errorDetails","aria-label":e.error,"data-microtip-position":"bottom","data-microtip-size":"medium",onClick:t,type:"button"},"?"):null};function Xr(i){const{file:e}=i;return o("div",{className:"uppy-Dashboard-Item-fileInfo","data-uppy-file-source":e.source},o("div",{className:"uppy-Dashboard-Item-fileName"},Wr(i),o(Kr,{file:i.file,onClick:()=>alert(i.file.error)})),o("div",{className:"uppy-Dashboard-Item-status"},jr(i),Gr(i),Qr(i)),o(fs,{file:i.file,i18n:i.i18n,toggleFileCard:i.toggleFileCard,metaFields:i.metaFields}))}function Yr(i,e){return e===void 0&&(e="Copy the URL below"),new Promise(t=>{const s=document.createElement("textarea");s.setAttribute("style",{position:"fixed",top:0,left:0,width:"2em",height:"2em",padding:0,border:"none",outline:"none",boxShadow:"none",background:"transparent"}),s.value=i,document.body.appendChild(s),s.select();const n=()=>{document.body.removeChild(s),window.prompt(e,i),t()};try{return document.execCommand("copy")?(document.body.removeChild(s),t()):n()}catch{return document.body.removeChild(s),n()}})}function Jr(i){let{file:e,uploadInProgressOrComplete:t,metaFields:s,canEditFile:n,i18n:a,onClick:r}=i;return!t&&s&&s.length>0||!t&&n(e)?o("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-action uppy-Dashboard-Item-action--edit",type:"button","aria-label":a("editFileWithFilename",{file:e.meta.name}),title:a("editFileWithFilename",{file:e.meta.name}),onClick:()=>r()},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 14"},o("g",{fillRule:"evenodd"},o("path",{d:"M1.5 10.793h2.793A1 1 0 0 0 5 10.5L11.5 4a1 1 0 0 0 0-1.414L9.707.793a1 1 0 0 0-1.414 0l-6.5 6.5A1 1 0 0 0 1.5 8v2.793zm1-1V8L9 1.5l1.793 1.793-6.5 6.5H2.5z",fillRule:"nonzero"}),o("rect",{x:"1",y:"12.293",width:"11",height:"1",rx:".5"}),o("path",{fillRule:"nonzero",d:"M6.793 2.5L9.5 5.207l.707-.707L7.5 1.793z"})))):null}function Zr(i){let{i18n:e,onClick:t,file:s}=i;return o("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--remove",type:"button","aria-label":e("removeFile",{file:s.meta.name}),title:e("removeFile",{file:s.meta.name}),onClick:()=>t()},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"18",height:"18",viewBox:"0 0 18 18"},o("path",{d:"M9 0C4.034 0 0 4.034 0 9s4.034 9 9 9 9-4.034 9-9-4.034-9-9-9z"}),o("path",{fill:"#FFF",d:"M13 12.222l-.778.778L9 9.778 5.778 13 5 12.222 8.222 9 5 5.778 5.778 5 9 8.222 12.222 5l.778.778L9.778 9z"})))}const ea=(i,e)=>{Yr(e.file.uploadURL,e.i18n("copyLinkToClipboardFallback")).then(()=>{e.uppy.log("Link copied to clipboard."),e.uppy.info(e.i18n("copyLinkToClipboardSuccess"),"info",3e3)}).catch(e.uppy.log).then(()=>i.target.focus({preventScroll:!0}))};function ta(i){const{i18n:e}=i;return o("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--copyLink",type:"button","aria-label":e("copyLink"),title:e("copyLink"),onClick:t=>ea(t,i)},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 12"},o("path",{d:"M7.94 7.703a2.613 2.613 0 0 1-.626 2.681l-.852.851a2.597 2.597 0 0 1-1.849.766A2.616 2.616 0 0 1 2.764 7.54l.852-.852a2.596 2.596 0 0 1 2.69-.625L5.267 7.099a1.44 1.44 0 0 0-.833.407l-.852.851a1.458 1.458 0 0 0 1.03 2.486c.39 0 .755-.152 1.03-.426l.852-.852c.231-.231.363-.522.406-.824l1.04-1.038zm4.295-5.937A2.596 2.596 0 0 0 10.387 1c-.698 0-1.355.272-1.849.766l-.852.851a2.614 2.614 0 0 0-.624 2.688l1.036-1.036c.041-.304.173-.6.407-.833l.852-.852c.275-.275.64-.426 1.03-.426a1.458 1.458 0 0 1 1.03 2.486l-.852.851a1.442 1.442 0 0 1-.824.406l-1.04 1.04a2.596 2.596 0 0 0 2.683-.628l.851-.85a2.616 2.616 0 0 0 0-3.697zm-6.88 6.883a.577.577 0 0 0 .82 0l3.474-3.474a.579.579 0 1 0-.819-.82L5.355 7.83a.579.579 0 0 0 0 .819z"})))}function ia(i){const{uppy:e,file:t,uploadInProgressOrComplete:s,canEditFile:n,metaFields:a,showLinkToFileUploadResult:r,showRemoveButton:l,i18n:u,toggleFileCard:d,openFileEditor:h}=i;return o("div",{className:"uppy-Dashboard-Item-actionWrapper"},o(Jr,{i18n:u,file:t,uploadInProgressOrComplete:s,canEditFile:n,metaFields:a,onClick:()=>{a&&a.length>0?d(!0,t.id):h(t)}}),r&&t.uploadURL?o(ta,{file:t,uppy:e,i18n:u}):null,l?o(Zr,{i18n:u,file:t,uppy:e,onClick:()=>e.removeFile(t.id,"removed-by-user")}):null)}class sa extends ve{componentDidMount(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}shouldComponentUpdate(e){return!Nr(this.props,e)}componentDidUpdate(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}componentWillUnmount(){const{file:e}=this.props;e.preview||this.props.handleCancelThumbnail(e)}render(){const{file:e}=this.props,t=e.progress.preprocess||e.progress.postprocess,s=e.progress.uploadComplete&&!t&&!e.error,n=e.progress.uploadStarted||t,a=e.progress.uploadStarted&&!e.progress.uploadComplete||t,r=e.error||!1,{isGhost:l}=e;let u=(this.props.individualCancellation||!a)&&!s;s&&this.props.showRemoveButtonAfterComplete&&(u=!0);const d=x({"uppy-Dashboard-Item":!0,"is-inprogress":a&&!this.props.recoveredState,"is-processing":t,"is-complete":s,"is-error":!!r,"is-resumable":this.props.resumableUploads,"is-noIndividualCancellation":!this.props.individualCancellation,"is-ghost":l});return o("div",{className:d,id:`uppy_${e.id}`,role:this.props.role},o("div",{className:"uppy-Dashboard-Item-preview"},o(Hr,{file:e,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,i18n:this.props.i18n,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields}),o(qr,{uppy:this.props.uppy,file:e,error:r,isUploaded:s,hideRetryButton:this.props.hideRetryButton,hideCancelButton:this.props.hideCancelButton,hidePauseResumeButton:this.props.hidePauseResumeButton,recoveredState:this.props.recoveredState,showRemoveButtonAfterComplete:this.props.showRemoveButtonAfterComplete,resumableUploads:this.props.resumableUploads,individualCancellation:this.props.individualCancellation,i18n:this.props.i18n})),o("div",{className:"uppy-Dashboard-Item-fileInfoAndButtons"},o(Xr,{file:e,id:this.props.id,acquirers:this.props.acquirers,containerWidth:this.props.containerWidth,containerHeight:this.props.containerHeight,i18n:this.props.i18n,toggleAddFilesPanel:this.props.toggleAddFilesPanel,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields,isSingleFile:this.props.isSingleFile}),o(ia,{file:e,metaFields:this.props.metaFields,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,showRemoveButton:u,canEditFile:this.props.canEditFile,uploadInProgressOrComplete:n,toggleFileCard:this.props.toggleFileCard,openFileEditor:this.props.openFileEditor,uppy:this.props.uppy,i18n:this.props.i18n})))}}function na(i,e){const t=[];let s=[];return i.forEach(n=>{s.length<e?s.push(n):(t.push(s),s=[n])}),s.length&&t.push(s),t}function ra(i){let{id:e,error:t,i18n:s,uppy:n,files:a,acquirers:r,resumableUploads:l,hideRetryButton:u,hidePauseResumeButton:d,hideCancelButton:h,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:f,metaFields:m,isSingleFile:b,toggleFileCard:g,handleRequestThumbnail:v,handleCancelThumbnail:P,recoveredState:w,individualCancellation:S,itemsPerRow:T,openFileEditor:R,canEditFile:I,toggleAddFilesPanel:D,containerWidth:N,containerHeight:me}=i;const Pe=T===1?71:200,ae=Oi(()=>{const Q=(dt,Fe)=>a[Fe].isGhost-a[dt].isGhost,le=Object.keys(a);return w&&le.sort(Q),na(le,T)},[a,T,w]),oe=Q=>o("div",{class:"uppy-Dashboard-filesInner",role:"presentation",key:Q[0]},Q.map(le=>o(sa,{key:le,uppy:n,id:e,error:t,i18n:s,acquirers:r,resumableUploads:l,individualCancellation:S,hideRetryButton:u,hidePauseResumeButton:d,hideCancelButton:h,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:f,metaFields:m,recoveredState:w,isSingleFile:b,containerWidth:N,containerHeight:me,toggleFileCard:g,handleRequestThumbnail:v,handleCancelThumbnail:P,role:"listitem",openFileEditor:R,canEditFile:I,toggleAddFilesPanel:D,file:a[le]})));return b?o("div",{class:"uppy-Dashboard-files"},oe(ae[0])):o($i,{class:"uppy-Dashboard-files",role:"list",data:ae,renderRow:oe,rowHeight:Pe})}let gs;gs=Symbol.for("uppy test: disable unused locale key warning");class ys extends ve{constructor(){super(...arguments),this.triggerFileInputClick=()=>{this.fileInput.click()},this.triggerFolderInputClick=()=>{this.folderInput.click()},this.triggerVideoCameraInputClick=()=>{this.mobileVideoFileInput.click()},this.triggerPhotoCameraInputClick=()=>{this.mobilePhotoFileInput.click()},this.onFileInputChange=e=>{this.props.handleInputChange(e),e.target.value=null},this.renderHiddenInput=(e,t)=>o("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,webkitdirectory:e,type:"file",name:"files[]",multiple:this.props.maxNumberOfFiles!==1,onChange:this.onFileInputChange,accept:this.props.allowedFileTypes,ref:t}),this.renderHiddenCameraInput=(e,t,s)=>{const a={photo:"image/*",video:"video/*"}[e];return o("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,type:"file",name:`camera-${e}`,onChange:this.onFileInputChange,capture:t,accept:a,ref:s})},this.renderMyDeviceAcquirer=()=>o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MyDevice"},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerFileInputClick},o("div",{className:"uppy-DashboardTab-inner"},o("svg",{className:"uppy-DashboardTab-iconMyDevice","aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},o("path",{d:"M8.45 22.087l-1.305-6.674h17.678l-1.572 6.674H8.45zm4.975-12.412l1.083 1.765a.823.823 0 00.715.386h7.951V13.5H8.587V9.675h4.838zM26.043 13.5h-1.195v-2.598c0-.463-.336-.75-.798-.75h-8.356l-1.082-1.766A.823.823 0 0013.897 8H7.728c-.462 0-.815.256-.815.718V13.5h-.956a.97.97 0 00-.746.37.972.972 0 00-.19.81l1.724 8.565c.095.44.484.755.933.755H24c.44 0 .824-.3.929-.727l2.043-8.568a.972.972 0 00-.176-.825.967.967 0 00-.753-.38z",fill:"currentcolor","fill-rule":"evenodd"}))),o("div",{className:"uppy-DashboardTab-name"},this.props.i18n("myDevice")))),this.renderPhotoCamera=()=>o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobilePhotoCamera"},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerPhotoCameraInputClick},o("div",{className:"uppy-DashboardTab-inner"},o("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},o("path",{d:"M23.5 9.5c1.417 0 2.5 1.083 2.5 2.5v9.167c0 1.416-1.083 2.5-2.5 2.5h-15c-1.417 0-2.5-1.084-2.5-2.5V12c0-1.417 1.083-2.5 2.5-2.5h2.917l1.416-2.167C13 7.167 13.25 7 13.5 7h5c.25 0 .5.167.667.333L20.583 9.5H23.5zM16 11.417a4.706 4.706 0 00-4.75 4.75 4.704 4.704 0 004.75 4.75 4.703 4.703 0 004.75-4.75c0-2.663-2.09-4.75-4.75-4.75zm0 7.825c-1.744 0-3.076-1.332-3.076-3.074 0-1.745 1.333-3.077 3.076-3.077 1.744 0 3.074 1.333 3.074 3.076s-1.33 3.075-3.074 3.075z",fill:"#02B383","fill-rule":"nonzero"}))),o("div",{className:"uppy-DashboardTab-name"},this.props.i18n("takePictureBtn")))),this.renderVideoCamera=()=>o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobileVideoCamera"},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerVideoCameraInputClick},o("div",{className:"uppy-DashboardTab-inner"},o("svg",{"aria-hidden":"true",width:"32",height:"32",viewBox:"0 0 32 32"},o("path",{fill:"#FF675E",fillRule:"nonzero",d:"m21.254 14.277 2.941-2.588c.797-.313 1.243.818 1.09 1.554-.01 2.094.02 4.189-.017 6.282-.126.915-1.145 1.08-1.58.34l-2.434-2.142c-.192.287-.504 1.305-.738.468-.104-1.293-.028-2.596-.05-3.894.047-.312.381.823.426 1.069.063-.384.206-.744.362-1.09zm-12.939-3.73c3.858.013 7.717-.025 11.574.02.912.129 1.492 1.237 1.351 2.217-.019 2.412.04 4.83-.03 7.239-.17 1.025-1.166 1.59-2.029 1.429-3.705-.012-7.41.025-11.114-.019-.913-.129-1.492-1.237-1.352-2.217.018-2.404-.036-4.813.029-7.214.136-.82.83-1.473 1.571-1.454z "}))),o("div",{className:"uppy-DashboardTab-name"},this.props.i18n("recordVideoBtn")))),this.renderBrowseButton=(e,t)=>{const s=this.props.acquirers.length;return o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-browse",onClick:t,"data-uppy-super-focusable":s===0},e)},this.renderDropPasteBrowseTagline=e=>{const t=this.renderBrowseButton(this.props.i18n("browseFiles"),this.triggerFileInputClick),s=this.renderBrowseButton(this.props.i18n("browseFolders"),this.triggerFolderInputClick),n=this.props.fileManagerSelectionType,a=n.charAt(0).toUpperCase()+n.slice(1);return o("div",{class:"uppy-Dashboard-AddFiles-title"},this.props.disableLocalFiles?this.props.i18n("importFiles"):e>0?this.props.i18nArray(`dropPasteImport${a}`,{browseFiles:t,browseFolders:s,browse:t}):this.props.i18nArray(`dropPaste${a}`,{browseFiles:t,browseFolders:s,browse:t}))},this.renderAcquirer=e=>{var t;return o("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":e.id},o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-cy":e.id,"aria-controls":`uppy-DashboardContent-panel--${e.id}`,"aria-selected":((t=this.props.activePickerPanel)==null?void 0:t.id)===e.id,"data-uppy-super-focusable":!0,onClick:()=>this.props.showPanel(e.id)},o("div",{className:"uppy-DashboardTab-inner"},e.icon()),o("div",{className:"uppy-DashboardTab-name"},e.name)))},this.renderAcquirers=e=>{const t=[...e],s=t.splice(e.length-2,e.length);return o(re,null,t.map(n=>this.renderAcquirer(n)),o("span",{role:"presentation",style:{"white-space":"nowrap"}},s.map(n=>this.renderAcquirer(n))))},this.renderSourcesList=(e,t)=>{const{showNativePhotoCameraButton:s,showNativeVideoCameraButton:n}=this.props;let a=[];const r="myDevice";t||a.push({key:r,elements:this.renderMyDeviceAcquirer()}),s&&a.push({key:"nativePhotoCameraButton",elements:this.renderPhotoCamera()}),n&&a.push({key:"nativePhotoCameraButton",elements:this.renderVideoCamera()}),a.push(...e.map(c=>({key:c.id,elements:this.renderAcquirer(c)}))),a.length===1&&a[0].key===r&&(a=[]);const u=[...a],d=u.splice(a.length-2,a.length),h=c=>c.map(p=>{let{key:f,elements:m}=p;return o(re,{key:f},m)});return o(re,null,this.renderDropPasteBrowseTagline(a.length),o("div",{className:"uppy-Dashboard-AddFiles-list",role:"tablist"},h(u),o("span",{role:"presentation",style:{"white-space":"nowrap"}},h(d))))}}[gs](){this.props.i18nArray("dropPasteBoth"),this.props.i18nArray("dropPasteFiles"),this.props.i18nArray("dropPasteFolders"),this.props.i18nArray("dropPasteImportBoth"),this.props.i18nArray("dropPasteImportFiles"),this.props.i18nArray("dropPasteImportFolders")}renderPoweredByUppy(){const{i18nArray:e}=this.props,t=o("span",null,o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-poweredByIcon",width:"11",height:"11",viewBox:"0 0 11 11"},o("path",{d:"M7.365 10.5l-.01-4.045h2.612L5.5.806l-4.467 5.65h2.604l.01 4.044h3.718z",fillRule:"evenodd"})),o("span",{className:"uppy-Dashboard-poweredByUppy"},"Uppy")),s=e("poweredBy",{uppy:t});return o("a",{tabIndex:-1,href:"https://uppy.io",rel:"noreferrer noopener",target:"_blank",className:"uppy-Dashboard-poweredBy"},s)}render(){const{showNativePhotoCameraButton:e,showNativeVideoCameraButton:t,nativeCameraFacingMode:s}=this.props;return o("div",{className:"uppy-Dashboard-AddFiles"},this.renderHiddenInput(!1,n=>{this.fileInput=n}),this.renderHiddenInput(!0,n=>{this.folderInput=n}),e&&this.renderHiddenCameraInput("photo",s,n=>{this.mobilePhotoFileInput=n}),t&&this.renderHiddenCameraInput("video",s,n=>{this.mobileVideoFileInput=n}),this.renderSourcesList(this.props.acquirers,this.props.disableLocalFiles),o("div",{className:"uppy-Dashboard-AddFiles-info"},this.props.note&&o("div",{className:"uppy-Dashboard-note"},this.props.note),this.props.proudlyDisplayPoweredByUppy&&this.renderPoweredByUppy(this.props)))}}const aa=i=>o("div",{className:x("uppy-Dashboard-AddFilesPanel",i.className),"data-uppy-panelType":"AddFiles","aria-hidden":!i.showAddFilesPanel},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18n("addingMoreFiles")),o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>i.toggleAddFilesPanel(!1)},i.i18n("back"))),o(ys,i));function ne(i){const{tagName:e}=i.target;if(e==="INPUT"||e==="TEXTAREA"){i.stopPropagation();return}i.preventDefault(),i.stopPropagation()}function oa(i){let{activePickerPanel:e,className:t,hideAllPanels:s,i18n:n,state:a,uppy:r}=i;return o("div",{className:x("uppy-DashboardContent-panel",t),role:"tabpanel","data-uppy-panelType":"PickerPanel",id:`uppy-DashboardContent-panel--${e.id}`,onDragOver:ne,onDragLeave:ne,onDrop:ne,onPaste:ne},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},n("importFrom",{name:e.name})),o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:s},n("cancel"))),o("div",{className:"uppy-DashboardContent-panelBody"},r.getPlugin(e.id).render(a)))}function la(i){const e=i.files[i.fileCardFor],t=()=>{i.uppy.emit("file-editor:cancel",e),i.closeFileEditor()};return o("div",{className:x("uppy-DashboardContent-panel",i.className),role:"tabpanel","data-uppy-panelType":"FileEditor",id:"uppy-DashboardContent-panel--editor"},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18nArray("editing",{file:o("span",{className:"uppy-DashboardContent-titleFile"},e.meta?e.meta.name:e.name)})),o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:t},i.i18n("cancel")),o("button",{className:"uppy-DashboardContent-save",type:"button",onClick:i.saveFileEditor},i.i18n("save"))),o("div",{className:"uppy-DashboardContent-panelBody"},i.editors.map(s=>i.uppy.getPlugin(s.id).render(i.state))))}const K={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete",STATE_PAUSED:"paused"};function ua(i,e,t,s){if(s===void 0&&(s={}),i)return K.STATE_ERROR;if(e)return K.STATE_COMPLETE;if(t)return K.STATE_PAUSED;let n=K.STATE_WAITING;const a=Object.keys(s);for(let r=0;r<a.length;r++){const{progress:l}=s[a[r]];if(l.uploadStarted&&!l.uploadComplete)return K.STATE_UPLOADING;l.preprocess&&n!==K.STATE_UPLOADING&&(n=K.STATE_PREPROCESSING),l.postprocess&&n!==K.STATE_UPLOADING&&n!==K.STATE_PREPROCESSING&&(n=K.STATE_POSTPROCESSING)}return n}function da(i){let{files:e,i18n:t,isAllComplete:s,isAllErrored:n,isAllPaused:a,inProgressNotPausedFiles:r,newFiles:l,processingFiles:u}=i;switch(ua(n,s,a,e)){case"uploading":return t("uploadingXFiles",{smart_count:r.length});case"preprocessing":case"postprocessing":return t("processingXFiles",{smart_count:u.length});case"paused":return t("uploadPaused");case"waiting":return t("xFilesSelected",{smart_count:l.length});case"complete":return t("uploadComplete");case"error":return t("error")}}function ha(i){const{i18n:e,isAllComplete:t,hideCancelButton:s,maxNumberOfFiles:n,toggleAddFilesPanel:a,uppy:r}=i;let{allowNewUpload:l}=i;return l&&n&&(l=i.totalFileCount<i.maxNumberOfFiles),o("div",{className:"uppy-DashboardContent-bar"},!t&&!s?o("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>r.cancelAll()},e("cancel")):o("div",null),o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},o(da,i)),l?o("button",{className:"uppy-DashboardContent-addMore",type:"button","aria-label":e("addMoreFiles"),title:e("addMoreFiles"),onClick:()=>a(!0)},o("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"15",height:"15",viewBox:"0 0 15 15"},o("path",{d:"M8 6.5h6a.5.5 0 0 1 .5.5v.5a.5.5 0 0 1-.5.5H8v6a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V8h-6a.5.5 0 0 1-.5-.5V7a.5.5 0 0 1 .5-.5h6v-6A.5.5 0 0 1 7 0h.5a.5.5 0 0 1 .5.5v6z"})),o("span",{className:"uppy-DashboardContent-addMoreCaption"},e("addMore"))):o("div",null))}function ca(i){const{computedMetaFields:e,requiredMetaFields:t,updateMeta:s,form:n,formState:a}=i,r={text:"uppy-u-reset uppy-c-textInput uppy-Dashboard-FileCard-input"};return e.map(l=>{const u=`uppy-Dashboard-FileCard-input-${l.id}`,d=t.includes(l.id);return o("fieldset",{key:l.id,className:"uppy-Dashboard-FileCard-fieldset"},o("label",{className:"uppy-Dashboard-FileCard-label",htmlFor:u},l.name),l.render!==void 0?l.render({value:a[l.id],onChange:h=>s(h,l.id),fieldCSSClasses:r,required:d,form:n.id},o):o("input",{className:r.text,id:u,form:n.id,type:l.type||"text",required:d,value:a[l.id],placeholder:l.placeholder,onInput:h=>s(h.target.value,l.id),"data-uppy-super-focusable":!0}))})}function pa(i){var e;const{files:t,fileCardFor:s,toggleFileCard:n,saveFileCard:a,metaFields:r,requiredMetaFields:l,openFileEditor:u,i18n:d,i18nArray:h,className:c,canEditFile:p}=i,f=()=>typeof r=="function"?r(t[s]):r,m=t[s],b=(e=f())!=null?e:[],g=p(m),v={};b.forEach(D=>{var N;v[D.id]=(N=m.meta[D.id])!=null?N:""});const[P,w]=be(v),S=it(D=>{D.preventDefault(),a(P,s)},[a,P,s]),T=(D,N)=>{w({...P,[N]:D})},R=()=>{n(!1)},[I]=be(()=>{const D=document.createElement("form");return D.setAttribute("tabindex","-1"),D.id=zt(),D});return st(()=>(document.body.appendChild(I),I.addEventListener("submit",S),()=>{I.removeEventListener("submit",S),document.body.removeChild(I)}),[I,S]),o("div",{className:x("uppy-Dashboard-FileCard",c),"data-uppy-panelType":"FileCard",onDragOver:ne,onDragLeave:ne,onDrop:ne,onPaste:ne},o("div",{className:"uppy-DashboardContent-bar"},o("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},h("editing",{file:o("span",{className:"uppy-DashboardContent-titleFile"},m.meta?m.meta.name:m.name)})),o("button",{className:"uppy-DashboardContent-back",type:"button",form:I.id,title:d("finishEditingFile"),onClick:R},d("cancel"))),o("div",{className:"uppy-Dashboard-FileCard-inner"},o("div",{className:"uppy-Dashboard-FileCard-preview",style:{backgroundColor:jt(m.type).color}},o(ps,{file:m}),g&&o("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-FileCard-edit",onClick:D=>{S(D),u(m)}},d("editImage"))),o("div",{className:"uppy-Dashboard-FileCard-info"},o(ca,{computedMetaFields:b,requiredMetaFields:l,updateMeta:T,form:I,formState:P})),o("div",{className:"uppy-Dashboard-FileCard-actions"},o("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Dashboard-FileCard-actionsBtn",type:"submit",form:I.id},d("saveChanges")),o("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-link uppy-Dashboard-FileCard-actionsBtn",type:"button",onClick:R,form:I.id},d("cancel")))))}const ge="uppy-transition-slideDownUp",Ii=250;function Xe(i){let{children:e}=i;const[t,s]=be(null),[n,a]=be(""),r=ct(),l=ct(),u=ct(),d=()=>{a(`${ge}-enter`),cancelAnimationFrame(u.current),clearTimeout(l.current),l.current=void 0,u.current=requestAnimationFrame(()=>{a(`${ge}-enter ${ge}-enter-active`),r.current=setTimeout(()=>{a("")},Ii)})},h=()=>{a(`${ge}-leave`),cancelAnimationFrame(u.current),clearTimeout(r.current),r.current=void 0,u.current=requestAnimationFrame(()=>{a(`${ge}-leave ${ge}-leave-active`),l.current=setTimeout(()=>{s(null),a("")},Ii)})};return st(()=>{const c=te(e)[0];t!==c&&(c&&!t?d():t&&!c&&!l.current&&h(),s(c))},[e,t]),st(()=>()=>{clearTimeout(r.current),clearTimeout(l.current),cancelAnimationFrame(u.current)},[]),t?Ni(t,{className:x(n,t.props.className)}):null}function fe(){return fe=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},fe.apply(this,arguments)}const Ei=900,Ai=700,Tt=576,ki=330;function fa(i){const e=i.totalFileCount===0,t=i.totalFileCount===1,s=i.containerWidth>Tt,n=i.containerHeight>ki,a=x({"uppy-Dashboard":!0,"uppy-Dashboard--isDisabled":i.disabled,"uppy-Dashboard--animateOpenClose":i.animateOpenClose,"uppy-Dashboard--isClosing":i.isClosing,"uppy-Dashboard--isDraggingOver":i.isDraggingOver,"uppy-Dashboard--modal":!i.inline,"uppy-size--md":i.containerWidth>Tt,"uppy-size--lg":i.containerWidth>Ai,"uppy-size--xl":i.containerWidth>Ei,"uppy-size--height-md":i.containerHeight>ki,"uppy-Dashboard--isAddFilesPanelVisible":i.showAddFilesPanel,"uppy-Dashboard--isInnerWrapVisible":i.areInsidesReadyToBeVisible,"uppy-Dashboard--singleFile":i.singleFileFullScreen&&t&&n});let r=1;i.containerWidth>Ei?r=5:i.containerWidth>Ai?r=4:i.containerWidth>Tt&&(r=3);const l=i.showSelectedFiles&&!e,u=i.recoveredState?Object.keys(i.recoveredState.files).length:null,d=i.files?Object.keys(i.files).filter(p=>i.files[p].isGhost).length:null,h=()=>d>0?i.i18n("recoveredXFiles",{smart_count:d}):i.i18n("recoveredAllFiles");return o("div",{className:a,"data-uppy-theme":i.theme,"data-uppy-num-acquirers":i.acquirers.length,"data-uppy-drag-drop-supported":!i.disableLocalFiles&&Br(),"aria-hidden":i.inline?"false":i.isHidden,"aria-disabled":i.disabled,"aria-label":i.inline?i.i18n("dashboardTitle"):i.i18n("dashboardWindowTitle"),onPaste:i.handlePaste,onDragOver:i.handleDragOver,onDragLeave:i.handleDragLeave,onDrop:i.handleDrop},o("div",{"aria-hidden":"true",className:"uppy-Dashboard-overlay",tabIndex:-1,onClick:i.handleClickOutside}),o("div",{className:"uppy-Dashboard-inner","aria-modal":!i.inline&&"true",role:i.inline?void 0:"dialog",style:{width:i.inline&&i.width?i.width:"",height:i.inline&&i.height?i.height:""}},i.inline?null:o("button",{className:"uppy-u-reset uppy-Dashboard-close",type:"button","aria-label":i.i18n("closeModal"),title:i.i18n("closeModal"),onClick:i.closeModal},o("span",{"aria-hidden":"true"},"×")),o("div",{className:"uppy-Dashboard-innerWrap"},o("div",{className:"uppy-Dashboard-dropFilesHereHint"},i.i18n("dropHint")),l&&o(ha,i),u&&o("div",{className:"uppy-Dashboard-serviceMsg"},o("svg",{className:"uppy-Dashboard-serviceMsg-icon","aria-hidden":"true",focusable:"false",width:"21",height:"16",viewBox:"0 0 24 19"},o("g",{transform:"translate(0 -1)",fill:"none",fillRule:"evenodd"},o("path",{d:"M12.857 1.43l10.234 17.056A1 1 0 0122.234 20H1.766a1 1 0 01-.857-1.514L11.143 1.429a1 1 0 011.714 0z",fill:"#FFD300"}),o("path",{fill:"#000",d:"M11 6h2l-.3 8h-1.4z"}),o("circle",{fill:"#000",cx:"12",cy:"17",r:"1"}))),o("strong",{className:"uppy-Dashboard-serviceMsg-title"},i.i18n("sessionRestored")),o("div",{className:"uppy-Dashboard-serviceMsg-text"},h())),l?o(ra,{id:i.id,error:i.error,i18n:i.i18n,uppy:i.uppy,files:i.files,acquirers:i.acquirers,resumableUploads:i.resumableUploads,hideRetryButton:i.hideRetryButton,hidePauseResumeButton:i.hidePauseResumeButton,hideCancelButton:i.hideCancelButton,showLinkToFileUploadResult:i.showLinkToFileUploadResult,showRemoveButtonAfterComplete:i.showRemoveButtonAfterComplete,isWide:i.isWide,metaFields:i.metaFields,toggleFileCard:i.toggleFileCard,handleRequestThumbnail:i.handleRequestThumbnail,handleCancelThumbnail:i.handleCancelThumbnail,recoveredState:i.recoveredState,individualCancellation:i.individualCancellation,openFileEditor:i.openFileEditor,canEditFile:i.canEditFile,toggleAddFilesPanel:i.toggleAddFilesPanel,isSingleFile:t,itemsPerRow:r}):o(ys,fe({},i,{isSizeMD:s})),o(Xe,null,i.showAddFilesPanel?o(aa,fe({key:"AddFiles"},i,{isSizeMD:s})):null),o(Xe,null,i.fileCardFor?o(pa,fe({key:"FileCard"},i)):null),o(Xe,null,i.activePickerPanel?o(oa,fe({key:"Picker"},i)):null),o(Xe,null,i.showFileEditor?o(la,fe({key:"Editor"},i)):null),o("div",{className:"uppy-Dashboard-progressindicators"},i.progressindicators.map(p=>i.uppy.getPlugin(p.id).render(i.state))))))}const ma={strings:{closeModal:"Close Modal",addMoreFiles:"Add more files",addingMoreFiles:"Adding more files",importFrom:"Import from %{name}",dashboardWindowTitle:"Uppy Dashboard Window (Press escape to close)",dashboardTitle:"Uppy Dashboard",copyLinkToClipboardSuccess:"Link copied to clipboard.",copyLinkToClipboardFallback:"Copy the URL below",copyLink:"Copy link",back:"Back",removeFile:"Remove file",editFile:"Edit file",editImage:"Edit image",editing:"Editing %{file}",error:"Error",finishEditingFile:"Finish editing file",saveChanges:"Save changes",myDevice:"My Device",dropHint:"Drop your files here",uploadComplete:"Upload complete",uploadPaused:"Upload paused",resumeUpload:"Resume upload",pauseUpload:"Pause upload",retryUpload:"Retry upload",cancelUpload:"Cancel upload",xFilesSelected:{0:"%{smart_count} file selected",1:"%{smart_count} files selected"},uploadingXFiles:{0:"Uploading %{smart_count} file",1:"Uploading %{smart_count} files"},processingXFiles:{0:"Processing %{smart_count} file",1:"Processing %{smart_count} files"},poweredBy:"Powered by %{uppy}",addMore:"Add more",editFileWithFilename:"Edit file %{file}",save:"Save",cancel:"Cancel",dropPasteFiles:"Drop files here or %{browseFiles}",dropPasteFolders:"Drop files here or %{browseFolders}",dropPasteBoth:"Drop files here, %{browseFiles} or %{browseFolders}",dropPasteImportFiles:"Drop files here, %{browseFiles} or import from:",dropPasteImportFolders:"Drop files here, %{browseFolders} or import from:",dropPasteImportBoth:"Drop files here, %{browseFiles}, %{browseFolders} or import from:",importFiles:"Import files from:",browseFiles:"browse files",browseFolders:"browse folders",recoveredXFiles:{0:"We could not fully recover 1 file. Please re-select it and resume the upload.",1:"We could not fully recover %{smart_count} files. Please re-select them and resume the upload."},recoveredAllFiles:"We restored all files. You can now resume the upload.",sessionRestored:"Session restored",reSelect:"Re-select",missingRequiredMetaFields:{0:"Missing required meta field: %{fields}.",1:"Missing required meta fields: %{fields}."},takePictureBtn:"Take Picture",recordVideoBtn:"Record Video"}};function C(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var ga=0;function G(i){return"__private_"+ga+++"_"+i}const ya={version:"3.9.1"},It=Qt.default||Qt,Di=9,ba=27;function Bi(){const i={};return i.promise=new Promise((e,t)=>{i.resolve=e,i.reject=t}),i}const va={target:"body",metaFields:[],inline:!1,width:750,height:550,thumbnailWidth:280,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,defaultPickerIcon:Wi,showLinkToFileUploadResult:!1,showProgressDetails:!1,hideUploadButton:!1,hideCancelButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideProgressAfterFinish:!1,note:null,closeModalOnClickOutside:!1,closeAfterFinish:!1,singleFileFullScreen:!0,disableStatusBar:!1,disableInformer:!1,disableThumbnailGenerator:!1,disablePageScrollWhenModalOpen:!0,animateOpenClose:!0,fileManagerSelectionType:"files",proudlyDisplayPoweredByUppy:!0,showSelectedFiles:!0,showRemoveButtonAfterComplete:!1,browserBackButtonClose:!1,showNativePhotoCameraButton:!1,showNativeVideoCameraButton:!1,theme:"light",autoOpen:null,autoOpenFileEditor:!1,disabled:!1,disableLocalFiles:!1,doneButtonHandler:void 0,onRequestCloseModal:null};var de=G("disabledNodes"),Y=G("generateLargeThumbnailIfSingleFile"),Ae=G("openFileEditorWhenFilesAdded"),he=G("attachRenderFunctionToTarget"),Et=G("isTargetSupported"),At=G("getAcquirers"),kt=G("getProgressIndicators"),J=G("getEditors"),Dt=G("addSpecifiedPluginsFromOptions"),Bt=G("autoDiscoverPlugins"),ce=G("addSupportedPluginIfNoTarget");class wa extends lt{constructor(e,t){var s,n;let a;t?t.autoOpen===void 0?a=t.autoOpenFileEditor?"imageEditor":null:a=t.autoOpen:a=null,super(e,{...va,...t,autoOpen:a}),Object.defineProperty(this,de,{writable:!0,value:void 0}),this.modalName=`uppy-Dashboard-${zt()}`,this.superFocus=Dr(),this.ifFocusedOnUppyRecently=!1,this.removeTarget=r=>{const u=this.getPluginState().targets.filter(d=>d.id!==r.id);this.setPluginState({targets:u})},this.addTarget=r=>{const l=r.id||r.constructor.name,u=r.title||l,d=r.type;if(d!=="acquirer"&&d!=="progressindicator"&&d!=="editor"){const f="Dashboard: can only be targeted by plugins of types: acquirer, progressindicator, editor";return this.uppy.log(f,"error"),null}const h={id:l,name:u,type:d},p=this.getPluginState().targets.slice();return p.push(h),this.setPluginState({targets:p}),this.el},this.hideAllPanels=()=>{var r;const l=this.getPluginState(),u={activePickerPanel:void 0,showAddFilesPanel:!1,activeOverlayType:null,fileCardFor:null,showFileEditor:!1};l.activePickerPanel===u.activePickerPanel&&l.showAddFilesPanel===u.showAddFilesPanel&&l.showFileEditor===u.showFileEditor&&l.activeOverlayType===u.activeOverlayType||(this.setPluginState(u),this.uppy.emit("dashboard:close-panel",(r=l.activePickerPanel)==null?void 0:r.id))},this.showPanel=r=>{const{targets:l}=this.getPluginState(),u=l.find(d=>d.type==="acquirer"&&d.id===r);this.setPluginState({activePickerPanel:u,activeOverlayType:"PickerPanel"}),this.uppy.emit("dashboard:show-panel",r)},this.canEditFile=r=>{const{targets:l}=this.getPluginState();return C(this,J)[J](l).some(d=>this.uppy.getPlugin(d.id).canEditFile(r))},this.openFileEditor=r=>{const{targets:l}=this.getPluginState(),u=C(this,J)[J](l);this.setPluginState({showFileEditor:!0,fileCardFor:r.id||null,activeOverlayType:"FileEditor"}),u.forEach(d=>{this.uppy.getPlugin(d.id).selectFile(r)})},this.closeFileEditor=()=>{const{metaFields:r}=this.getPluginState();r&&r.length>0?this.setPluginState({showFileEditor:!1,activeOverlayType:"FileCard"}):this.setPluginState({showFileEditor:!1,fileCardFor:null,activeOverlayType:"AddFiles"})},this.saveFileEditor=()=>{const{targets:r}=this.getPluginState();C(this,J)[J](r).forEach(u=>{this.uppy.getPlugin(u.id).save()}),this.closeFileEditor()},this.openModal=()=>{const{promise:r,resolve:l}=Bi();if(this.savedScrollPosition=window.pageYOffset,this.savedActiveElement=document.activeElement,this.opts.disablePageScrollWhenModalOpen&&document.body.classList.add("uppy-Dashboard-isFixed"),this.opts.animateOpenClose&&this.getPluginState().isClosing){const u=()=>{this.setPluginState({isHidden:!1}),this.el.removeEventListener("animationend",u,!1),l()};this.el.addEventListener("animationend",u,!1)}else this.setPluginState({isHidden:!1}),l();return this.opts.browserBackButtonClose&&this.updateBrowserHistory(),document.addEventListener("keydown",this.handleKeyDownInModal),this.uppy.emit("dashboard:modal-open"),r},this.closeModal=r=>{var l;const u=(l=r==null?void 0:r.manualClose)!=null?l:!0,{isHidden:d,isClosing:h}=this.getPluginState();if(d||h)return;const{promise:c,resolve:p}=Bi();if(this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.opts.animateOpenClose){this.setPluginState({isClosing:!0});const m=()=>{this.setPluginState({isHidden:!0,isClosing:!1}),this.superFocus.cancel(),this.savedActiveElement.focus(),this.el.removeEventListener("animationend",m,!1),p()};this.el.addEventListener("animationend",m,!1)}else this.setPluginState({isHidden:!0}),this.superFocus.cancel(),this.savedActiveElement.focus(),p();if(document.removeEventListener("keydown",this.handleKeyDownInModal),u&&this.opts.browserBackButtonClose){var f;(f=history.state)!=null&&f[this.modalName]&&history.back()}return this.uppy.emit("dashboard:modal-closed"),c},this.isModalOpen=()=>!this.getPluginState().isHidden||!1,this.requestCloseModal=()=>this.opts.onRequestCloseModal?this.opts.onRequestCloseModal():this.closeModal(),this.setDarkModeCapability=r=>{const{capabilities:l}=this.uppy.getState();this.uppy.setState({capabilities:{...l,darkMode:r}})},this.handleSystemDarkModeChange=r=>{const l=r.matches;this.uppy.log(`[Dashboard] Dark mode is ${l?"on":"off"}`),this.setDarkModeCapability(l)},this.toggleFileCard=(r,l)=>{const u=this.uppy.getFile(l);r?this.uppy.emit("dashboard:file-edit-start",u):this.uppy.emit("dashboard:file-edit-complete",u),this.setPluginState({fileCardFor:r?l:null,activeOverlayType:r?"FileCard":null})},this.toggleAddFilesPanel=r=>{this.setPluginState({showAddFilesPanel:r,activeOverlayType:r?"AddFiles":null})},this.addFiles=r=>{const l=r.map(u=>({source:this.id,name:u.name,type:u.type,data:u,meta:{relativePath:u.relativePath||u.webkitRelativePath||null}}));try{this.uppy.addFiles(l)}catch(u){this.uppy.log(u)}},this.startListeningToResize=()=>{this.resizeObserver=new ResizeObserver(r=>{const l=r[0],{width:u,height:d}=l.contentRect;this.setPluginState({containerWidth:u,containerHeight:d,areInsidesReadyToBeVisible:!0})}),this.resizeObserver.observe(this.el.querySelector(".uppy-Dashboard-inner")),this.makeDashboardInsidesVisibleAnywayTimeout=setTimeout(()=>{const r=this.getPluginState(),l=!this.opts.inline&&r.isHidden;!r.areInsidesReadyToBeVisible&&!l&&(this.uppy.log("[Dashboard] resize event didn’t fire on time: defaulted to mobile layout","warning"),this.setPluginState({areInsidesReadyToBeVisible:!0}))},1e3)},this.stopListeningToResize=()=>{this.resizeObserver.disconnect(),clearTimeout(this.makeDashboardInsidesVisibleAnywayTimeout)},this.recordIfFocusedOnUppyRecently=r=>{this.el.contains(r.target)?this.ifFocusedOnUppyRecently=!0:(this.ifFocusedOnUppyRecently=!1,this.superFocus.cancel())},this.disableInteractiveElements=r=>{var l;const u=["a[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])",'[role="button"]:not([disabled])'],d=(l=C(this,de)[de])!=null?l:Le(this.el.querySelectorAll(u)).filter(h=>!h.classList.contains("uppy-Dashboard-close"));for(const h of d)h.tagName==="A"?h.setAttribute("aria-disabled",r):h.disabled=r;r?C(this,de)[de]=d:C(this,de)[de]=null,this.dashboardIsDisabled=r},this.updateBrowserHistory=()=>{var r;(r=history.state)!=null&&r[this.modalName]||history.pushState({...history.state,[this.modalName]:!0},""),window.addEventListener("popstate",this.handlePopState,!1)},this.handlePopState=r=>{var l;this.isModalOpen()&&(!r.state||!r.state[this.modalName])&&this.closeModal({manualClose:!1}),!this.isModalOpen()&&(l=r.state)!=null&&l[this.modalName]&&history.back()},this.handleKeyDownInModal=r=>{r.keyCode===ba&&this.requestCloseModal(),r.keyCode===Di&&cs(r,this.getPluginState().activeOverlayType,this.el)},this.handleClickOutside=()=>{this.opts.closeModalOnClickOutside&&this.requestCloseModal()},this.handlePaste=r=>{this.uppy.iteratePlugins(u=>{u.type==="acquirer"&&(u.handleRootPaste==null||u.handleRootPaste(r))});const l=Le(r.clipboardData.files);l.length>0&&(this.uppy.log("[Dashboard] Files pasted"),this.addFiles(l))},this.handleInputChange=r=>{r.preventDefault();const l=Le(r.target.files);l.length>0&&(this.uppy.log("[Dashboard] Files selected through input"),this.addFiles(l))},this.handleDragOver=r=>{var l,u;r.preventDefault(),r.stopPropagation();const d=()=>{let f=!0;return this.uppy.iteratePlugins(m=>{m.canHandleRootDrop!=null&&m.canHandleRootDrop(r)&&(f=!0)}),f},h=()=>{const{types:f}=r.dataTransfer;return f.some(m=>m==="Files")},c=d(),p=h();if(!c&&!p||this.opts.disabled||this.opts.disableLocalFiles&&(p||!c)||!this.uppy.getState().allowNewUpload){r.dataTransfer.dropEffect="none",clearTimeout(this.removeDragOverClassTimeout);return}r.dataTransfer.dropEffect="copy",clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!0}),(l=(u=this.opts).onDragOver)==null||l.call(u,r)},this.handleDragLeave=r=>{var l,u;r.preventDefault(),r.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.removeDragOverClassTimeout=setTimeout(()=>{this.setPluginState({isDraggingOver:!1})},50),(l=(u=this.opts).onDragLeave)==null||l.call(u,r)},this.handleDrop=async r=>{var l,u;r.preventDefault(),r.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!1}),this.uppy.iteratePlugins(p=>{p.type==="acquirer"&&(p.handleRootDrop==null||p.handleRootDrop(r))});let d=!1;const h=p=>{this.uppy.log(p,"error"),d||(this.uppy.info(p.message,"error"),d=!0)};this.uppy.log("[Dashboard] Processing dropped files");const c=await Cr(r.dataTransfer,{logDropError:h});c.length>0&&(this.uppy.log("[Dashboard] Files dropped"),this.addFiles(c)),(l=(u=this.opts).onDrop)==null||l.call(u,r)},this.handleRequestThumbnail=r=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:request",r)},this.handleCancelThumbnail=r=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:cancel",r)},this.handleKeyDownInInline=r=>{r.keyCode===Di&&Er(r,this.getPluginState().activeOverlayType,this.el)},this.handlePasteOnBody=r=>{this.el.contains(document.activeElement)&&this.handlePaste(r)},this.handleComplete=r=>{let{failed:l}=r;this.opts.closeAfterFinish&&!(l!=null&&l.length)&&this.requestCloseModal()},this.handleCancelRestore=()=>{this.uppy.emit("restore-canceled")},Object.defineProperty(this,Y,{writable:!0,value:()=>{if(this.opts.disableThumbnailGenerator)return;const r=600,l=this.uppy.getFiles();if(l.length===1){const u=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);u==null||u.setOptions({thumbnailWidth:r});const d={...l[0],preview:void 0};u==null||u.requestThumbnail(d).then(()=>{u==null||u.setOptions({thumbnailWidth:this.opts.thumbnailWidth})})}}}),Object.defineProperty(this,Ae,{writable:!0,value:r=>{const l=r[0],{metaFields:u}=this.getPluginState(),d=u&&u.length>0,h=this.canEditFile(l);d&&this.opts.autoOpen==="metaEditor"?this.toggleFileCard(!0,l.id):h&&this.opts.autoOpen==="imageEditor"&&this.openFileEditor(l)}}),this.initEvents=()=>{if(this.opts.trigger&&!this.opts.inline){const r=Si(this.opts.trigger);r?r.forEach(l=>l.addEventListener("click",this.openModal)):this.uppy.log("Dashboard modal trigger not found. Make sure `trigger` is set in Dashboard options, unless you are planning to call `dashboard.openModal()` method yourself","warning")}this.startListeningToResize(),document.addEventListener("paste",this.handlePasteOnBody),this.uppy.on("plugin-added",C(this,ce)[ce]),this.uppy.on("plugin-remove",this.removeTarget),this.uppy.on("file-added",this.hideAllPanels),this.uppy.on("dashboard:modal-closed",this.hideAllPanels),this.uppy.on("complete",this.handleComplete),this.uppy.on("files-added",C(this,Y)[Y]),this.uppy.on("file-removed",C(this,Y)[Y]),document.addEventListener("focus",this.recordIfFocusedOnUppyRecently,!0),document.addEventListener("click",this.recordIfFocusedOnUppyRecently,!0),this.opts.inline&&this.el.addEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpen&&this.uppy.on("files-added",C(this,Ae)[Ae])},this.removeEvents=()=>{const r=Si(this.opts.trigger);!this.opts.inline&&r&&r.forEach(l=>l.removeEventListener("click",this.openModal)),this.stopListeningToResize(),document.removeEventListener("paste",this.handlePasteOnBody),window.removeEventListener("popstate",this.handlePopState,!1),this.uppy.off("plugin-added",C(this,ce)[ce]),this.uppy.off("plugin-remove",this.removeTarget),this.uppy.off("file-added",this.hideAllPanels),this.uppy.off("dashboard:modal-closed",this.hideAllPanels),this.uppy.off("complete",this.handleComplete),this.uppy.off("files-added",C(this,Y)[Y]),this.uppy.off("file-removed",C(this,Y)[Y]),document.removeEventListener("focus",this.recordIfFocusedOnUppyRecently),document.removeEventListener("click",this.recordIfFocusedOnUppyRecently),this.opts.inline&&this.el.removeEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpen&&this.uppy.off("files-added",C(this,Ae)[Ae])},this.superFocusOnEachUpdate=()=>{const r=this.el.contains(document.activeElement),l=document.activeElement===document.body||document.activeElement===null,u=this.uppy.getState().info.length===0,d=!this.opts.inline;u&&(d||r||l&&this.ifFocusedOnUppyRecently)?this.superFocus(this.el,this.getPluginState().activeOverlayType):this.superFocus.cancel()},this.afterUpdate=()=>{if(this.opts.disabled&&!this.dashboardIsDisabled){this.disableInteractiveElements(!0);return}!this.opts.disabled&&this.dashboardIsDisabled&&this.disableInteractiveElements(!1),this.superFocusOnEachUpdate()},this.saveFileCard=(r,l)=>{this.uppy.setFileMeta(l,r),this.toggleFileCard(!1,l)},Object.defineProperty(this,he,{writable:!0,value:r=>{const l=this.uppy.getPlugin(r.id);return{...r,icon:l.icon||this.opts.defaultPickerIcon,render:l.render}}}),Object.defineProperty(this,Et,{writable:!0,value:r=>{const l=this.uppy.getPlugin(r.id);return typeof l.isSupported!="function"?!0:l.isSupported()}}),Object.defineProperty(this,At,{writable:!0,value:It(r=>r.filter(l=>l.type==="acquirer"&&C(this,Et)[Et](l)).map(C(this,he)[he]))}),Object.defineProperty(this,kt,{writable:!0,value:It(r=>r.filter(l=>l.type==="progressindicator").map(C(this,he)[he]))}),Object.defineProperty(this,J,{writable:!0,value:It(r=>r.filter(l=>l.type==="editor").map(C(this,he)[he]))}),this.render=r=>{const l=this.getPluginState(),{files:u,capabilities:d,allowNewUpload:h}=r,{newFiles:c,uploadStartedFiles:p,completeFiles:f,erroredFiles:m,inProgressFiles:b,inProgressNotPausedFiles:g,processingFiles:v,isUploadStarted:P,isAllComplete:w,isAllErrored:S,isAllPaused:T}=this.uppy.getObjectOfFilesPerState(),R=C(this,At)[At](l.targets),I=C(this,kt)[kt](l.targets),D=C(this,J)[J](l.targets);let N;return this.opts.theme==="auto"?N=d.darkMode?"dark":"light":N=this.opts.theme,["files","folders","both"].indexOf(this.opts.fileManagerSelectionType)<0&&(this.opts.fileManagerSelectionType="files",console.warn(`Unsupported option for "fileManagerSelectionType". Using default of "${this.opts.fileManagerSelectionType}".`)),fa({state:r,isHidden:l.isHidden,files:u,newFiles:c,uploadStartedFiles:p,completeFiles:f,erroredFiles:m,inProgressFiles:b,inProgressNotPausedFiles:g,processingFiles:v,isUploadStarted:P,isAllComplete:w,isAllErrored:S,isAllPaused:T,totalFileCount:Object.keys(u).length,totalProgress:r.totalProgress,allowNewUpload:h,acquirers:R,theme:N,disabled:this.opts.disabled,disableLocalFiles:this.opts.disableLocalFiles,direction:this.opts.direction,activePickerPanel:l.activePickerPanel,showFileEditor:l.showFileEditor,saveFileEditor:this.saveFileEditor,closeFileEditor:this.closeFileEditor,disableInteractiveElements:this.disableInteractiveElements,animateOpenClose:this.opts.animateOpenClose,isClosing:l.isClosing,progressindicators:I,editors:D,autoProceed:this.uppy.opts.autoProceed,id:this.id,closeModal:this.requestCloseModal,handleClickOutside:this.handleClickOutside,handleInputChange:this.handleInputChange,handlePaste:this.handlePaste,inline:this.opts.inline,showPanel:this.showPanel,hideAllPanels:this.hideAllPanels,i18n:this.i18n,i18nArray:this.i18nArray,uppy:this.uppy,note:this.opts.note,recoveredState:r.recoveredState,metaFields:l.metaFields,resumableUploads:d.resumableUploads||!1,individualCancellation:d.individualCancellation,isMobileDevice:d.isMobileDevice,fileCardFor:l.fileCardFor,toggleFileCard:this.toggleFileCard,toggleAddFilesPanel:this.toggleAddFilesPanel,showAddFilesPanel:l.showAddFilesPanel,saveFileCard:this.saveFileCard,openFileEditor:this.openFileEditor,canEditFile:this.canEditFile,width:this.opts.width,height:this.opts.height,showLinkToFileUploadResult:this.opts.showLinkToFileUploadResult,fileManagerSelectionType:this.opts.fileManagerSelectionType,proudlyDisplayPoweredByUppy:this.opts.proudlyDisplayPoweredByUppy,hideCancelButton:this.opts.hideCancelButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,showRemoveButtonAfterComplete:this.opts.showRemoveButtonAfterComplete,containerWidth:l.containerWidth,containerHeight:l.containerHeight,areInsidesReadyToBeVisible:l.areInsidesReadyToBeVisible,isTargetDOMEl:this.isTargetDOMEl,parentElement:this.el,allowedFileTypes:this.uppy.opts.restrictions.allowedFileTypes,maxNumberOfFiles:this.uppy.opts.restrictions.maxNumberOfFiles,requiredMetaFields:this.uppy.opts.restrictions.requiredMetaFields,showSelectedFiles:this.opts.showSelectedFiles,showNativePhotoCameraButton:this.opts.showNativePhotoCameraButton,showNativeVideoCameraButton:this.opts.showNativeVideoCameraButton,nativeCameraFacingMode:this.opts.nativeCameraFacingMode,singleFileFullScreen:this.opts.singleFileFullScreen,handleCancelRestore:this.handleCancelRestore,handleRequestThumbnail:this.handleRequestThumbnail,handleCancelThumbnail:this.handleCancelThumbnail,isDraggingOver:l.isDraggingOver,handleDragOver:this.handleDragOver,handleDragLeave:this.handleDragLeave,handleDrop:this.handleDrop})},Object.defineProperty(this,Dt,{writable:!0,value:()=>{(this.opts.plugins||[]).forEach(l=>{const u=this.uppy.getPlugin(l);u?u.mount(this,u):this.uppy.log(`[Uppy] Dashboard could not find plugin '${l}', make sure to uppy.use() the plugins you are specifying`,"warning")})}}),Object.defineProperty(this,Bt,{writable:!0,value:()=>{this.uppy.iteratePlugins(C(this,ce)[ce])}}),Object.defineProperty(this,ce,{writable:!0,value:r=>{var l;const u=["acquirer","editor"];r&&!((l=r.opts)!=null&&l.target)&&u.includes(r.type)&&(this.getPluginState().targets.some(h=>r.id===h.id)||r.mount(this,r))}}),this.install=()=>{this.setPluginState({isHidden:!0,fileCardFor:null,activeOverlayType:null,showAddFilesPanel:!1,activePickerPanel:void 0,showFileEditor:!1,metaFields:this.opts.metaFields,targets:[],areInsidesReadyToBeVisible:!1,isDraggingOver:!1});const{inline:r,closeAfterFinish:l}=this.opts;if(r&&l)throw new Error("[Dashboard] `closeAfterFinish: true` cannot be used on an inline Dashboard, because an inline Dashboard cannot be closed at all. Either set `inline: false`, or disable the `closeAfterFinish` option.");const{allowMultipleUploads:u,allowMultipleUploadBatches:d}=this.uppy.opts;(u||d)&&l&&this.uppy.log("[Dashboard] When using `closeAfterFinish`, we recommended setting the `allowMultipleUploadBatches` option to `false` in the Uppy constructor. See https://uppy.io/docs/uppy/#allowMultipleUploads-true","warning");const{target:h}=this.opts;h&&this.mount(h,this),this.opts.disableStatusBar||this.uppy.use(Ki,{id:`${this.id}:StatusBar`,target:this,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,showProgressDetails:this.opts.showProgressDetails,hideAfterFinish:this.opts.hideProgressAfterFinish,locale:this.opts.locale,doneButtonHandler:this.opts.doneButtonHandler}),this.opts.disableInformer||this.uppy.use(Yi,{id:`${this.id}:Informer`,target:this}),this.opts.disableThumbnailGenerator||this.uppy.use(as,{id:`${this.id}:ThumbnailGenerator`,thumbnailWidth:this.opts.thumbnailWidth,thumbnailHeight:this.opts.thumbnailHeight,thumbnailType:this.opts.thumbnailType,waitForThumbnailsBeforeUpload:this.opts.waitForThumbnailsBeforeUpload,lazy:!this.opts.waitForThumbnailsBeforeUpload}),this.darkModeMediaQuery=typeof window<"u"&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null;const c=this.darkModeMediaQuery?this.darkModeMediaQuery.matches:!1;if(this.uppy.log(`[Dashboard] Dark mode is ${c?"on":"off"}`),this.setDarkModeCapability(c),this.opts.theme==="auto"){var p;(p=this.darkModeMediaQuery)==null||p.addListener(this.handleSystemDarkModeChange)}C(this,Dt)[Dt](),C(this,Bt)[Bt](),this.initEvents()},this.uninstall=()=>{if(!this.opts.disableInformer){const u=this.uppy.getPlugin(`${this.id}:Informer`);u&&this.uppy.removePlugin(u)}if(!this.opts.disableStatusBar){const u=this.uppy.getPlugin(`${this.id}:StatusBar`);u&&this.uppy.removePlugin(u)}if(!this.opts.disableThumbnailGenerator){const u=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);u&&this.uppy.removePlugin(u)}if((this.opts.plugins||[]).forEach(u=>{const d=this.uppy.getPlugin(u);d&&d.unmount()}),this.opts.theme==="auto"){var l;(l=this.darkModeMediaQuery)==null||l.removeListener(this.handleSystemDarkModeChange)}this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.unmount(),this.removeEvents()},this.id=this.opts.id||"Dashboard",this.title="Dashboard",this.type="orchestrator",this.defaultLocale=ma,this.opts.doneButtonHandler===void 0&&(this.opts.doneButtonHandler=()=>{this.uppy.clearUploadedFiles(),this.requestCloseModal()}),(n=(s=this.opts).onRequestCloseModal)!=null||(s.onRequestCloseModal=()=>this.closeModal()),this.i18nInit()}}wa.VERSION=ya.version;export{dn as P,vn as S,Cr as g,Br as i,Qt as m,Le as t};
