import React from "react";
import {
  MultiSelector,
  MultiSelectorContent,
  MultiSelectorInput,
  MultiSelectorItem,
  MultiSelectorList,
  MultiSelectorTrigger,
} from "./MultiSelect";

const FormMultiSelect = ({
  values,
  onValuesChange,
  options,
  placeholder = "",
  className = "h-[36px] w-full",
  selectRef,
}) => {
  return (
    <div className={className}>
      <MultiSelector
        values={values}
        onValuesChange={onValuesChange}
        data={options}
        placeholder={placeholder}
      >
        <MultiSelectorTrigger className={className}>
          <MultiSelectorInput
            className="relative border-transparent"
            placeholder={placeholder}
            ref={selectRef}
          />
        </MultiSelectorTrigger>
        <div>
          <MultiSelectorContent>
            <MultiSelectorList className="z-[50] bg-form-input">
              {options.map((option, i) => (
                <MultiSelectorItem key={option.value} value={option.value}>
                  <span className="cursor-pointer"> {option.label}</span>
                </MultiSelectorItem>
              ))}
            </MultiSelectorList>
          </MultiSelectorContent>
        </div>
      </MultiSelector>
    </div>
  );
};

export default FormMultiSelect;
