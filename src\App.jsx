import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import "@uppy/core/dist/style.css";
import "@uppy/dashboard/dist/style.css";
import React from "react";
import "react-loading-skeleton/dist/skeleton.css";
import { BrowserRouter as Router } from "react-router-dom";
import AuthProvider from "./authContext";
import GlobalProvider from "./globalContext";
import Main from "./main";

const stripePromise = loadStripe(
  "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"
);

function App() {
  if (typeof window !== "undefined" && typeof global === "undefined") {
    window.global = window;
  }

  return (
    <AuthProvider>
      <GlobalProvider>
        <Router>
          <Elements stripe={stripePromise}>
            <Main />
          </Elements>
        </Router>
      </GlobalProvider>
    </AuthProvider>
  );
}

export default App;
