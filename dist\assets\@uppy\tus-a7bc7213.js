import{B as Mt,R as Gt,E as Kt,f as Xt,a as Zt,h as we,i as Wt,N as Yt}from"./aws-s3-5653d8cf.js";import{g as Vt,d as ze}from"../vendor-3aca5368.js";const bt="3.7.7",Jt=bt,J=typeof Buffer=="function",He=typeof TextDecoder=="function"?new TextDecoder:void 0,Me=typeof TextEncoder=="function"?new TextEncoder:void 0,Qt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",re=Array.prototype.slice.call(Qt),pe=(e=>{let t={};return e.forEach((r,n)=>t[r]=n),t})(re),er=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,R=String.fromCharCode.bind(String),Ge=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),_t=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),wt=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),St=e=>{let t,r,n,o,a="";const l=e.length%3;for(let f=0;f<e.length;){if((r=e.charCodeAt(f++))>255||(n=e.charCodeAt(f++))>255||(o=e.charCodeAt(f++))>255)throw new TypeError("invalid character found");t=r<<16|n<<8|o,a+=re[t>>18&63]+re[t>>12&63]+re[t>>6&63]+re[t&63]}return l?a.slice(0,l-3)+"===".substring(l):a},qe=typeof btoa=="function"?e=>btoa(e):J?e=>Buffer.from(e,"binary").toString("base64"):St,Pe=J?e=>Buffer.from(e).toString("base64"):e=>{let r=[];for(let n=0,o=e.length;n<o;n+=4096)r.push(R.apply(null,e.subarray(n,n+4096)));return qe(r.join(""))},de=(e,t=!1)=>t?_t(Pe(e)):Pe(e),tr=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?R(192|t>>>6)+R(128|t&63):R(224|t>>>12&15)+R(128|t>>>6&63)+R(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return R(240|t>>>18&7)+R(128|t>>>12&63)+R(128|t>>>6&63)+R(128|t&63)}},rr=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,Ut=e=>e.replace(rr,tr),Ke=J?e=>Buffer.from(e,"utf8").toString("base64"):Me?e=>Pe(Me.encode(e)):e=>qe(Ut(e)),W=(e,t=!1)=>t?_t(Ke(e)):Ke(e),Xe=e=>W(e,!0),nr=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,or=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),r=t-65536;return R((r>>>10)+55296)+R((r&1023)+56320);case 3:return R((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return R((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},Pt=e=>e.replace(nr,or),xt=e=>{if(e=e.replace(/\s+/g,""),!er.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,r="",n,o;for(let a=0;a<e.length;)t=pe[e.charAt(a++)]<<18|pe[e.charAt(a++)]<<12|(n=pe[e.charAt(a++)])<<6|(o=pe[e.charAt(a++)]),r+=n===64?R(t>>16&255):o===64?R(t>>16&255,t>>8&255):R(t>>16&255,t>>8&255,t&255);return r},$e=typeof atob=="function"?e=>atob(wt(e)):J?e=>Buffer.from(e,"base64").toString("binary"):xt,Et=J?e=>Ge(Buffer.from(e,"base64")):e=>Ge($e(e).split("").map(t=>t.charCodeAt(0))),Ot=e=>Et(Rt(e)),ir=J?e=>Buffer.from(e,"base64").toString("utf8"):He?e=>He.decode(Et(e)):e=>Pt($e(e)),Rt=e=>wt(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),xe=e=>ir(Rt(e)),ar=e=>{if(typeof e!="string")return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},kt=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),jt=function(){const e=(t,r)=>Object.defineProperty(String.prototype,t,kt(r));e("fromBase64",function(){return xe(this)}),e("toBase64",function(t){return W(this,t)}),e("toBase64URI",function(){return W(this,!0)}),e("toBase64URL",function(){return W(this,!0)}),e("toUint8Array",function(){return Ot(this)})},Lt=function(){const e=(t,r)=>Object.defineProperty(Uint8Array.prototype,t,kt(r));e("toBase64",function(t){return de(this,t)}),e("toBase64URI",function(){return de(this,!0)}),e("toBase64URL",function(){return de(this,!0)})},ur=()=>{jt(),Lt()},sr={version:bt,VERSION:Jt,atob:$e,atobPolyfill:xt,btoa:qe,btoaPolyfill:St,fromBase64:xe,toBase64:W,encode:W,encodeURI:Xe,encodeURL:Xe,utob:Ut,btou:Pt,decode:xe,isValid:ar,fromUint8Array:de,toUint8Array:Ot,extendString:jt,extendUint8Array:Lt,extendBuiltins:ur};var lr=function(t,r){if(r=r.split(":")[0],t=+t,!t)return!1;switch(r){case"http":case"ws":return t!==80;case"https":case"wss":return t!==443;case"ftp":return t!==21;case"gopher":return t!==70;case"file":return!1}return t!==0},De={},fr=Object.prototype.hasOwnProperty,cr;function Ze(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch{return null}}function We(e){try{return encodeURIComponent(e)}catch{return null}}function pr(e){for(var t=/([^=?#&]+)=?([^&]*)/g,r={},n;n=t.exec(e);){var o=Ze(n[1]),a=Ze(n[2]);o===null||a===null||o in r||(r[o]=a)}return r}function hr(e,t){t=t||"";var r=[],n,o;typeof t!="string"&&(t="?");for(o in e)if(fr.call(e,o)){if(n=e[o],!n&&(n===null||n===cr||isNaN(n))&&(n=""),o=We(o),n=We(n),o===null||n===null)continue;r.push(o+"="+n)}return r.length?t+r.join("&"):""}De.stringify=hr;De.parse=pr;var At=lr,ge=De,dr=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,Ft=/[\n\r\t]/g,yr=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,Ct=/:\d+$/,mr=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,vr=/^[a-zA-Z]:/;function Be(e){return(e||"").toString().replace(dr,"")}var Ee=[["#","hash"],["?","query"],function(t,r){return q(r.protocol)?t.replace(/\\/g,"/"):t},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],Ye={hash:1,query:1};function Tt(e){var t;typeof window<"u"?t=window:typeof ze<"u"?t=ze:typeof self<"u"?t=self:t={};var r=t.location||{};e=e||r;var n={},o=typeof e,a;if(e.protocol==="blob:")n=new $(unescape(e.pathname),{});else if(o==="string"){n=new $(e,{});for(a in Ye)delete n[a]}else if(o==="object"){for(a in e)a in Ye||(n[a]=e[a]);n.slashes===void 0&&(n.slashes=yr.test(e.href))}return n}function q(e){return e==="file:"||e==="ftp:"||e==="http:"||e==="https:"||e==="ws:"||e==="wss:"}function qt(e,t){e=Be(e),e=e.replace(Ft,""),t=t||{};var r=mr.exec(e),n=r[1]?r[1].toLowerCase():"",o=!!r[2],a=!!r[3],l=0,f;return o?a?(f=r[2]+r[3]+r[4],l=r[2].length+r[3].length):(f=r[2]+r[4],l=r[2].length):a?(f=r[3]+r[4],l=r[3].length):f=r[4],n==="file:"?l>=2&&(f=f.slice(2)):q(n)?f=r[4]:n?o&&(f=f.slice(2)):l>=2&&q(t.protocol)&&(f=r[4]),{protocol:n,slashes:o||q(n),slashesCount:l,rest:f}}function gr(e,t){if(e==="")return t;for(var r=(t||"/").split("/").slice(0,-1).concat(e.split("/")),n=r.length,o=r[n-1],a=!1,l=0;n--;)r[n]==="."?r.splice(n,1):r[n]===".."?(r.splice(n,1),l++):l&&(n===0&&(a=!0),r.splice(n,1),l--);return a&&r.unshift(""),(o==="."||o==="..")&&r.push(""),r.join("/")}function $(e,t,r){if(e=Be(e),e=e.replace(Ft,""),!(this instanceof $))return new $(e,t,r);var n,o,a,l,f,h,m=Ee.slice(),w=typeof t,y=this,E=0;for(w!=="object"&&w!=="string"&&(r=t,t=null),r&&typeof r!="function"&&(r=ge.parse),t=Tt(t),o=qt(e||"",t),n=!o.protocol&&!o.slashes,y.slashes=o.slashes||n&&t.slashes,y.protocol=o.protocol||t.protocol||"",e=o.rest,(o.protocol==="file:"&&(o.slashesCount!==2||vr.test(e))||!o.slashes&&(o.protocol||o.slashesCount<2||!q(y.protocol)))&&(m[3]=[/(.*)/,"pathname"]);E<m.length;E++){if(l=m[E],typeof l=="function"){e=l(e,y);continue}a=l[0],h=l[1],a!==a?y[h]=e:typeof a=="string"?(f=a==="@"?e.lastIndexOf(a):e.indexOf(a),~f&&(typeof l[2]=="number"?(y[h]=e.slice(0,f),e=e.slice(f+l[2])):(y[h]=e.slice(f),e=e.slice(0,f)))):(f=a.exec(e))&&(y[h]=f[1],e=e.slice(0,f.index)),y[h]=y[h]||n&&l[3]&&t[h]||"",l[4]&&(y[h]=y[h].toLowerCase())}r&&(y.query=r(y.query)),n&&t.slashes&&y.pathname.charAt(0)!=="/"&&(y.pathname!==""||t.pathname!=="")&&(y.pathname=gr(y.pathname,t.pathname)),y.pathname.charAt(0)!=="/"&&q(y.protocol)&&(y.pathname="/"+y.pathname),At(y.port,y.protocol)||(y.host=y.hostname,y.port=""),y.username=y.password="",y.auth&&(f=y.auth.indexOf(":"),~f?(y.username=y.auth.slice(0,f),y.username=encodeURIComponent(decodeURIComponent(y.username)),y.password=y.auth.slice(f+1),y.password=encodeURIComponent(decodeURIComponent(y.password))):y.username=encodeURIComponent(decodeURIComponent(y.auth)),y.auth=y.password?y.username+":"+y.password:y.username),y.origin=y.protocol!=="file:"&&q(y.protocol)&&y.host?y.protocol+"//"+y.host:"null",y.href=y.toString()}function br(e,t,r){var n=this;switch(e){case"query":typeof t=="string"&&t.length&&(t=(r||ge.parse)(t)),n[e]=t;break;case"port":n[e]=t,At(t,n.protocol)?t&&(n.host=n.hostname+":"+t):(n.host=n.hostname,n[e]="");break;case"hostname":n[e]=t,n.port&&(t+=":"+n.port),n.host=t;break;case"host":n[e]=t,Ct.test(t)?(t=t.split(":"),n.port=t.pop(),n.hostname=t.join(":")):(n.hostname=t,n.port="");break;case"protocol":n.protocol=t.toLowerCase(),n.slashes=!r;break;case"pathname":case"hash":if(t){var o=e==="pathname"?"/":"#";n[e]=t.charAt(0)!==o?o+t:t}else n[e]=t;break;case"username":case"password":n[e]=encodeURIComponent(t);break;case"auth":var a=t.indexOf(":");~a?(n.username=t.slice(0,a),n.username=encodeURIComponent(decodeURIComponent(n.username)),n.password=t.slice(a+1),n.password=encodeURIComponent(decodeURIComponent(n.password))):n.username=encodeURIComponent(decodeURIComponent(t))}for(var l=0;l<Ee.length;l++){var f=Ee[l];f[4]&&(n[f[1]]=n[f[1]].toLowerCase())}return n.auth=n.password?n.username+":"+n.password:n.username,n.origin=n.protocol!=="file:"&&q(n.protocol)&&n.host?n.protocol+"//"+n.host:"null",n.href=n.toString(),n}function _r(e){(!e||typeof e!="function")&&(e=ge.stringify);var t,r=this,n=r.host,o=r.protocol;o&&o.charAt(o.length-1)!==":"&&(o+=":");var a=o+(r.protocol&&r.slashes||q(r.protocol)?"//":"");return r.username?(a+=r.username,r.password&&(a+=":"+r.password),a+="@"):r.password?(a+=":"+r.password,a+="@"):r.protocol!=="file:"&&q(r.protocol)&&!n&&r.pathname!=="/"&&(a+="@"),(n[n.length-1]===":"||Ct.test(r.hostname)&&!r.port)&&(n+=":"),a+=n+r.pathname,t=typeof r.query=="object"?e(r.query):r.query,t&&(a+=t.charAt(0)!=="?"?"?"+t:t),r.hash&&(a+=r.hash),a}$.prototype={set:br,toString:_r};$.extractProtocol=qt;$.location=Tt;$.trimLeft=Be;$.qs=ge;var wr=$;const Sr=Vt(wr);function Y(e){"@babel/helpers - typeof";return Y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(e)}function Ve(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Pr(n.key),n)}}function Ur(e,t,r){return t&&Ve(e.prototype,t),r&&Ve(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Pr(e){var t=xr(e,"string");return Y(t)==="symbol"?t:String(t)}function xr(e,t){if(Y(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Y(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Er(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Or(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&oe(e,t)}function Rr(e){var t=$t();return function(){var n=ie(e),o;if(t){var a=ie(this).constructor;o=Reflect.construct(n,arguments,a)}else o=n.apply(this,arguments);return kr(this,o)}}function kr(e,t){if(t&&(Y(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return jr(e)}function jr(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Oe(e){var t=typeof Map=="function"?new Map:void 0;return Oe=function(n){if(n===null||!Lr(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof t<"u"){if(t.has(n))return t.get(n);t.set(n,o)}function o(){return ye(n,arguments,ie(this).constructor)}return o.prototype=Object.create(n.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),oe(o,n)},Oe(e)}function ye(e,t,r){return $t()?ye=Reflect.construct.bind():ye=function(o,a,l){var f=[null];f.push.apply(f,a);var h=Function.bind.apply(o,f),m=new h;return l&&oe(m,l.prototype),m},ye.apply(null,arguments)}function $t(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Lr(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}function oe(e,t){return oe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},oe(e,t)}function ie(e){return ie=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ie(e)}var he=function(e){Or(r,e);var t=Rr(r);function r(n){var o,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,f=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;if(Er(this,r),o=t.call(this,n),o.originalRequest=l,o.originalResponse=f,o.causingError=a,a!=null&&(n+=", caused by ".concat(a.toString())),l!=null){var h=l.getHeader("X-Request-ID")||"n/a",m=l.getMethod(),w=l.getURL(),y=f?f.getStatus():"n/a",E=f?f.getBody()||"":"n/a";n+=", originated from request (method: ".concat(m,", url: ").concat(w,", response code: ").concat(y,", response text: ").concat(E,", request id: ").concat(h,")")}return o.message=n,o}return Ur(r)}(Oe(Error));function Ar(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=Math.random()*16|0,r=e==="x"?t:t&3|8;return r.toString(16)})}function Re(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Re=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(s,i,u){s[i]=u.value},a=typeof Symbol=="function"?Symbol:{},l=a.iterator||"@@iterator",f=a.asyncIterator||"@@asyncIterator",h=a.toStringTag||"@@toStringTag";function m(s,i,u){return Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}),s[i]}try{m({},"")}catch{m=function(u,c,d){return u[c]=d}}function w(s,i,u,c){var d=i&&i.prototype instanceof v?i:v,p=Object.create(d.prototype),g=new I(c||[]);return o(p,"_invoke",{value:be(s,u,g)}),p}function y(s,i,u){try{return{type:"normal",arg:s.call(i,u)}}catch(c){return{type:"throw",arg:c}}}t.wrap=w;var E="suspendedStart",B="suspendedYield",F="executing",C="completed",U={};function v(){}function b(){}function _(){}var x={};m(x,l,function(){return this});var L=Object.getPrototypeOf,T=L&&L(L(z([])));T&&T!==r&&n.call(T,l)&&(x=T);var A=_.prototype=v.prototype=Object.create(x);function Q(s){["next","throw","return"].forEach(function(i){m(s,i,function(u){return this._invoke(i,u)})})}function D(s,i){function u(d,p,g,S){var P=y(s[d],s,p);if(P.type!=="throw"){var k=P.arg,O=k.value;return O&&M(O)=="object"&&n.call(O,"__await")?i.resolve(O.__await).then(function(j){u("next",j,g,S)},function(j){u("throw",j,g,S)}):i.resolve(O).then(function(j){k.value=j,g(k)},function(j){return u("throw",j,g,S)})}S(P.arg)}var c;o(this,"_invoke",{value:function(p,g){function S(){return new i(function(P,k){u(p,g,P,k)})}return c=c?c.then(S,S):S()}})}function be(s,i,u){var c=E;return function(d,p){if(c===F)throw new Error("Generator is already running");if(c===C){if(d==="throw")throw p;return{value:e,done:!0}}for(u.method=d,u.arg=p;;){var g=u.delegate;if(g){var S=ee(g,u);if(S){if(S===U)continue;return S}}if(u.method==="next")u.sent=u._sent=u.arg;else if(u.method==="throw"){if(c===E)throw c=C,u.arg;u.dispatchException(u.arg)}else u.method==="return"&&u.abrupt("return",u.arg);c=F;var P=y(s,i,u);if(P.type==="normal"){if(c=u.done?C:B,P.arg===U)continue;return{value:P.arg,done:u.done}}P.type==="throw"&&(c=C,u.method="throw",u.arg=P.arg)}}}function ee(s,i){var u=i.method,c=s.iterator[u];if(c===e)return i.delegate=null,u==="throw"&&s.iterator.return&&(i.method="return",i.arg=e,ee(s,i),i.method==="throw")||u!=="return"&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+u+"' method")),U;var d=y(c,s.iterator,i.arg);if(d.type==="throw")return i.method="throw",i.arg=d.arg,i.delegate=null,U;var p=d.arg;return p?p.done?(i[s.resultName]=p.value,i.next=s.nextLoc,i.method!=="return"&&(i.method="next",i.arg=e),i.delegate=null,U):p:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,U)}function _e(s){var i={tryLoc:s[0]};1 in s&&(i.catchLoc=s[1]),2 in s&&(i.finallyLoc=s[2],i.afterLoc=s[3]),this.tryEntries.push(i)}function N(s){var i=s.completion||{};i.type="normal",delete i.arg,s.completion=i}function I(s){this.tryEntries=[{tryLoc:"root"}],s.forEach(_e,this),this.reset(!0)}function z(s){if(s||s===""){var i=s[l];if(i)return i.call(s);if(typeof s.next=="function")return s;if(!isNaN(s.length)){var u=-1,c=function d(){for(;++u<s.length;)if(n.call(s,u))return d.value=s[u],d.done=!1,d;return d.value=e,d.done=!0,d};return c.next=c}}throw new TypeError(M(s)+" is not iterable")}return b.prototype=_,o(A,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:b,configurable:!0}),b.displayName=m(_,h,"GeneratorFunction"),t.isGeneratorFunction=function(s){var i=typeof s=="function"&&s.constructor;return!!i&&(i===b||(i.displayName||i.name)==="GeneratorFunction")},t.mark=function(s){return Object.setPrototypeOf?Object.setPrototypeOf(s,_):(s.__proto__=_,m(s,h,"GeneratorFunction")),s.prototype=Object.create(A),s},t.awrap=function(s){return{__await:s}},Q(D.prototype),m(D.prototype,f,function(){return this}),t.AsyncIterator=D,t.async=function(s,i,u,c,d){d===void 0&&(d=Promise);var p=new D(w(s,i,u,c),d);return t.isGeneratorFunction(i)?p:p.next().then(function(g){return g.done?g.value:p.next()})},Q(A),m(A,h,"Generator"),m(A,l,function(){return this}),m(A,"toString",function(){return"[object Generator]"}),t.keys=function(s){var i=Object(s),u=[];for(var c in i)u.push(c);return u.reverse(),function d(){for(;u.length;){var p=u.pop();if(p in i)return d.value=p,d.done=!1,d}return d.done=!0,d}},t.values=z,I.prototype={constructor:I,reset:function(i){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!i)for(var u in this)u.charAt(0)==="t"&&n.call(this,u)&&!isNaN(+u.slice(1))&&(this[u]=e)},stop:function(){this.done=!0;var i=this.tryEntries[0].completion;if(i.type==="throw")throw i.arg;return this.rval},dispatchException:function(i){if(this.done)throw i;var u=this;function c(k,O){return g.type="throw",g.arg=i,u.next=k,O&&(u.method="next",u.arg=e),!!O}for(var d=this.tryEntries.length-1;d>=0;--d){var p=this.tryEntries[d],g=p.completion;if(p.tryLoc==="root")return c("end");if(p.tryLoc<=this.prev){var S=n.call(p,"catchLoc"),P=n.call(p,"finallyLoc");if(S&&P){if(this.prev<p.catchLoc)return c(p.catchLoc,!0);if(this.prev<p.finallyLoc)return c(p.finallyLoc)}else if(S){if(this.prev<p.catchLoc)return c(p.catchLoc,!0)}else{if(!P)throw new Error("try statement without catch or finally");if(this.prev<p.finallyLoc)return c(p.finallyLoc)}}}},abrupt:function(i,u){for(var c=this.tryEntries.length-1;c>=0;--c){var d=this.tryEntries[c];if(d.tryLoc<=this.prev&&n.call(d,"finallyLoc")&&this.prev<d.finallyLoc){var p=d;break}}p&&(i==="break"||i==="continue")&&p.tryLoc<=u&&u<=p.finallyLoc&&(p=null);var g=p?p.completion:{};return g.type=i,g.arg=u,p?(this.method="next",this.next=p.finallyLoc,U):this.complete(g)},complete:function(i,u){if(i.type==="throw")throw i.arg;return i.type==="break"||i.type==="continue"?this.next=i.arg:i.type==="return"?(this.rval=this.arg=i.arg,this.method="return",this.next="end"):i.type==="normal"&&u&&(this.next=u),U},finish:function(i){for(var u=this.tryEntries.length-1;u>=0;--u){var c=this.tryEntries[u];if(c.finallyLoc===i)return this.complete(c.completion,c.afterLoc),N(c),U}},catch:function(i){for(var u=this.tryEntries.length-1;u>=0;--u){var c=this.tryEntries[u];if(c.tryLoc===i){var d=c.completion;if(d.type==="throw"){var p=d.arg;N(c)}return p}}throw new Error("illegal catch attempt")},delegateYield:function(i,u,c){return this.delegate={iterator:z(i),resultName:u,nextLoc:c},this.method==="next"&&(this.arg=e),U}},t}function Je(e,t,r,n,o,a,l){try{var f=e[a](l),h=f.value}catch(m){r(m);return}f.done?t(h):Promise.resolve(h).then(n,o)}function Fr(e){return function(){var t=this,r=arguments;return new Promise(function(n,o){var a=e.apply(t,r);function l(h){Je(a,n,o,l,f,"next",h)}function f(h){Je(a,n,o,l,f,"throw",h)}l(void 0)})}}function Dt(e,t){return $r(e)||qr(e,t)||Tr(e,t)||Cr()}function Cr(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Tr(e,t){if(e){if(typeof e=="string")return Qe(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qe(e,t)}}function Qe(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function qr(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,o,a,l,f=[],h=!0,m=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;h=!1}else for(;!(h=(n=a.call(r)).done)&&(f.push(n.value),f.length!==t);h=!0);}catch(w){m=!0,o=w}finally{try{if(!h&&r.return!=null&&(l=r.return(),Object(l)!==l))return}finally{if(m)throw o}}return f}}function $r(e){if(Array.isArray(e))return e}function M(e){"@babel/helpers - typeof";return M=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(e)}function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function K(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?et(Object(r),!0).forEach(function(n){Dr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Dr(e,t,r){return t=Bt(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Br(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function tt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Bt(n.key),n)}}function Nr(e,t,r){return t&&tt(e.prototype,t),r&&tt(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Bt(e){var t=Ir(e,"string");return M(t)==="symbol"?t:String(t)}function Ir(e,t){if(M(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(M(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var zr={endpoint:null,uploadUrl:null,metadata:{},fingerprint:null,uploadSize:null,onProgress:null,onChunkComplete:null,onSuccess:null,onError:null,onUploadUrlAvailable:null,overridePatchMethod:!1,headers:{},addRequestId:!1,onBeforeRequest:null,onAfterResponse:null,onShouldRetry:Nt,chunkSize:1/0,retryDelays:[0,1e3,3e3,5e3],parallelUploads:1,parallelUploadBoundaries:null,storeFingerprintForResuming:!0,removeFingerprintOnSuccess:!1,uploadLengthDeferred:!1,uploadDataDuringCreation:!1,urlStorage:null,fileReader:null,httpStack:null},me=function(){function e(t,r){Br(this,e),"resume"in r&&console.log("tus: The `resume` option has been removed in tus-js-client v2. Please use the URL storage API instead."),this.options=r,this.options.chunkSize=Number(this.options.chunkSize),this._urlStorage=this.options.urlStorage,this.file=t,this.url=null,this._req=null,this._fingerprint=null,this._urlStorageKey=null,this._offset=null,this._aborted=!1,this._size=null,this._source=null,this._retryAttempt=0,this._retryTimeout=null,this._offsetBeforeRetry=0,this._parallelUploads=null,this._parallelUploadUrls=null}return Nr(e,[{key:"findPreviousUploads",value:function(){var r=this;return this.options.fingerprint(this.file,this.options).then(function(n){return r._urlStorage.findUploadsByFingerprint(n)})}},{key:"resumeFromPreviousUpload",value:function(r){this.url=r.uploadUrl||null,this._parallelUploadUrls=r.parallelUploadUrls||null,this._urlStorageKey=r.urlStorageKey}},{key:"start",value:function(){var r=this,n=this.file;if(!n){this._emitError(new Error("tus: no file or stream to upload provided"));return}if(!this.options.endpoint&&!this.options.uploadUrl&&!this.url){this._emitError(new Error("tus: neither an endpoint or an upload URL is provided"));return}var o=this.options.retryDelays;if(o!=null&&Object.prototype.toString.call(o)!=="[object Array]"){this._emitError(new Error("tus: the `retryDelays` option must either be an array or null"));return}if(this.options.parallelUploads>1)for(var a=0,l=["uploadUrl","uploadSize","uploadLengthDeferred"];a<l.length;a++){var f=l[a];if(this.options[f]){this._emitError(new Error("tus: cannot use the ".concat(f," option when parallelUploads is enabled")));return}}if(this.options.parallelUploadBoundaries){if(this.options.parallelUploads<=1){this._emitError(new Error("tus: cannot use the `parallelUploadBoundaries` option when `parallelUploads` is disabled"));return}if(this.options.parallelUploads!==this.options.parallelUploadBoundaries.length){this._emitError(new Error("tus: the `parallelUploadBoundaries` must have the same length as the value of `parallelUploads`"));return}}this.options.fingerprint(n,this.options).then(function(h){return r._fingerprint=h,r._source?r._source:r.options.fileReader.openFile(n,r.options.chunkSize)}).then(function(h){if(r._source=h,r.options.uploadLengthDeferred)r._size=null;else if(r.options.uploadSize!=null){if(r._size=Number(r.options.uploadSize),Number.isNaN(r._size)){r._emitError(new Error("tus: cannot convert `uploadSize` option into a number"));return}}else if(r._size=r._source.size,r._size==null){r._emitError(new Error("tus: cannot automatically derive upload's size from input. Specify it manually using the `uploadSize` option or use the `uploadLengthDeferred` option"));return}r.options.parallelUploads>1||r._parallelUploadUrls!=null?r._startParallelUpload():r._startSingleUpload()}).catch(function(h){r._emitError(h)})}},{key:"_startParallelUpload",value:function(){var r,n=this,o=this._size,a=0;this._parallelUploads=[];var l=this._parallelUploadUrls!=null?this._parallelUploadUrls.length:this.options.parallelUploads,f=(r=this.options.parallelUploadBoundaries)!==null&&r!==void 0?r:Mr(this._source.size,l);this._parallelUploadUrls&&f.forEach(function(w,y){w.uploadUrl=n._parallelUploadUrls[y]||null}),this._parallelUploadUrls=new Array(f.length);var h=f.map(function(w,y){var E=0;return n._source.slice(w.start,w.end).then(function(B){var F=B.value;return new Promise(function(C,U){var v=K(K({},n.options),{},{uploadUrl:w.uploadUrl||null,storeFingerprintForResuming:!1,removeFingerprintOnSuccess:!1,parallelUploads:1,parallelUploadBoundaries:null,metadata:{},headers:K(K({},n.options.headers),{},{"Upload-Concat":"partial"}),onSuccess:C,onError:U,onProgress:function(x){a=a-E+x,E=x,n._emitProgress(a,o)},onUploadUrlAvailable:function(){n._parallelUploadUrls[y]=b.url,n._parallelUploadUrls.filter(function(x){return!!x}).length===f.length&&n._saveUploadInUrlStorage()}}),b=new e(F,v);b.start(),n._parallelUploads.push(b)})})}),m;Promise.all(h).then(function(){m=n._openRequest("POST",n.options.endpoint),m.setHeader("Upload-Concat","final;".concat(n._parallelUploadUrls.join(" ")));var w=rt(n.options.metadata);return w!==""&&m.setHeader("Upload-Metadata",w),n._sendRequest(m,null)}).then(function(w){if(!X(w.getStatus(),200)){n._emitHttpError(m,w,"tus: unexpected response while creating upload");return}var y=w.getHeader("Location");if(y==null){n._emitHttpError(m,w,"tus: invalid or missing Location header");return}n.url=at(n.options.endpoint,y),"Created upload at ".concat(n.url),n._emitSuccess()}).catch(function(w){n._emitError(w)})}},{key:"_startSingleUpload",value:function(){if(this._aborted=!1,this.url!=null){"Resuming upload from previous URL: ".concat(this.url),this._resumeUpload();return}if(this.options.uploadUrl!=null){"Resuming upload from provided URL: ".concat(this.options.uploadUrl),this.url=this.options.uploadUrl,this._resumeUpload();return}this._createUpload()}},{key:"abort",value:function(r){var n=this;return this._parallelUploads!=null&&this._parallelUploads.forEach(function(o){o.abort(r)}),this._req!==null&&this._req.abort(),this._aborted=!0,this._retryTimeout!=null&&(clearTimeout(this._retryTimeout),this._retryTimeout=null),!r||this.url==null?Promise.resolve():e.terminate(this.url,this.options).then(function(){return n._removeFromUrlStorage()})}},{key:"_emitHttpError",value:function(r,n,o,a){this._emitError(new he(o,a,r,n))}},{key:"_emitError",value:function(r){var n=this;if(!this._aborted){if(this.options.retryDelays!=null){var o=this._offset!=null&&this._offset>this._offsetBeforeRetry;if(o&&(this._retryAttempt=0),it(r,this._retryAttempt,this.options)){var a=this.options.retryDelays[this._retryAttempt++];this._offsetBeforeRetry=this._offset,this._retryTimeout=setTimeout(function(){n.start()},a);return}}if(typeof this.options.onError=="function")this.options.onError(r);else throw r}}},{key:"_emitSuccess",value:function(){this.options.removeFingerprintOnSuccess&&this._removeFromUrlStorage(),typeof this.options.onSuccess=="function"&&this.options.onSuccess()}},{key:"_emitProgress",value:function(r,n){typeof this.options.onProgress=="function"&&this.options.onProgress(r,n)}},{key:"_emitChunkComplete",value:function(r,n,o){typeof this.options.onChunkComplete=="function"&&this.options.onChunkComplete(r,n,o)}},{key:"_createUpload",value:function(){var r=this;if(!this.options.endpoint){this._emitError(new Error("tus: unable to create upload because no endpoint is provided"));return}var n=this._openRequest("POST",this.options.endpoint);this.options.uploadLengthDeferred?n.setHeader("Upload-Defer-Length",1):n.setHeader("Upload-Length",this._size);var o=rt(this.options.metadata);o!==""&&n.setHeader("Upload-Metadata",o);var a;this.options.uploadDataDuringCreation&&!this.options.uploadLengthDeferred?(this._offset=0,a=this._addChunkToRequest(n)):a=this._sendRequest(n,null),a.then(function(l){if(!X(l.getStatus(),200)){r._emitHttpError(n,l,"tus: unexpected response while creating upload");return}var f=l.getHeader("Location");if(f==null){r._emitHttpError(n,l,"tus: invalid or missing Location header");return}if(r.url=at(r.options.endpoint,f),"Created upload at ".concat(r.url),typeof r.options.onUploadUrlAvailable=="function"&&r.options.onUploadUrlAvailable(),r._size===0){r._emitSuccess(),r._source.close();return}r._saveUploadInUrlStorage().then(function(){r.options.uploadDataDuringCreation?r._handleUploadResponse(n,l):(r._offset=0,r._performUpload())})}).catch(function(l){r._emitHttpError(n,null,"tus: failed to create upload",l)})}},{key:"_resumeUpload",value:function(){var r=this,n=this._openRequest("HEAD",this.url),o=this._sendRequest(n,null);o.then(function(a){var l=a.getStatus();if(!X(l,200)){if(l===423){r._emitHttpError(n,a,"tus: upload is currently locked; retry later");return}if(X(l,400)&&r._removeFromUrlStorage(),!r.options.endpoint){r._emitHttpError(n,a,"tus: unable to resume upload (new upload cannot be created without an endpoint)");return}r.url=null,r._createUpload();return}var f=parseInt(a.getHeader("Upload-Offset"),10);if(Number.isNaN(f)){r._emitHttpError(n,a,"tus: invalid or missing offset value");return}var h=parseInt(a.getHeader("Upload-Length"),10);if(Number.isNaN(h)&&!r.options.uploadLengthDeferred){r._emitHttpError(n,a,"tus: invalid or missing length value");return}typeof r.options.onUploadUrlAvailable=="function"&&r.options.onUploadUrlAvailable(),r._saveUploadInUrlStorage().then(function(){if(f===h){r._emitProgress(h,h),r._emitSuccess();return}r._offset=f,r._performUpload()})}).catch(function(a){r._emitHttpError(n,null,"tus: failed to resume upload",a)})}},{key:"_performUpload",value:function(){var r=this;if(!this._aborted){var n;this.options.overridePatchMethod?(n=this._openRequest("POST",this.url),n.setHeader("X-HTTP-Method-Override","PATCH")):n=this._openRequest("PATCH",this.url),n.setHeader("Upload-Offset",this._offset);var o=this._addChunkToRequest(n);o.then(function(a){if(!X(a.getStatus(),200)){r._emitHttpError(n,a,"tus: unexpected response while uploading chunk");return}r._handleUploadResponse(n,a)}).catch(function(a){r._aborted||r._emitHttpError(n,null,"tus: failed to upload chunk at offset ".concat(r._offset),a)})}}},{key:"_addChunkToRequest",value:function(r){var n=this,o=this._offset,a=this._offset+this.options.chunkSize;return r.setProgressHandler(function(l){n._emitProgress(o+l,n._size)}),r.setHeader("Content-Type","application/offset+octet-stream"),(a===1/0||a>this._size)&&!this.options.uploadLengthDeferred&&(a=this._size),this._source.slice(o,a).then(function(l){var f=l.value,h=l.done,m=f&&f.size?f.size:0;n.options.uploadLengthDeferred&&h&&(n._size=n._offset+m,r.setHeader("Upload-Length",n._size));var w=n._offset+m;return!n.options.uploadLengthDeferred&&h&&w!==n._size?Promise.reject(new Error("upload was configured with a size of ".concat(n._size," bytes, but the source is done after ").concat(w," bytes"))):f===null?n._sendRequest(r):(n._emitProgress(n._offset,n._size),n._sendRequest(r,f))})}},{key:"_handleUploadResponse",value:function(r,n){var o=parseInt(n.getHeader("Upload-Offset"),10);if(Number.isNaN(o)){this._emitHttpError(r,n,"tus: invalid or missing offset value");return}if(this._emitProgress(o,this._size),this._emitChunkComplete(o-this._offset,o,this._size),this._offset=o,o===this._size){this._emitSuccess(),this._source.close();return}this._performUpload()}},{key:"_openRequest",value:function(r,n){var o=nt(r,n,this.options);return this._req=o,o}},{key:"_removeFromUrlStorage",value:function(){var r=this;this._urlStorageKey&&(this._urlStorage.removeUpload(this._urlStorageKey).catch(function(n){r._emitError(n)}),this._urlStorageKey=null)}},{key:"_saveUploadInUrlStorage",value:function(){var r=this;if(!this.options.storeFingerprintForResuming||!this._fingerprint||this._urlStorageKey!==null)return Promise.resolve();var n={size:this._size,metadata:this.options.metadata,creationTime:new Date().toString()};return this._parallelUploads?n.parallelUploadUrls=this._parallelUploadUrls:n.uploadUrl=this.url,this._urlStorage.addUpload(this._fingerprint,n).then(function(o){r._urlStorageKey=o})}},{key:"_sendRequest",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return ot(r,n,this.options)}}],[{key:"terminate",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=nt("DELETE",r,n);return ot(o,null,n).then(function(a){if(a.getStatus()!==204)throw new he("tus: unexpected response while terminating upload",null,o,a)}).catch(function(a){if(a instanceof he||(a=new he("tus: failed to terminate upload",a,o,null)),!it(a,0,n))throw a;var l=n.retryDelays[0],f=n.retryDelays.slice(1),h=K(K({},n),{},{retryDelays:f});return new Promise(function(m){return setTimeout(m,l)}).then(function(){return e.terminate(r,h)})})}}]),e}();function rt(e){return Object.entries(e).map(function(t){var r=Dt(t,2),n=r[0],o=r[1];return"".concat(n," ").concat(sr.encode(String(o)))}).join(",")}function X(e,t){return e>=t&&e<t+100}function nt(e,t,r){var n=r.httpStack.createRequest(e,t);n.setHeader("Tus-Resumable","1.0.0");var o=r.headers||{};if(Object.entries(o).forEach(function(l){var f=Dt(l,2),h=f[0],m=f[1];n.setHeader(h,m)}),r.addRequestId){var a=Ar();n.setHeader("X-Request-ID",a)}return n}function ot(e,t,r){return ke.apply(this,arguments)}function ke(){return ke=Fr(Re().mark(function e(t,r,n){var o;return Re().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:if(typeof n.onBeforeRequest!="function"){l.next=3;break}return l.next=3,n.onBeforeRequest(t);case 3:return l.next=5,t.send(r);case 5:if(o=l.sent,typeof n.onAfterResponse!="function"){l.next=9;break}return l.next=9,n.onAfterResponse(t,o);case 9:return l.abrupt("return",o);case 10:case"end":return l.stop()}},e)})),ke.apply(this,arguments)}function Hr(){var e=!0;return typeof window<"u"&&"navigator"in window&&window.navigator.onLine===!1&&(e=!1),e}function it(e,t,r){return r.retryDelays==null||t>=r.retryDelays.length||e.originalRequest==null?!1:r&&typeof r.onShouldRetry=="function"?r.onShouldRetry(e,t,r):Nt(e)}function Nt(e){var t=e.originalResponse?e.originalResponse.getStatus():0;return(!X(t,400)||t===409||t===423)&&Hr()}function at(e,t){return new Sr(t,e).toString()}function Mr(e,t){for(var r=Math.floor(e/t),n=[],o=0;o<t;o++)n.push({start:r*o,end:r*(o+1)});return n[t-1].end=e,n}me.defaultOptions=zr;function ae(e){"@babel/helpers - typeof";return ae=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ae(e)}function Gr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ut(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xr(n.key),n)}}function Kr(e,t,r){return t&&ut(e.prototype,t),r&&ut(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Xr(e){var t=Zr(e,"string");return ae(t)==="symbol"?t:String(t)}function Zr(e,t){if(ae(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ae(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Wr=function(){function e(){Gr(this,e)}return Kr(e,[{key:"listAllUploads",value:function(){return Promise.resolve([])}},{key:"findUploadsByFingerprint",value:function(r){return Promise.resolve([])}},{key:"removeUpload",value:function(r){return Promise.resolve()}},{key:"addUpload",value:function(r,n){return Promise.resolve(null)}}]),e}();function ue(e){"@babel/helpers - typeof";return ue=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ue(e)}function Yr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function st(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Jr(n.key),n)}}function Vr(e,t,r){return t&&st(e.prototype,t),r&&st(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Jr(e){var t=Qr(e,"string");return ue(t)==="symbol"?t:String(t)}function Qr(e,t){if(ue(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ue(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var je=!1;try{je="localStorage"in window;var Se="tusSupport",lt=localStorage.getItem(Se);localStorage.setItem(Se,lt),lt===null&&localStorage.removeItem(Se)}catch(e){if(e.code===e.SECURITY_ERR||e.code===e.QUOTA_EXCEEDED_ERR)je=!1;else throw e}var en=je,tn=function(){function e(){Yr(this,e)}return Vr(e,[{key:"findAllUploads",value:function(){var r=this._findEntries("tus::");return Promise.resolve(r)}},{key:"findUploadsByFingerprint",value:function(r){var n=this._findEntries("tus::".concat(r,"::"));return Promise.resolve(n)}},{key:"removeUpload",value:function(r){return localStorage.removeItem(r),Promise.resolve()}},{key:"addUpload",value:function(r,n){var o=Math.round(Math.random()*1e12),a="tus::".concat(r,"::").concat(o);return localStorage.setItem(a,JSON.stringify(n)),Promise.resolve(a)}},{key:"_findEntries",value:function(r){for(var n=[],o=0;o<localStorage.length;o++){var a=localStorage.key(o);if(a.indexOf(r)===0)try{var l=JSON.parse(localStorage.getItem(a));l.urlStorageKey=a,n.push(l)}catch{}}return n}}]),e}();function se(e){"@babel/helpers - typeof";return se=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},se(e)}function Ne(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ft(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,rn(n.key),n)}}function Ie(e,t,r){return t&&ft(e.prototype,t),r&&ft(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function rn(e){var t=nn(e,"string");return se(t)==="symbol"?t:String(t)}function nn(e,t){if(se(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(se(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var on=function(){function e(){Ne(this,e)}return Ie(e,[{key:"createRequest",value:function(r,n){return new an(r,n)}},{key:"getName",value:function(){return"XHRHttpStack"}}]),e}(),an=function(){function e(t,r){Ne(this,e),this._xhr=new XMLHttpRequest,this._xhr.open(t,r,!0),this._method=t,this._url=r,this._headers={}}return Ie(e,[{key:"getMethod",value:function(){return this._method}},{key:"getURL",value:function(){return this._url}},{key:"setHeader",value:function(r,n){this._xhr.setRequestHeader(r,n),this._headers[r]=n}},{key:"getHeader",value:function(r){return this._headers[r]}},{key:"setProgressHandler",value:function(r){"upload"in this._xhr&&(this._xhr.upload.onprogress=function(n){n.lengthComputable&&r(n.loaded)})}},{key:"send",value:function(){var r=this,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;return new Promise(function(o,a){r._xhr.onload=function(){o(new un(r._xhr))},r._xhr.onerror=function(l){a(l)},r._xhr.send(n)})}},{key:"abort",value:function(){return this._xhr.abort(),Promise.resolve()}},{key:"getUnderlyingObject",value:function(){return this._xhr}}]),e}(),un=function(){function e(t){Ne(this,e),this._xhr=t}return Ie(e,[{key:"getStatus",value:function(){return this._xhr.status}},{key:"getHeader",value:function(r){return this._xhr.getResponseHeader(r)}},{key:"getBody",value:function(){return this._xhr.responseText}},{key:"getUnderlyingObject",value:function(){return this._xhr}}]),e}(),It=function(){return typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative"};function sn(e){return new Promise(function(t,r){var n=new XMLHttpRequest;n.responseType="blob",n.onload=function(){var o=n.response;t(o)},n.onerror=function(o){r(o)},n.open("GET",e),n.send()})}var ln=function(){return typeof window<"u"&&(typeof window.PhoneGap<"u"||typeof window.Cordova<"u"||typeof window.cordova<"u")};function fn(e){return new Promise(function(t,r){var n=new FileReader;n.onload=function(){var o=new Uint8Array(n.result);t({value:o})},n.onerror=function(o){r(o)},n.readAsArrayBuffer(e)})}function le(e){"@babel/helpers - typeof";return le=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},le(e)}function cn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ct(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hn(n.key),n)}}function pn(e,t,r){return t&&ct(e.prototype,t),r&&ct(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function hn(e){var t=dn(e,"string");return le(t)==="symbol"?t:String(t)}function dn(e,t){if(le(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(le(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var pt=function(){function e(t){cn(this,e),this._file=t,this.size=t.size}return pn(e,[{key:"slice",value:function(r,n){if(ln())return fn(this._file.slice(r,n));var o=this._file.slice(r,n),a=n>=this.size;return Promise.resolve({value:o,done:a})}},{key:"close",value:function(){}}]),e}();function fe(e){"@babel/helpers - typeof";return fe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fe(e)}function yn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ht(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vn(n.key),n)}}function mn(e,t,r){return t&&ht(e.prototype,t),r&&ht(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function vn(e){var t=gn(e,"string");return fe(t)==="symbol"?t:String(t)}function gn(e,t){if(fe(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(fe(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dt(e){return e===void 0?0:e.size!==void 0?e.size:e.length}function bn(e,t){if(e.concat)return e.concat(t);if(e instanceof Blob)return new Blob([e,t],{type:e.type});if(e.set){var r=new e.constructor(e.length+t.length);return r.set(e),r.set(t,e.length),r}throw new Error("Unknown data type")}var _n=function(){function e(t){yn(this,e),this._buffer=void 0,this._bufferOffset=0,this._reader=t,this._done=!1}return mn(e,[{key:"slice",value:function(r,n){return r<this._bufferOffset?Promise.reject(new Error("Requested data is before the reader's current offset")):this._readUntilEnoughDataOrDone(r,n)}},{key:"_readUntilEnoughDataOrDone",value:function(r,n){var o=this,a=n<=this._bufferOffset+dt(this._buffer);if(this._done||a){var l=this._getDataFromBuffer(r,n),f=l==null?this._done:!1;return Promise.resolve({value:l,done:f})}return this._reader.read().then(function(h){var m=h.value,w=h.done;return w?o._done=!0:o._buffer===void 0?o._buffer=m:o._buffer=bn(o._buffer,m),o._readUntilEnoughDataOrDone(r,n)})}},{key:"_getDataFromBuffer",value:function(r,n){r>this._bufferOffset&&(this._buffer=this._buffer.slice(r-this._bufferOffset),this._bufferOffset=r);var o=dt(this._buffer)===0;return this._done&&o?null:this._buffer.slice(0,n-r)}},{key:"close",value:function(){this._reader.cancel&&this._reader.cancel()}}]),e}();function G(e){"@babel/helpers - typeof";return G=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},G(e)}function Le(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Le=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(s,i,u){s[i]=u.value},a=typeof Symbol=="function"?Symbol:{},l=a.iterator||"@@iterator",f=a.asyncIterator||"@@asyncIterator",h=a.toStringTag||"@@toStringTag";function m(s,i,u){return Object.defineProperty(s,i,{value:u,enumerable:!0,configurable:!0,writable:!0}),s[i]}try{m({},"")}catch{m=function(u,c,d){return u[c]=d}}function w(s,i,u,c){var d=i&&i.prototype instanceof v?i:v,p=Object.create(d.prototype),g=new I(c||[]);return o(p,"_invoke",{value:be(s,u,g)}),p}function y(s,i,u){try{return{type:"normal",arg:s.call(i,u)}}catch(c){return{type:"throw",arg:c}}}t.wrap=w;var E="suspendedStart",B="suspendedYield",F="executing",C="completed",U={};function v(){}function b(){}function _(){}var x={};m(x,l,function(){return this});var L=Object.getPrototypeOf,T=L&&L(L(z([])));T&&T!==r&&n.call(T,l)&&(x=T);var A=_.prototype=v.prototype=Object.create(x);function Q(s){["next","throw","return"].forEach(function(i){m(s,i,function(u){return this._invoke(i,u)})})}function D(s,i){function u(d,p,g,S){var P=y(s[d],s,p);if(P.type!=="throw"){var k=P.arg,O=k.value;return O&&G(O)=="object"&&n.call(O,"__await")?i.resolve(O.__await).then(function(j){u("next",j,g,S)},function(j){u("throw",j,g,S)}):i.resolve(O).then(function(j){k.value=j,g(k)},function(j){return u("throw",j,g,S)})}S(P.arg)}var c;o(this,"_invoke",{value:function(p,g){function S(){return new i(function(P,k){u(p,g,P,k)})}return c=c?c.then(S,S):S()}})}function be(s,i,u){var c=E;return function(d,p){if(c===F)throw new Error("Generator is already running");if(c===C){if(d==="throw")throw p;return{value:e,done:!0}}for(u.method=d,u.arg=p;;){var g=u.delegate;if(g){var S=ee(g,u);if(S){if(S===U)continue;return S}}if(u.method==="next")u.sent=u._sent=u.arg;else if(u.method==="throw"){if(c===E)throw c=C,u.arg;u.dispatchException(u.arg)}else u.method==="return"&&u.abrupt("return",u.arg);c=F;var P=y(s,i,u);if(P.type==="normal"){if(c=u.done?C:B,P.arg===U)continue;return{value:P.arg,done:u.done}}P.type==="throw"&&(c=C,u.method="throw",u.arg=P.arg)}}}function ee(s,i){var u=i.method,c=s.iterator[u];if(c===e)return i.delegate=null,u==="throw"&&s.iterator.return&&(i.method="return",i.arg=e,ee(s,i),i.method==="throw")||u!=="return"&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+u+"' method")),U;var d=y(c,s.iterator,i.arg);if(d.type==="throw")return i.method="throw",i.arg=d.arg,i.delegate=null,U;var p=d.arg;return p?p.done?(i[s.resultName]=p.value,i.next=s.nextLoc,i.method!=="return"&&(i.method="next",i.arg=e),i.delegate=null,U):p:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,U)}function _e(s){var i={tryLoc:s[0]};1 in s&&(i.catchLoc=s[1]),2 in s&&(i.finallyLoc=s[2],i.afterLoc=s[3]),this.tryEntries.push(i)}function N(s){var i=s.completion||{};i.type="normal",delete i.arg,s.completion=i}function I(s){this.tryEntries=[{tryLoc:"root"}],s.forEach(_e,this),this.reset(!0)}function z(s){if(s||s===""){var i=s[l];if(i)return i.call(s);if(typeof s.next=="function")return s;if(!isNaN(s.length)){var u=-1,c=function d(){for(;++u<s.length;)if(n.call(s,u))return d.value=s[u],d.done=!1,d;return d.value=e,d.done=!0,d};return c.next=c}}throw new TypeError(G(s)+" is not iterable")}return b.prototype=_,o(A,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:b,configurable:!0}),b.displayName=m(_,h,"GeneratorFunction"),t.isGeneratorFunction=function(s){var i=typeof s=="function"&&s.constructor;return!!i&&(i===b||(i.displayName||i.name)==="GeneratorFunction")},t.mark=function(s){return Object.setPrototypeOf?Object.setPrototypeOf(s,_):(s.__proto__=_,m(s,h,"GeneratorFunction")),s.prototype=Object.create(A),s},t.awrap=function(s){return{__await:s}},Q(D.prototype),m(D.prototype,f,function(){return this}),t.AsyncIterator=D,t.async=function(s,i,u,c,d){d===void 0&&(d=Promise);var p=new D(w(s,i,u,c),d);return t.isGeneratorFunction(i)?p:p.next().then(function(g){return g.done?g.value:p.next()})},Q(A),m(A,h,"Generator"),m(A,l,function(){return this}),m(A,"toString",function(){return"[object Generator]"}),t.keys=function(s){var i=Object(s),u=[];for(var c in i)u.push(c);return u.reverse(),function d(){for(;u.length;){var p=u.pop();if(p in i)return d.value=p,d.done=!1,d}return d.done=!0,d}},t.values=z,I.prototype={constructor:I,reset:function(i){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!i)for(var u in this)u.charAt(0)==="t"&&n.call(this,u)&&!isNaN(+u.slice(1))&&(this[u]=e)},stop:function(){this.done=!0;var i=this.tryEntries[0].completion;if(i.type==="throw")throw i.arg;return this.rval},dispatchException:function(i){if(this.done)throw i;var u=this;function c(k,O){return g.type="throw",g.arg=i,u.next=k,O&&(u.method="next",u.arg=e),!!O}for(var d=this.tryEntries.length-1;d>=0;--d){var p=this.tryEntries[d],g=p.completion;if(p.tryLoc==="root")return c("end");if(p.tryLoc<=this.prev){var S=n.call(p,"catchLoc"),P=n.call(p,"finallyLoc");if(S&&P){if(this.prev<p.catchLoc)return c(p.catchLoc,!0);if(this.prev<p.finallyLoc)return c(p.finallyLoc)}else if(S){if(this.prev<p.catchLoc)return c(p.catchLoc,!0)}else{if(!P)throw new Error("try statement without catch or finally");if(this.prev<p.finallyLoc)return c(p.finallyLoc)}}}},abrupt:function(i,u){for(var c=this.tryEntries.length-1;c>=0;--c){var d=this.tryEntries[c];if(d.tryLoc<=this.prev&&n.call(d,"finallyLoc")&&this.prev<d.finallyLoc){var p=d;break}}p&&(i==="break"||i==="continue")&&p.tryLoc<=u&&u<=p.finallyLoc&&(p=null);var g=p?p.completion:{};return g.type=i,g.arg=u,p?(this.method="next",this.next=p.finallyLoc,U):this.complete(g)},complete:function(i,u){if(i.type==="throw")throw i.arg;return i.type==="break"||i.type==="continue"?this.next=i.arg:i.type==="return"?(this.rval=this.arg=i.arg,this.method="return",this.next="end"):i.type==="normal"&&u&&(this.next=u),U},finish:function(i){for(var u=this.tryEntries.length-1;u>=0;--u){var c=this.tryEntries[u];if(c.finallyLoc===i)return this.complete(c.completion,c.afterLoc),N(c),U}},catch:function(i){for(var u=this.tryEntries.length-1;u>=0;--u){var c=this.tryEntries[u];if(c.tryLoc===i){var d=c.completion;if(d.type==="throw"){var p=d.arg;N(c)}return p}}throw new Error("illegal catch attempt")},delegateYield:function(i,u,c){return this.delegate={iterator:z(i),resultName:u,nextLoc:c},this.method==="next"&&(this.arg=e),U}},t}function yt(e,t,r,n,o,a,l){try{var f=e[a](l),h=f.value}catch(m){r(m);return}f.done?t(h):Promise.resolve(h).then(n,o)}function wn(e){return function(){var t=this,r=arguments;return new Promise(function(n,o){var a=e.apply(t,r);function l(h){yt(a,n,o,l,f,"next",h)}function f(h){yt(a,n,o,l,f,"throw",h)}l(void 0)})}}function Sn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function mt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Pn(n.key),n)}}function Un(e,t,r){return t&&mt(e.prototype,t),r&&mt(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Pn(e){var t=xn(e,"string");return G(t)==="symbol"?t:String(t)}function xn(e,t){if(G(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(G(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var En=function(){function e(){Sn(this,e)}return Un(e,[{key:"openFile",value:function(){var t=wn(Le().mark(function n(o,a){var l;return Le().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:if(!(It()&&o&&typeof o.uri<"u")){h.next=11;break}return h.prev=1,h.next=4,sn(o.uri);case 4:return l=h.sent,h.abrupt("return",new pt(l));case 8:throw h.prev=8,h.t0=h.catch(1),new Error("tus: cannot fetch `file.uri` as Blob, make sure the uri is correct and accessible. ".concat(h.t0));case 11:if(!(typeof o.slice=="function"&&typeof o.size<"u")){h.next=13;break}return h.abrupt("return",Promise.resolve(new pt(o)));case 13:if(typeof o.read!="function"){h.next=18;break}if(a=Number(a),Number.isFinite(a)){h.next=17;break}return h.abrupt("return",Promise.reject(new Error("cannot create source for stream without a finite value for the `chunkSize` option")));case 17:return h.abrupt("return",Promise.resolve(new _n(o,a)));case 18:return h.abrupt("return",Promise.reject(new Error("source object may only be an instance of File, Blob, or Reader in this environment")));case 19:case"end":return h.stop()}},n,null,[[1,8]])}));function r(n,o){return t.apply(this,arguments)}return r}()}]),e}();function On(e,t){return It()?Promise.resolve(Rn(e,t)):Promise.resolve(["tus-br",e.name,e.type,e.size,e.lastModified,t.endpoint].join("-"))}function Rn(e,t){var r=e.exif?kn(JSON.stringify(e.exif)):"noexif";return["tus-rn",e.name||"noname",e.size||"nosize",r,t.endpoint].join("/")}function kn(e){var t=0;if(e.length===0)return t;for(var r=0;r<e.length;r++){var n=e.charCodeAt(r);t=(t<<5)-t+n,t&=t}return t}function V(e){"@babel/helpers - typeof";return V=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},V(e)}function jn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zt(n.key),n)}}function Ln(e,t,r){return t&&vt(e.prototype,t),r&&vt(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function An(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ae(e,t)}function Ae(e,t){return Ae=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},Ae(e,t)}function Fn(e){var t=qn();return function(){var n=ve(e),o;if(t){var a=ve(this).constructor;o=Reflect.construct(n,arguments,a)}else o=n.apply(this,arguments);return Cn(this,o)}}function Cn(e,t){if(t&&(V(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Tn(e)}function Tn(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qn(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ve(e){return ve=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ve(e)}function gt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Z(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gt(Object(r),!0).forEach(function(n){$n(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gt(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $n(e,t,r){return t=zt(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zt(e){var t=Dn(e,"string");return V(t)==="symbol"?t:String(t)}function Dn(e,t){if(V(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(V(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Fe=Z(Z({},me.defaultOptions),{},{httpStack:new on,fileReader:new En,urlStorage:en?new tn:new Wr,fingerprint:On}),Bn=function(e){An(r,e);var t=Fn(r);function r(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return jn(this,r),o=Z(Z({},Fe),o),t.call(this,n,o)}return Ln(r,null,[{key:"terminate",value:function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return a=Z(Z({},Fe),a),me.terminate(o,a)}}]),r}(me);function Nn(){return typeof window<"u"&&(typeof window.PhoneGap<"u"||typeof window.Cordova<"u"||typeof window.cordova<"u")}function In(){return typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative"}function zn(e){return(t,r)=>{if(Nn()||In())return Fe.fingerprint(t,r);const n=["tus",e.id,r.endpoint].join("-");return Promise.resolve(n)}}function H(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var Hn=0;function ce(e){return"__private_"+Hn+++"_"+e}const Mn={version:"3.5.5"},Ht={endpoint:"",uploadUrl:null,metadata:{},uploadSize:null,onProgress:null,onChunkComplete:null,onSuccess:null,onError:null,overridePatchMethod:!1,headers:{},addRequestId:!1,chunkSize:1/0,retryDelays:[100,1e3,3e3,5e3],parallelUploads:1,removeFingerprintOnSuccess:!1,uploadLengthDeferred:!1,uploadDataDuringCreation:!1},Gn={limit:20,retryDelays:Ht.retryDelays,withCredentials:!1};var ne=ce("retryDelayIterator"),Ce=ce("uploadLocalFile"),Te=ce("getCompanionClientArgs"),Ue=ce("uploadFiles"),te=ce("handleUpload");class Kn extends Mt{constructor(t,r){var n,o;if(super(t,{...Gn,...r}),Object.defineProperty(this,Ue,{value:Wn}),Object.defineProperty(this,Te,{value:Zn}),Object.defineProperty(this,Ce,{value:Xn}),Object.defineProperty(this,ne,{writable:!0,value:void 0}),Object.defineProperty(this,te,{writable:!0,value:async a=>{if(a.length===0){this.uppy.log("[Tus] No files to upload");return}this.opts.limit===0&&this.uppy.log("[Tus] When uploading multiple files at once, consider setting the `limit` option (to `10` for example), to limit the number of concurrent uploads, which helps prevent memory and network issues: https://uppy.io/docs/tus/#limit-0","warning"),this.uppy.log("[Tus] Uploading...");const l=this.uppy.getFilesByIds(a);await H(this,Ue)[Ue](l)}}),this.type="uploader",this.id=this.opts.id||"Tus",(r==null?void 0:r.allowedMetaFields)===void 0&&"metaFields"in this.opts)throw new Error("The `metaFields` option has been renamed to `allowedMetaFields`.");if("autoRetry"in r)throw new Error("The `autoRetry` option was deprecated and has been removed.");this.requests=(n=this.opts.rateLimitedQueue)!=null?n:new Gt(this.opts.limit),H(this,ne)[ne]=(o=this.opts.retryDelays)==null?void 0:o.values(),this.uploaders=Object.create(null),this.uploaderEvents=Object.create(null),this.handleResetProgress=this.handleResetProgress.bind(this)}handleResetProgress(){const t={...this.uppy.getState().files};Object.keys(t).forEach(r=>{var n;if((n=t[r])!=null&&(n=n.tus)!=null&&n.uploadUrl){const o={...t[r].tus};delete o.uploadUrl,t[r]={...t[r],tus:o}}}),this.uppy.setState({files:t})}resetUploaderReferences(t,r){const n=this.uploaders[t];n&&(n.abort(),r!=null&&r.abort&&n.abort(!0),this.uploaders[t]=null),this.uploaderEvents[t]&&(this.uploaderEvents[t].remove(),this.uploaderEvents[t]=null)}onReceiveUploadUrl(t,r){const n=this.uppy.getFile(t.id);n&&(!n.tus||n.tus.uploadUrl!==r)&&(this.uppy.log("[Tus] Storing upload url"),this.uppy.setFileState(n.id,{tus:{...n.tus,uploadUrl:r}}))}install(){this.uppy.setState({capabilities:{...this.uppy.getState().capabilities,resumableUploads:!0}}),this.uppy.addUploader(H(this,te)[te]),this.uppy.on("reset-progress",this.handleResetProgress)}uninstall(){this.uppy.setState({capabilities:{...this.uppy.getState().capabilities,resumableUploads:!1}}),this.uppy.removeUploader(H(this,te)[te])}}function Xn(e){var t=this;return this.resetUploaderReferences(e.id),new Promise((r,n)=>{let o,a,l;const f={...this.opts,...e.tus||{}};typeof f.headers=="function"&&(f.headers=f.headers(e));const{onShouldRetry:h,onBeforeRequest:m,...w}=f,y={...Ht,...w};y.fingerprint=zn(e),y.onBeforeRequest=async v=>{const b=v.getUnderlyingObject();b.withCredentials=!!f.withCredentials;let _;if(typeof m=="function"&&(_=m(v,e)),we(o,"shouldBeRequeued")){if(!o.shouldBeRequeued)return Promise.reject();let x;const L=new Promise(T=>{x=T});o=this.requests.run(()=>(e.isPaused&&o.abort(),x(),()=>{})),await Promise.all([L,_]);return}return _},y.onError=v=>{var b;this.uppy.log(v);const _=v.originalRequest!=null?v.originalRequest.getUnderlyingObject():null;Wt(_)&&(v=new Yt(v,_)),this.resetUploaderReferences(e.id),(b=o)==null||b.abort(),this.uppy.emit("upload-error",e,v),typeof f.onError=="function"&&f.onError(v),n(v)},y.onProgress=(v,b)=>{this.onReceiveUploadUrl(e,l.url),typeof f.onProgress=="function"&&f.onProgress(v,b),this.uppy.emit("upload-progress",this.uppy.getFile(e.id),{uploader:this,bytesUploaded:v,bytesTotal:b})},y.onSuccess=()=>{var v;const b={uploadURL:(v=l.url)!=null?v:void 0,status:200,body:{}};if(this.resetUploaderReferences(e.id),o.done(),this.uppy.emit("upload-success",this.uppy.getFile(e.id),b),l.url){const{name:_}=l.file;this.uppy.log(`Download ${_} from ${l.url}`)}typeof f.onSuccess=="function"&&f.onSuccess(),r(l)};const E=v=>{var b;const _=v==null||(b=v.originalResponse)==null?void 0:b.getStatus();if(_===429){if(!this.requests.isPaused){var x;const L=(x=H(this,ne)[ne])==null?void 0:x.next();if(L==null||L.done)return!1;this.requests.rateLimit(L.value)}}else{if(_!=null&&_>400&&_<500&&_!==409&&_!==423)return!1;typeof navigator<"u"&&navigator.onLine===!1&&(this.requests.isPaused||(this.requests.pause(),window.addEventListener("online",()=>{this.requests.resume()},{once:!0})))}return o.abort(),o={shouldBeRequeued:!0,abort(){this.shouldBeRequeued=!1},done(){throw new Error("Cannot mark a queued request as done: this indicates a bug")},fn(){throw new Error("Cannot run a queued request: this indicates a bug")}},!0};h!=null?y.onShouldRetry=(v,b)=>h(v,b,f,E):y.onShouldRetry=E;const B=(v,b,_)=>{we(v,b)&&!we(v,_)&&(v[_]=v[b])},F={};(Array.isArray(f.allowedMetaFields)?f.allowedMetaFields:Object.keys(e.meta)).forEach(v=>{F[v]=String(e.meta[v])}),B(F,"type","filetype"),B(F,"name","filename"),y.metadata=F,l=new Bn(e.data,y),this.uploaders[e.id]=l;const U=new Kt(this.uppy);this.uploaderEvents[e.id]=U,a=()=>(e.isPaused||l.start(),()=>{}),l.findPreviousUploads().then(v=>{const b=v[0];b&&(this.uppy.log(`[Tus] Resuming upload of ${e.id} started at ${b.creationTime}`),l.resumeFromPreviousUpload(b))}),o=this.requests.run(a),U.onFileRemove(e.id,v=>{o.abort(),this.resetUploaderReferences(e.id,{abort:!!l.url}),r(`upload ${v} was removed`)}),U.onPause(e.id,v=>{o.abort(),v?l.abort():o=this.requests.run(a)}),U.onPauseAll(e.id,()=>{o.abort(),l.abort()}),U.onCancelAll(e.id,function(v){let{reason:b}=v===void 0?{}:v;b==="user"&&(o.abort(),t.resetUploaderReferences(e.id,{abort:!!l.url})),r(`upload ${e.id} was canceled`)}),U.onResumeAll(e.id,()=>{o.abort(),e.error&&l.abort(),o=this.requests.run(a)})}).catch(r=>{throw this.uppy.emit("upload-error",e,r),r})}function Zn(e){var t;const r={...this.opts};return e.tus&&Object.assign(r,e.tus),typeof r.headers=="function"&&(r.headers=r.headers(e)),{...(t=e.remote)==null?void 0:t.body,endpoint:r.endpoint,uploadUrl:r.uploadUrl,protocol:"tus",size:e.data.size,headers:r.headers,metadata:e.meta}}async function Wn(e){const t=Xt(e),r=Zt(t);this.uppy.emit("upload-start",r),await Promise.allSettled(t.map(n=>{if(n.isRemote){const o=()=>this.requests,a=new AbortController,l=h=>{h.id===n.id&&a.abort()};this.uppy.on("file-removed",l);const f=this.uppy.getRequestClientForFile(n).uploadRemoteFile(n,H(this,Te)[Te](n),{signal:a.signal,getQueue:o});return this.requests.wrapSyncFunction(()=>{this.uppy.off("file-removed",l)},{priority:-1})(),f}return H(this,Ce)[Ce](n)}))}Kn.VERSION=Mn.version;export{Kn as T};
