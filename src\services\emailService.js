import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const sendEmailAPI = async (payload) => {
  try {
    const uri = `/v2/api/lambda/mail/send`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const sendEmailAPIV3 = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/email/send`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllEmailAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/email/get_all`;
    const res = await sdk.callRawAPI(uri, {}, 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const getOneEmailBySlugAPI = async (slug) => {
  try {
    const uri = `/v3/api/custom/equality_record/email/get_one?slug=${slug}`;
    const res = await sdk.callRawAPI(uri, {}, 'GET');
    return res;
  } catch (error) {
    return error;
  }
};
