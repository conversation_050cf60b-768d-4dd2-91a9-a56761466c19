import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import AddIdeaModal from "Components/ViewProject/Idea/AddIdeaModal";
import ConfirmModal from "Components/Modal/ConfirmModal";
import { uploadFilesDataAPI } from "Src/services/workOrderService";
import {
  addAndAssignIdeaAPI,
  getAllSubProjectIdeaAPI,
} from "Src/services/projectService";
import { replaceNextLineToBrTag, replaceBrTagToNextLine } from "Utils/utils";
import SubProjectShowWriter from "./SubProjectShowWriter";
import SubProjectShowEngineer from "./SubProjectShowEngineer";
import SubProjectCollapseWriterAndArtist from "./SubProjectCollapseWriterAndArtist";
import SubProjectCollapseEngineer from "./SubProjectCollapseEngineer";
import NoWorkOrder from "./NoWorkOrder";
import AddFileSubProject from "../AddFile/FileSubProject";
import { useS3Upload } from "Src/libs/uploads3Hook";
import CustomSelect2 from "Components/CustomSelect2";

const SubProject = ({
  expandAll,
  mixSeasons,
  setTempUploadCount,
  tempUploadCount,
  UploadCount,
  isSong = false,
  isEdit,
  ideas,
  theme,
  projectId,
  setUpdateSubprojectPayload,
  writers,
  artists,
  engineers,
  writer,
  artist,
  engineer,
  surveySubmitStatus,
  setLyrics,
  setEightCountPayload,
  setWriterPayload,
  setWriterCostPayload,
  setArtistPayload,
  setArtistCostPayload,
  setDeleteIdeaPayload,
  subProject,
  setShowAssignIdeaModal,
  setSelectedSubProjectId,
  setSelectedSubProjectIdForDelete,
  setUnSelectedSubProjectIdForDelete,
  setDeleteFileId,
  setLoadIdeaFromViewProject,
  setResetWriterPayload,
  setResetArtistPayload,
}) => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const {
    dispatch: globalDispatch,
    state: { subproject_update },
  } = React.useContext(GlobalContext);

  const navigate = useNavigate();

  const [selectedWriterId, setSelectedWriterId] = React.useState("");
  const [selectedArtistId, setSelectedArtistId] = React.useState("");
  const [selectedEngineerId, setSelectedEngineerId] = React.useState("");
  const [tempWriterId, setTempWriterId] = React.useState(null);
  const [tempArtistId, setTempArtistId] = React.useState(null);
  const [tempEngineerId, setTempEngineerId] = React.useState(null);
  const [writerCost, setWriterCost] = React.useState(0);
  const writerCostRef = useRef(null);
  const artistCostRef = useRef(null);
  const [artistCost, setArtistCost] = React.useState(0);
  const [engineerCost, setEngineerCost] = React.useState(0);
  const [totalCost, setTotalCost] = React.useState(0);
  const [showDeleteIdeaModal, setShowDeleteIdeaModal] = React.useState(false);
  const [deleteIdeaId, setDeleteIdeaId] = React.useState(null);
  const [localEightCount, setLocalEightCount] = React.useState(0);
  const [lyricsVal, setLyricsVal] = React.useState(
    subProject.lyrics ? replaceBrTagToNextLine(subProject.lyrics) : ""
  );
  const { uploadS3FilesAPI, progress, error, isUploading } = useS3Upload();
  const [localSelectedSubProjectId, setLocalSelectedSubProjectId] =
    React.useState(null);

  const [workOrderFound, setWorkOrderFound] = React.useState(false);

  const [showAddIdeaModal, setShowAddIdeaModal] = React.useState(false);

  const [showCheckboxToDelete, setShowCheckboxToDelete] = React.useState(false);

  const [assignedIdeas, setAssignedIdeas] = React.useState([]);

  const [showWriterDetails, setShowWriterDetails] = React.useState(false);
  const [showEngineerDetails, setShowEngineerDetails] = React.useState(false);
  const [FileUploadOpen, setFileUploadOpen] = useState(false);

  console.log(writerCostRef.current?.value);
  console.log(writerCost);

  useEffect(() => {
    if (writerCostRef.current) writerCostRef.current.value = writerCost;
  }, [writerCost]);

  useEffect(() => {
    if (artistCostRef.current) artistCostRef.current.value = artistCost;
  }, [artistCost]);

  const getAllSubProjectIdea = async () => {
    if (subProject.id) {
      const result = await getAllSubProjectIdeaAPI(Number(subProject.id));
      if (!result.error) {
        setAssignedIdeas(result.list);
      }
    }
  };

  const handleSelectedSubProjectId = (id) => {
    setLocalSelectedSubProjectId(null);
    if (localSelectedSubProjectId === id) {
      setLocalSelectedSubProjectId(null);
      setSelectedSubProjectId(null);
    } else {
      setLocalSelectedSubProjectId(id);
      setSelectedSubProjectId(id);
    }
  };

  const handleEightCountChange = (e) => {
    e.preventDefault();
    setLocalEightCount(e.target.value);
    setEightCountPayload({
      eight_count: Number(e.target.value),
      subproject_id: Number(subProject.id),
    });
  };

  const handleWriterChange = (value) => {
    if (value === "") {
      setSelectedWriterId("");
      setWriterCost(0);
      setTotalCost(0 + Number(artistCost) + Number(engineerCost));
      setResetWriterPayload({
        subproject_id: Number(subProject.id),
        employee_type: "writer",
      });
      return;
    } else {
      const writer = writers.find((x) => x.id === Number(value));
      if (writer && writer.is_writer) {
        setSelectedWriterId(writer.id);
        setWriterCost(Number(writer?.writer_cost));
        setTotalCost(
          Number(writer?.writer_cost) +
            Number(artistCost) +
            Number(engineerCost)
        );
        setWriterPayload({
          subproject_id: Number(subProject.id),
          employee_type: "writer",
          old_employee_id: tempWriterId ?? null,
          new_employee_id: Number(writer.id),
          employee_cost: Number(writer.writer_cost),
        });
      } else {
        setSelectedWriterId("");
        setWriterCost(0);
        setTotalCost(
          Number(writer?.writer_cost) +
            Number(artistCost) +
            Number(engineerCost)
        );
      }
    }
  };

  const handleArtistChange = (value) => {
    if (value === "") {
      setSelectedArtistId("");
      setArtistCost(0);
      setTotalCost(Number(writerCost) + 0 + Number(engineerCost));
      setResetArtistPayload({
        subproject_id: Number(subProject.id),
        employee_type: "artist",
      });
      return;
    } else {
      const artist = artists.find((x) => x.id === Number(value));
      if (artist && artist.is_artist) {
        setSelectedArtistId(artist.id);
        setArtistCost(Number(artist?.artist_cost));
        setTotalCost(
          Number(writerCost) +
            Number(artist?.artist_cost) +
            Number(engineerCost)
        );
        setArtistPayload({
          subproject_id: Number(subProject.id),
          employee_type: "artist",
          old_employee_id: tempArtistId ?? null,
          new_employee_id: Number(artist.id),
          employee_cost: Number(artist.artist_cost),
        });
      } else {
        setSelectedArtistId("");
        setArtistCost(0);
        setTotalCost(
          Number(writerCost) +
            Number(artist?.artist_cost) +
            Number(engineerCost)
        );
      }
    }
  };

  const submitLyrics = (e) => {
    e.preventDefault();
    if (lyricsVal === "" || !lyricsVal) {
      showToast(globalDispatch, "Lyrics cannot be empty.", 5000, "error");
      return;
    }
    setLyrics({
      subproject_id: subProject.id,
      lyrics: replaceNextLineToBrTag(lyricsVal),
    });
  };

  const handleDeleteAssignedIdea = async () => {
    setShowDeleteIdeaModal(false);
    await setDeleteIdeaPayload({
      idea_id: deleteIdeaId,
      subproject_id: subProject.id,
    });
    await getAllSubProjectIdea();
  };

  const handleDeleteIdeaModalClose = () => {
    setShowDeleteIdeaModal(false);
  };

  const handleShowAddIdeaModalClose = async () => {
    setShowAddIdeaModal(false);
    await getAllSubProjectIdea();
  };

  const handleShowAddIdeaModalOpen = async () => {
    setShowAddIdeaModal(true);
    await getAllSubProjectIdea();
  };

  const handleAddIdea = async (data) => {
    try {
      const payload = {
        subproject_id: Number(subProject.id),
        project_id: Number(projectId),
        idea_key: data.idea_key,
        idea_value: data.idea_value,
      };
      const result = await addAndAssignIdeaAPI(payload);
      if (!result.error) {
        showToast(globalDispatch, result.message, 5000);
        handleShowAddIdeaModalClose();
        setLoadIdeaFromViewProject(true);
      }
    } catch (error) {
      tokenExpireError(dispatch, error.message);
    }
  };

  const handleInstrumentalUploads = async (formData) => {
    try {
      // setIsLoading(true);
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        const payload = {
          project_id: Number(subProject.project_id),
          subproject_id: Number(subProject.id),
          workorder_id: Number(subProject.workorder_id),
          employee_id: null,
          employee_type: "writer",
          type: "instrumental",
          attachments: result.attachments,
          is_from_admin: 1,
        };

        const fileUploadDataResult = await uploadFilesDataAPI(payload);
        if (!fileUploadDataResult.error) {
          showToast(globalDispatch, "Files updated successfully", 5000);
          globalDispatch({
            type: "SET_SUBPROJECT_UPDATE",
            payload: !subproject_update,
          });
        } else {
          showToast(
            globalDispatch,
            fileUploadDataResult.message,
            5000,
            "error"
          );
        }
        // setIsLoading(false);
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
        // setIsLoading(false);
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };

  const handleOnclickShowWriterDetails = (e) => {
    e.preventDefault();
    setShowWriterDetails(!showWriterDetails);
  };

  const handleOnclickShowEngineerDetails = (e) => {
    e.preventDefault();
    setShowEngineerDetails(!showEngineerDetails);
  };

  React.useEffect(() => {
    if (isEdit) {
      setShowCheckboxToDelete(true);
    } else {
      setShowCheckboxToDelete(false);
    }
  }, [isEdit]);

  React.useEffect(() => {
    if (writer) {
      let localWriterCost =
        writer && writer.length > 0 ? Number(writer[0]?.emp_cost) : 0;
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      //
      setWriterCost(localWriterCost);
      setTotalCost(localWriterCost + Number(artistCost) + Number(engineerCost));
    }

    if (artist) {
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
      setArtistCost(localArtistCost);
      setTotalCost(Number(writerCost) + localArtistCost + Number(engineerCost));
    }

    if (engineer) {
      let localEngineerCost =
        engineer && engineer.length > 0 ? Number(engineer[0]?.emp_cost) : 0;
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");

      setEngineerCost(localEngineerCost);
      setTotalCost(Number(writerCost) + Number(artistCost) + localEngineerCost);
    }

    if (writer && artist) {
      let localWriterCost =
        writer && writer.length > 0 ? Number(writer[0]?.emp_cost) : 0;
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      setTotalCost(localWriterCost + localArtistCost + Number(engineerCost));
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
    }

    if (writer && engineer) {
      let localWriterCost =
        writer && writer.length > 0 ? writer[0]?.emp_cost : 0;
      let localEngineerCost =
        engineer && engineer.length > 0 ? engineer[0]?.emp_cost : 0;
      setTotalCost(localWriterCost + Number(artistCost) + localEngineerCost);
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");
    }

    if (artist && engineer) {
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      let localEngineerCost =
        engineer && engineer.length > 0 ? Number(engineer[0]?.emp_cost) : 0;
      setTotalCost(Number(writerCost) + localArtistCost + localEngineerCost);
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");
    }

    if (writer && artist && engineer) {
      let localWriterCost =
        writer && writer.length > 0 ? Number(writer[0]?.emp_cost) : 0;
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      let localEngineerCost =
        engineer && engineer.length > 0 ? Number(engineer[0]?.emp_cost) : 0;
      setTotalCost(localWriterCost + localArtistCost + localEngineerCost);
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");
    }
  }, [writer, artist, engineer]);

  React.useEffect(() => {
    if (subProject) {
      setLocalEightCount(subProject.eight_count);
    } else {
      setLocalEightCount(0);
    }
    if (subProject && subProject.workorder_id) {
      setWorkOrderFound(true);
    } else {
      setWorkOrderFound(false);
    }
    if (subProject) {
      (async function () {
        await getAllSubProjectIdea();
      })();
    }
  }, [subProject]);

  React.useEffect(() => {
    if (expandAll) {
      handleSelectedSubProjectId(subProject.id);
    } else {
      handleSelectedSubProjectId(null);
    }
  }, [expandAll]);

  const CreateWorkOrder = async () => {
    await localStorage.setItem("workorder-artist", selectedArtistId);
    await localStorage.setItem("workorder-writer", selectedWriterId);
    await localStorage.setItem("workorder-id", subProject.id);
    navigate(`/${authState.role}/add-work-order/`);
  };

  const timeoutRefWC = React.useRef(null);
  const timeoutRefAC = React.useRef(null);

  console.log(FileUploadOpen, isSong, UploadCount);

  return (
    <>
      {FileUploadOpen && (
        <AddFileSubProject
          mixSeasons={mixSeasons}
          tempUploadCount={tempUploadCount}
          setUpdateSubprojectPayload={setUpdateSubprojectPayload}
          UploadCount={UploadCount}
          setTempUploadCount={setTempUploadCount}
          id={Number(subProject.id)}
          setIsOpen={setFileUploadOpen}
          isOpen={FileUploadOpen}
          writers={writers}
          artists={artists}
          engineers={engineers}
          subprojectType={subProject.type}
        />
      )}

      <div className="p-4 my-4 w-full rounded-md border border-gray-500 shadow subproject-container bg-boxdark-2/40">
        <div className="flex flex-row justify-between">
          <div className="flex flex-row gap-4 ml-4 w-2/3">
            {isSong ? (
              <>
                <div className="">
                  {subProject.type_name
                    ? subProject.type_name.length > 15
                      ? `${subProject.type_name.substring(0, 15)}...`
                      : subProject.type_name
                    : "Song"}
                  ,
                </div>
                <div className="">{subProject.song_key ?? "Key N/A"},</div>
                <div className="">{subProject.bpm ?? "BPM N/A"}</div>
              </>
            ) : null}
          </div>
          <div className="flex flex-row gap-3 justify-end items-center w-1/3">
            {subProject.workorder_id ? (
              <span
                className={`cursor-pointer text-xs font-semibold ${
                  subProject.workorder_status == 5
                    ? "text-green-500"
                    : "text-blue-500"
                }`}
                onClick={() =>
                  navigate(
                    `/${authState.role}/edit-work-order/${subProject.workorder_id}`
                  )
                }
              >
                View Work order
              </span>
            ) : (
              <div
                className={`group group relative flex cursor-pointer flex-col items-center rounded bg-primary px-1 py-1 text-white hover:bg-primary/90 ${
                  selectedArtistId && selectedWriterId
                    ? "opacity-100"
                    : "cursor-not-allowed bg-blue-400 opacity-60"
                }`}
                onClick={() => {
                  selectedArtistId && selectedWriterId && CreateWorkOrder();
                }}
              >
                <span className="text-[12px] font-medium">WO+</span>
                {selectedArtistId && selectedWriterId ? (
                  <div className="hidden absolute top-0 flex-col items-center mt-8 group-hover:flex">
                    <span className="whitespace-no-wrap relative z-[4] rounded bg-gray-100 p-1 text-center text-xs leading-none text-gray-900 shadow-lg">
                      Create Work order
                    </span>
                  </div>
                ) : null}
              </div>
            )}
            {!subProject.workorder_id &&
              !subProject.type.includes("Upload") &&
              !(
                subProject.is_song && !/^Song\s*\d+$/.test(subProject.type)
              ) && (
                <div
                  className={`flex relative flex-col items-center px-1 py-1 text-white rounded cursor-pointer group h-[26px] w-[26px] bg-primary hover:bg-primary/90`}
                  onClick={() => {
                    setFileUploadOpen(true);
                    setTempUploadCount(tempUploadCount + 1);
                  }}
                >
                  <FontAwesomeIcon icon={`upload`} className="w-4 h-4" />
                </div>
              )}
          </div>
        </div>

        <div className="flex flex-row flex-wrap gap-2 justify-between w-full">
          <div
            className="text-white cursor-pointer"
            style={
              {
                // position: "relative",
                // top: "30px",
                // right: '10px',
              }
            }
            onClick={() => {
              handleSelectedSubProjectId(subProject.id);
            }}
          >
            {localSelectedSubProjectId === subProject.id ? (
              <FontAwesomeIcon
                icon="fa-solid fa-chevron-up"
                width={16}
                height={16}
              />
            ) : (
              <FontAwesomeIcon
                icon="fa-solid fa-chevron-down"
                width={16}
                height={16}
              />
            )}
          </div>
          {showCheckboxToDelete && (
            <div className="flex flex-col justify-center items-center">
              <input
                type="checkbox"
                className="ml-2 w-4 h-4 text-blue-600 bg-gray-100 rounded border-gray-300 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedSubProjectIdForDelete(subProject.id);
                  } else {
                    setUnSelectedSubProjectIdForDelete(subProject.id);
                  }
                }}
              />
            </div>
          )}

          {!isSong ? (
            <div>
              <div class="relative mt-6">
                <input
                  type="text"
                  id="eightCountInput"
                  class={`border-1 peer block h-[42px]  w-[104px] cursor-default appearance-none  rounded-lg border  border-gray-500  p-2.5 text-sm  font-medium text-white  placeholder-gray-300 focus:border-blue-600 focus:outline-none focus:ring-0 focus:ring-blue-500 dark:border-gray-500 dark:text-white dark:focus:border-blue-500 ${
                    subProject.type.match(/Voiceover/)
                      ? "bg-orange-500"
                      : "bg-sky-500"
                  }`}
                  placeholder="Type"
                  value={subProject.type}
                  readOnly
                />
                {/* <label
                  htmlFor='name'
                  class='absolute start-1 top-2  z-[4] disabled:opacity-30 block origin-[0] -translate-y-2.5 scale-75 transform bg-white px-2 text-sm font-normal text-gray-100  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-2.5 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-orange-500 dark:text-white peer-focus:dark:text-blue-500'
                >
                  Type
                </label> */}
              </div>
              {/* <div className='flex flex-col'>
                <label<div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='total'
            >
              Total
            </label>
            <input
              type='text'
              className='w-20 rounded-lg border border-neutral-500 bg-neutral-700 p-2.5 text-sm text-white placeholder-neutral-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Total'
              value={totalCost ?? 0}
              disabled
            />
          </div>
                  className='block mb-1 text-sm font-normal text-gray-100'
                  htmlFor='name'
                >
                  Type
                </label>
                <input
                  type='text'
                  className={`w-24 cursor-default rounded-lg border border-gray-500 p-2.5 text-sm text-white placeholder-gray-300 focus:border-blue-500 focus:ring-blue-500 ${
                    subProject.type.match(/Voiceover/)
                      ? 'bg-orange-500'
                      : 'bg-sky-500'
                  }`}
                  placeholder='Type'
                  value={subProject.type}
                  readOnly
                />
              </div> */}
            </div>
          ) : (
            <div className="relative mt-6">
              {/* <label
                className='block mb-1 text-sm font-normal text-gray-100'
                htmlFor='name'
              >
                Type
              </label> */}
              <input
                type="text"
                className={`w-[104px] cursor-default rounded-lg border border-gray-500 p-2.5  text-sm font-medium text-white placeholder-gray-300    ${
                  subProject.type.match(/song/i)
                    ? "bg-sky-500 focus:border-blue-500 focus:ring-blue-500"
                    : "bg-sky-500 focus:border-blue-500 focus:ring-blue-500"
                }`}
                placeholder="Type"
                value={subProject.type}
                readOnly
              />
            </div>
          )}
          <div class="relative mt-6">
            <input
              type="number"
              id="eightCountInput"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-stroke/50 bg-transparent  px-2.5 pb-2.5 pt-4 text-sm text-white focus:border-blue-500  focus:outline-none focus:ring-0 disabled:opacity-30 dark:text-white dark:focus:border-blue-500 "
              placeholder=" "
              min="0"
              value={localEightCount}
              onChange={handleEightCountChange}
              disabled={workOrderFound}
            />
            <label
              for="eightCountInput"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform bg-gray-800  px-2 text-sm  font-medium text-white  duration-300 disabled:text-white/40 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              # of 8cts
            </label>
          </div>
          {/* <div className='relative mt-6'>
            <input
              type='number'
              id='eightCountInput'
              className='peer w-16 border-b border-stone-500 bg-transparent p-2.5 text-sm text-white text-white placeholder-stone-300 outline-none transition-all duration-200 focus:border-blue-500 focus:ring-blue-500'
            />
            <label
              htmlFor='eightCountInput'
              className='absolute top-2 left-2 text-xs text-white transition-all duration-200 pointer-events-none peer-placeholder-shown:-top-2 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-focus:-top-2 peer-focus:text-sm peer-focus:text-gray-500'
            ></label>
          </div> */}

          <div class="relative mt-6">
            <CustomSelect2
              className="peer block h-[42px] !w-[120px] appearance-none text-ellipsis rounded-lg border border-stroke/50 bg-transparent p-2.5 pr-5 text-sm text-white focus:border-blue-500 focus:outline-none focus:ring-0 disabled:opacity-30 dark:text-white dark:focus:border-blue-500"
              name="writer"
              position="up"
              id="writer"
              value={selectedWriterId}
              onChange={(e) => {
                handleWriterChange(e);
              }}
              disabled={workOrderFound}
            >
              <option className="text-white bg-gray-800" value="">
                Select
              </option>
              {writers?.map((writer) => (
                <option
                  className="text-white bg-gray-800"
                  key={writer.id}
                  value={writer.id}
                >
                  {writer.name}
                </option>
              ))}
            </CustomSelect2>
            <label
              htmlFor="writer"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform bg-gray-800  px-2 text-sm  font-medium text-white  duration-300 disabled:text-white/40 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Writer
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='writer'
            >
              Writer
            </label>
            <select
              className='w-28 rounded-lg border border-stone-500 bg-transparent p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500'
              name='writer'
              id='writer'
              value={selectedWriterId}
              onChange={(e) => {
                handleWriterChange(e);
              }}
              disabled={workOrderFound}
            >
              <option className='text-white bg-gray-800' value=''>
                Select
              </option>
              {writers?.map((writer) => (
                <option
                  className='text-white bg-gray-800'
                  key={writer.id}
                  value={writer.id}
                >
                  {writer.name}
                </option>
              ))}
            </select>
          </div> */}
          <div class="relative mt-6">
            <input
              type="number"
              id="writer_cost"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-stroke/50 bg-transparent  px-2.5 pb-2.5 pt-4 text-sm text-white focus:border-blue-500  focus:outline-none focus:ring-0 disabled:opacity-30 dark:text-white dark:focus:border-blue-500 "
              placeholder="Cost"
              defaultValue={writerCost}
              min="0"
              ref={writerCostRef}
              onKeyDown={(e) => {
                if (!selectedWriterId) {
                  showToast(
                    globalDispatch,
                    "Please select a writer",
                    5000,
                    "warning"
                  );
                  e.preventDefault();
                  return;
                }
              }}
              onBlur={(e) => {
                if (!selectedWriterId) {
                  showToast(
                    globalDispatch,
                    "Please select a writer",
                    5000,
                    "warning"
                  );
                  return;
                } else {
                  setWriterCost(Number(e.target.value));

                  setTotalCost(
                    Number(e.target.value) +
                      Number(artistCost) +
                      Number(engineerCost)
                  );

                  clearTimeout(timeoutRefWC.current);

                  // Set a new timeout
                  timeoutRefWC.current = setTimeout(async () => {
                    setWriterCostPayload({
                      subproject_id: Number(subProject.id),
                      employee_type: "writer",
                      employee_id: selectedWriterId,
                      employee_cost: Number(e.target.value),
                    });
                  }, 500);
                }
              }}
              disabled={workOrderFound}
            />
            <label
              for="writer_cost"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform bg-gray-800  px-2 text-sm  font-medium text-white  duration-300 disabled:text-white/40 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Cost
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='writer_cost'
            >
              Cost
            </label>
            <input
              type='number'
              className='w-16 rounded-lg border border-stone-500 bg-transparent p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500'
            
            />
          </div> */}
          <div class="relative mt-6">
            <CustomSelect2
              className="peer block h-[42px] !w-[120px] appearance-none text-ellipsis rounded-lg border border-stroke/50 bg-transparent p-2.5 pr-5 text-sm text-white focus:border-blue-500 focus:outline-none focus:ring-0 disabled:opacity-30 dark:text-white dark:focus:border-blue-500"
              name="artist"
              position="up"
              id="artist"
              value={selectedArtistId}
              onChange={(e) => {
                handleArtistChange(e);
              }}
              disabled={workOrderFound}
            >
              <option className="text-white bg-gray-800" value="">
                Select
              </option>
              {artists?.map((artist) => (
                <option
                  className="text-white bg-gray-800"
                  key={artist.id}
                  value={artist.id}
                >
                  {artist.name}
                </option>
              ))}
            </CustomSelect2>
            <label
              htmlFor="artist"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform bg-gray-800  px-2 text-sm  font-medium text-white  duration-300 disabled:text-white/40 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Artist
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='artist'
            >
              Artist
            </label>
            <select
              className='w-28 rounded-lg border border-slate-500 bg-transparent p-2.5 text-sm text-white placeholder-slate-300 focus:border-blue-500 focus:ring-blue-500'
              name='artist'
              id='artist'
              value={selectedArtistId}
              onChange={(e) => {
                handleArtistChange(e);
              }}
              disabled={workOrderFound}
            >
              <option className='text-white bg-gray-800' value=''>
                Select
              </option>
              {artists?.map((artist) => (
                <option
                  className='text-white bg-gray-800'
                  key={artist.id}
                  value={artist.id}
                >
                  {artist.name}
                </option>
              ))}
            </select>
          </div> */}
          <div class="relative mt-6">
            <input
              type="number"
              id="artist_cost"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-stroke/50 bg-transparent  px-2.5 pb-2.5 pt-4 text-sm text-white focus:border-blue-500  focus:outline-none focus:ring-0 disabled:opacity-30 dark:text-white dark:focus:border-blue-500 "
              placeholder="Cost"
              defaultValue={artistCost}
              min="0"
              ref={artistCostRef}
              onBlur={(e) => {
                if (!selectedArtistId) {
                  showToast(
                    globalDispatch,
                    "Please select an artist",
                    5000,
                    "warning"
                  );
                  return;
                } else {
                  setArtistCost(Number(e.target.value));
                  setTotalCost(
                    Number(writerCost) +
                      Number(e.target.value) +
                      Number(engineerCost)
                  );

                  clearTimeout(timeoutRefAC.current);

                  timeoutRefAC.current = setTimeout(async () => {
                    setArtistCostPayload({
                      subproject_id: Number(subProject.id),
                      employee_type: "artist",
                      employee_id: selectedArtistId,
                      employee_cost: Number(e.target.value),
                    });
                  }, 500);
                }
              }}
              disabled={workOrderFound}
            />
            <label
              for="artist_cost"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform bg-gray-800  px-2 text-sm  font-medium text-white  duration-300 disabled:text-white/40 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Cost
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='artist_cost'
            >
              Cost
            </label>
            <input
              type='number'
              className='w-16 rounded-lg border border-slate-500 bg-transparent p-2.5 text-sm text-white placeholder-slate-300 focus:border-blue-500 focus:ring-blue-500'
            />
          </div> */}
          <div class="relative mt-6">
            <select
              class="border-1 peer block h-[42px] w-28 appearance-none text-ellipsis rounded-lg border-zinc-500 bg-zinc-700 p-2.5 pr-5 text-sm  font-medium text-white focus:border-blue-500 focus:outline-none focus:ring-0 disabled:text-white/40 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500"
              name="engineer"
              id="engineer"
              value={selectedEngineerId}
              disabled
            >
              <option className="text-white bg-gray-800" value="">
                Select
              </option>
              {engineers?.map((engineer) => (
                <option
                  className="text-white bg-gray-800"
                  key={engineer.id}
                  value={engineer.id}
                >
                  {engineer.name}
                </option>
              ))}
            </select>
            <label
              htmlFor="engineer"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Engineer
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='engineer'
            >
              Engineer
            </label>
            <select
              className='w-28 rounded-lg border border-zinc-500 bg-zinc-700 p-2.5 text-sm text-white placeholder-zinc-300 focus:border-blue-500 focus:ring-blue-500'
              name='engineer'
              id='engineer'
              value={selectedEngineerId}
              disabled
            >
              <option className='text-white bg-gray-800' value=''>
                Select
              </option>
              {engineers?.map((engineer) => (
                <option
                  className='text-white bg-gray-800'
                  key={engineer.id}
                  value={engineer.id}
                >
                  {engineer.name}
                </option>
              ))}
            </select>
          </div> */}
          <div class="relative mt-6">
            <input
              type="text"
              id="engineer_cost"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-zinc-500 bg-zinc-700 bg-zinc-700 px-2.5 pb-2.5 pt-4 text-sm font-medium text-white focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500   "
              placeholder="Cost"
              value={engineerCost}
              disabled
            />
            <label
              for="engineer_cost"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Cost
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 w-16 text-sm font-normal text-gray-100'
              htmlFor='engineer_cost'
            >
              Cost
            </label>
            <input
              type='text'
              className='w-16 rounded-lg border border-zinc-500 bg-zinc-700 p-2.5 text-sm text-white placeholder-zinc-300 focus:border-blue-500 focus:ring-blue-500'
            />
          </div> */}

          <div class="relative mt-6">
            <input
              type="text"
              id="total"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-zinc-500 bg-zinc-700 bg-zinc-700 px-2.5 pb-2.5 pt-4 text-sm font-medium text-white focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500   "
              value={totalCost}
              disabled
            />
            <label
              for="total"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Total
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='total'
            >
              Total
            </label>
            <input
              type='text'
              className='w-20 rounded-lg border border-neutral-500 bg-neutral-700 p-2.5 text-sm text-white placeholder-neutral-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Total'
              value={totalCost}
              disabled
            />
          </div> */}
        </div>
      </div>
      {localSelectedSubProjectId === subProject.id && (
        <div className="">
          <div
            className={`flex w-full flex-col items-start justify-start  rounded-md border border-form-strokedark shadow ${
              subProject.admin_writer_instrumentals.length > 0 ? "gap-4" : ""
            }`}
          >
            {/* show contents for sub-project which has no workorder */}
            {!subProject.workorder_id ? (
              <NoWorkOrder
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                surveySubmitStatus={surveySubmitStatus}
                assignedIdeas={assignedIdeas}
                subProjectId={subProject.id}
                setDeleteFileId={setDeleteFileId}
                uploadedFilesProgressData={{
                  progress,
                  error,
                  isUploading,
                }}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleInstrumentalUploads={handleInstrumentalUploads}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
              />
            ) : null}

            {/* expand icon for sub-project workorder stats 5: completed */}
            {/* {subProject.workorder_id && subProject.workorder_status === 5 ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showEngineerDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowEngineerDetails(e)}
                >
                  {!showEngineerDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for writer -> when writer and artist are same and no auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 1 &&
            subProject.workorder_auto_approve !== 1 &&
            selectedWriterId === selectedArtistId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      {" "}
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for writer -> when writer and artist are different and no auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 1 &&
            !subProject.writer_submit_status &&
            subProject.workorder_auto_approve !== 1 &&
            selectedWriterId !== selectedArtistId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      {" "}
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for writer -> when writer and artist are different and  auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 1 &&
            subProject.workorder_auto_approve === 1 &&
            selectedWriterId !== selectedArtistId &&
            selectedEngineerId === selectedWriterId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      {" "}
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for writer -> when writer, artist and engineer are different and no auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 1 &&
            subProject.workorder_auto_approve === 1 &&
            selectedWriterId !== selectedArtistId &&
            selectedWriterId !== selectedEngineerId &&
            selectedArtistId !== selectedEngineerId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      {" "}
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for artist -> when writer and artist are same and no auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 2 &&
            subProject.workorder_auto_approve !== 1 &&
            selectedWriterId === selectedArtistId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for artist -> when writer and artist are different and no auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 2 &&
            subProject.workorder_auto_approve !== 1 &&
            selectedWriterId !== selectedArtistId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      {" "}
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null}  */}

            {/* expand icon for artist -> when writer and artist are same and auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 2 &&
            subProject.workorder_auto_approve === 1 &&
            selectedWriterId === selectedArtistId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for artist -> when writer and artist are different and auto-approve */}
            {/* {subProject.workorder_id &&
            subProject.workorder_status === 2 &&
            subProject.workorder_auto_approve === 1 &&
            selectedWriterId !== selectedArtistId &&
            selectedArtistId !== selectedEngineerId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      {" "}
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for writer/artist */}
            {/* {subProject.workorder_id &&
            subProject.workorder_auto_approve === 1 &&
            subProject.workorder_status === 1 &&
            selectedWriterId === selectedArtistId &&
            selectedWriterId !== selectedEngineerId ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showWriterDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowWriterDetails(e)}
                >
                  {!showWriterDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for engineer */}
            {/* {subProject.workorder_id && subProject.workorder_status === 3 ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showEngineerDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowEngineerDetails(e)}
                >
                  {!showEngineerDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for artist/engineer */}
            {/* {subProject.workorder_id &&
            selectedWriterId !== selectedArtistId &&
            selectedArtistId === selectedEngineerId &&
            subProject.workorder_status !== 5 ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showEngineerDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowEngineerDetails(e)}
                >
                  {!showEngineerDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* expand icon for Writer/Artist/Engineer */}
            {/* {subProject.workorder_id &&
            subProject.workorder_auto_approve === 1 &&
            selectedWriterId === selectedArtistId &&
            selectedWriterId === selectedEngineerId &&
            subProject.workorder_status !== 5 ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showEngineerDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowEngineerDetails(e)}
                >
                  {!showEngineerDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* when Writer/Artist/Engineer & not auto approved work order  */}
            {/* {subProject.workorder_id &&
            !subProject.workorder_auto_approve &&
            selectedWriterId === selectedArtistId &&
            selectedWriterId === selectedEngineerId &&
            subProject.workorder_status !== 5 ? (
              <div className="flex flex-row justify-end w-full">
                <div
                  className={`w-max cursor-pointer rounded-md ${
                    !showEngineerDetails
                      ? "bg-primary hover:bg-blue-700"
                      : "bg-amber-500 hover:bg-amber-700"
                  } px-1.5`}
                  onClick={(e) => handleOnclickShowEngineerDetails(e)}
                >
                  {!showEngineerDetails ? (
                    <>
                      <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                    </>
                  ) : (
                    <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                  )}
                </div>
              </div>
            ) : null} */}

            {/* init show component for Writer, Artist when writer & artist are same but no auto-approve */}
            {subProject.workorder_id &&
            (subProject.workorder_status === 1 ||
              subProject.workorder_status === 2) &&
            subProject.workorder_auto_approve !== 1 &&
            selectedWriterId === selectedArtistId ? (
              <>
                <SubProjectShowWriter
                  subProject={subProject}
                  lyricsVal={lyricsVal}
                  setDeleteFileId={setDeleteFileId}
                  setLyricsVal={setLyricsVal}
                  submitLyrics={submitLyrics}
                  subProjectId={subProject.id}
                  assignedIdeas={assignedIdeas}
                  surveySubmitStatus={surveySubmitStatus}
                  adminWriterInstrumentals={
                    subProject.admin_writer_instrumentals
                  }
                  uploadedFilesProgressData={{
                    progress,
                    error,
                    isUploading,
                  }}
                  setShowAssignIdeaModal={setShowAssignIdeaModal}
                  setSelectedSubProjectId={setSelectedSubProjectId}
                  handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                  handleInstrumentalUploads={handleInstrumentalUploads}
                  uploadedFilesProgressData2={{
                    progress,
                    error,
                    isUploading,
                  }}
                />
              </>
            ) : null}

            {/* init show component for Writer, Artist when writer & artist are not same */}
            {subProject.workorder_id &&
            (subProject.workorder_status === 1 ||
              subProject.workorder_status === 2) &&
            selectedWriterId !== selectedArtistId ? (
              <>
                <SubProjectShowWriter
                  subProject={subProject}
                  lyricsVal={lyricsVal}
                  setDeleteFileId={setDeleteFileId}
                  setLyricsVal={setLyricsVal}
                  submitLyrics={submitLyrics}
                  subProjectId={subProject.id}
                  assignedIdeas={assignedIdeas}
                  surveySubmitStatus={surveySubmitStatus}
                  adminWriterInstrumentals={
                    subProject.admin_writer_instrumentals
                  }
                  uploadedFilesProgressData={{
                    progress,
                    error,
                    isUploading,
                  }}
                  setShowAssignIdeaModal={setShowAssignIdeaModal}
                  setSelectedSubProjectId={setSelectedSubProjectId}
                  handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                  handleInstrumentalUploads={handleInstrumentalUploads}
                  uploadedFilesProgressData2={{
                    progress,
                    error,
                    isUploading,
                  }}
                />
              </>
            ) : null}

            {/* init show component for Writer/Artist */}
            {subProject.workorder_id &&
            subProject.workorder_auto_approve === 1 &&
            subProject.workorder_status === 1 &&
            selectedWriterId === selectedArtistId &&
            selectedWriterId !== selectedEngineerId ? (
              <>
                <SubProjectShowWriter
                  subProject={subProject}
                  lyricsVal={lyricsVal}
                  setDeleteFileId={setDeleteFileId}
                  setLyricsVal={setLyricsVal}
                  submitLyrics={submitLyrics}
                  subProjectId={subProject.id}
                  assignedIdeas={assignedIdeas}
                  surveySubmitStatus={surveySubmitStatus}
                  adminWriterInstrumentals={
                    subProject.admin_writer_instrumentals
                  }
                  uploadedFilesProgressData={{
                    progress,
                    error,
                    isUploading,
                  }}
                  setShowAssignIdeaModal={setShowAssignIdeaModal}
                  setSelectedSubProjectId={setSelectedSubProjectId}
                  handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                  handleInstrumentalUploads={handleInstrumentalUploads}
                  uploadedFilesProgressData2={{
                    progress,
                    error,
                    isUploading,
                  }}
                />
              </>
            ) : null}

            {/* collapsed component of Writer, Artist, Writer/Artist */}
            {showWriterDetails ? (
              <SubProjectCollapseWriterAndArtist
                subProjectId={subProject.id}
                assignedIdeas={assignedIdeas}
                surveySubmitStatus={surveySubmitStatus}
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                uploadedFilesProgressData={{
                  progress,
                  error,
                  isUploading,
                }}
                handleInstrumentalUploads={handleInstrumentalUploads}
                setDeleteFileId={setDeleteFileId}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
              />
            ) : null}

            {/* init show component for workorder completed */}
            {subProject.workorder_id && subProject.workorder_status === 5 ? (
              <SubProjectShowEngineer
                masters={subProject.masters}
                setDeleteFileId={setDeleteFileId}
                subProjectId={subProject.id}
                assignedIdeas={assignedIdeas}
                demos={subProject.demos}
                lyricsVal={lyricsVal}
                uploadedFilesProgressData={{ progress, error, isUploading }}
                surveySubmitStatus={surveySubmitStatus}
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                handleInstrumentalUploads={handleInstrumentalUploads}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                setLyricsVal={setLyricsVal}
                submitLyrics={submitLyrics}
              />
            ) : null}

            {/* init show component for Engineer */}
            {subProject.workorder_id && subProject.workorder_status === 3 ? (
              <SubProjectShowEngineer
                masters={subProject.masters}
                setDeleteFileId={setDeleteFileId}
                subProjectId={subProject.id}
                assignedIdeas={assignedIdeas}
                demos={subProject.demos}
                lyricsVal={lyricsVal}
                uploadedFilesProgressData={{ progress, error, isUploading }}
                surveySubmitStatus={surveySubmitStatus}
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                handleInstrumentalUploads={handleInstrumentalUploads}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                setLyricsVal={setLyricsVal}
                submitLyrics={submitLyrics}
              />
            ) : null}

            {/* init show component for Writer/Artist/Engineer */}
            {subProject.workorder_id &&
            subProject.workorder_auto_approve === 1 &&
            selectedWriterId === selectedArtistId &&
            selectedWriterId === selectedEngineerId &&
            subProject.workorder_status !== 5 ? (
              <SubProjectShowEngineer
                masters={subProject.masters}
                setDeleteFileId={setDeleteFileId}
                subProjectId={subProject.id}
                assignedIdeas={assignedIdeas}
                demos={subProject.demos}
                lyricsVal={lyricsVal}
                uploadedFilesProgressData={{ progress, error, isUploading }}
                surveySubmitStatus={surveySubmitStatus}
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                handleInstrumentalUploads={handleInstrumentalUploads}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                setLyricsVal={setLyricsVal}
                submitLyrics={submitLyrics}
              />
            ) : null}

            {/* init show component for Artist/Engineer */}
            {subProject.workorder_id &&
            selectedWriterId !== selectedArtistId &&
            selectedArtistId === selectedEngineerId &&
            subProject.workorder_status !== 5 ? (
              <SubProjectShowEngineer
                masters={subProject.masters}
                setDeleteFileId={setDeleteFileId}
                subProjectId={subProject.id}
                assignedIdeas={assignedIdeas}
                demos={subProject.demos}
                lyricsVal={lyricsVal}
                uploadedFilesProgressData={{ progress, error, isUploading }}
                surveySubmitStatus={surveySubmitStatus}
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                handleInstrumentalUploads={handleInstrumentalUploads}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                setLyricsVal={setLyricsVal}
                submitLyrics={submitLyrics}
              />
            ) : null}

            {/* init show component for Writer/Artist/Engineer but not auto approved work order */}
            {subProject.workorder_id &&
            selectedWriterId === selectedArtistId &&
            selectedArtistId === selectedEngineerId &&
            !subProject.workorder_auto_approve &&
            subProject.workorder_status !== 5 ? (
              <SubProjectShowEngineer
                masters={subProject.masters}
                setDeleteFileId={setDeleteFileId}
                subProjectId={subProject.id}
                assignedIdeas={assignedIdeas}
                demos={subProject.demos}
                lyricsVal={lyricsVal}
                uploadedFilesProgressData={{ progress, error, isUploading }}
                surveySubmitStatus={surveySubmitStatus}
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                handleInstrumentalUploads={handleInstrumentalUploads}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                setLyricsVal={setLyricsVal}
                submitLyrics={submitLyrics}
              />
            ) : null}

            {/* collapsed component of Engineer, Writer/Artist/Engineer, Artist/Engineer */}
            {showEngineerDetails ? (
              <SubProjectCollapseEngineer
                subProjectId={subProject.id}
                assignedIdeas={assignedIdeas}
                demos={subProject.demos}
                lyricsVal={lyricsVal}
                uploadedFilesProgressData={{ progress, error, isUploading }}
                surveySubmitStatus={surveySubmitStatus}
                adminWriterInstrumentals={subProject.admin_writer_instrumentals}
                handleInstrumentalUploads={handleInstrumentalUploads}
                setDeleteFileId={setDeleteFileId}
                setShowAssignIdeaModal={setShowAssignIdeaModal}
                setSelectedSubProjectId={setSelectedSubProjectId}
                handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
                setLyricsVal={setLyricsVal}
                submitLyrics={submitLyrics}
              />
            ) : null}
          </div>
        </div>
      )}
      {showDeleteIdeaModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to delete this idea?`}
          setModalClose={handleDeleteIdeaModalClose}
          setFormYes={handleDeleteAssignedIdea}
        />
      ) : null}
      {showAddIdeaModal ? (
        <AddIdeaModal
          theme={theme}
          ideas={ideas}
          setModalClose={handleShowAddIdeaModalClose}
          setIdeaAddForm={handleAddIdea}
          projectId={projectId}
        />
      ) : null}
    </>
  );
};

export default SubProject;
