<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/new/cheerEQ-2-Ed2.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Cheer Equality Records - Music Production Management"
    />
    <meta name="keywords" content="music, records, equality, artists" />

    <meta property="og:type" content="website" />
    <meta property="og:title" content="Cheer Equality Records" />
    <meta
      property="og:description"
      content="Cheer Equality Records - Music Production Management"
    />
    <meta property="og:image" content="/logos/CheerEQ_Mark-2.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Cheer Equality Records" />
    <meta
      name="twitter:description"
      content="Cheer Equality Records - Music Production Management"
    />
    <meta name="twitter:image" content="/logos/CheerEQ_Mark-2.png" />

    <title>Cheer Equality Records</title>
    <script type="module" crossorigin src="/assets/index-a238f67b.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-3aca5368.js">
    <link rel="modulepreload" crossorigin href="/assets/@floating-ui/react-2bb8505c.js">
    <link rel="modulepreload" crossorigin href="/assets/react-confirm-alert-c0286b0e.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-0d830203.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-8ac6acae.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-3acbc22e.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/@react-pdf/renderer-15eed3d8.js">
    <link rel="modulepreload" crossorigin href="/assets/jszip-cde5bc57.js">
    <link rel="modulepreload" crossorigin href="/assets/moment-55cb88ed.js">
    <link rel="modulepreload" crossorigin href="/assets/axios-439bb627.js">
    <link rel="modulepreload" crossorigin href="/assets/lucide-react-8d0f4c11.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-f0d7eca8.js">
    <link rel="modulepreload" crossorigin href="/assets/react-spinners-8f8c10ae.js">
    <link rel="modulepreload" crossorigin href="/assets/react-hook-form-8d9d9404.js">
    <link rel="modulepreload" crossorigin href="/assets/@hookform/resolvers-d03d48d5.js">
    <link rel="modulepreload" crossorigin href="/assets/yup-6550c88f.js">
    <link rel="modulepreload" crossorigin href="/assets/suneditor-233fc11b.js">
    <link rel="modulepreload" crossorigin href="/assets/suneditor-react-33c9c0e3.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-acf59ef3.js">
    <link rel="modulepreload" crossorigin href="/assets/@popperjs/core-8746c87e.js">
    <link rel="modulepreload" crossorigin href="/assets/moment-timezone-c556e14f.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/aws-s3-5653d8cf.js">
    <link rel="modulepreload" crossorigin href="/assets/react-dates-9940ba2a.js">
    <link rel="modulepreload" crossorigin href="/assets/clsx-0839fdbe.js">
    <link rel="modulepreload" crossorigin href="/assets/class-variance-authority-ff2c9236.js">
    <link rel="modulepreload" crossorigin href="/assets/tailwind-merge-05141ada.js">
    <link rel="modulepreload" crossorigin href="/assets/@radix-ui/react-progress-dd2d1682.js">
    <link rel="modulepreload" crossorigin href="/assets/cmdk-d62e18e9.js">
    <link rel="modulepreload" crossorigin href="/assets/html2canvas-e0a7d97b.js">
    <link rel="modulepreload" crossorigin href="/assets/jspdf-962b66a2.js">
    <link rel="modulepreload" crossorigin href="/assets/bootstrap-a957bf94.js">
    <link rel="modulepreload" crossorigin href="/assets/react-dropzone-d3606665.js">
    <link rel="modulepreload" crossorigin href="/assets/jspdf-autotable-4996b9ec.js">
    <link rel="modulepreload" crossorigin href="/assets/@tailwindcss/forms-68ac209c.js">
    <link rel="modulepreload" crossorigin href="/assets/react-pdf-tailwind-82cb7411.js">
    <link rel="modulepreload" crossorigin href="/assets/@fullcalendar/core-ff88745d.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/core-5d4a6f29.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/dashboard-9ed0e038.js">
    <link rel="modulepreload" crossorigin href="/assets/react-select-07284ed8.js">
    <link rel="modulepreload" crossorigin href="/assets/react-window-fa81bf5d.js">
    <link rel="modulepreload" crossorigin href="/assets/lodash-c86e3346.js">
    <link rel="modulepreload" crossorigin href="/assets/@fullcalendar/react-1e2d3533.js">
    <link rel="modulepreload" crossorigin href="/assets/@fullcalendar/daygrid-b382c522.js">
    <link rel="modulepreload" crossorigin href="/assets/xlsx-js-style-bcaaed7d.js">
    <link rel="modulepreload" crossorigin href="/assets/react-loading-skeleton-a8f73c32.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/drag-drop-593d2a9d.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/react-07ca6ec5.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/dropbox-d3ac5536.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/google-drive-543a142d.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/onedrive-24a3367e.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/tus-a7bc7213.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/xhr-upload-dc2d48e2.js">
    <link rel="modulepreload" crossorigin href="/assets/uppy-e92775e5.js">
    <link rel="modulepreload" crossorigin href="/assets/react-signature-pad-wrapper-0a8111f7.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-9481002f.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-28525c60.js">
    <link rel="stylesheet" href="/assets/index-a99c8fa4.css">
  </head>
  <body>
    <div id="root"></div>
    <div id="portal"></div>

    
  </body>
</html>
