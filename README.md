# equalityrecord_frontend

const teamTypes
1: All Girl
2: Coed
3:TBD
0: none

subproject status
0: created
1: completed

workorder status
1: 'Writer',
2: 'Artist',
3: 'Engineer',
4: 'Rejected',
5: 'Completed',
6: 'Inactive',

### Files:

1. instrumental (loops) files -> uploaded by writer -> belongs to a workOrder and writer
2. demo files -> uploaded by writer -> belongs to a subProject and writer
3. session files -> uploaded by artist -> belongs to a workOrder and artist
4. master files -> uploaded by engineer -> belongs to a subProject and engineer

#### note: lyrics (text stored at database) are uploaded by writer and belongs to a subProject and writer

## dashboard notification

1. we show notification to the user when a new workOrder is waiting for approval
2. we show notification to the user when a workorder is completed

cp node_modules/coi-serviceworker/coi-serviceworker.js dist/
