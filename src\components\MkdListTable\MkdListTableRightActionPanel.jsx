import React, { Fragment } from "react";
import { capitalize } from "Utils/utils";
import { LazyLoad } from "Components/LazyLoad";
import { ModalPrompt } from "Components/Modal";
import MkdListTableRowDropdown from "./MkdListTableRowDropdown";
import MkdListTableRowButtons from "./MkdListTableRowButtons";

const MkdListTableRightActionPanel = ({
  table,
  loading,
  columns = [],
  actions,
  actionPostion = [],
  tableRole,
  deleteItem,
  deleteLoading,
  actionId = "id",
  showDeleteModal,
  currentTableData = [],
  setShowDeleteModal,
}) => {
  const [deleteId, setIdToDelete] = React.useState(null);

  const setDeleteId = async (id) => {
    setShowDeleteModal(true);
    setIdToDelete(id);
  };
  return (
    <Fragment>
      {!loading && currentTableData.length && columns.length ? (
        <>
          {(columns.find((item) => item.accessor === "") &&
            actions?.delete?.show) ||
          Object.keys(actions).filter(
            (key) =>
              actions[key]?.show &&
              actions[key]?.locations &&
              actions[key]?.locations?.length &&
              (actions[key]?.locations?.includes("dropdown") ||
                actions[key]?.locations?.includes("buttons"))
          )?.length ? (
            <div className="sticky right-0 z-10 h-full bg-white">
              <div className="grid grid-rows-[auto_1fr]">
                <div className="flex !h-[2.5rem] !max-h-[2.5rem] !min-h-[2.5rem] border-b !bg-weak-100 "></div>

                <div className="flex flex-col px-1">
                  {currentTableData?.map((rowData, rowDataIndex) => {
                    return (
                      <div
                        className="flex !h-[3rem] !max-h-[3rem] !min-h-[3rem] w-full items-center border-b"
                        key={rowDataIndex}
                      >
                        {actionPostion?.includes("dropdown") ? (
                          <MkdListTableRowDropdown
                            actions={actions}
                            columns={columns}
                            row={rowData}
                            setDeleteId={setDeleteId}
                            actionId={actionId}
                          />
                        ) : null}
                        {actionPostion?.includes("buttons") ? (
                          <MkdListTableRowButtons
                            row={rowData}
                            actions={actions}
                            columns={columns}
                            actionId={actionId}
                            setDeleteId={setDeleteId}
                          />
                        ) : null}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          ) : null}

          <LazyLoad>
            <ModalPrompt
              open={showDeleteModal}
              actionHandler={() => {
                deleteItem(deleteId);
              }}
              closeModalFunction={() => {
                setIdToDelete(null);
                setShowDeleteModal(false);
              }}
              title={`Delete ${capitalize(table)} `}
              message={`You are about to delete ${capitalize(
                table
              )} ${deleteId}, note that this action is irreversible`}
              acceptText={`DELETE`}
              rejectText={`CANCEL`}
              loading={deleteLoading}
            />
          </LazyLoad>
        </>
      ) : null}
    </Fragment>
  );
};

export default MkdListTableRightActionPanel;
