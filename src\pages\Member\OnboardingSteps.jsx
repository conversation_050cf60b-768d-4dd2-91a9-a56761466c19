import React, { useState } from "react";
import { GlobalContext, showToast } from "Src/globalContext";
import { useNavigate } from "react-router-dom";
import { updateUserDetailsAPI } from "Src/services/userService";
import { createOrUpdateAllSettingsAPI } from "Src/services/settingService";
import { uploadS3FilesAPI } from "Src/services/workOrderService";
import PhotoUpload from "Components/PhotoUpload";
import LogoUpload from "Components/logoUpload";
import EditPolicyPdfUpload from "Components/EditPolicyPdfUpload";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";

const OnboardingSteps = ({ userDetails, subscriptionType }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  // Step state
  const [currentStep, setCurrentStep] = useState(1);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [privacyPolicyReviewed, setPrivacyPolicyReviewed] = useState(false);

  // Form data state
  console.log("Initializing formData with userDetails:", userDetails);
  const [formData, setFormData] = useState({
    // Company settings
    companyName: userDetails?.company_name || "",
    officeEmail: userDetails?.office_email || "",
    companyAddress: userDetails?.company_address || "",
    phone: userDetails?.phone || "",

    // Management settings (if applicable)
    managementValue: "",
    managementValueType: "%",

    // Deadline settings
    artistDeadline: "3",
    engineerDeadline: "3",
    artistEngineerDeadline: "3",

    // Survey settings
    surveyWeeks: "8",
    surveyDay: "Monday",

    // Routine submission settings
    routineWeeks: "1",
    routineDay: "Monday",

    // Estimated completion settings
    completionWeeks: "1",
    completionDay: "Friday",

    // Auto-approval setting
    autoApprove: false,

    // Deposit settings
    depositRequired: false,
    depositPercentage: "50",

    // Logo settings
    companyLogo: userDetails?.company_logo || null,
    companyLogoFile: null, // For storing the actual file object
    licenseLogo: userDetails?.license_company_logo || null,
    licenseLogoFile: null, // For storing the actual file object

    // Edit policy
    editPolicy: userDetails?.edit_policy_link || null,
    editPolicyFile: null, // For storing the actual file object

    // Service agreement
    serviceAgreement: userDetails?.contract_agreement || "",
  });

  // Debug license logo state
  React.useEffect(() => {
    console.log(
      "License logo state:",
      formData.licenseLogo ? "URL exists" : "URL null",
      formData.licenseLogoFile ? "File exists" : "File null"
    );
  }, [formData.licenseLogo, formData.licenseLogoFile]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Navigate to next step
  const nextStep = () => {
    setCurrentStep(currentStep + 1);
  };

  // Navigate to previous step
  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  // Save settings and complete onboarding
  const completeOnboarding = async () => {
    try {
      console.log("Completing onboarding with formData:", formData);

      // Create comprehensive payload for user settings
      let userSettingsPayload = {
        id: userDetails.id,
        company_name: formData.companyName,
        office_email: formData.officeEmail,
        company_address: formData.companyAddress,
        phone: formData.phone,
        survey: JSON.stringify({
          weeks: parseInt(formData.surveyWeeks),
          day: formData.surveyDay,
        }),
        routine_submission_date: JSON.stringify({
          weeks: parseInt(formData.routineWeeks),
          day: formData.routineDay,
        }),
        estimated_delivery: JSON.stringify({
          weeks: parseInt(formData.completionWeeks),
          day: formData.completionDay,
        }),
        deposit_percent: formData.depositRequired
          ? parseInt(formData.depositPercentage)
          : 0,
        contract_agreement: formData.serviceAgreement,
      };

      // Create separate payload for management settings if applicable
      if (
        subscriptionType === "Complete Suite" ||
        subscriptionType === "The Studio"
      ) {
        // This will be saved using the settings API
        const managementSettingsPayload = {
          settings: [
            {
              setting_key: "management_value",
              setting_value: formData.managementValue || "0",
            },
            {
              setting_key: "management_value_type",
              setting_value: formData.managementValueType || "%",
            },
            {
              setting_key: "artist_deadline",
              setting_value: formData.artistDeadline || "3",
            },
            {
              setting_key: "engineer_deadline",
              setting_value: formData.engineerDeadline || "3",
            },
            {
              setting_key: "artist_engineer_deadline",
              setting_value: formData.artistEngineerDeadline || "3",
            },
            {
              setting_key: "auto_approve",
              setting_value: formData.autoApprove ? "1" : "0",
            },
          ],
        };

        // Save management settings using the dedicated API
        const managementSettingsResult = await createOrUpdateAllSettingsAPI(
          managementSettingsPayload
        );
        if (managementSettingsResult.error) {
          console.error(
            "Error saving management settings:",
            managementSettingsResult
          );
          showToast(
            globalDispatch,
            "Failed to save management settings",
            4000,
            "error"
          );
        }
      }

      // Upload company logo if it exists
      if (formData.companyLogoFile) {
        try {
          // Create a FormData object for the company logo
          const companyLogoFormData = new FormData();

          // Use the stored file object directly
          companyLogoFormData.append("files", formData.companyLogoFile);

          // Upload to S3
          const companyLogoResult = await uploadS3FilesAPI(companyLogoFormData);
          if (!companyLogoResult.error) {
            let attachmentsArr = JSON.parse(companyLogoResult.attachments);
            userSettingsPayload.company_logo = attachmentsArr[0];
          } else {
            showToast(
              globalDispatch,
              "Failed to upload company logo",
              4000,
              "error"
            );
          }
        } catch (error) {
          console.error("Error uploading company logo:", error);
          showToast(
            globalDispatch,
            "Failed to upload company logo",
            4000,
            "error"
          );
        }
      } else if (userDetails.company_logo) {
        userSettingsPayload.company_logo = userDetails.company_logo;
      }

      // Upload license logo if it exists
      if (formData.licenseLogoFile) {
        try {
          console.log(
            "License logo file exists, uploading...",
            formData.licenseLogoFile.name,
            formData.licenseLogoFile.type,
            formData.licenseLogoFile.size
          );

          // Create a FormData object for the license logo
          const licenseLogoFormData = new FormData();

          // Use the stored file object directly
          licenseLogoFormData.append("files", formData.licenseLogoFile);

          // Verify FormData content
          console.log(
            "License logo FormData created, contains file:",
            licenseLogoFormData.has("files"),
            "File name:",
            formData.licenseLogoFile.name
          );

          // Upload to S3
          const licenseLogoResult = await uploadS3FilesAPI(licenseLogoFormData);
          console.log("License logo upload result:", licenseLogoResult);

          if (!licenseLogoResult.error) {
            let attachmentsArr = JSON.parse(licenseLogoResult.attachments);
            userSettingsPayload.license_company_logo = attachmentsArr[0];
            console.log("License logo URL:", attachmentsArr[0]);
          } else {
            console.error(
              "Failed to upload license logo:",
              licenseLogoResult.error
            );
            showToast(
              globalDispatch,
              "Failed to upload license logo",
              4000,
              "error"
            );
          }
        } catch (error) {
          console.error("Error uploading license logo:", error);
          showToast(
            globalDispatch,
            "Failed to upload license logo",
            4000,
            "error"
          );
        }
      } else if (userDetails.license_company_logo) {
        userSettingsPayload.license_company_logo =
          userDetails.license_company_logo;
      }

      // Upload edit policy if it exists
      if (formData.editPolicyFile) {
        try {
          // Create a FormData object for the edit policy
          const editPolicyFormData = new FormData();

          // Use the stored file object directly
          editPolicyFormData.append("files", formData.editPolicyFile);

          // Upload to S3
          const editPolicyResult = await uploadS3FilesAPI(editPolicyFormData);
          if (!editPolicyResult.error) {
            let attachmentsArr = JSON.parse(editPolicyResult.attachments);
            userSettingsPayload.edit_policy_link = attachmentsArr[0];
          } else {
            showToast(
              globalDispatch,
              "Failed to upload edit policy",
              4000,
              "error"
            );
          }
        } catch (error) {
          console.error("Error uploading edit policy:", error);
          showToast(
            globalDispatch,
            "Failed to upload edit policy",
            4000,
            "error"
          );
        }
      }

      // Add step field to mark onboarding as complete and include settings
      userSettingsPayload.steps = JSON.stringify({
        onboarding_complete: true,
        settings: {
          survey: {
            weeks: parseInt(formData.surveyWeeks),
            day: formData.surveyDay,
          },
          routine_submission: {
            weeks: parseInt(formData.routineWeeks),
            day: formData.routineDay,
          },
          estimated_completion: {
            weeks: parseInt(formData.completionWeeks),
            day: formData.completionDay,
          },
          deposit_percent: formData.depositRequired
            ? parseInt(formData.depositPercentage)
            : 0,
        },
      });

      console.log("Saving all settings with payload:", userSettingsPayload);

      // Update user details with all settings in a single API call
      await updateUserDetailsAPI(userSettingsPayload);

      // Mark onboarding as complete
      // await sdk.callRawAPI(
      //   "/v3/api/custom/equality_record/onboarding/complete",
      //   {},
      //   "POST"
      // );

      showToast(
        globalDispatch,
        "Onboarding completed successfully!",
        4000,
        "success"
      );

      // Navigate to backend dashboard
      navigate("/member/dashboard");

      // Reload the page to ensure all settings are applied
      // setTimeout(() => {
      //   window.location.href = "/member/dashboard";
      // }, 1000);
    } catch (error) {
      console.error("Error completing onboarding:", error);
      showToast(
        globalDispatch,
        "Error saving settings. Please try again.",
        4000,
        "error"
      );
    }
  };

  // Render step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">Terms of Service</h2>
            <div className="overflow-y-auto p-4 h-64 rounded-lg border border-strokedark bg-boxdark-2">
              <p className="text-bodydark">
                [Terms of Service content would go here] Lorem ipsum dolor sit
                amet, consectetur adipiscing elit. Nullam auctor, nisl eget
                ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl
                nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt,
                nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl.
              </p>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="terms"
                checked={termsAgreed}
                onChange={() => setTermsAgreed(!termsAgreed)}
                className="mr-2"
              />
              <label htmlFor="terms" className="text-white">
                I agree to the Terms of Service
              </label>
            </div>
            <div className="flex justify-end">
              <button
                onClick={nextStep}
                disabled={!termsAgreed}
                className="px-6 py-2 text-white rounded-lg bg-primary disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">Privacy Policy</h2>
            <div className="overflow-y-auto p-4 h-64 rounded-lg border border-strokedark bg-boxdark-2">
              <p className="text-bodydark">
                [Privacy Policy content would go here] Lorem ipsum dolor sit
                amet, consectetur adipiscing elit. Nullam auctor, nisl eget
                ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl
                nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt,
                nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl.
              </p>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="privacy"
                checked={privacyPolicyReviewed}
                onChange={() =>
                  setPrivacyPolicyReviewed(!privacyPolicyReviewed)
                }
                className="mr-2"
              />
              <label htmlFor="privacy" className="text-white">
                I have read and understand the Privacy Policy
              </label>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                disabled={!privacyPolicyReviewed}
                className="px-6 py-2 text-white rounded-lg bg-primary disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">
              Company Information
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block mb-2 text-white">Company Name</label>
                <input
                  type="text"
                  name="companyName"
                  value={formData.companyName}
                  onChange={handleChange}
                  className="px-4 py-2 w-full text-white rounded-lg border border-strokedark bg-boxdark-2"
                />
              </div>
              <div>
                <label className="block mb-2 text-white">Office Email</label>
                <input
                  type="email"
                  name="officeEmail"
                  value={formData.officeEmail}
                  onChange={handleChange}
                  className="px-4 py-2 w-full text-white rounded-lg border border-strokedark bg-boxdark-2"
                />
                <p className="mt-1 text-xs text-bodydark">
                  This is the email address you use for any general questions
                  pertaining to your company.
                </p>
              </div>
              <div>
                <label className="block mb-2 text-white">Company Address</label>
                <input
                  type="text"
                  name="companyAddress"
                  value={formData.companyAddress}
                  onChange={handleChange}
                  className="px-4 py-2 w-full text-white rounded-lg border border-strokedark bg-boxdark-2"
                />
              </div>
              <div>
                <label className="block mb-2 text-white">Phone Number</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="px-4 py-2 w-full text-white rounded-lg border border-strokedark bg-boxdark-2"
                />
              </div>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                disabled={
                  !formData.companyName ||
                  !formData.officeEmail ||
                  !formData.companyAddress ||
                  !formData.phone
                }
                className="px-6 py-2 text-white rounded-lg bg-primary disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">Logo Settings</h2>
            <p className="mb-4 text-bodydark">
              Both company logo and license logo are required to proceed.
            </p>
            <div className="space-y-6">
              <div>
                <label className="block mb-2 text-white">Company Logo</label>
                <p className="mb-3 text-xs text-bodydark">
                  This logo will be displayed on your profile, dashboard, and
                  8-count sheets. Please ensure it is suitable for placement on
                  both white and black backgrounds.
                </p>
                <div className="mb-4">
                  {formData.companyLogo && (
                    <div className="mb-2">
                      <img
                        src={formData.companyLogo}
                        alt="Company Logo"
                        className="w-auto h-20 rounded border border-strokedark"
                      />
                    </div>
                  )}
                  <PhotoUpload
                    maxFileSize={2}
                    setFileUpload={(formData) => {
                      // Store the file object directly for preview
                      const file = formData.get("files");
                      if (file) {
                        // Create a temporary URL for preview purposes
                        const tempUrl = URL.createObjectURL(file);
                        setFormData((prev) => ({
                          ...prev,
                          companyLogo: tempUrl,
                          companyLogoFile: file, // Store the actual file object
                        }));
                      }
                    }}
                  />
                </div>
              </div>

              <div>
                <label className="block mb-2 text-white">License Logo</label>
                <p className="mb-3 text-xs text-bodydark">
                  This logo will be displayed on the auto-generated music
                  license. Please ensure it is suitable for placement on a white
                  background. Recommended dimensions: 300x100 pixels.
                </p>
                <div className="mb-4">
                  {formData.licenseLogo && (
                    <div className="mb-2">
                      <img
                        src={formData.licenseLogo}
                        alt="License Logo"
                        className="w-auto h-20 rounded border border-strokedark"
                      />
                    </div>
                  )}
                  <LogoUpload
                    maxFileSize={2}
                    setFileUpload={(formData) => {
                      // Store the file object directly for preview
                      const file = formData.get("files");
                      if (file) {
                        // Create a temporary URL for preview purposes
                        const tempUrl = URL.createObjectURL(file);
                        console.log(
                          "License logo file:",
                          file,
                          "Name:",
                          file.name
                        );

                        // Update state with the new file
                        setFormData((prev) => {
                          const updatedState = {
                            ...prev,
                            licenseLogo: tempUrl,
                            licenseLogoFile: file, // Store the actual file object
                          };
                          console.log(
                            "Updated license logo state:",
                            updatedState.licenseLogo ? "URL set" : "null",
                            updatedState.licenseLogoFile ? "File set" : "null"
                          );
                          return updatedState;
                        });
                      }
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                disabled={!formData.companyLogo || !formData.licenseLogo}
                className="px-6 py-2 text-white rounded-lg bg-primary disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">Edit Policy</h2>
            <p className="mb-4 text-bodydark">
              Please upload your edit policy in PDF format. It will be displayed
              when clients submit project edits.
            </p>
            <div className="mb-6">
              {formData.editPolicy ? (
                <div className="flex items-center p-3 mb-4 rounded-lg border border-strokedark bg-boxdark-2">
                  <div className="mr-3 text-primary">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-6 h-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <div className="flex-grow">
                    <p className="text-white">Edit Policy Uploaded</p>
                    <p className="text-xs text-bodydark">PDF Document</p>
                  </div>
                  <button
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, editPolicy: null }))
                    }
                    className="px-2 py-1 ml-2 text-xs text-white rounded-lg bg-danger"
                  >
                    Remove
                  </button>
                </div>
              ) : (
                <EditPolicyPdfUpload
                  maxFileSize={5}
                  setFileUpload={(formData) => {
                    // Store the file object directly for preview
                    const file = formData.get("files");
                    if (file) {
                      // Create a temporary URL for preview purposes
                      const tempUrl = URL.createObjectURL(file);
                      setFormData((prev) => ({
                        ...prev,
                        editPolicy: tempUrl,
                        editPolicyFile: file, // Store the actual file object
                      }));
                    }
                  }}
                />
              )}
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                className="px-6 py-2 text-white rounded-lg bg-primary"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 6:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">Service Agreement</h2>
            <p className="mb-4 text-bodydark">
              Enter your service agreement text. This will be shown to clients
              when they book your services.
            </p>
            <div className="mb-6">
              <SunEditor
                setContents={formData.serviceAgreement}
                onChange={(content) => {
                  setFormData((prev) => ({
                    ...prev,
                    serviceAgreement: content,
                  }));
                }}
                setOptions={{
                  buttonList: [
                    ["undo", "redo"],
                    ["font", "fontSize", "formatBlock"],
                    [
                      "bold",
                      "underline",
                      "italic",
                      "strike",
                      "subscript",
                      "superscript",
                    ],
                    ["removeFormat"],
                    ["fontColor", "hiliteColor"],
                    ["indent", "outdent"],
                    ["align", "horizontalRule", "list", "table"],
                    ["link"],
                    ["fullScreen", "showBlocks", "codeView"],
                    ["preview"],
                  ],
                  minHeight: "300px",
                  height: "auto",
                }}
              />
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                className="px-6 py-2 text-white rounded-lg bg-primary"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 7:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">Project Settings</h2>
            <div className="space-y-4">
              <div>
                <label className="block mb-2 text-white">
                  Music Survey Settings
                </label>
                <div className="flex gap-4">
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Weeks before production
                    </label>
                    <input
                      type="number"
                      name="surveyWeeks"
                      value={formData.surveyWeeks}
                      onChange={handleChange}
                      min="1"
                      max="12"
                      className="px-4 py-2 w-20 text-white rounded-lg border border-strokedark bg-boxdark-2"
                    />
                  </div>
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Day of week
                    </label>
                    <select
                      name="surveyDay"
                      value={formData.surveyDay}
                      onChange={handleChange}
                      className="px-4 py-2 text-white rounded-lg border border-strokedark bg-boxdark-2"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </select>
                  </div>
                </div>
              </div>

              <div>
                <label className="block mb-2 text-white">
                  Routine Submission Settings
                </label>
                <div className="flex gap-4">
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Weeks before production
                    </label>
                    <input
                      type="number"
                      name="routineWeeks"
                      value={formData.routineWeeks}
                      onChange={handleChange}
                      min="1"
                      max="12"
                      className="px-4 py-2 w-20 text-white rounded-lg border border-strokedark bg-boxdark-2"
                    />
                  </div>
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Day of week
                    </label>
                    <select
                      name="routineDay"
                      value={formData.routineDay}
                      onChange={handleChange}
                      className="px-4 py-2 text-white rounded-lg border border-strokedark bg-boxdark-2"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </select>
                  </div>
                </div>
              </div>

              <div>
                <label className="block mb-2 text-white">
                  Estimated Completion Settings
                </label>
                <div className="flex gap-4">
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Weeks after production
                    </label>
                    <input
                      type="number"
                      name="completionWeeks"
                      value={formData.completionWeeks}
                      onChange={handleChange}
                      min="1"
                      max="12"
                      className="px-4 py-2 w-20 text-white rounded-lg border border-strokedark bg-boxdark-2"
                    />
                  </div>
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Day of week
                    </label>
                    <select
                      name="completionDay"
                      value={formData.completionDay}
                      onChange={handleChange}
                      className="px-4 py-2 text-white rounded-lg border border-strokedark bg-boxdark-2"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </select>
                  </div>
                </div>
              </div>

              <div>
                <label className="block mb-2 text-white">
                  Deposit Settings
                </label>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="depositRequired"
                      name="depositRequired"
                      checked={formData.depositRequired}
                      onChange={handleChange}
                      className="mr-2"
                    />
                    <label htmlFor="depositRequired" className="text-white">
                      Require minimum deposit payment for bookings
                    </label>
                  </div>

                  {formData.depositRequired && (
                    <div className="flex gap-2 items-center pl-6">
                      <input
                        type="number"
                        name="depositPercentage"
                        value={formData.depositPercentage}
                        onChange={handleChange}
                        min="1"
                        max="100"
                        className="px-4 py-2 w-20 text-white rounded-lg border border-strokedark bg-boxdark-2"
                      />
                      <span className="text-white">% of total price</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                className="px-6 py-2 text-white rounded-lg bg-primary"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 8:
        // Management settings for all subscription types
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">
              Management Settings
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block mb-2 text-white">
                  Management Value
                </label>
                <div className="flex gap-4 items-center">
                  <input
                    type="number"
                    name="managementValue"
                    value={formData.managementValue}
                    onChange={handleChange}
                    className="px-4 py-2 w-32 text-white rounded-lg border border-strokedark bg-boxdark-2"
                  />
                  <select
                    name="managementValueType"
                    value={formData.managementValueType}
                    onChange={handleChange}
                    className="px-4 py-2 text-white rounded-lg border border-strokedark bg-boxdark-2"
                  >
                    <option value="%">%</option>
                    <option value="$">$</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block mb-2 text-white">
                  Deadlines for Vocal Orders
                </label>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Days for artist after writer submits
                    </label>
                    <input
                      type="number"
                      name="artistDeadline"
                      value={formData.artistDeadline}
                      onChange={handleChange}
                      min="1"
                      className="px-4 py-2 w-full text-white rounded-lg border border-strokedark bg-boxdark-2"
                    />
                  </div>
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Days for engineer after artist uploads
                    </label>
                    <input
                      type="number"
                      name="engineerDeadline"
                      value={formData.engineerDeadline}
                      onChange={handleChange}
                      min="1"
                      className="px-4 py-2 w-full text-white rounded-lg border border-strokedark bg-boxdark-2"
                    />
                  </div>
                  <div>
                    <label className="block mb-1 text-xs text-bodydark">
                      Days for artist who engineers their own vocals
                    </label>
                    <input
                      type="number"
                      name="artistEngineerDeadline"
                      value={formData.artistEngineerDeadline}
                      onChange={handleChange}
                      min="1"
                      className="px-4 py-2 w-full text-white rounded-lg border border-strokedark bg-boxdark-2"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="flex gap-2 items-center">
                  <input
                    type="checkbox"
                    name="autoApprove"
                    checked={formData.autoApprove}
                    onChange={handleChange}
                  />
                  <span className="text-white">
                    Enable auto-approval for work orders
                  </span>
                </label>
                <p className="mt-1 ml-6 text-xs text-bodydark">
                  This allows the writing stage to move directly to artist
                  recording without pre-approval.
                </p>
              </div>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                className="px-6 py-2 text-white rounded-lg bg-primary"
              >
                Next
              </button>
            </div>
          </div>
        );

      case 9:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">Review & Complete</h2>
            <div className="p-4 rounded-lg border border-strokedark bg-boxdark-2">
              <p className="text-white">
                You're all set! Click the button below to complete your
                onboarding and start using CheerEQ.
              </p>
            </div>
            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-2 text-white rounded-lg bg-meta-4"
              >
                Back
              </button>
              <button
                onClick={completeOnboarding}
                className="px-6 py-2 text-white rounded-lg bg-primary"
              >
                Complete Setup
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Render progress bar
  const renderProgressBar = () => {
    const totalSteps = 9; // Total steps is now fixed at 9 for all subscription types
    const progress = (currentStep / totalSteps) * 100;

    return (
      <div className="mb-8">
        <div className="w-full h-2 rounded-full bg-boxdark-2">
          <div
            className="h-full rounded-full bg-primary"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-2 text-xs text-bodydark">
          <span>Terms</span>
          <span>Privacy</span>
          <span>Company</span>
          <span>Logos</span>
          <span>Edit Policy</span>
          <span>Agreement</span>
          <span>Project</span>
          <span>Management</span>
          <span>Complete</span>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 mx-auto max-w-3xl rounded-lg shadow-lg bg-boxdark">
      {renderProgressBar()}
      {renderStepContent()}
    </div>
  );
};

export default OnboardingSteps;
