import { X, Volume2, Pause, Play } from "lucide-react";
import React, { useState, useRef, useEffect } from "react";

const AudioPlayer = ({ fileSource, setModalClose }) => {
  const audioRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.addEventListener("loadedmetadata", () => {
        setDuration(audioRef.current.duration);
      });

      audioRef.current.addEventListener("timeupdate", () => {
        setCurrentTime(audioRef.current.currentTime);
      });
    }
  }, []);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handlePlayPause = () => {
    if (audioRef.current.paused) {
      audioRef.current.play();
      setIsPlaying(true);
    } else {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleTimeUpdate = (e) => {
    const time = e.target.value;
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (e) => {
    const value = e.target.value;
    setVolume(value);
    audioRef.current.volume = value;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => setModalClose(false)}
      />

      <div className="relative w-full max-w-md rounded-lg bg-boxdark p-6 shadow-xl">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <h3 className="text-xl font-semibold text-white">Audio Player</h3>
          <button
            onClick={() => setModalClose(false)}
            className="rounded-full p-1 hover:bg-meta-4"
          >
            <X className="h-6 w-6 text-white" />
          </button>
        </div>

        {/* Audio Element */}
        <audio ref={audioRef} src={fileSource} className="hidden" />

        {/* Progress Bar */}
        <div className="mb-4">
          <input
            type="range"
            value={currentTime}
            max={duration}
            onChange={handleTimeUpdate}
            className="h-2 w-full cursor-pointer appearance-none rounded-lg bg-meta-4"
            style={{
              background: `linear-gradient(to right, #3C50E0 ${
                (currentTime / duration) * 100
              }%, #333F51 ${(currentTime / duration) * 100}%)`,
            }}
          />
          <div className="mt-2 flex justify-between text-sm text-white">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handlePlayPause}
              className="rounded-full bg-primary p-3 text-white hover:bg-primary/90"
            >
              {isPlaying ? (
                <Pause className="h-6 w-6" />
              ) : (
                <Play className="h-6 w-6" />
              )}
            </button>

            {/* Volume Control */}
            <div className="flex items-center gap-2">
              <Volume2 className="h-5 w-5 text-white" />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={handleVolumeChange}
                className="h-1.5 w-24 cursor-pointer appearance-none rounded-lg bg-meta-4"
                style={{
                  background: `linear-gradient(to right, #3C50E0 ${
                    volume * 100
                  }%, #333F51 ${volume * 100}%)`,
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
