import React from "react";
import { AuthContext } from "Src/authContext";
import { GlobalContext } from "Context/Global";
import { StripeOnetimeProductsComponent } from "Components/StripeOnetimeProductsComponent";
import AdminStripePricesListPage from "../List/AdminStripePricesListPage";
import { StripePlansComponent } from "Components/StripePlansComponent";

const AdminDashboardPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "dashboard",
      },
    });
  }, []);
  return (
    <>
      <div className=" w-full items-center justify-center text-7xl text-gray-700 ">
        {/* Dashboard */}
        {/* <StripeOnetimeProductsComponent/> */}
        {/* <StripePlansComponent /> */}
      </div>
    </>
  );
};

export default AdminDashboardPage;
