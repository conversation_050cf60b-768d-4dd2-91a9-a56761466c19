import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import CustomSelect2 from "Components/CustomSelect2";

const HomePage = () => {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [selectedProjectRange, setSelectedProjectRange] = useState("1-50");
  const [openFaq, setOpenFaq] = useState(null);

  const pricingRanges = {
    "1-50": {
      portal: { monthly: 150, annual: 1500 },
      studio: { monthly: 150, annual: 1500 },
      complete: { monthly: 250, annual: 2500 },
    },
    "51-100": {
      portal: { monthly: 175, annual: 1750 },
      studio: { monthly: 175, annual: 1750 },
      complete: { monthly: 275, annual: 2750 },
    },
    "101-150": {
      portal: { monthly: 200, annual: 2000 },
      studio: { monthly: 200, annual: 2000 },
      complete: { monthly: 300, annual: 3000 },
    },
    "151-200": {
      portal: { monthly: 225, annual: 2250 },
      studio: { monthly: 225, annual: 2250 },
      complete: { monthly: 325, annual: 3250 },
    },
    "201+": {
      portal: { monthly: 250, annual: 2500 },
      studio: { monthly: 250, annual: 2500 },
      complete: { monthly: 350, annual: 3500 },
    },
    "301+": {
      portal: { monthly: 300, annual: 3000 },
      studio: { monthly: 300, annual: 3000 },
      complete: { monthly: 400, annual: 4000 },
    },
  };

  const toggleFaq = (index) => {
    if (openFaq === index) {
      setOpenFaq(null);
    } else {
      setOpenFaq(index);
    }
  };

  return (
    <div className="min-h-screen bg-boxdark">
      {/* Navbar */}
      <nav className="fixed top-0 z-50 w-full bg-transparent backdrop-blur-sm">
        <div className="container mx-auto px-4">
          <div className="flex h-20 items-center justify-between">
            <div className="flex items-center">
              <img
                src={`${window.location.origin}/new/cheerEQ-1-Ed1.png`}
                alt="Logo"
                className="h-[80px] w-[180px] object-contain"
              />
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex md:items-center md:gap-8">
              <a
                href="#home"
                className="text-sm font-medium text-white hover:text-primary"
              >
                Home
              </a>
              <a
                href="#features"
                className="text-sm font-medium text-bodydark hover:text-primary"
              >
                Features
              </a>
              <a
                href="#subscriptions"
                className="text-sm font-medium text-bodydark hover:text-primary"
              >
                Subscriptions
              </a>
              <a
                href="#contact"
                className="text-sm font-medium text-bodydark hover:text-primary"
              >
                Contact
              </a>
              <button
                onClick={() => navigate("/member/login")}
                className="rounded-full bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
              >
                Sign In
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <FontAwesomeIcon
                icon="fa-solid fa-bars"
                className="h-6 w-6 text-white"
              />
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 bg-black md:hidden">
          <div className="flex h-full flex-col p-4">
            <button onClick={() => setIsMenuOpen(false)} className="self-end">
              <FontAwesomeIcon
                icon="fa-solid fa-times"
                className="h-6 w-6 text-white"
              />
            </button>
            <div className="mt-8 flex flex-col gap-4">
              <a
                href="#home"
                className="text-center text-lg font-medium text-white"
              >
                Home
              </a>
              <a
                href="#features"
                className="text-center text-lg font-medium text-white"
              >
                Features
              </a>
              <a
                href="#subscriptions"
                className="text-center text-lg font-medium text-white"
              >
                Subscriptions
              </a>
              <a
                href="#contact"
                className="text-center text-lg font-medium text-white"
              >
                Contact
              </a>
              <button
                onClick={() => navigate("/member/login")}
                className="mt-4 rounded-full bg-primary px-6 py-2 text-lg font-medium text-white"
              >
                Sign In
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section id="home" className="relative pt-20">
        <div className="container mx-auto px-4 py-20 2xl:px-6">
          <div className="grid items-center gap-12 md:grid-cols-2">
            <div>
              <h1 className="mb-6 text-3xl font-bold leading-normal text-white xl:text-5xl">
                Streamline Your Cheerleading Music Production - Faster, Smarter,
                Easier.
              </h1>
              <p className="mb-8 text-lg text-bodydark">
                CheerEQ is the ultimate platform for custom cheerleading music
                production, automating vocal workflows and client project
                management. Save time on administrative tasks, deliver
                high-quality mixes faster, and focus on what matters—perfecting
                your sound.
              </p>
              <div className="flex gap-4">
                <button
                  onClick={() => navigate("/member/login")}
                  className="rounded-full bg-primary px-3 py-3 text-sm font-semibold text-white transition-all hover:bg-opacity-90 md:px-8 md:py-3 md:text-lg"
                >
                  Get Started
                </button>

                <a href="#contact">
                  <button className="rounded-full border border-primary bg-transparent px-3 py-3 text-sm font-semibold text-primary transition-all hover:bg-primary/80 hover:text-white md:px-8 md:py-3 md:text-lg">
                    Learn More
                  </button>
                </a>
              </div>
            </div>
            <div className="relative mx-auto max-w-[520px]">
              <div className="aspect-square h-full rounded-[100px] bg-gradient-to-r from-primary to-meta-5">
                <img
                  src={`${window.location.origin}/logos/hero.jpg`}
                  alt="Logo"
                  className="h-full w-full rounded-[100px] object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold text-white">
            Why Choose CheerEQ?
          </h2>
          <div className="grid gap-8 md:grid-cols-3">
            <div className="rounded-xl bg-boxdark-2 p-8">
              <div className="mb-4 inline-flex rounded-lg bg-primary/10 p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-clipboard-check"
                  className="h-6 w-6 text-primary"
                />
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                Automated Surveys
              </h3>
              <p className="text-bodydark">
                Streamline client project notes with automated music surveys,
                requiring team details before production begins.
              </p>
            </div>

            <div className="rounded-xl bg-boxdark-2 p-8">
              <div className="mb-4 inline-flex rounded-lg bg-primary/10 p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-pen-to-square"
                  className="h-6 w-6 text-primary"
                />
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                Automated Edits
              </h3>
              <p className="text-bodydark">
                CheerEQ removes the hassle of manual tracking, letting clients
                submit edits at their convenience while automatically
                identifying the type and providing an estimated delivery date.
              </p>
            </div>

            <div className="rounded-xl bg-boxdark-2 p-8">
              <div className="mb-4 inline-flex rounded-lg bg-primary/10 p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-bell"
                  className="h-6 w-6 text-primary"
                />
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                Automated Reminder Emails
              </h3>
              <p className="text-bodydark">
                Fed up with scheduling reminder emails? CheerEQ automates
                reminders for key deadlines, including surveys, 8-count sheets,
                routine videos, and more.
              </p>
            </div>

            <div className="rounded-xl bg-boxdark-2 p-8">
              <div className="mb-4 inline-flex rounded-lg bg-primary/10 p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-microphone"
                  className="h-6 w-6 text-primary"
                />
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                Vocal Production Management
              </h3>
              <p className="text-bodydark">
                CheerEQ simplifies vocal production by automating emails,
                organizing files, and providing real-time progress updates in
                each project.
              </p>
            </div>

            <div className="rounded-xl bg-boxdark-2 p-8">
              <div className="mb-4 inline-flex rounded-lg bg-primary/10 p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-file-contract"
                  className="h-6 w-6 text-primary"
                />
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                Contracts and Payment System
              </h3>
              <p className="text-bodydark">
                Ditch QuickBooks, Adobe, and other systems—our new multi-step
                form keeps finances, quotes, invoices, agreements, and payments
                all in one place.
              </p>
            </div>

            <div className="rounded-xl bg-boxdark-2 p-8">
              <div className="mb-4 inline-flex rounded-lg bg-primary/10 p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-certificate"
                  className="h-6 w-6 text-primary"
                />
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                Auto-generated Music Licenses
              </h3>
              <p className="text-bodydark">
                CheerEQ automatically generates each team's music license with
                your company logo, creating a PDF ready for clients to download.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="bg-boxdark-2 py-20">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold text-white">
            Our Services
          </h2>
          <div className="grid gap-8 md:grid-cols-3">
            <div className="rounded-xl bg-boxdark p-8 transition-all hover:bg-opacity-90">
              <div className="mb-6 inline-flex rounded-lg bg-primary p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-globe"
                  className="h-8 w-8 text-white"
                />
              </div>
              <h3 className="mb-4 text-2xl font-semibold text-white">
                Access anywhere, Anytime
              </h3>
              <p className="text-lg text-bodydark">
                Our web application keeps all your information securely stored
                on an online server, ensuring you can access your portal from
                any device with an internet connection.
              </p>
            </div>

            <div className="rounded-xl bg-boxdark p-8 transition-all hover:bg-opacity-90">
              <div className="mb-6 inline-flex rounded-lg bg-primary p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-chart-line"
                  className="h-8 w-8 text-white"
                />
              </div>
              <h3 className="mb-4 text-2xl font-semibold text-white">
                Cut costs, boost profits
              </h3>
              <p className="text-lg text-bodydark">
                Our project management software allows you to increase revenue,
                streamline operations, and improve profitability by lowering
                administrative costs.
              </p>
            </div>

            <div className="rounded-xl bg-boxdark p-8 transition-all hover:bg-opacity-90">
              <div className="mb-6 inline-flex rounded-lg bg-primary p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-headset"
                  className="h-8 w-8 text-white"
                />
              </div>
              <h3 className="mb-4 text-2xl font-semibold text-white">
                Premium customer experience
              </h3>
              <p className="text-lg text-bodydark">
                CheerEQ's dedicated support team is committed to your success,
                ensuring you're supported during the setup process and
                throughout your experience with us.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold text-white">
            Frequently Asked Questions
          </h2>
          <div className="mx-auto max-w-3xl">
            {/* FAQ 1 */}
            <div className="mb-4 overflow-hidden rounded-lg border border-stroke bg-boxdark">
              <button
                className="flex w-full items-center justify-between px-6 py-4 text-left"
                onClick={() => toggleFaq(0)}
              >
                <span className="text-lg font-medium text-white">
                  What more can you tell me about Cheer EQ?
                </span>
                <FontAwesomeIcon
                  icon={
                    openFaq === 0 ? "fa-solid fa-minus" : "fa-solid fa-plus"
                  }
                  className={`h-4 w-4 text-primary transition-transform ${
                    openFaq === 0 ? "rotate-180" : ""
                  }`}
                />
              </button>
              <div
                className={`transition-all duration-300 ease-in-out ${
                  openFaq === 0 ? "max-h-96" : "max-h-0"
                }`}
              >
                <div className="border-t border-stroke p-6">
                  <p className="text-base text-bodydark">
                    Unlock the power of automation with our all-in-one music
                    production platform designed specifically for cheerleading
                    music. From client portals to streamlined workflows, our
                    system simplifies the entire production process. Save
                    valuable time on administrative tasks and focus on what you
                    do best—producing top-tier music. With our tool, you can
                    easily manage projects, reduce overhead costs, and deliver
                    faster, more efficient results.
                  </p>
                </div>
              </div>
            </div>

            {/* FAQ 2 */}
            <div className="mb-4 overflow-hidden rounded-lg border border-stroke bg-boxdark">
              <button
                className="flex w-full items-center justify-between px-6 py-4 text-left"
                onClick={() => toggleFaq(1)}
              >
                <span className="text-lg font-medium text-white">
                  Can I get special pricing based on number of projects?
                </span>
                <FontAwesomeIcon
                  icon={
                    openFaq === 1 ? "fa-solid fa-minus" : "fa-solid fa-plus"
                  }
                  className={`h-4 w-4 text-primary transition-transform ${
                    openFaq === 1 ? "rotate-180" : ""
                  }`}
                />
              </button>
              <div
                className={`transition-all duration-300 ease-in-out ${
                  openFaq === 1 ? "max-h-96" : "max-h-0"
                }`}
              >
                <div className="border-t border-stroke p-6">
                  <p className="text-base text-bodydark">
                    Yes! We offer flexible pricing based on your project volume.
                    Check out our pricing section above to see the different
                    ranges available.
                  </p>
                </div>
              </div>
            </div>

            {/* FAQ 3 */}
            <div className="mb-4 overflow-hidden rounded-lg border border-stroke bg-boxdark">
              <button
                className="flex w-full items-center justify-between px-6 py-4 text-left"
                onClick={() => toggleFaq(2)}
              >
                <span className="text-lg font-medium text-white">
                  Can I get a demo or free trial before subscribing?
                </span>
                <FontAwesomeIcon
                  icon={
                    openFaq === 2 ? "fa-solid fa-minus" : "fa-solid fa-plus"
                  }
                  className={`h-4 w-4 text-primary transition-transform ${
                    openFaq === 2 ? "rotate-180" : ""
                  }`}
                />
              </button>
              <div
                className={`transition-all duration-300 ease-in-out ${
                  openFaq === 2 ? "max-h-96" : "max-h-0"
                }`}
              >
                <div className="border-t border-stroke p-6">
                  <p className="text-base text-bodydark">
                    Yes, we offer demos of our platform. Contact our team to
                    schedule a personalized demonstration of CheerEQ's features.
                  </p>
                </div>
              </div>
            </div>

            {/* FAQ 4 */}
            <div className="mb-4 overflow-hidden rounded-lg border border-stroke bg-boxdark">
              <button
                className="flex w-full items-center justify-between px-6 py-4 text-left"
                onClick={() => toggleFaq(3)}
              >
                <span className="text-lg font-medium text-white">
                  Does CheerEQ offer engineering services?
                </span>
                <FontAwesomeIcon
                  icon={
                    openFaq === 3 ? "fa-solid fa-minus" : "fa-solid fa-plus"
                  }
                  className={`h-4 w-4 text-primary transition-transform ${
                    openFaq === 3 ? "rotate-180" : ""
                  }`}
                />
              </button>
              <div
                className={`transition-all duration-300 ease-in-out ${
                  openFaq === 3 ? "max-h-96" : "max-h-0"
                }`}
              >
                <div className="border-t border-stroke p-6">
                  <p className="text-base text-bodydark">
                    CheerEQ is a project management platform. While we don't
                    provide direct engineering services, our platform helps
                    music producers manage their engineering workflow
                    efficiently.
                  </p>
                </div>
              </div>
            </div>

            {/* FAQ 5 */}
            <div className="mb-4 overflow-hidden rounded-lg border border-stroke bg-boxdark">
              <button
                className="flex w-full items-center justify-between px-6 py-4 text-left"
                onClick={() => toggleFaq(4)}
              >
                <span className="text-lg font-medium text-white">
                  Can I manually add my vocal production files into CheerEQ?
                </span>
                <FontAwesomeIcon
                  icon={
                    openFaq === 4 ? "fa-solid fa-minus" : "fa-solid fa-plus"
                  }
                  className={`h-4 w-4 text-primary transition-transform ${
                    openFaq === 4 ? "rotate-180" : ""
                  }`}
                />
              </button>
              <div
                className={`transition-all duration-300 ease-in-out ${
                  openFaq === 4 ? "max-h-96" : "max-h-0"
                }`}
              >
                <div className="border-t border-stroke p-6">
                  <p className="text-base text-bodydark">
                    Yes, CheerEQ allows you to manually upload and manage your
                    vocal production files within the platform while maintaining
                    organized project structures.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscription Plans */}
      <section id="subscriptions" className="bg-boxdark-2 py-20">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold text-white">
            Choose Your Plan
          </h2>
          <div className="mb-8 flex w-full items-center justify-center gap-4">
            <span className="whitespace-nowrap font-medium text-white">
              Show pricing for
            </span>
            <div className="w-[200px]">
              <CustomSelect2
                label="Show pricing for"
                value={selectedProjectRange}
                defaultValue={selectedProjectRange}
                onChange={(value) => setSelectedProjectRange(value)}
                className="whitespace-nowrap rounded-lg border border-stroke bg-transparent px-4 py-2 text-white outline-none focus:border-primary"
              >
                <option value="1-50">1-50 Mix Projects</option>
                <option value="51-100">51-100 Mix Projects</option>
                <option value="101-150">101-150 Mix Projects</option>
                <option value="151-200">151-200 Mix Projects</option>
                <option value="201+">201+ Mix Projects</option>
              </CustomSelect2>
            </div>
          </div>
          <div className="grid gap-8 md:grid-cols-3">
            <div className="rounded-xl bg-boxdark p-8">
              <h3 className="mb-4 text-xl font-semibold text-white">
                The Portal
              </h3>
              <p className="mb-6 text-4xl font-bold text-primary">
                ${pricingRanges[selectedProjectRange].portal.monthly}
                <span className="text-lg text-bodydark">/mo</span>
              </p>
              <p className="mb-4 text-sm text-white">
                ${pricingRanges[selectedProjectRange].portal.annual} billed
                annually
              </p>
              <ul className="mb-8 space-y-4 text-bodydark">
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Project Management
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Project Calendar
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Client Login Portal
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Digital 8-count sheets
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Automated Music Licenses
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Automated Reminder Emails
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Automated Music Surveys
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Project Edit Management
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  8-Count Track Management
                </li>
                <li className="flex items-center gap-2">
                  <FontAwesomeIcon
                    icon="fa-solid fa-check"
                    className="text-primary"
                  />
                  Custom Email Domain
                </li>
              </ul>
              <button className="w-full rounded-full border border-primary bg-transparent px-8 py-3 font-semibold text-primary transition-all hover:bg-primary/80 hover:text-white">
                Get Started
              </button>
            </div>

            <div className="flex flex-col justify-between rounded-xl bg-boxdark p-8">
              <div>
                <h3 className="mb-4 text-xl font-semibold text-white">
                  The Studio
                </h3>
                <p className="mb-6 text-4xl font-bold text-primary">
                  ${pricingRanges[selectedProjectRange].studio.monthly}
                  <span className="text-lg text-bodydark">/mo</span>
                </p>
                <p className="mb-4 text-sm text-white">
                  ${pricingRanges[selectedProjectRange].studio.annual} billed
                  annually
                </p>
                <ul className="mb-8 space-y-4 text-bodydark">
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Automated Music Surveys
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Project Management
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Project Calendar
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Project Budget Review
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Automated Vocal Orders
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Excel Style Order View
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Automated Reminder Emails
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Company Logo Customization
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Custom Email Domain
                  </li>
                </ul>
              </div>
              <button className="w-full rounded-full bg-primary px-8 py-3 font-semibold text-white transition-all hover:bg-opacity-90">
                Get Started
              </button>
            </div>

            <div className="flex flex-col justify-between rounded-xl bg-boxdark p-8">
              <div>
                <h3 className="mb-4 text-xl font-semibold text-white">
                  Complete Suite
                </h3>

                <p className="mb-6 text-4xl font-bold text-primary">
                  ${pricingRanges[selectedProjectRange].complete.monthly}
                  <span className="text-lg text-bodydark">/mo</span>
                </p>
                <p className="mb-4 text-sm text-white">
                  ${pricingRanges[selectedProjectRange].complete.annual} billed
                  annually
                </p>
                <ul className="mb-8 space-y-4 text-bodydark">
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Everything in The Portal
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Everything in The Studio
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Priority Support
                  </li>
                  <li className="flex items-center gap-2">
                    <FontAwesomeIcon
                      icon="fa-solid fa-check"
                      className="text-primary"
                    />
                    Dedicated Account Manager
                  </li>
                </ul>
              </div>
              <button className="w-full rounded-full border border-primary bg-transparent px-8 py-3 font-semibold text-primary transition-all hover:bg-primary/80 hover:text-white">
                Get Started
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold text-white">
            Get In Touch
          </h2>
          <div className="mx-auto max-w-3xl">
            <div className="rounded-xl bg-boxdark-2 p-8">
              <form className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <label
                      htmlFor="firstName"
                      className="mb-2.5 block font-medium text-white"
                    >
                      First Name
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      placeholder="John"
                      className="w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 text-white outline-none focus:border-primary"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="lastName"
                      className="mb-2.5 block font-medium text-white"
                    >
                      Last Name
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      placeholder="Doe"
                      className="w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 text-white outline-none focus:border-primary"
                    />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="email"
                    className="mb-2.5 block font-medium text-white"
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    placeholder="Enter your email"
                    className="w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 text-white outline-none focus:border-primary"
                  />
                </div>

                <div>
                  <label
                    htmlFor="subject"
                    className="mb-2.5 block font-medium text-white"
                  >
                    Subject
                  </label>
                  <input
                    type="text"
                    id="subject"
                    placeholder="How can we help?"
                    className="w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 text-white outline-none focus:border-primary"
                  />
                </div>

                <div>
                  <label
                    htmlFor="message"
                    className="mb-2.5 block font-medium text-white"
                  >
                    Message
                  </label>
                  <textarea
                    id="message"
                    rows="6"
                    placeholder="Type your message..."
                    className="w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 text-white outline-none focus:border-primary"
                  ></textarea>
                </div>

                <div className="flex justify-center">
                  <button
                    type="submit"
                    className="rounded-full bg-primary px-12 py-3 text-lg font-medium text-white transition-all hover:bg-opacity-90"
                  >
                    Send Message
                  </button>
                </div>
              </form>
            </div>

            <div className="mt-16 grid gap-8 md:grid-cols-3">
              <div className="text-center">
                <div className="mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <FontAwesomeIcon
                    icon="fa-solid fa-phone"
                    className="h-5 w-5 text-white"
                  />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-white">Phone</h3>
                <p className="text-bodydark">************</p>
              </div>

              <div className="text-center">
                <div className="mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <FontAwesomeIcon
                    icon="fa-solid fa-envelope"
                    className="h-5 w-5 text-white"
                  />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-white">Email</h3>
                <p className="text-bodydark">
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </p>
              </div>

              <div className="text-center">
                <div className="mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <FontAwesomeIcon
                    icon="fa-solid fa-location-dot"
                    className="h-5 w-5 text-white"
                  />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-white">
                  Location
                </h3>
                <p className="text-bodydark">Austin, TX</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black py-12">
        <div className="container mx-auto px-4">
          <div className="grid gap-8 md:grid-cols-4">
            <div>
              <div className="-mt-5 flex items-center">
                <img
                  src={`${window.location.origin}/new/cheerEQ-1-Ed1.png`}
                  alt="Logo"
                  className="h-[80px] w-[180px] object-contain"
                />
              </div>
              <p className="text-bodydark">
                Transform your music production workflow with our cutting-edge
                project management system.
              </p>
            </div>
            <div>
              <h4 className="mb-4 font-semibold text-white">Quick Links</h4>
              <ul className="space-y-2 text-bodydark">
                <li>
                  <a href="#home" className="hover:text-primary">
                    Home
                  </a>
                </li>
                <li>
                  <a href="#features" className="hover:text-primary">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#subscriptions" className="hover:text-primary">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="#contact" className="hover:text-primary">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="mb-4 font-semibold text-white">Legal</h4>
              <ul className="space-y-2 text-bodydark">
                <li>
                  <a href="#" className="hover:text-primary">
                    Terms of Service
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary">
                    Disclaimer
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="mb-4 font-semibold text-white">Contact</h4>
              <ul className="space-y-2 text-bodydark">
                <li>Email: <EMAIL></li>
                <li>Phone: ************</li>
                <li>Address: Austin, TX</li>
              </ul>
            </div>
          </div>
          <div className="mt-12 border-t border-strokedark pt-8 text-center text-bodydark">
            <p>© {new Date().getFullYear()} MYEQ. All rights reserved.</p>
          </div>
        </div>
      </footer>

      <style>
        {`
          @keyframes moveAround {
            0% { transform: translate(0, 0); }
            25% { transform: translate(calc(100% - 12px), 0); }
            50% { transform: translate(calc(100% - 12px), calc(100% - 16px)); }
            75% { transform: translate(0, calc(100% - 16px)); }
            100% { transform: translate(0, 0); }
          }

          html {
            scroll-behavior: smooth;
          }
        `}
      </style>
    </div>
  );
};

export default HomePage;
