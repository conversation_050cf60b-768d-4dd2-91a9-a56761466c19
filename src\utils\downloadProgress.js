export function createDownloadProgressBox() {
  console.log("Creating progress box");
  const progressDiv = document.createElement("div");
  progressDiv.style.position = "fixed";
  progressDiv.style.top = "50%";
  progressDiv.style.left = "50%";
  progressDiv.style.transform = "translate(-50%, -50%)";
  progressDiv.style.background = "rgba(0, 0, 0, 0.8)";
  progressDiv.style.padding = "15px";
  progressDiv.style.borderRadius = "8px";
  progressDiv.style.color = "white";
  progressDiv.style.zIndex = "1000";
  progressDiv.style.minWidth = "250px";
  progressDiv.style.maxHeight = "250px";
  progressDiv.style.overflowY = "auto";
  progressDiv.style.cursor = "move";
  progressDiv.style.userSelect = "none";
  progressDiv.style.fontSize = "12px";
  progressDiv.classList.add("custom-overflow");

  // Add handle for dragging
  const handleDiv = document.createElement("div");
  handleDiv.style.position = "sticky";
  handleDiv.style.top = "0";
  handleDiv.style.left = "0";
  handleDiv.style.right = "0";
  handleDiv.style.height = "20px";
  handleDiv.style.cursor = "move";
  handleDiv.style.background = "rgba(0, 0, 0, 0.5)";
  handleDiv.style.borderTopLeftRadius = "8px";
  handleDiv.style.borderTopRightRadius = "8px";
  handleDiv.style.marginBottom = "5px";

  // Add close button
  const closeButton = document.createElement("button");
  closeButton.innerHTML = "✕";
  closeButton.style.position = "absolute";
  closeButton.style.right = "8px";
  closeButton.style.top = "2px";
  closeButton.style.background = "none";
  closeButton.style.border = "none";
  closeButton.style.color = "white";
  closeButton.style.cursor = "pointer";
  closeButton.style.fontSize = "14px";

  // Content div for downloads
  const contentDiv = document.createElement("div");
  contentDiv.style.marginTop = "5px";

  progressDiv.appendChild(handleDiv);
  handleDiv.appendChild(closeButton);
  progressDiv.appendChild(contentDiv);
  document.body.appendChild(progressDiv);

  // Dragging functionality
  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;
  let xOffset = 0;
  let yOffset = 0;

  const dragStart = (e) => {
    if (e.type === "touchstart") {
      initialX = e.touches[0].clientX - xOffset;
      initialY = e.touches[0].clientY - yOffset;
    } else {
      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;
    }
    if (e.target === handleDiv) isDragging = true;
  };

  const dragEnd = () => {
    isDragging = false;
  };

  const drag = (e) => {
    if (isDragging) {
      e.preventDefault();
      if (e.type === "touchmove") {
        currentX = e.touches[0].clientX - initialX;
        currentY = e.touches[0].clientY - initialY;
      } else {
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;
      }
      xOffset = currentX;
      yOffset = currentY;
      progressDiv.style.transform = `translate(${currentX}px, ${currentY}px)`;
    }
  };

  handleDiv.addEventListener("mousedown", dragStart);
  document.addEventListener("mousemove", drag);
  document.addEventListener("mouseup", dragEnd);
  handleDiv.addEventListener("touchstart", dragStart);
  document.addEventListener("touchmove", drag);
  document.addEventListener("touchend", dragEnd);

  closeButton.onclick = () => {
    console.log("Hiding progress box");
    progressDiv.style.display = "none";
  };

  return {
    updateDownloads: (downloads) => {
      console.log("Updating downloads:", downloads);
      // Group downloads by type
      const videoDownloads = Array.from(downloads.values()).filter(
        (d) => d.type === "video"
      );
      const musicDownloads = Array.from(downloads.values()).filter(
        (d) => d.type === "music"
      );
      const trackDownloads = Array.from(downloads.values()).filter(
        (d) => d.type === "track"
      );
      const masterDownloads = Array.from(downloads.values()).filter(
        (d) => d.type === "master"
      );
      const projectDownloads = Array.from(downloads.values()).filter(
        (d) => d.type === "project"
      );

      contentDiv.innerHTML = `
        ${
          videoDownloads.length
            ? `
          <div style="margin-bottom: 10px;">
            <div style="font-size: 11px; font-weight: bold; padding-bottom: 3px; border-bottom: 1px solid rgba(255,255,255,0.3); margin-bottom: 5px;">
              Videos
            </div>
            ${videoDownloads
              .map((download) => createDownloadItem(download))
              .join("")}
          </div>
        `
            : ""
        }
        
        ${
          musicDownloads.length
            ? `
          <div style="margin-bottom: 10px;">
            <div style="font-size: 11px; font-weight: bold; padding-bottom: 3px; border-bottom: 1px solid rgba(255,255,255,0.3); margin-bottom: 5px;">
              Music
            </div>
            ${musicDownloads
              .map((download) => createDownloadItem(download))
              .join("")}
          </div>
        `
            : ""
        }

        ${
          trackDownloads.length
            ? `
          <div style="margin-bottom: 10px;">
            <div style="font-size: 11px; font-weight: bold; padding-bottom: 3px; border-bottom: 1px solid rgba(255,255,255,0.3); margin-bottom: 5px;">
              Tracks
            </div>
            ${trackDownloads
              .map((download) => createDownloadItem(download))
              .join("")}
          </div>
        `
            : ""
        }

        ${
          masterDownloads.length
            ? `
          <div style="margin-bottom: 10px;">
            <div style="font-size: 11px; font-weight: bold; padding-bottom: 3px; border-bottom: 1px solid rgba(255,255,255,0.3); margin-bottom: 5px;">
              Masters
            </div>
            ${masterDownloads
              .map((download) => createDownloadItem(download))
              .join("")}
          </div>
        `
            : ""
        }

        ${
          projectDownloads.length
            ? `
          <div style="margin-bottom: 10px;">
            <div style="font-size: 11px; font-weight: bold; padding-bottom: 3px; border-bottom: 1px solid rgba(255,255,255,0.3); margin-bottom: 5px;">
              Projects
            </div>
            ${projectDownloads
              .map((download) => createDownloadItem(download))
              .join("")}
          </div>
        `
            : ""
        }
      `;
    },
    remove: () => {
      progressDiv.remove();
      window.downloadManager.progressBox = null;
    },
    hide: () => {
      console.log("Hiding progress box");
      progressDiv.style.display = "none";
    },
    show: () => {
      console.log("Showing progress box");
      if (!document.body.contains(progressDiv)) {
        console.log("Re-adding to document");
        document.body.appendChild(progressDiv);
      }
      progressDiv.style.display = "block";
      this.updateDownloads(window.downloadManager.downloads);
    },
  };
}

function createDownloadItem(download) {
  return `
    <div style="margin-bottom: 8px; padding-bottom: 5px;">
      <div style="margin-bottom: 3px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
        ${download.fileName}
      </div>
      <div style="display: flex; align-items: center; gap: 5px;">
        <div style="flex-grow: 1; background: rgba(255,255,255,0.1); height: 6px; border-radius: 3px;">
          <div style="width: ${download.progress}%; height: 100%; background: ${
    download.status === "failed"
      ? "#ff4444"
      : download.status === "complete"
      ? "#00C851"
      : "#2196F3"
  }; border-radius: 3px;"></div>
        </div>
        <div style="min-width: 45px; font-size: 11px;">${
          download.status === "failed"
            ? "Failed"
            : download.status === "complete"
            ? "Done"
            : `${download.progress}%`
        }</div>
      </div>
    </div>
  `;
}
