import { LazyLoad } from "Components/LazyLoad";
import { MkdPopover } from "Components/MkdPopover";
import React, { memo } from "react";
import { MdPhoto } from "react-icons/md";

const ImageCell = memo(({ src, onPopoverStateChange }) => (
  <LazyLoad>
    <MkdPopover
      display={<MdPhoto className="peer h-8 w-8" />}
      openOnClick={false}
      zIndex={999999999999999}
      onPopoverStateChange={onPopoverStateChange}
      place="left-start"
      tooltipClasses={`whitespace-nowrap h-fit min-h-[1rem] max-h-fit w-[18.75rem] !rounded-lg border border-[#a8a8a8] !bg-white p-2 text-sm text-[#525252] shadow-md`}
    >
      <LazyLoad
        className={`h-[18.75rem] w-[18.75rem] whitespace-nowrap !rounded-lg border border-[#a8a8a8] !bg-white p-2 text-sm text-[#525252] shadow-md`}
      >
        <img src={src} className="w-[18.75rem]" alt="" />
      </LazyLoad>
    </MkdPopover>
  </LazyLoad>
));

export default ImageCell;
