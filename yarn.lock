# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.25.9", "@babel/code-frame@^7.26.0", "@babel/code-frame@^7.26.2":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz"
  integrity sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.25.9", "@babel/compat-data@^7.26.0":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.3.tgz"
  integrity sha512-nHIxvKPniQXpmQLb0vhY3VaFb3S0YrTAwpOWJZh1wn3oJPjJk9Asva204PsBdmAE8vpzfHudT8DB0scYvy9q0g==

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.0.0-0 || ^8.0.0-0 <8.0.0", "@babel/core@^7.11.0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.16.0", "@babel/core@^7.26.0", "@babel/core@^7.4.0 || ^8.0.0-0 <8.0.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.26.0.tgz"
  integrity sha512-i1SLeK+DzNnQ3LL/CswPCa/E5u4lh1k6IAEphON8F+cXt0t9euTshDru0q7/IqMa1PMPz5RnHuHscF8/ZJsStg==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.0"
    "@babel/generator" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.0"
    "@babel/parser" "^7.26.0"
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.26.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/eslint-parser@^7.16.3":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.25.9.tgz"
  integrity sha512-5UXfgpK0j0Xr/xIdgdLEhOFxaDZ0bRPWJJchRpqOSur/3rZoPbqqki5mm0p4NE2cs28krBEiSM2MB7//afRSQQ==
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.1"

"@babel/generator@^7.26.0", "@babel/generator@^7.26.3":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.26.3.tgz"
  integrity sha512-6FF/urZvD0sTeO7k6/B15pMLC4CHUv1426lzr3N01aHJTl046uCAh9LXW/fzeXXjPNCJ6iABW5XaWOsIZB93aQ==
  dependencies:
    "@babel/parser" "^7.26.3"
    "@babel/types" "^7.26.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz"
  integrity sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.9.tgz"
  integrity sha512-j9Db8Suy6yV/VHa4qzrj9yZfZxhLWQdVnRlXxmKLYlhWUVB1sB2G5sxuWYXk/whHD9iW76PmNzxZ4UCnTQTVEQ==
  dependencies:
    "@babel/compat-data" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.9.tgz"
  integrity sha512-UTZQMvt0d/rSz6KI+qdu7GQze5TIajwTS++GUozlw8VBJDEOAqSXwm1WvmYEZwqdqSGQshRocPDqrt4HBZB3fQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.25.9":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.26.3.tgz"
  integrity sha512-G7ZRb40uUgdKOQqPLjfD12ZmGA54PzqDFUv2BKImnC9QIfGhIHKvVML0oN8IUiDq4iRqpq74ABpvOaerfWdong==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.2", "@babel/helper-define-polyfill-provider@^0.6.3":
  version "0.6.3"
  resolved "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.3.tgz"
  integrity sha512-HK7Bi+Hj6H+VTHA3ZvBis7V/6hu9QuTrnMXNybfUf2iiuU/N97I8VjB+KbhFF8Rld/Lx5MzoCwPCpPjfK+n8Cg==
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-member-expression-to-functions@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.25.9.tgz"
  integrity sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz"
  integrity sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.25.9", "@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz"
  integrity sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-optimise-call-expression@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.9.tgz"
  integrity sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.8.0":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.25.9.tgz"
  integrity sha512-kSMlyUVdWe25rEsRGviIgOWnoT/nfABVWlqt9N19/dIPWViAOW2s9wznP5tURbs/IDuNk4gPy3YdYRgH3uxhBw==

"@babel/helper-remap-async-to-generator@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.25.9.tgz"
  integrity sha512-IZtukuUeBbhgOcaW2s06OXTzVNJR0ybm4W5xC1opWFFJMZbwRj5LCk+ByYH7WdZPZTt8KnFwA8pvjN2yqcPlgw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-wrap-function" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-replace-supers@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.25.9.tgz"
  integrity sha512-IiDqTOTBQy0sWyeXyGSC5TBJpGFXBkRynjBeXsvbhQFKj2viwJC76Epz35YLU1fpe/Am6Vppb7W7zM4fPQzLsQ==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0", "@babel/helper-skip-transparent-expression-wrappers@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz"
  integrity sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz"
  integrity sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz"
  integrity sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz"
  integrity sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==

"@babel/helper-wrap-function@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.25.9.tgz"
  integrity sha512-ETzz9UTjQSTmw39GboatdymDq4XIQbR8ySgVrylRhPOFpsd+JrKHIuF0de7GCWmem+T4uC5z7EZguod7Wj4A4g==
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helpers@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.26.0.tgz"
  integrity sha512-tbhNuIxNcVb21pInl3ZSjksLCvgdZy9KwJ8brv993QtIVKJBBkYXz4q4ZbAv31GdnC+R90np23L5FbEBlthAEw==
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.25.9", "@babel/parser@^7.26.0", "@babel/parser@^7.26.3":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.26.3.tgz"
  integrity sha512-WJ/CvmY8Mea8iDXo6a7RK2wbmJITT5fN3BEkRuFlxVyNx8jOKIIhmC4fSkTcPcf8JyavbBwIe6OpiCOBXt/IcA==
  dependencies:
    "@babel/types" "^7.26.3"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.25.9.tgz"
  integrity sha512-ZkRyVkThtxQ/J6nv3JFYv1RYY+JT5BvU0y3k5bWrmuG4woXypRa4PXmm9RhOwodRkYFWqC0C0cqcJ4OqR7kW+g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.25.9.tgz"
  integrity sha512-MrGRLZxLD/Zjj0gdU15dfs+HH/OXvnw/U4jJD8vpcP2CJQapPEv1IWwjc/qMg7ItBlPwSv1hRBbb7LeuANdcnw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.9.tgz"
  integrity sha512-2qUwwfAFpJLZqxd02YW9btUCZHl+RFvdDkNfZwaIJrvB8Tesjsk8pEQkTvGwZXLqXUx/2oyY3ySRhm6HOXuCug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.25.9.tgz"
  integrity sha512-6xWgLZTJXwilVjlnV7ospI3xi+sl8lN8rXXbBD6vYn3UYDlGsag8wrZkKcSI8G6KgqKP7vNFaDgeDnfAABq61g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.25.9.tgz"
  integrity sha512-aLnMXYPnzwwqhYSCyXfKkIkYgJ8zv9RK+roo9DkTXz38ynIhd9XCbN08s3MGvqL2MYGVUGdRQLL/JqBIeJhJBg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-proposal-class-properties@^7.16.0":
  version "7.18.6"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
  integrity sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.16.4":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.25.9.tgz"
  integrity sha512-smkNLL/O1ezy9Nhy4CNosc4Va+1wo5w4gzSZeLe6y6dM4mmHfYOCPolXQPHQxonZCF+ZyebxN9vqOolkYrSn5g==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-decorators" "^7.25.9"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0":
  version "7.18.6"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  integrity sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.0":
  version "7.18.6"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz"
  integrity sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-optional-chaining@^7.16.0":
  version "7.21.0"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz"
  integrity sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.0":
  version "7.18.6"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz"
  integrity sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  integrity sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==

"@babel/plugin-syntax-decorators@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.25.9.tgz"
  integrity sha512-ryzI0McXUPJnRCvMo4lumIKZUzhYUO/ScI+Mz4YVaTLt04DHNSjEUjKVvbzQjZFLuod/cYEc07mJWhzl6v4DPg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-flow@^7.14.5", "@babel/plugin-syntax-flow@^7.25.9":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.26.0.tgz"
  integrity sha512-B+O2DnPc0iG+YXFqOxv2WNuNU97ToWjOomUQ78DouOENWUaM5sVrmet9mcomUGQFwpJd//gvUagXBSdzO1fRKg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-assertions@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.26.0.tgz"
  integrity sha512-QCWT5Hh830hK5EQa7XzuqIkQU9tT/whqbDz7kuaZMHFl1inRRg7JnuAEOQ0Ur0QUl0NufCk1msK2BeY79Aj/eg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-attributes@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.26.0.tgz"
  integrity sha512-e2dttdsJ1ZTpi3B9UYGLw41hifAubg19AtCu/2I/F1QNVclOBr1dYpTdmdyZ84Xiz43BS/tCUkMAZNLv12Pi+A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-jsx@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.25.9.tgz"
  integrity sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-typescript@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz"
  integrity sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  integrity sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.25.9.tgz"
  integrity sha512-6jmooXYIwn9ca5/RylZADJ+EnSxVUS5sjeJ9UPk6RWRzXCmOJCy6dqItPJFpw2cuCangPK4OYr5uhGKcmrm5Qg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-async-generator-functions@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.25.9.tgz"
  integrity sha512-RXV6QAzTBbhDMO9fWwOmwwTuYaiPbggWQ9INdZqAYeSHyG7FzQ+nOZaUUjNwKv9pV3aE4WFqFm1Hnbci5tBCAw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-async-to-generator@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.25.9.tgz"
  integrity sha512-NT7Ejn7Z/LjUH0Gv5KsBCxh7BH3fbLTV0ptHvpeMvrt3cPThHfJfst9Wrb7S8EvJ7vRTFI7z+VAvFVEQn/m5zQ==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"

"@babel/plugin-transform-block-scoped-functions@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.25.9.tgz"
  integrity sha512-toHc9fzab0ZfenFpsyYinOX0J/5dgJVA2fm64xPewu7CoYHWEivIWKxkK2rMi4r3yQqLnVmheMXRdG+k239CgA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-block-scoping@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.25.9.tgz"
  integrity sha512-1F05O7AYjymAtqbsFETboN1NvBdcnzMerO+zlMyJBEz6WkMdejvGWw9p05iTSjC85RLlBseHHQpYaM4gzJkBGg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-properties@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.25.9.tgz"
  integrity sha512-bbMAII8GRSkcd0h0b4X+36GksxuheLFjP65ul9w6C3KgAamI3JqErNgSrosX6ZPj+Mpim5VvEbawXxJCyEUV3Q==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-static-block@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.26.0.tgz"
  integrity sha512-6J2APTs7BDDm+UMqP1useWqhcRAXo0WIoVj26N7kPFB6S73Lgvyka4KTZYIxtgYXiN5HTyRObA72N2iu628iTQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-classes@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.25.9.tgz"
  integrity sha512-mD8APIXmseE7oZvZgGABDyM34GUmK45Um2TXiBUt7PnuAxrgoSVf123qUzPxEr/+/BHrRn5NMZCdE2m/1F8DGg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.25.9.tgz"
  integrity sha512-HnBegGqXZR12xbcTHlJ9HGxw1OniltT26J5YpfruGqtUHlz/xKf/G2ak9e+t0rVqrjXa9WOhvYPz1ERfMj23AA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/template" "^7.25.9"

"@babel/plugin-transform-destructuring@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.25.9.tgz"
  integrity sha512-WkCGb/3ZxXepmMiX101nnGiU+1CAdut8oHyEOHxkKuS1qKpU2SMXE2uSvfz8PBuLd49V6LEsbtyPhWC7fnkgvQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dotall-regex@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.25.9.tgz"
  integrity sha512-t7ZQ7g5trIgSRYhI9pIJtRl64KHotutUJsh4Eze5l7olJv+mRSg4/MmbZ0tv1eeqRbdvo/+trvJD/Oc5DmW2cA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-keys@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.25.9.tgz"
  integrity sha512-LZxhJ6dvBb/f3x8xwWIuyiAHy56nrRG3PeYTpBkkzkYRRQ6tJLu68lEF5VIqMUZiAV7a8+Tb78nEoMCMcqjXBw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.25.9.tgz"
  integrity sha512-0UfuJS0EsXbRvKnwcLjFtJy/Sxc5J5jhLHnFhy7u4zih97Hz6tJkLU+O+FMMrNZrosUPxDi6sYxJ/EA8jDiAog==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dynamic-import@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.25.9.tgz"
  integrity sha512-GCggjexbmSLaFhqsojeugBpeaRIgWNTcgKVq/0qIteFEqY2A+b9QidYadrWlnbWQUrW5fn+mCvf3tr7OeBFTyg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-exponentiation-operator@^7.25.9":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.26.3.tgz"
  integrity sha512-7CAHcQ58z2chuXPWblnn1K6rLDnDWieghSOEmqQsrBenH0P9InCUtOJYD89pvngljmZlJcz3fcmgYsXFNGa1ZQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-export-namespace-from@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.25.9.tgz"
  integrity sha512-2NsEz+CxzJIVOPx2o9UsW1rXLqtChtLoVnwYHHiB04wS5sgn7mrV45fWMBX0Kk+ub9uXytVYfNP2HjbVbCB3Ww==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-flow-strip-types@^7.16.0":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.25.9.tgz"
  integrity sha512-/VVukELzPDdci7UUsWQaSkhgnjIWXnIyRpM02ldxaVoFK96c41So8JcKT3m0gYjyv7j5FNPGS5vfELrWalkbDA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-flow" "^7.25.9"

"@babel/plugin-transform-for-of@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.25.9.tgz"
  integrity sha512-LqHxduHoaGELJl2uhImHwRQudhCM50pT46rIBNvtT/Oql3nqiS3wOwP+5ten7NpYSXrrVLgtZU3DZmPtWZo16A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-function-name@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.25.9.tgz"
  integrity sha512-8lP+Yxjv14Vc5MuWBpJsoUCd3hD6V9DgBon2FVYL4jJgbnVQ9fTgYmonchzZJOVNgzEgbxp4OwAf6xz6M/14XA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-json-strings@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.25.9.tgz"
  integrity sha512-xoTMk0WXceiiIvsaquQQUaLLXSW1KJ159KP87VilruQm0LNNGxWzahxSS6T6i4Zg3ezp4vA4zuwiNUR53qmQAw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-literals@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.25.9.tgz"
  integrity sha512-9N7+2lFziW8W9pBl2TzaNht3+pgMIRP74zizeCSrtnSKVdUl8mAjjOP2OOVQAfZ881P2cNjDj1uAMEdeD50nuQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-logical-assignment-operators@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.25.9.tgz"
  integrity sha512-wI4wRAzGko551Y8eVf6iOY9EouIDTtPb0ByZx+ktDGHwv6bHFimrgJM/2T021txPZ2s4c7bqvHbd+vXG6K948Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-member-expression-literals@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.25.9.tgz"
  integrity sha512-PYazBVfofCQkkMzh2P6IdIUaCEWni3iYEerAsRWuVd8+jlM1S9S9cz1dF9hIzyoZ8IA3+OwVYIp9v9e+GbgZhA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-amd@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.25.9.tgz"
  integrity sha512-g5T11tnI36jVClQlMlt4qKDLlWnG5pP9CSM4GhdRciTNMRgkfpo5cR6b4rGIOYPgRRuFAvwjPQ/Yk+ql4dyhbw==
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-commonjs@^7.25.9":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.26.3.tgz"
  integrity sha512-MgR55l4q9KddUDITEzEFYn5ZsGDXMSsU9E+kh7fjRXTIC3RHqfCo8RPRbyReYJh44HQ/yomFkqbOFohXvDCiIQ==
  dependencies:
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-systemjs@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.25.9.tgz"
  integrity sha512-hyss7iIlH/zLHaehT+xwiymtPOpsiwIIRlCAOwBB04ta5Tt+lNItADdlXw3jAWZ96VJ2jlhl/c+PNIQPKNfvcA==
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-modules-umd@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.25.9.tgz"
  integrity sha512-bS9MVObUgE7ww36HEfwe6g9WakQ0KF07mQF74uuXdkoziUPfKyu/nIm663kz//e5O1nPInPFx36z7WJmJ4yNEw==
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.25.9.tgz"
  integrity sha512-oqB6WHdKTGl3q/ItQhpLSnWWOpjUJLsOCLVyeFgeTktkBSCiurvPOsyt93gibI9CmuKvTUEtWmG5VhZD+5T/KA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-new-target@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.25.9.tgz"
  integrity sha512-U/3p8X1yCSoKyUj2eOBIx3FOn6pElFOKvAAGf8HTtItuPyB+ZeOqfn+mvTtg9ZlOAjsPdK3ayQEjqHjU/yLeVQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-nullish-coalescing-operator@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.25.9.tgz"
  integrity sha512-ENfftpLZw5EItALAD4WsY/KUWvhUlZndm5GC7G3evUsVeSJB6p0pBeLQUnRnBCBx7zV0RKQjR9kCuwrsIrjWog==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-numeric-separator@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.25.9.tgz"
  integrity sha512-TlprrJ1GBZ3r6s96Yq8gEQv82s8/5HnCVHtEJScUj90thHQbwe+E5MLhi2bbNHBEJuzrvltXSru+BUxHDoog7Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-object-rest-spread@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.25.9.tgz"
  integrity sha512-fSaXafEE9CVHPweLYw4J0emp1t8zYTXyzN3UuG+lylqkvYd7RMrsOQ8TYx5RF231be0vqtFC6jnx3UmpJmKBYg==
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"

"@babel/plugin-transform-object-super@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.25.9.tgz"
  integrity sha512-Kj/Gh+Rw2RNLbCK1VAWj2U48yxxqL2x0k10nPtSdRa0O2xnHXalD0s+o1A6a0W43gJ00ANo38jxkQreckOzv5A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"

"@babel/plugin-transform-optional-catch-binding@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.25.9.tgz"
  integrity sha512-qM/6m6hQZzDcZF3onzIhZeDHDO43bkNNlOX0i8n3lR6zLbu0GN2d8qfM/IERJZYauhAHSLHy39NF0Ctdvcid7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-optional-chaining@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.25.9.tgz"
  integrity sha512-6AvV0FsLULbpnXeBjrY4dmWF8F7gf8QnvTEoO/wX/5xm/xE1Xo8oPuD3MPS+KS9f9XBEAWN7X1aWr4z9HdOr7A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-parameters@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.25.9.tgz"
  integrity sha512-wzz6MKwpnshBAiRmn4jR8LYz/g8Ksg0o80XmwZDlordjwEk9SxBzTWC7F5ef1jhbrbOW2DJ5J6ayRukrJmnr0g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-methods@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.25.9.tgz"
  integrity sha512-D/JUozNpQLAPUVusvqMxyvjzllRaF8/nSrP1s2YGQT/W4LHK4xxsMcHjhOGTS01mp9Hda8nswb+FblLdJornQw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-property-in-object@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.25.9.tgz"
  integrity sha512-Evf3kcMqzXA3xfYJmZ9Pg1OvKdtqsDMSWBDzZOPLvHiTt36E75jLDQo5w1gtRU95Q4E5PDttrTf25Fw8d/uWLw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-property-literals@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.25.9.tgz"
  integrity sha512-IvIUeV5KrS/VPavfSM/Iu+RE6llrHrYIKY1yfCzyO/lMXHQ+p7uGhonmGVisv6tSBSVgWzMBohTcvkC9vQcQFA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-display-name@^7.16.0", "@babel/plugin-transform-react-display-name@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.25.9.tgz"
  integrity sha512-KJfMlYIUxQB1CJfO3e0+h0ZHWOTLCPP115Awhaz8U0Zpq36Gl/cXlpoyMRnUWlhNUBAzldnCiAZNvCDj7CrKxQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx-development@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.25.9.tgz"
  integrity sha512-9mj6rm7XVYs4mdLIpbZnHOYdpW42uoiBCTVowg7sP1thUOiANgMb4UtpRivR0pp5iL+ocvUv7X4mZgFRpJEzGw==
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.25.9"

"@babel/plugin-transform-react-jsx-self@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.9.tgz"
  integrity sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx-source@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.9.tgz"
  integrity sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx@^7.14.9", "@babel/plugin-transform-react-jsx@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.25.9.tgz"
  integrity sha512-s5XwpQYCqGerXl+Pu6VDL3x0j2d82eiV77UJ8a2mDHAW7j9SWRqQ2y1fNo1Z74CdcYipl5Z41zvjj4Nfzq36rw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/plugin-transform-react-pure-annotations@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.25.9.tgz"
  integrity sha512-KQ/Takk3T8Qzj5TppkS1be588lkbTp5uj7w6a0LeQaTMSckU/wK0oJ/pih+T690tkgI5jfmg2TqDJvd41Sj1Cg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-regenerator@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.25.9.tgz"
  integrity sha512-vwDcDNsgMPDGP0nMqzahDWE5/MLcX8sv96+wfX7as7LoF/kr97Bo/7fI00lXY4wUXYfVmwIIyG80fGZ1uvt2qg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-regexp-modifiers@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.26.0.tgz"
  integrity sha512-vN6saax7lrA2yA/Pak3sCxuD6F5InBjn9IcrIKQPjpsLvuHYLVroTxjdlVRHjjBWxKOqIwpTXDkOssYT4BFdRw==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-reserved-words@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.25.9.tgz"
  integrity sha512-7DL7DKYjn5Su++4RXu8puKZm2XBPHyjWLUidaPEkCUBbE7IPcsrkRHggAOOKydH1dASWdcUBxrkOGNxUv5P3Jg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-runtime@^7.16.4":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.25.9.tgz"
  integrity sha512-nZp7GlEl+yULJrClz0SwHPqir3lc0zsPrDHQUcxGspSL7AKrexNSEfTbfqnDNJUO13bgKyfuOLMF8Xqtu8j3YQ==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.25.9.tgz"
  integrity sha512-MUv6t0FhO5qHnS/W8XCbHmiRWOphNufpE1IVxhK5kuN3Td9FT1x4rx4K42s3RYdMXCXpfWkGSbCSd0Z64xA7Ng==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-spread@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.25.9.tgz"
  integrity sha512-oNknIB0TbURU5pqJFVbOOFspVlrpVwo2H1+HUIsVDvp5VauGGDP1ZEvO8Nn5xyMEs3dakajOxlmkNW7kNgSm6A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-sticky-regex@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.25.9.tgz"
  integrity sha512-WqBUSgeVwucYDP9U/xNRQam7xV8W5Zf+6Eo7T2SRVUFlhRiMNFdFz58u0KZmCVVqs2i7SHgpRnAhzRNmKfi2uA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-template-literals@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.25.9.tgz"
  integrity sha512-o97AE4syN71M/lxrCtQByzphAdlYluKPDBzDVzMmfCobUjjhAryZV0AIpRPrxN0eAkxXO6ZLEScmt+PNhj2OTw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typeof-symbol@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.25.9.tgz"
  integrity sha512-v61XqUMiueJROUv66BVIOi0Fv/CUuZuZMl5NkRoCVxLAnMexZ0A3kMe7vvZ0nulxMuMp0Mk6S5hNh48yki08ZA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typescript@^7.25.9":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.3.tgz"
  integrity sha512-6+5hpdr6mETwSKjmJUdYw0EIkATiQhnELWlE3kJFBwSg/BGIVwVaVbX+gOXBCdc7Ln1RXZxyWGecIXhUfnl7oA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-syntax-typescript" "^7.25.9"

"@babel/plugin-transform-unicode-escapes@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.25.9.tgz"
  integrity sha512-s5EDrE6bW97LtxOcGj1Khcx5AaXwiMmi4toFWRDP9/y0Woo6pXC+iyPu/KuhKtfSrNFd7jJB+/fkOtZy6aIC6Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-property-regex@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.25.9.tgz"
  integrity sha512-Jt2d8Ga+QwRluxRQ307Vlxa6dMrYEMZCgGxoPR8V52rxPyldHu3hdlHspxaqYmE7oID5+kB+UKUB/eWS+DkkWg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-regex@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.25.9.tgz"
  integrity sha512-yoxstj7Rg9dlNn9UQxzk4fcNivwv4nUYz7fYXBaKxvw/lnmPuOm/ikoELygbYq68Bls3D/D+NBPHiLwZdZZ4HA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-sets-regex@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.25.9.tgz"
  integrity sha512-8BYqO3GeVNHtx69fdPshN3fnzUNLrWdHhk/icSwigksJGczKSizZ+Z6SBCxTs723Fr5VSNorTIK7a+R2tISvwQ==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/preset-env@^7.16.4":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.26.0.tgz"
  integrity sha512-H84Fxq0CQJNdPFT2DrfnylZ3cf5K43rGfWK4LJGPpjKHiZlk0/RzwEus3PDDZZg+/Er7lCA03MVacueUuXdzfw==
  dependencies:
    "@babel/compat-data" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.25.9"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.25.9"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.25.9"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.25.9"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.25.9"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.26.0"
    "@babel/plugin-syntax-import-attributes" "^7.26.0"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.25.9"
    "@babel/plugin-transform-async-generator-functions" "^7.25.9"
    "@babel/plugin-transform-async-to-generator" "^7.25.9"
    "@babel/plugin-transform-block-scoped-functions" "^7.25.9"
    "@babel/plugin-transform-block-scoping" "^7.25.9"
    "@babel/plugin-transform-class-properties" "^7.25.9"
    "@babel/plugin-transform-class-static-block" "^7.26.0"
    "@babel/plugin-transform-classes" "^7.25.9"
    "@babel/plugin-transform-computed-properties" "^7.25.9"
    "@babel/plugin-transform-destructuring" "^7.25.9"
    "@babel/plugin-transform-dotall-regex" "^7.25.9"
    "@babel/plugin-transform-duplicate-keys" "^7.25.9"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-dynamic-import" "^7.25.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.25.9"
    "@babel/plugin-transform-export-namespace-from" "^7.25.9"
    "@babel/plugin-transform-for-of" "^7.25.9"
    "@babel/plugin-transform-function-name" "^7.25.9"
    "@babel/plugin-transform-json-strings" "^7.25.9"
    "@babel/plugin-transform-literals" "^7.25.9"
    "@babel/plugin-transform-logical-assignment-operators" "^7.25.9"
    "@babel/plugin-transform-member-expression-literals" "^7.25.9"
    "@babel/plugin-transform-modules-amd" "^7.25.9"
    "@babel/plugin-transform-modules-commonjs" "^7.25.9"
    "@babel/plugin-transform-modules-systemjs" "^7.25.9"
    "@babel/plugin-transform-modules-umd" "^7.25.9"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-new-target" "^7.25.9"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.25.9"
    "@babel/plugin-transform-numeric-separator" "^7.25.9"
    "@babel/plugin-transform-object-rest-spread" "^7.25.9"
    "@babel/plugin-transform-object-super" "^7.25.9"
    "@babel/plugin-transform-optional-catch-binding" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"
    "@babel/plugin-transform-private-methods" "^7.25.9"
    "@babel/plugin-transform-private-property-in-object" "^7.25.9"
    "@babel/plugin-transform-property-literals" "^7.25.9"
    "@babel/plugin-transform-regenerator" "^7.25.9"
    "@babel/plugin-transform-regexp-modifiers" "^7.26.0"
    "@babel/plugin-transform-reserved-words" "^7.25.9"
    "@babel/plugin-transform-shorthand-properties" "^7.25.9"
    "@babel/plugin-transform-spread" "^7.25.9"
    "@babel/plugin-transform-sticky-regex" "^7.25.9"
    "@babel/plugin-transform-template-literals" "^7.25.9"
    "@babel/plugin-transform-typeof-symbol" "^7.25.9"
    "@babel/plugin-transform-unicode-escapes" "^7.25.9"
    "@babel/plugin-transform-unicode-property-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-sets-regex" "^7.25.9"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.38.1"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz"
  integrity sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.16.0":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.26.3.tgz"
  integrity sha512-Nl03d6T9ky516DGK2YMxrTqvnpUW63TnJMOMonj+Zae0JiPC5BC9xPMSL6L8fiSpA5vP88qfygavVQvnLp+6Cw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-transform-react-display-name" "^7.25.9"
    "@babel/plugin-transform-react-jsx" "^7.25.9"
    "@babel/plugin-transform-react-jsx-development" "^7.25.9"
    "@babel/plugin-transform-react-pure-annotations" "^7.25.9"

"@babel/preset-typescript@^7.16.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.26.0.tgz"
  integrity sha512-NMk1IGZ5I/oHhoXEElcm+xUnL/szL6xflkFZmoEU9xj1qSJXpiS7rsspYo92B4DRCDvZn2erT5LdsCeXAKNCkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/plugin-transform-modules-commonjs" "^7.25.9"
    "@babel/plugin-transform-typescript" "^7.25.9"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.10.2", "@babel/runtime@^7.12.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.16.3", "@babel/runtime@^7.17.9", "@babel/runtime@^7.18.3", "@babel/runtime@^7.20.13", "@babel/runtime@^7.23.2", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.4", "@babel/runtime@^7.8.7", "@babel/runtime@^7.9.2":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.26.0.tgz"
  integrity sha512-FDSOghenHTiToteC/QRlv2q3DhPZ/oOXTBoirfWNx1Cx3TMVcGWQtMMmQcSvb/JjpNeGzx8Pq/b4fKEJuWm1sw==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.25.9.tgz"
  integrity sha512-9DGttpmPvIxBb/2uwpVo3dqJ+O6RooAFOS+lB+xDqoE2PVCE8nfoHMdZLpfCQRLwvohzXISPZcgxt80xLfsuwg==
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/traverse@^7.25.9":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.26.3.tgz"
  integrity sha512-yTmc8J+Sj8yLzwr4PD5Xb/WF3bOYu2C2OoSZPzbuqRm4n98XirsbzaX+GloeO376UnSYIYJ4NCanwV5/ugZkwA==
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.3"
    "@babel/parser" "^7.26.3"
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.3"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.25.9", "@babel/types@^7.26.0", "@babel/types@^7.26.3", "@babel/types@^7.4.4":
  version "7.26.3"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.26.3.tgz"
  integrity sha512-vN5p+1kl59GVKMvTHt55NzzmYVxprfJD+ql7U9NFIfKCBkYE55LYtS+WtPlaYOyzydrKI8Nezd+aZextrd+FMA==
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@codexteam/icons@^0.0.2":
  version "0.0.2"
  resolved "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.2.tgz"
  integrity sha512-KdeKj3TwaTHqM3IXd5YjeJP39PBUZTb+dtHjGlf5+b0VgsxYD4qzsZkb11lzopZbAuDsHaZJmAYQ8LFligIT6Q==

"@codexteam/icons@^0.0.4":
  version "0.0.4"
  resolved "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.4.tgz"
  integrity sha512-V8N/TY2TGyas4wLrPIFq7bcow68b3gu8DfDt1+rrHPtXxcexadKauRJL6eQgfG7Z0LCrN4boLRawR4S9gjIh/Q==

"@codexteam/icons@^0.0.5":
  version "0.0.5"
  resolved "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.5.tgz"
  integrity sha512-s6H2KXhLz2rgbMZSkRm8dsMJvyUNZsEjxobBEg9ztdrb1B2H3pEzY6iTwI4XUPJWJ3c3qRKwV4TrO3J5jUdoQA==

"@codexteam/icons@^0.0.6":
  version "0.0.6"
  resolved "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.6.tgz"
  integrity sha512-L7Q5PET8PjKcBT5wp7VR+FCjwCi5PUp7rd/XjsgQ0CI5FJz0DphyHGRILMuDUdCW2MQT9NHbVr4QP31vwAkS/A==

"@codexteam/icons@^0.3.0":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.3.tgz"
  integrity sha512-cp7mkZPgmBuSxigTm3Vb+DtVHYeX7qXfQd7o05vcLD8Ag5WvRlol2QSn5P10k0CDAJwmkH9nQGQLBycErS9lsQ==

"@codexteam/icons@^0.3.2":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.3.tgz"
  integrity sha512-cp7mkZPgmBuSxigTm3Vb+DtVHYeX7qXfQd7o05vcLD8Ag5WvRlol2QSn5P10k0CDAJwmkH9nQGQLBycErS9lsQ==

"@editorjs/attaches@^1.3.0":
  version "1.3.0"
  resolved "https://registry.npmjs.org/@editorjs/attaches/-/attaches-1.3.0.tgz"
  integrity sha512-D+dj55EsC5bvXV///Dh/+KN4unuI0j6SU3f5QcMA5zrfda47PNKwbKOuwMBJwn0O+o5qwqrFv67g98lAo2qqVQ==
  dependencies:
    "@codexteam/icons" "^0.0.4"

"@editorjs/checklist@^1.5.0":
  version "1.6.0"
  resolved "https://registry.npmjs.org/@editorjs/checklist/-/checklist-1.6.0.tgz"
  integrity sha512-hRNP36DInr73mSK3noHBQeoQb7DA12ANfqTXufEkTgQzx+k4mRJ0HdeGukTIR4JbwjHJ9ecUBnnQqIEGnxCFEg==
  dependencies:
    "@codexteam/icons" "^0.3.0"

"@editorjs/code@^2.8.0":
  version "2.9.3"
  resolved "https://registry.npmjs.org/@editorjs/code/-/code-2.9.3.tgz"
  integrity sha512-nXUrK3CjhpubvShYtcbkpZ9SU15IYwmJOsWZrlWYSzy9unZBRQthii6eABndsCtODzzV0yiSKmTp00RQkFow3Q==
  dependencies:
    "@codexteam/icons" "^0.3.2"

"@editorjs/delimiter@^1.3.0":
  version "1.4.2"
  resolved "https://registry.npmjs.org/@editorjs/delimiter/-/delimiter-1.4.2.tgz"
  integrity sha512-S8q2LpeYdYkVShLp7K8c4HLthDHBevLw+sT+iO0+SH0oMvFmld9SUon3DFzMQ2gG07EOdZGRZ958+sVxyvFjZw==
  dependencies:
    "@codexteam/icons" "^0.3.2"

"@editorjs/dom@^0.0.5":
  version "0.0.5"
  resolved "https://registry.npmjs.org/@editorjs/dom/-/dom-0.0.5.tgz"
  integrity sha512-SZ78Gwpkp3EUhjBIp0lSojeQ35V9acF8SubJsMeOH/vlOUE40GOnvvwWZnF05lO7bIB0dOHhhJy4N7IIAWxP2w==
  dependencies:
    "@editorjs/helpers" "^0.0.4"

"@editorjs/editorjs@^2.26.5", "@editorjs/editorjs@^2.29.1", "@editorjs/editorjs@^2.30.7":
  version "2.30.7"
  resolved "https://registry.npmjs.org/@editorjs/editorjs/-/editorjs-2.30.7.tgz"
  integrity sha512-FfdeUqrgcKWC+Cy2GW6Dxup6s2TaRKokR4FL+HKXshu6h9Y//rrx4SQkURgkZOCSbV77t9btbmAXdFXWGB+diw==

"@editorjs/embed@^2.5.3":
  version "2.7.6"
  resolved "https://registry.npmjs.org/@editorjs/embed/-/embed-2.7.6.tgz"
  integrity sha512-L3agW/23mOI0L+oksUE9UOR5VSNCqapxLH5lma+5j+idjKCC31nxbx07x53MSJ4rlOTO1L7cFVhkqptEdOliJA==
  dependencies:
    "@editorjs/editorjs" "^2.29.1"

"@editorjs/header@^2.7.0":
  version "2.8.8"
  resolved "https://registry.npmjs.org/@editorjs/header/-/header-2.8.8.tgz"
  integrity sha512-bsMSs34u2hoi0UBuRoc5EGWXIFzJiwYgkFUYQGVm63y5FU+s8zPBmVx5Ip2sw1xgs0fqfDROqmteMvvmbCy62w==
  dependencies:
    "@codexteam/icons" "^0.0.5"
    "@editorjs/editorjs" "^2.29.1"

"@editorjs/helpers@^0.0.4":
  version "0.0.4"
  resolved "https://registry.npmjs.org/@editorjs/helpers/-/helpers-0.0.4.tgz"
  integrity sha512-ieg3dzo2m1/ELze/RMNADiAiC5amXxIlVXoJ5vvXITOu/p/dPsrF+Oi3h5gBYvtGk9vg5LJUSG5YWU0tBUO1tw==

"@editorjs/image@^2.8.1":
  version "2.10.1"
  resolved "https://registry.npmjs.org/@editorjs/image/-/image-2.10.1.tgz"
  integrity sha512-ECjYQCEuPRzyruKY4+6XOyukQ2sCw4+9O1Jg8sDHyqbHLq0DMe+tgVsuXR+yfOXdocnywBOg4kLsJiP1uUYFcA==
  dependencies:
    "@codexteam/icons" "^0.3.0"

"@editorjs/inline-code@^1.4.0":
  version "1.5.1"
  resolved "https://registry.npmjs.org/@editorjs/inline-code/-/inline-code-1.5.1.tgz"
  integrity sha512-XvKpqw9y1bOYgyuVUHGDuu7KlNWCYIXD5uU1Lpc4s4LJ8VN4TjWFGKJ3sS+LR7zzasWe8hu2ffR1JC6MHIS0EQ==
  dependencies:
    "@codexteam/icons" "^0.3.2"

"@editorjs/link-autocomplete@^0.1.0":
  version "0.1.0"
  resolved "https://registry.npmjs.org/@editorjs/link-autocomplete/-/link-autocomplete-0.1.0.tgz"
  integrity sha512-Suij3TwZZREAUB2aNASrxPSsCwNN3gBAkQGL2N6rnrxwr2RI6KWw98QC/GFCTP2/hTctfNvXRbxnFi2fsJldZA==
  dependencies:
    "@codexteam/icons" "^0.0.5"

"@editorjs/link@^2.5.0":
  version "2.6.2"
  resolved "https://registry.npmjs.org/@editorjs/link/-/link-2.6.2.tgz"
  integrity sha512-3cPx6M4ZvwDDvsi0E0fvMR3rvveAV/C0GRo1JLeZJ9cG9QgyoNolj4eu5Eqx3/r1XTC/he54qYEIZ/Dc4Lr4Ow==
  dependencies:
    "@babel/runtime" "^7.10.2"
    "@codexteam/icons" "^0.0.4"

"@editorjs/list@^1.8.0":
  version "1.10.0"
  resolved "https://registry.npmjs.org/@editorjs/list/-/list-1.10.0.tgz"
  integrity sha512-zXCHaNcIscpefnteBOS3x+98f/qBgEVsv+OvtKoTDZipMNqck2uVG+X2qMQr8xcwtJrj9ySX54lUac9FDlAHnA==
  dependencies:
    "@codexteam/icons" "^0.0.4"

"@editorjs/marker@^1.3.0":
  version "1.4.0"
  resolved "https://registry.npmjs.org/@editorjs/marker/-/marker-1.4.0.tgz"
  integrity sha512-5ipEXfL44jTTRzgNp/p/YjMO7jT08S5z4V8qA3FFJTfdhKgQyM3mvP1zpdYKw47ZZpVWMCncvk5Nto3BxihEtg==
  dependencies:
    "@codexteam/icons" "^0.0.5"

"@editorjs/nested-list@^1.3.0":
  version "1.4.3"
  resolved "https://registry.npmjs.org/@editorjs/nested-list/-/nested-list-1.4.3.tgz"
  integrity sha512-3jTJd6H2Zwe8SeiI9li9AolkCxFVitEnCpJjDI6+la/HslSJC827fHivc9hue6JmC8esA4yri0KCMyp8N4ykww==
  dependencies:
    "@codexteam/icons" "^0.0.2"

"@editorjs/opensea@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@editorjs/opensea/-/opensea-1.0.2.tgz"
  integrity sha512-H3cAaG8WgSqh9xpN7V9t6U8JTUnqD+QUV1PsAGF6OMI9yoLEXGN6SbqUQUtFae+/npCMKj/2l8n82qhuzsCS7Q==

"@editorjs/paragraph@^2.9.0":
  version "2.11.6"
  resolved "https://registry.npmjs.org/@editorjs/paragraph/-/paragraph-2.11.6.tgz"
  integrity sha512-i9B50Ylvh+0ZzUGWIba09PfUXsA00Y//zFZMwqsyaXXKxMluSIJ6ADFJbbK0zaV9Ijx49Xocrlg+CEPRqATk9w==
  dependencies:
    "@codexteam/icons" "^0.0.4"

"@editorjs/personality@^2.0.2":
  version "2.0.2"
  resolved "https://registry.npmjs.org/@editorjs/personality/-/personality-2.0.2.tgz"
  integrity sha512-CnQ15cW14bHQrjUT6qWQHJJOrTyAbTlIOeARQcQ1COU3OFWuJOmMsWFUFYpz5POMmQk30Cgi9qdFyyFfZf0asA==

"@editorjs/quote@^2.5.0":
  version "2.7.6"
  resolved "https://registry.npmjs.org/@editorjs/quote/-/quote-2.7.6.tgz"
  integrity sha512-D01KUMSDj2r+6Z+xjDkQqI+y6URpeHCvj0+P4pah+GtkG040lWjFb2H4pgHFXuol2cbfyAoraYSw85fuPheCvw==
  dependencies:
    "@codexteam/icons" "^0.3.2"
    "@editorjs/dom" "^0.0.5"

"@editorjs/raw@^2.4.0":
  version "2.5.1"
  resolved "https://registry.npmjs.org/@editorjs/raw/-/raw-2.5.1.tgz"
  integrity sha512-h7CjXtfSTe4Bj1CL84ch5NW0q0CdrlWsLzQ/kbaFTgx914CzKz4Ug+xgdvytVO1dd1QzB5/HCW0ic1VxtgkhMQ==
  dependencies:
    "@codexteam/icons" "^0.0.4"

"@editorjs/simple-image@^1.5.1":
  version "1.6.0"
  resolved "https://registry.npmjs.org/@editorjs/simple-image/-/simple-image-1.6.0.tgz"
  integrity sha512-WvdGfQPlozwZd3PXQrJnRXk6gEYbv1U2vRupYJ6lTd3/UsLInXYUX5jSFcnGB5ZMH3bd0JDZfcb4d4Sv1/1big==
  dependencies:
    "@codexteam/icons" "^0.0.6"

"@editorjs/table@^2.2.1":
  version "2.4.3"
  resolved "https://registry.npmjs.org/@editorjs/table/-/table-2.4.3.tgz"
  integrity sha512-TBLVFA1T6PIZRSWz0gXZUtNrFu4a59FeABM/wS0/Coc/bhOfWBRrf5/Ic5tVHaTazXEo1dYWWrVgg/jRaRnYbw==
  dependencies:
    "@codexteam/icons" "^0.0.6"

"@editorjs/translate-inline@^1.0.0-rc.0":
  version "1.0.0-rc.0"
  resolved "https://registry.npmjs.org/@editorjs/translate-inline/-/translate-inline-1.0.0-rc.0.tgz"
  integrity sha512-8Wa7xs16E/u2i30Qm5XcrL0UydOUFMvvrY72PIv9dAO8pxKe4qHOd8+zmH3CSZL61BymrnrHNGUL8g334/aVSg==

"@editorjs/underline@^1.1.0":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@editorjs/underline/-/underline-1.2.1.tgz"
  integrity sha512-LjJ01ia6Pj0Z/elifycp9+T1bQs0eXNnIcEYKOlk0sGHLy88DPcBOh9oz1i+uWarZ4AjGsvSMaptpWe+yQNsjQ==
  dependencies:
    "@codexteam/icons" "^0.3.2"

"@editorjs/warning@^1.3.0":
  version "1.4.1"
  resolved "https://registry.npmjs.org/@editorjs/warning/-/warning-1.4.1.tgz"
  integrity sha512-7A+irJ6gIh6li4DC7qw/NiWCThlBgtoPUeeIVdldVjoATAu+a1GjS+w/5L24ReacgQJNPVQPPCoMaS1nB83agw==
  dependencies:
    "@codexteam/icons" "^0.3.2"
    "@editorjs/editorjs" "^2.30.7"

"@emoji-mart/data@1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@emoji-mart/data/-/data-1.2.1.tgz"
  integrity sha512-no2pQMWiBy6gpBEiqGeU77/bFejDqUTRY7KX+0+iur13op3bqUsXdnwoZs6Xb1zbv0gAj5VvS1PWoUUckSr5Dw==

"@emoji-mart/react@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@emoji-mart/react/-/react-1.1.1.tgz"
  integrity sha512-NMlFNeWgv1//uPsvLxvGQoIerPuVdXwK/EUek8OOkJ6wVOWPUizRBJU0hDqWZCOROVpfBgCemaC3m6jDOXi03g==

"@emotion/babel-plugin@^11.13.5":
  version "11.13.5"
  resolved "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz"
  integrity sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/serialize" "^1.3.3"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.13.5", "@emotion/cache@^11.4.0":
  version "11.13.5"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.13.5.tgz"
  integrity sha512-Z3xbtJ+UcK76eWkagZ1onvn/wAVb1GOMuR15s30Fm2wrMgC7jzpnO2JZXr4eujTTqoQFUrZIw/rT0c6Zzjca1g==
  dependencies:
    "@emotion/memoize" "^0.9.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    stylis "4.2.0"

"@emotion/hash@^0.9.2":
  version "0.9.2"
  resolved "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.2.tgz"
  integrity sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==

"@emotion/is-prop-valid@^0.8.2":
  version "0.8.8"
  resolved "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-0.8.8.tgz"
  integrity sha512-u5WtneEAr5IDG2Wv65yhunPSMLIpuKsbuOktRojfrEiEvRyC85LgPMZI63cr7NUqT8ZIGdSVg8ZKGxIug4lXcA==
  dependencies:
    "@emotion/memoize" "0.7.4"

"@emotion/memoize@^0.9.0":
  version "0.9.0"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.9.0.tgz"
  integrity sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==

"@emotion/memoize@0.7.4":
  version "0.7.4"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.7.4.tgz"
  integrity sha512-Ja/Vfqe3HpuzRsG1oBtWTHk2PGZ7GR+2Vz5iYGelAw8dx32K0y7PjVuxK6z1nMpZOqAFsRUPCkK1YjJ56qJlgw==

"@emotion/react@^11.8.1":
  version "11.13.5"
  resolved "https://registry.npmjs.org/@emotion/react/-/react-11.13.5.tgz"
  integrity sha512-6zeCUxUH+EPF1s+YF/2hPVODeV/7V07YU5x+2tfuRL8MdW6rv5vb2+CBEGTGwBdux0OIERcOS+RzxeK80k2DsQ==
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/cache" "^11.13.5"
    "@emotion/serialize" "^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.1.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.3.3":
  version "1.3.3"
  resolved "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.3.3.tgz"
  integrity sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==
  dependencies:
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/unitless" "^0.10.0"
    "@emotion/utils" "^1.4.2"
    csstype "^3.0.2"

"@emotion/sheet@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmjs.org/@emotion/sheet/-/sheet-1.4.0.tgz"
  integrity sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==

"@emotion/unitless@^0.10.0":
  version "0.10.0"
  resolved "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.10.0.tgz"
  integrity sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==

"@emotion/use-insertion-effect-with-fallbacks@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.1.0.tgz"
  integrity sha512-+wBOcIV5snwGgI2ya3u99D7/FJquOIniQT1IKyDsBmEgwvpxMNeS65Oib7OnE2d2aY+3BU4OiH+0Wchf8yk3Hw==

"@emotion/utils@^1.4.2":
  version "1.4.2"
  resolved "https://registry.npmjs.org/@emotion/utils/-/utils-1.4.2.tgz"
  integrity sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==

"@emotion/weak-memoize@^0.4.0":
  version "0.4.0"
  resolved "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz"
  integrity sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==

"@esbuild/win32-x64@0.18.20":
  version "0.18.20"
  resolved "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz"
  integrity sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.1"
  resolved "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.1.tgz"
  integrity sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  integrity sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz"
  integrity sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==

"@ffmpeg/util@^0.12.1":
  version "0.12.1"
  resolved "https://registry.npmjs.org/@ffmpeg/util/-/util-0.12.1.tgz"
  integrity sha512-10jjfAKWaDyb8+nAkijcsi9wgz/y26LOc1NKJradNMyCIl6usQcBbhkjX5qhALrSBcOy6TOeksunTYa+a03qNQ==

"@floating-ui/core@^1.6.0":
  version "1.6.8"
  resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.8.tgz"
  integrity sha512-7XJ9cPU+yI2QeLS+FCSlqNFZJq8arvswefkZrYI1yQBbftw6FyrZOxYSh+9S7z7TpeWlRt9zJ5IhM1WIL334jA==
  dependencies:
    "@floating-ui/utils" "^0.2.8"

"@floating-ui/dom@^1.0.0", "@floating-ui/dom@^1.0.1":
  version "1.6.12"
  resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.12.tgz"
  integrity sha512-NP83c0HjokcGVEMeoStg317VD9W7eDlGK7457dMBANbKA6GJZdc7rjujdgqzTaz93jkGgc5P/jeWbaCHnMNc+w==
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.8"

"@floating-ui/react-dom@^2.1.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz"
  integrity sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@floating-ui/react@^0.27.8":
  version "0.27.8"
  resolved "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.8.tgz"
  integrity sha512-EQJ4Th328y2wyHR3KzOUOoTW2UKjFk53fmyahfwExnFQ8vnsMYqKc+fFPOkeYtj5tcp1DUMiNJ7BFhed7e9ONw==
  dependencies:
    "@floating-ui/react-dom" "^2.1.2"
    "@floating-ui/utils" "^0.2.9"
    tabbable "^6.0.0"

"@floating-ui/utils@^0.2.8", "@floating-ui/utils@^0.2.9":
  version "0.2.9"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz"
  integrity sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==

"@fontsource/poppins@^4.5.10":
  version "4.5.10"
  resolved "https://registry.npmjs.org/@fontsource/poppins/-/poppins-4.5.10.tgz"
  integrity sha512-oYZVHsnlwV3VWM1SE/m6SUxVh0QLk5+2wB+lBiXcI7N/0KVQWmV9YykaPqfJHBLvmdya+MAOnv+BQHeLU1vOOw==

"@fortawesome/fontawesome-common-types@6.7.1":
  version "6.7.1"
  resolved "https://registry.npmjs.org/@fortawesome/fontawesome-common-types/-/fontawesome-common-types-6.7.1.tgz"
  integrity sha512-gbDz3TwRrIPT3i0cDfujhshnXO9z03IT1UKRIVi/VEjpNHtSBIP2o5XSm+e816FzzCFEzAxPw09Z13n20PaQJQ==

"@fortawesome/fontawesome-svg-core@^1.2.36", "@fortawesome/fontawesome-svg-core@^6.4.0", "@fortawesome/fontawesome-svg-core@~1 || ~6":
  version "6.7.1"
  resolved "https://registry.npmjs.org/@fortawesome/fontawesome-svg-core/-/fontawesome-svg-core-6.7.1.tgz"
  integrity sha512-8dBIHbfsKlCk2jHQ9PoRBg2Z+4TwyE3vZICSnoDlnsHA6SiMlTwfmW6yX0lHsRmWJugkeb92sA0hZdkXJhuz+g==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.7.1"

"@fortawesome/free-brands-svg-icons@^6.4.0":
  version "6.7.1"
  resolved "https://registry.npmjs.org/@fortawesome/free-brands-svg-icons/-/free-brands-svg-icons-6.7.1.tgz"
  integrity sha512-nJR76eqPzCnMyhbiGf6X0aclDirZriTPRcFm1YFvuupyJOGwlNF022w3YBqu+yrHRhnKRpzFX+8wJKqiIjWZkA==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.7.1"

"@fortawesome/free-regular-svg-icons@^6.4.0":
  version "6.7.1"
  resolved "https://registry.npmjs.org/@fortawesome/free-regular-svg-icons/-/free-regular-svg-icons-6.7.1.tgz"
  integrity sha512-e13cp+bAx716RZOTQ59DhqikAgETA9u1qTBHO3e3jMQQ+4H/N1NC1ZVeFYt1V0m+Th68BrEL1/X6XplISutbXg==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.7.1"

"@fortawesome/free-solid-svg-icons@^5.15.4", "@fortawesome/free-solid-svg-icons@^6.4.0":
  version "6.7.1"
  resolved "https://registry.npmjs.org/@fortawesome/free-solid-svg-icons/-/free-solid-svg-icons-6.7.1.tgz"
  integrity sha512-BTKc0b0mgjWZ2UDKVgmwaE0qt0cZs6ITcDgjrti5f/ki7aF5zs+N91V6hitGo3TItCFtnKg6cUVGdTmBFICFRg==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.7.1"

"@fortawesome/react-fontawesome@^0.1.16", "@fortawesome/react-fontawesome@^0.2.0":
  version "0.2.2"
  resolved "https://registry.npmjs.org/@fortawesome/react-fontawesome/-/react-fontawesome-0.2.2.tgz"
  integrity sha512-EnkrprPNqI6SXJl//m29hpaNzOp1bruISWaOiRtkMi/xSvHJlzc2j2JAYS7egxt/EbjSNV/k6Xy0AQI6vB2+1g==
  dependencies:
    prop-types "^15.8.1"

"@fullcalendar/core@^6.1.8", "@fullcalendar/core@~6.1.15":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/core/-/core-6.1.15.tgz"
  integrity sha512-BuX7o6ALpLb84cMw1FCB9/cSgF4JbVO894cjJZ6kP74jzbUZNjtwffwRdA+Id8rrLjT30d/7TrkW90k4zbXB5Q==
  dependencies:
    preact "~10.12.1"

"@fullcalendar/daygrid@^6.1.8":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/daygrid/-/daygrid-6.1.15.tgz"
  integrity sha512-j8tL0HhfiVsdtOCLfzK2J0RtSkiad3BYYemwQKq512cx6btz6ZZ2RNc/hVnIxluuWFyvx5sXZwoeTJsFSFTEFA==

"@fullcalendar/react@^6.1.8":
  version "6.1.15"
  resolved "https://registry.npmjs.org/@fullcalendar/react/-/react-6.1.15.tgz"
  integrity sha512-L0b9hybS2J4e7lq6G2CD4nqriyLEqOH1tE8iI6JQjAMTVh5JicOo5Mqw+fhU5bJ7hLfMw2K3fksxX3Ul1ssw5w==

"@headlessui/react@^1.7.18":
  version "1.7.19"
  resolved "https://registry.npmjs.org/@headlessui/react/-/react-1.7.19.tgz"
  integrity sha512-Ll+8q3OlMJfJbAKM/+/Y2q6PPYbryqNTXDbryx7SXLIDamkF6iQFbriYHga0dY44PvDhvvBWCx1Xj4U5+G4hOw==
  dependencies:
    "@tanstack/react-virtual" "^3.0.0-beta.60"
    client-only "^0.0.1"

"@heroicons/react@^2.0.18":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@heroicons/react/-/react-2.2.0.tgz"
  integrity sha512-LMcepvRaS9LYHJGsF0zzmgKCUim/X3N/DQKc4jepAXJ7l8QxJ1PmxJzqplF2Z3FE4PqBAIGyJAQ/w4B5dsqbtQ==

"@hookform/resolvers@^3.1.0":
  version "3.9.1"
  resolved "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.9.1.tgz"
  integrity sha512-ud2HqmGBM0P0IABqoskKWI6PEf6ZDDBZkFqe2Vnl+mTHCEHzr3ISjjZyCwTjC/qpL25JC9aIDkloQejvMeq0ug==

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz"
  integrity sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz"
  integrity sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@legendapp/state@^0.23.4":
  version "0.23.4"
  resolved "https://registry.npmjs.org/@legendapp/state/-/state-0.23.4.tgz"
  integrity sha512-9N4HIG+RIyIfdbb7B9i6wW1B+8SO/zSno2lu6ztj48Vz6UixFPsn8dNgJv/qC1B9YjXp8f5Lh0gKhgudX0jDhA==

"@mapbox/node-pre-gyp@^1.0.0":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz"
  integrity sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==
  dependencies:
    detect-libc "^2.0.0"
    https-proxy-agent "^5.0.0"
    make-dir "^3.1.0"
    node-fetch "^2.6.7"
    nopt "^5.0.0"
    npmlog "^5.0.1"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.11"

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "https://registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz"
  integrity sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==
  dependencies:
    eslint-scope "5.1.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@popperjs/core@^2.11.8", "@popperjs/core@^2.6.0", "@popperjs/core@^2.9.0":
  version "2.11.8"
  resolved "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz"
  integrity sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==

"@radix-ui/primitive@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1.tgz"
  integrity sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==

"@radix-ui/react-compose-refs@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1.tgz"
  integrity sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==

"@radix-ui/react-context@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz"
  integrity sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==

"@radix-ui/react-dialog@^1.1.2":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.4.tgz"
  integrity sha512-Ur7EV1IwQGCyaAuyDRiOLA5JIUZxELJljF+MbM/2NC0BYwfuRrbpS30BiQBJrVruscgUkieKkqXYDOoByaxIoA==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.3"
    "@radix-ui/react-focus-guards" "1.1.1"
    "@radix-ui/react-focus-scope" "1.1.1"
    "@radix-ui/react-id" "1.1.0"
    "@radix-ui/react-portal" "1.1.3"
    "@radix-ui/react-presence" "1.1.2"
    "@radix-ui/react-primitive" "2.0.1"
    "@radix-ui/react-slot" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.1.0"
    aria-hidden "^1.1.1"
    react-remove-scroll "^2.6.1"

"@radix-ui/react-dismissable-layer@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.3.tgz"
  integrity sha512-onrWn/72lQoEucDmJnr8uczSNTujT0vJnA/X5+3AkChVPowr8n1yvIKIabhWyMQeMvvmdpsvcyDqx3X1LEXCPg==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-primitive" "2.0.1"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-escape-keydown" "1.1.0"

"@radix-ui/react-focus-guards@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.1.tgz"
  integrity sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==

"@radix-ui/react-focus-scope@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.1.tgz"
  integrity sha512-01omzJAYRxXdG2/he/+xy+c8a8gCydoQ1yOxnWNcRhrrBW5W+RQJ22EK1SaO8tb3WoUsuEw7mJjBozPzihDFjA==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-primitive" "2.0.1"
    "@radix-ui/react-use-callback-ref" "1.1.0"

"@radix-ui/react-id@^1.1.0", "@radix-ui/react-id@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.0.tgz"
  integrity sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-portal@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.3.tgz"
  integrity sha512-NciRqhXnGojhT93RPyDaMPfLH3ZSl4jjIFbZQ1b/vxvZEdHsBZ49wP9w8L3HzUQwep01LcWtkUvm0OVB5JAHTw==
  dependencies:
    "@radix-ui/react-primitive" "2.0.1"
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-presence@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2.tgz"
  integrity sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-primitive@^2.0.0", "@radix-ui/react-primitive@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.1.tgz"
  integrity sha512-sHCWTtxwNn3L3fH8qAfnF3WbUZycW93SM1j3NFDzXBiz8D6F5UTTy8G1+WFEaiCdvCVRJWj6N2R4Xq6HdiHmDg==
  dependencies:
    "@radix-ui/react-slot" "1.1.1"

"@radix-ui/react-progress@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-progress/-/react-progress-1.1.1.tgz"
  integrity sha512-6diOawA84f/eMxFHcWut0aE1C2kyE9dOyCTQOMRR2C/qPiXz/X0SaiA/RLbapQaXUCmy0/hLMf9meSccD1N0pA==
  dependencies:
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-primitive" "2.0.1"

"@radix-ui/react-slot@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.1.tgz"
  integrity sha512-RApLLOcINYJA+dMVbOju7MYv1Mb2EBp2nH4HdDzXTSyaR5optlm6Otrz1euW3HbdOR8UmmFK06TD+A9frYWv+g==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.1"

"@radix-ui/react-use-callback-ref@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0.tgz"
  integrity sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==

"@radix-ui/react-use-controllable-state@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.0.tgz"
  integrity sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.0"

"@radix-ui/react-use-escape-keydown@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0.tgz"
  integrity sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.0"

"@radix-ui/react-use-layout-effect@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.0.tgz"
  integrity sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==

"@react-pdf/fns@2.2.1":
  version "2.2.1"
  resolved "https://registry.npmjs.org/@react-pdf/fns/-/fns-2.2.1.tgz"
  integrity sha512-s78aDg0vDYaijU5lLOCsUD+qinQbfOvcNeaoX9AiE7+kZzzCo6B/nX+l48cmt9OosJmvZvE9DWR9cLhrhOi2pA==
  dependencies:
    "@babel/runtime" "^7.20.13"

"@react-pdf/font@^2.5.2":
  version "2.5.2"
  resolved "https://registry.npmjs.org/@react-pdf/font/-/font-2.5.2.tgz"
  integrity sha512-Ud0EfZ2FwrbvwAWx8nz+KKLmiqACCH9a/N/xNDOja0e/YgSnqTpuyHegFBgIMKjuBtO5dNvkb4dXkxAhGe/ayw==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/types" "^2.6.0"
    cross-fetch "^3.1.5"
    fontkit "^2.0.2"
    is-url "^1.2.4"

"@react-pdf/image@^2.3.6":
  version "2.3.6"
  resolved "https://registry.npmjs.org/@react-pdf/image/-/image-2.3.6.tgz"
  integrity sha512-7iZDYZrZlJqNzS6huNl2XdMcLFUo68e6mOdzQeJ63d5eApdthhSHBnkGzHfLhH5t8DCpZNtClmklzuLL63ADfw==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/png-js" "^2.3.1"
    cross-fetch "^3.1.5"
    jay-peg "^1.0.2"

"@react-pdf/layout@^3.13.0":
  version "3.13.0"
  resolved "https://registry.npmjs.org/@react-pdf/layout/-/layout-3.13.0.tgz"
  integrity sha512-lpPj/EJYHFOc0ALiJwLP09H28B4ADyvTjxOf67xTF+qkWd+dq1vg7dw3wnYESPnWk5T9NN+HlUenJqdYEY9AvA==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    "@react-pdf/image" "^2.3.6"
    "@react-pdf/pdfkit" "^3.2.0"
    "@react-pdf/primitives" "^3.1.1"
    "@react-pdf/stylesheet" "^4.3.0"
    "@react-pdf/textkit" "^4.4.1"
    "@react-pdf/types" "^2.6.0"
    cross-fetch "^3.1.5"
    emoji-regex "^10.3.0"
    queue "^6.0.1"
    yoga-layout "^2.0.1"

"@react-pdf/pdfkit@^3.2.0":
  version "3.2.0"
  resolved "https://registry.npmjs.org/@react-pdf/pdfkit/-/pdfkit-3.2.0.tgz"
  integrity sha512-OBfCcnTC6RpD9uv9L2woF60Zj1uQxhLFzTBXTdcYE9URzPE/zqXIyzpXEA4Vf3TFbvBCgFE2RzJ2ZUS0asq7yA==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/png-js" "^2.3.1"
    browserify-zlib "^0.2.0"
    crypto-js "^4.2.0"
    fontkit "^2.0.2"
    jay-peg "^1.0.2"
    vite-compatible-readable-stream "^3.6.1"

"@react-pdf/png-js@^2.3.1":
  version "2.3.1"
  resolved "https://registry.npmjs.org/@react-pdf/png-js/-/png-js-2.3.1.tgz"
  integrity sha512-pEZ18I4t1vAUS4lmhvXPmXYP4PHeblpWP/pAlMMRkEyP7tdAeHUN7taQl9sf9OPq7YITMY3lWpYpJU6t4CZgZg==
  dependencies:
    browserify-zlib "^0.2.0"

"@react-pdf/primitives@^3.1.1":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@react-pdf/primitives/-/primitives-3.1.1.tgz"
  integrity sha512-miwjxLwTnO3IjoqkTVeTI+9CdyDggwekmSLhVCw+a/7FoQc+gF3J2dSKwsHvAcVFM0gvU8mzCeTofgw0zPDq0w==

"@react-pdf/render@^3.5.0":
  version "3.5.0"
  resolved "https://registry.npmjs.org/@react-pdf/render/-/render-3.5.0.tgz"
  integrity sha512-gFOpnyqCgJ6l7VzfJz6rG1i2S7iVSD8bUHDjPW9Mze8TmyksHzN2zBH3y7NbsQOw1wU6hN4NhRmslrsn+BRDPA==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    "@react-pdf/primitives" "^3.1.1"
    "@react-pdf/textkit" "^4.4.1"
    "@react-pdf/types" "^2.6.0"
    abs-svg-path "^0.1.1"
    color-string "^1.9.1"
    normalize-svg-path "^1.1.0"
    parse-svg-path "^0.1.2"
    svg-arc-to-cubic-bezier "^3.2.0"

"@react-pdf/renderer@^3.1.14", "@react-pdf/renderer@^3.3.8":
  version "3.4.5"
  resolved "https://registry.npmjs.org/@react-pdf/renderer/-/renderer-3.4.5.tgz"
  integrity sha512-O1N8q45bTs7YuC+x9afJSKQWDYQy2RjoCxlxEGdbCwP+WD5G6dWRUWXlc8F0TtzU3uFglYMmDab2YhXTmnVN9g==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/font" "^2.5.2"
    "@react-pdf/layout" "^3.13.0"
    "@react-pdf/pdfkit" "^3.2.0"
    "@react-pdf/primitives" "^3.1.1"
    "@react-pdf/render" "^3.5.0"
    "@react-pdf/types" "^2.6.0"
    events "^3.3.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    queue "^6.0.1"
    scheduler "^0.17.0"

"@react-pdf/stylesheet@^4.3.0":
  version "4.3.0"
  resolved "https://registry.npmjs.org/@react-pdf/stylesheet/-/stylesheet-4.3.0.tgz"
  integrity sha512-x7IVZOqRrUum9quuDeFXBveXwBht+z/6B0M+z4a4XjfSg1vZVvzoTl07Oa1yvQ/4yIC5yIkG2TSMWeKnDB+hrw==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    "@react-pdf/types" "^2.6.0"
    color-string "^1.9.1"
    hsl-to-hex "^1.0.0"
    media-engine "^1.0.3"
    postcss-value-parser "^4.1.0"

"@react-pdf/textkit@^4.4.1":
  version "4.4.1"
  resolved "https://registry.npmjs.org/@react-pdf/textkit/-/textkit-4.4.1.tgz"
  integrity sha512-Jl9wdTqIvJ5pX+vAGz0EOhP7ut5Two9H6CzTKo/YYPeD79cM2yTXF3JzTERBC28y7LR0Waq9D2LHQjI+b/EYUQ==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "2.2.1"
    bidi-js "^1.0.2"
    hyphen "^1.6.4"
    unicode-properties "^1.4.1"

"@react-pdf/types@^2.6.0":
  version "2.7.0"
  resolved "https://registry.npmjs.org/@react-pdf/types/-/types-2.7.0.tgz"
  integrity sha512-7KrPPCpgRPKR+g+T127PE4bpw9Q84ZiY07EYRwXKVtTEVW9wJ5BZiF9smT9IvH19s+MQaDLmYRgjESsnqlyH0Q==

"@remix-run/router@1.21.0":
  version "1.21.0"
  resolved "https://registry.npmjs.org/@remix-run/router/-/router-1.21.0.tgz"
  integrity sha512-xfSkCAchbdG5PnbrKqFWwia4Bi61nH+wm8wLEqfHDyp7Y3dZzgqS2itV8i4gAq9pC2HsTpwyBC6Ds8VHZ96JlA==

"@rollup/pluginutils@^4.2.1":
  version "4.2.1"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-4.2.1.tgz"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@rtsao/scc@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@rtsao/scc/-/scc-1.1.0.tgz"
  integrity sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==

"@rushstack/eslint-patch@^1.1.0":
  version "1.10.4"
  resolved "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.10.4.tgz"
  integrity sha512-WJgX9nzTqknM393q1QJDJmoW28kUfEnybeTfVNcNAPnIx210RXm2DiXiHzfNPJNIUUb1tJnz/l4QGtJ30PgWmA==

"@splidejs/react-splide@^0.7.12":
  version "0.7.12"
  resolved "https://registry.npmjs.org/@splidejs/react-splide/-/react-splide-0.7.12.tgz"
  integrity sha512-UfXH+j47jsMc4x5HA/aOwuuHPqn6y9+ZTNYPWDRD8iLKvIVMZlzq2unjUEvyDAU+TTVPZOXkG2Ojeoz0P4AkZw==
  dependencies:
    "@splidejs/splide" "^4.1.3"

"@splidejs/splide@^4.1.3":
  version "4.1.4"
  resolved "https://registry.npmjs.org/@splidejs/splide/-/splide-4.1.4.tgz"
  integrity sha512-5I30evTJcAJQXt6vJ26g2xEkG+l1nXcpEw4xpKh0/FWQ8ozmAeTbtniVtVmz2sH1Es3vgfC4SS8B2X4o5JMptA==

"@stripe/react-stripe-js@^2.1.0":
  version "2.9.0"
  resolved "https://registry.npmjs.org/@stripe/react-stripe-js/-/react-stripe-js-2.9.0.tgz"
  integrity sha512-+/j2g6qKAKuWSurhgRMfdlIdKM+nVVJCy/wl0US2Ccodlqx0WqfIIBhUkeONkCG+V/b+bZzcj4QVa3E/rXtT4Q==
  dependencies:
    prop-types "^15.7.2"

"@stripe/stripe-js@^1.44.1 || ^2.0.0 || ^3.0.0 || ^4.0.0", "@stripe/stripe-js@^1.52.1":
  version "1.54.2"
  resolved "https://registry.npmjs.org/@stripe/stripe-js/-/stripe-js-1.54.2.tgz"
  integrity sha512-R1PwtDvUfs99cAjfuQ/WpwJ3c92+DAMy9xGApjqlWQMj0FKQabUAys2swfTRNzuYAYJh7NqK2dzcYVNkKLEKUg==

"@swc/helpers@^0.5.12":
  version "0.5.15"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.15.tgz"
  integrity sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==
  dependencies:
    tslib "^2.8.0"

"@tailwindcss/forms@^0.5.3":
  version "0.5.9"
  resolved "https://registry.npmjs.org/@tailwindcss/forms/-/forms-0.5.9.tgz"
  integrity sha512-tM4XVr2+UVTxXJzey9Twx48c1gcxFStqn1pQz0tRsX8o3DvxhN5oY5pvyAbUx7VTaZxpej4Zzvc6h+1RJBzpIg==
  dependencies:
    mini-svg-data-uri "^1.2.3"

"@tanstack/react-virtual@^3.0.0-beta.60":
  version "3.10.9"
  resolved "https://registry.npmjs.org/@tanstack/react-virtual/-/react-virtual-3.10.9.tgz"
  integrity sha512-OXO2uBjFqA4Ibr2O3y0YMnkrRWGVNqcvHQXmGvMu6IK8chZl3PrDxFXdGZ2iZkSrKh3/qUYoFqYe+Rx23RoU0g==
  dependencies:
    "@tanstack/virtual-core" "3.10.9"

"@tanstack/virtual-core@3.10.9":
  version "3.10.9"
  resolved "https://registry.npmjs.org/@tanstack/virtual-core/-/virtual-core-3.10.9.tgz"
  integrity sha512-kBknKOKzmeR7lN+vSadaKWXaLS0SZZG+oqpQ/k80Q6g9REn6zRHS/ZYdrIzHnpHgy/eWs00SujveUN/GJT2qTw==

"@tippyjs/react@^4.2.6":
  version "4.2.6"
  resolved "https://registry.npmjs.org/@tippyjs/react/-/react-4.2.6.tgz"
  integrity sha512-91RicDR+H7oDSyPycI13q3b7o4O60wa2oRbjlz2fyRLmHImc4vyDwuUP8NtZaN0VARJY5hybvDYrFzhY9+Lbyw==
  dependencies:
    tippy.js "^6.3.1"

"@transloadit/prettier-bytes@^0.3.4":
  version "0.3.5"
  resolved "https://registry.npmjs.org/@transloadit/prettier-bytes/-/prettier-bytes-0.3.5.tgz"
  integrity sha512-xF4A3d/ZyX2LJWeQZREZQw+qFX4TGQ8bGVP97OLRt6sPO6T0TNHBFTuRHOJh7RNmYOBmQ9MHxpolD9bXihpuVA==

"@types/babel__core@^7.20.5":
  version "7.20.5"
  resolved "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz"
  integrity sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.8"
  resolved "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz"
  integrity sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz"
  integrity sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  version "7.20.6"
  resolved "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.6.tgz"
  integrity sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==
  dependencies:
    "@babel/types" "^7.20.7"

"@types/eslint@^8.4.5":
  version "8.56.12"
  resolved "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.12.tgz"
  integrity sha512-03ruubjWyOHlmljCVoxSuNDdmfZDzsrrz0P2LeJsOXr+ZwFQ+0yQIwNCwt/GYhV7Z31fgtXJTAEs+FYlEL851g==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz"
  integrity sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==

"@types/google-maps@*":
  version "3.2.6"
  resolved "https://registry.npmjs.org/@types/google-maps/-/google-maps-3.2.6.tgz"
  integrity sha512-ySOadZErcnCnNG+Zkmv5n+QG9DyTt7Mkx5Yk1dTjjNPtD8ByFaf+klZ/CxzgYcweduWEijTP0ASkANl57D0PRQ==
  dependencies:
    "@types/google.maps" "*"

"@types/google.maps@*":
  version "3.58.1"
  resolved "https://registry.npmjs.org/@types/google.maps/-/google.maps-3.58.1.tgz"
  integrity sha512-X9QTSvGJ0nCfMzYOnaVs/k6/4L+7F5uCS+4iUmkLEls6J9S/Phv+m/i3mDeyc49ZBgwab3EFO1HEoBY7k98EGQ==

"@types/googlemaps@^3.0.0":
  version "3.43.3"
  resolved "https://registry.npmjs.org/@types/googlemaps/-/googlemaps-3.43.3.tgz"
  integrity sha512-ZWNoz/O8MPEpiajvj7QiqCY8tTLFNqNZ/a+s+zTV58wFVNAvvqV4bdGfnsjTb5Cs4V6wEsLrX8XRhmnyYJ2Tdg==

"@types/json-schema@*", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==

"@types/markerclustererplus@^2.1.29":
  version "2.1.33"
  resolved "https://registry.npmjs.org/@types/markerclustererplus/-/markerclustererplus-2.1.33.tgz"
  integrity sha512-ZDxsLUp8BmK9MVZZeQVDDzVrEXgRL0021AQcfXqfreFhdfZ9+PS0P6o4nZBvoIVYTmGSBemdCdI6mL6hf9yWvg==
  dependencies:
    "@types/google-maps" "*"

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz"
  integrity sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==

"@types/prop-types@*", "@types/prop-types@^15.7.3":
  version "15.7.13"
  resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.13.tgz"
  integrity sha512-hCZTSvwbzWGvhqxp/RqVqwU999pBf2vp7hzIjiYOsl8wqOmUxkQ6ddw1cV3l8811+kdUFus/q4d1Y3E3SyEifA==

"@types/raf@^3.4.0":
  version "3.4.3"
  resolved "https://registry.npmjs.org/@types/raf/-/raf-3.4.3.tgz"
  integrity sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw==

"@types/react-dom@*", "@types/react-dom@^18.2.4":
  version "18.3.1"
  resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.1.tgz"
  integrity sha512-qW1Mfv8taImTthu4KoXgDfLuk4bydU6Q/TkADnDWWHwi4NX4BR+LWfTp2sVmTqRrsHvyDDTelgelxJ+SsejKKQ==
  dependencies:
    "@types/react" "*"

"@types/react-transition-group@^4.4.0":
  version "4.4.11"
  resolved "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.11.tgz"
  integrity sha512-RM05tAniPZ5DZPzzNFP+DmrcOdD0efDUxMy3145oljWSl3x9ZV5vhme98gTxFrj2lhXvmGNnUiuDyJgY9IKkNA==
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^15.0.0 || ^16.0.0", "@types/react@^16.8.0 || ^17.0.0 || ^18.0.0", "@types/react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "@types/react@^18.2.6", "@types/react@>=0.14.0", "@types/react@0.14 - 19":
  version "18.3.13"
  resolved "https://registry.npmjs.org/@types/react/-/react-18.3.13.tgz"
  integrity sha512-ii/gswMmOievxAJed4PAHT949bpYjPKXvXo1v6cRB/kqc2ZR4n+SgyCyvyc5Fec5ez8VnUumI1Vk7j6fRyRogg==
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/retry@0.12.2":
  version "0.12.2"
  resolved "https://registry.npmjs.org/@types/retry/-/retry-0.12.2.tgz"
  integrity sha512-XISRgDJ2Tc5q4TRqvgJtzsRkFYNJzZrhTdtMoGVBttwzzQJkPnS3WWTFc7kuDRoPtPakl+T+OfdEUjYJj7Jbow==

"@types/semver@^7.3.12":
  version "7.5.8"
  resolved "https://registry.npmjs.org/@types/semver/-/semver-7.5.8.tgz"
  integrity sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==

"@types/signature_pad@^2.3.0":
  version "2.3.6"
  resolved "https://registry.npmjs.org/@types/signature_pad/-/signature_pad-2.3.6.tgz"
  integrity sha512-v3j92gCQJoxomHhd+yaG4Vsf8tRS/XbzWKqDv85UsqjMGy4zhokuwKe4b6vhbgncKkh+thF+gpz6+fypTtnFqQ==

"@typescript-eslint/eslint-plugin@^4.0.0 || ^5.0.0", "@typescript-eslint/eslint-plugin@^5.5.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.62.0.tgz"
  integrity sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@^5.0.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-5.62.0.tgz"
  integrity sha512-RTXpeB3eMkpoclG3ZHft6vG/Z30azNHuqY6wKPBHlVMZFuEvrtlEDe8gMqDb+SO+9hjC/pLekeSCryf9vMZlCw==
  dependencies:
    "@typescript-eslint/utils" "5.62.0"

"@typescript-eslint/parser@^5.0.0", "@typescript-eslint/parser@^5.5.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.62.0.tgz"
  integrity sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz"
  integrity sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-5.62.0.tgz"
  integrity sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.62.0.tgz"
  integrity sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz"
  integrity sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@^5.58.0", "@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.62.0.tgz"
  integrity sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz"
  integrity sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@ungap/structured-clone@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
  integrity sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==

"@uppy/audio@^1.1.9":
  version "1.1.9"
  resolved "https://registry.npmjs.org/@uppy/audio/-/audio-1.1.9.tgz"
  integrity sha512-PuA6RhTBr8KEATouWQ/PLyw/8LY+rxy2jcI/gzkQg36ohBCS/UouzmawFFI+WqEwztlLVGcKeUUH5Yd9ePUD5A==
  dependencies:
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/aws-s3-multipart@^3.10.2", "@uppy/aws-s3-multipart@^3.12.0":
  version "3.12.0"
  resolved "https://registry.npmjs.org/@uppy/aws-s3-multipart/-/aws-s3-multipart-3.12.0.tgz"
  integrity sha512-l6/TlRjde/mP4LMFWdJIRBEUUceYXtAiNAHukfyzM3VbY3/+YrEJTAchsa4DrqAiyToJJu6b+xxvL2H46cDs3Q==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/utils" "^5.9.0"

"@uppy/aws-s3@^3.1.1", "@uppy/aws-s3@^3.6.2":
  version "3.6.2"
  resolved "https://registry.npmjs.org/@uppy/aws-s3/-/aws-s3-3.6.2.tgz"
  integrity sha512-pXXSfJbPLR9tmmLFckKU3lyp7Zx4AVvamH/Y5MU2WHKj8TQMrGeM0/M/nXn8SIa7roYEaskY6dVYT/DcHLdO9A==
  dependencies:
    "@uppy/aws-s3-multipart" "^3.10.2"
    "@uppy/companion-client" "^3.7.2"
    "@uppy/utils" "^5.7.2"
    "@uppy/xhr-upload" "^3.6.2"
    nanoid "^4.0.0"

"@uppy/box@^2.4.0":
  version "2.4.0"
  resolved "https://registry.npmjs.org/@uppy/box/-/box-2.4.0.tgz"
  integrity sha512-7KEXzljhYcw0dMycJ4rEvLc6wkH9+yLmRQ69PQ+iPYzWe4q27DD7ux3lAhEqRocRFqqzFCt6M3pW79WfnqjkCA==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/companion-client@^3.7.2", "@uppy/companion-client@^3.8.1", "@uppy/companion-client@^3.8.2":
  version "3.8.2"
  resolved "https://registry.npmjs.org/@uppy/companion-client/-/companion-client-3.8.2.tgz"
  integrity sha512-WLjZ0Y6Fe7lzwU1YPvvQ/YqooejcgIZkT2TC39xr+QQ7Y1FwJECsyUdlKwgi1ee8TNpjoCrj3Q1Hjel/+p0VhA==
  dependencies:
    "@uppy/utils" "^5.9.0"
    namespace-emitter "^2.0.1"
    p-retry "^6.1.0"

"@uppy/compressor@^1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@uppy/compressor/-/compressor-1.1.4.tgz"
  integrity sha512-ZB8nsJQz9cZXQKFAiKVZjD9REiFXF5woeAQKrJvTTFdxhvH8PVn8EKmOFZDt1f2XuwgsIXdhsbfI6BzmLVFYYg==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/utils" "^5.9.0"
    compressorjs "^1.2.1"
    preact "^10.5.13"
    promise-queue "^2.2.5"

"@uppy/core@^3.10.0", "@uppy/core@^3.11.0", "@uppy/core@^3.11.2", "@uppy/core@^3.11.3", "@uppy/core@^3.12.0", "@uppy/core@^3.13.0", "@uppy/core@^3.13.1", "@uppy/core@^3.2.0", "@uppy/core@^3.4.0", "@uppy/core@^3.9.1", "@uppy/core@^3.9.3":
  version "3.13.1"
  resolved "https://registry.npmjs.org/@uppy/core/-/core-3.13.1.tgz"
  integrity sha512-iQGAUO4ziQRpfv7kix6tO6JOWqjI0K4vt8AynvHWzDPZxYSba3zd6RojGNPsYWSR7Xv+dRXYx+GU8oTiK1FRUA==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/store-default" "^3.2.2"
    "@uppy/utils" "^5.9.0"
    lodash "^4.17.21"
    mime-match "^1.0.2"
    namespace-emitter "^2.0.1"
    nanoid "^4.0.0"
    preact "^10.5.13"

"@uppy/dashboard@^3.4.0", "@uppy/dashboard@^3.9.0", "@uppy/dashboard@^3.9.1":
  version "3.9.1"
  resolved "https://registry.npmjs.org/@uppy/dashboard/-/dashboard-3.9.1.tgz"
  integrity sha512-zZp+5Dwqu1jUdAZEu0Os2kC/8bF3cdrkve8CYEwqP/12yjNe8PF+XUQKF1RCYITjDE4hPSXcTh0MWw6t2LONuw==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/informer" "^3.1.0"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/status-bar" "^3.3.3"
    "@uppy/thumbnail-generator" "^3.1.0"
    "@uppy/utils" "^5.9.0"
    classnames "^2.2.6"
    is-shallow-equal "^1.0.1"
    lodash "^4.17.21"
    memoize-one "^6.0.0"
    nanoid "^4.0.0"
    preact "^10.5.13"

"@uppy/drag-drop@^3.0.2", "@uppy/drag-drop@^3.1.0", "@uppy/drag-drop@^3.1.1":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@uppy/drag-drop/-/drag-drop-3.1.1.tgz"
  integrity sha512-ujUBswJ/Acvg1UQUPtfWO6PekPzFgQAEtDW7yxsi81wy1/WNIYjY36bxKVpM/cz5PjmWOTWoqdjBQa4LIJCdyw==
  dependencies:
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/drop-target@^2.1.0":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@uppy/drop-target/-/drop-target-2.1.0.tgz"
  integrity sha512-s05stmY2u6BK0X7c/jMAxnTigUj08ccesoD9kmkkfPZh+J0icgokJa+P/KwwAiPAyReBoJSS1ZBzjHjuAM0v9Q==
  dependencies:
    "@uppy/utils" "^5.9.0"

"@uppy/dropbox@^3.1.1", "@uppy/dropbox@^3.4.0":
  version "3.4.0"
  resolved "https://registry.npmjs.org/@uppy/dropbox/-/dropbox-3.4.0.tgz"
  integrity sha512-KcYb40b5qCnOJVB+2wNHG+DY/s2sLtW6vyp7jJNrQR87AtNaLXd1dIoYXgM5HrCTJ52IrnfrTgNcUb+EhaRisw==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/facebook@^3.3.1":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@uppy/facebook/-/facebook-3.3.1.tgz"
  integrity sha512-AUZKJc8XrGaAcCos8XFCvx5hsAO6cGNXJkxjFJrkMsj7z+dUCxkVumAlztZh/5SYyhymZj20Y8cUIjRI0gvpiw==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.12.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/file-input@^3.1.2":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@uppy/file-input/-/file-input-3.1.2.tgz"
  integrity sha512-IgZhK3EfO2bEqmEwpqfo3N9k8OxV2pmuGdKU4IuwJFv3Q1s1F6ceSDhWX8ivtVXlDvnbJIkqZbZjnvWwJxfjng==
  dependencies:
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/form@^3.2.2":
  version "3.2.2"
  resolved "https://registry.npmjs.org/@uppy/form/-/form-3.2.2.tgz"
  integrity sha512-dS54AtSicF7pA0wupt09t/xrz0kH/HS0dIaGnQaxhnk3a5TMUaF5/HVXvzdHli7udfiT8jzWfpOOlvgL7+YbQQ==
  dependencies:
    "@uppy/utils" "^5.9.0"
    get-form-data "^3.0.0"

"@uppy/golden-retriever@^3.2.0":
  version "3.2.0"
  resolved "https://registry.npmjs.org/@uppy/golden-retriever/-/golden-retriever-3.2.0.tgz"
  integrity sha512-r2U76tXLjGKQCR8mtCmuncqAZYwQR7Hwx6AiWGts+fbmwFTLI2sj5GbjEPonB6jfNqqHleqvihQnqPFGTKIAlw==
  dependencies:
    "@uppy/utils" "^5.7.5"
    lodash "^4.17.21"

"@uppy/google-drive@^3.1.1", "@uppy/google-drive@^3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@uppy/google-drive/-/google-drive-3.6.0.tgz"
  integrity sha512-7Db98dJW/7ajvKUjJxQbL7D5MarJHG7T958jZhmy0jRugx19r9HqBcM88bsIn+A3ljmnrqzb9IfsRwTZ2W6D5A==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/google-photos@^0.1.0":
  version "0.1.0"
  resolved "https://registry.npmjs.org/@uppy/google-photos/-/google-photos-0.1.0.tgz"
  integrity sha512-Ia61UjMJxItZXkPBZiSCXvr2tCHq5s4GTUdWFKAe+nDzNndcO0J4FvGR0wFq5lA7yJ0lGofIq7yvVGk3G64/nQ==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/image-editor@^2.4.6":
  version "2.4.6"
  resolved "https://registry.npmjs.org/@uppy/image-editor/-/image-editor-2.4.6.tgz"
  integrity sha512-uQ8k4pUSsYBv6ZBoICwKq3M1DqiKg6AFM/nbvxL/q5KpRkRTszzPvP4hyvjY2zDLLf/NlK3E45N2IcWraV87dQ==
  dependencies:
    "@uppy/utils" "^5.9.0"
    cropperjs "1.5.7"
    preact "^10.5.13"

"@uppy/informer@^3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@uppy/informer/-/informer-3.1.0.tgz"
  integrity sha512-vmpTLqzSLmZSuIVDZV0o19yXVqyTh5/uCbKUEiyfBhR726kQiuYQLP/ZHaKcvW3c1ESQGbNg53iNHbFBqF681w==
  dependencies:
    "@uppy/utils" "^5.7.4"
    preact "^10.5.13"

"@uppy/instagram@^3.3.1":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@uppy/instagram/-/instagram-3.3.1.tgz"
  integrity sha512-lD1abtGslNmZ3RA4UHKYYLeRllxhCMHD60HvRdud9LL6k/M6AvlWupG8Q5jnA6c+OcWdeCdIKCkSWsF+N795LA==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.12.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/onedrive@^3.1.1", "@uppy/onedrive@^3.4.0":
  version "3.4.0"
  resolved "https://registry.npmjs.org/@uppy/onedrive/-/onedrive-3.4.0.tgz"
  integrity sha512-UXNdvL62aT8RTYCK3rUVwtWapRP5UIJad/TRmkllavr9cLkhLnAd8jZNXjru2P2l7sRO6Zcd44EwMTHij0Aa8A==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/progress-bar@^3.1.1":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@uppy/progress-bar/-/progress-bar-3.1.1.tgz"
  integrity sha512-c7Wcv6/gvrdxICnZUaU/cZG6wUtS0V/GYGssGFQ6OW84h0smuzGGA+KOh9zKqr6HBHxgKRxmCDtrlTlSSvAuQQ==
  dependencies:
    "@uppy/utils" "^5.7.5"
    preact "^10.5.13"

"@uppy/provider-views@^3.12.0", "@uppy/provider-views@^3.13.0":
  version "3.13.0"
  resolved "https://registry.npmjs.org/@uppy/provider-views/-/provider-views-3.13.0.tgz"
  integrity sha512-Z2oI88A+GC2zIPk8beoeFN/miHKkhtF58mYjvb5miGCMMZM7p7LRj98sgb5OOdKsGrfeiuTavtgL424BvcVd8w==
  dependencies:
    "@uppy/utils" "^5.9.0"
    classnames "^2.2.6"
    nanoid "^4.0.0"
    p-queue "^7.3.4"
    preact "^10.5.13"

"@uppy/react@^3.1.2":
  version "3.4.0"
  resolved "https://registry.npmjs.org/@uppy/react/-/react-3.4.0.tgz"
  integrity sha512-UIDLIDKH4Hfy8OsEipwP6Q6cAzP55k/+I63UImcqerEyFnw4OxaV1POfVnmgp2AOLph9xmi3S2KQzJYfUENbpg==
  dependencies:
    "@uppy/utils" "^5.9.0"
    prop-types "^15.6.1"

"@uppy/redux-dev-tools@^3.0.3":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@uppy/redux-dev-tools/-/redux-dev-tools-3.0.3.tgz"
  integrity sha512-WwAF+Yp+C3k0tbeLN+AnQDsZZsSpbt0nPUyCTtvUrGXK4p05tI8vEFheVmChgZKud038nZ/ULPGsKNFmuOm81Q==

"@uppy/remote-sources@^1.3.0":
  version "1.3.0"
  resolved "https://registry.npmjs.org/@uppy/remote-sources/-/remote-sources-1.3.0.tgz"
  integrity sha512-jhxnwCvj5LYCPWd7k5dGFKlBcJqITG4GhZfZaWszRDYHtpYSC5blw+5AxKkBitvERoXk/su17x3VFo8jkiCG0g==
  dependencies:
    "@uppy/box" "^2.4.0"
    "@uppy/dashboard" "^3.9.0"
    "@uppy/dropbox" "^3.4.0"
    "@uppy/facebook" "^3.3.1"
    "@uppy/google-drive" "^3.6.0"
    "@uppy/google-photos" "^0.1.0"
    "@uppy/instagram" "^3.3.1"
    "@uppy/onedrive" "^3.4.0"
    "@uppy/unsplash" "^3.3.1"
    "@uppy/url" "^3.6.1"
    "@uppy/zoom" "^2.3.1"

"@uppy/screen-capture@^3.2.0":
  version "3.2.0"
  resolved "https://registry.npmjs.org/@uppy/screen-capture/-/screen-capture-3.2.0.tgz"
  integrity sha512-bwrIpp9nlymjMVD3DpULurAUfvVUVkOfZfGWH2xtaVGZbXU1Q7rwNM1jwn4ilqSLCRWbDNi8VSy81WOruKv/9g==
  dependencies:
    "@uppy/utils" "^5.7.5"
    preact "^10.5.13"

"@uppy/status-bar@^3.3.3":
  version "3.3.3"
  resolved "https://registry.npmjs.org/@uppy/status-bar/-/status-bar-3.3.3.tgz"
  integrity sha512-TCcnBjTDbq/AmnGOcWbCpQNsv05Z6Y36zdmTCt/xNe2/gTVAYAzGRoGOrkeb6jf/E4AAi25VyOolSqL2ibB8Kw==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/utils" "^5.9.0"
    classnames "^2.2.6"
    preact "^10.5.13"

"@uppy/store-default@^3.2.2":
  version "3.2.2"
  resolved "https://registry.npmjs.org/@uppy/store-default/-/store-default-3.2.2.tgz"
  integrity sha512-OiSgT++Jj4nLK0N9WTeod3UNjCH81OXE5BcMJCd9oWzl2d0xPNq2T/E9Y6O72XVd+6Y7+tf5vZlPElutfMB3KQ==

"@uppy/store-redux@^3.0.7":
  version "3.0.7"
  resolved "https://registry.npmjs.org/@uppy/store-redux/-/store-redux-3.0.7.tgz"
  integrity sha512-6+RDxoi6YBd6kmO5bh29zeHZXSObR8xxzHNGY3MYowSgxfPTidFvDvOepWRcx9+GPIK4Gux1AIYC6R2cnUNCBQ==
  dependencies:
    nanoid "^4.0.0"

"@uppy/thumbnail-generator@^3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@uppy/thumbnail-generator/-/thumbnail-generator-3.1.0.tgz"
  integrity sha512-tDKK/cukC0CrM0F/OlHFmvpGGUq+Db4YfakhIGPKtT7ZO8aWOiIu5JIvaYUnKRxGq3RGsk4zhkxYXuoxVzzsGA==
  dependencies:
    "@uppy/utils" "^5.7.5"
    exifr "^7.0.0"

"@uppy/transloadit@^3.8.1":
  version "3.8.1"
  resolved "https://registry.npmjs.org/@uppy/transloadit/-/transloadit-3.8.1.tgz"
  integrity sha512-HpK2y8/Pbnsih+HOYvJ2plpdwtjZ8QwEwAZIPLym0yYyTT+pv/+YTXy0Y+MVR1FXzW4Z+3ficZdXMhc9omw5hg==
  dependencies:
    "@uppy/companion-client" "^3.8.2"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/tus" "^3.5.5"
    "@uppy/utils" "^5.9.0"
    component-emitter "^1.2.1"

"@uppy/tus@^3.1.0", "@uppy/tus@^3.5.5":
  version "3.5.5"
  resolved "https://registry.npmjs.org/@uppy/tus/-/tus-3.5.5.tgz"
  integrity sha512-Dcvqc897tSWRe9oiJo2ZCiyebn0G3j8FMYa99GYeLV9AeL37V/7akMAPG5ama4mTQLBXHcpziLqosTrf07ZMYQ==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/utils" "^5.9.0"
    tus-js-client "^3.1.3"

"@uppy/unsplash@^3.3.1":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@uppy/unsplash/-/unsplash-3.3.1.tgz"
  integrity sha512-MQ2l3Rg7tyc4ntqQzjsGQAff1nyIku0w96yAYXZAEWGk9GzLq4H2Te1lXgL1tauh9+5zrzglGe2GQv7A2H4wgQ==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.12.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@uppy/url@^3.6.1":
  version "3.6.1"
  resolved "https://registry.npmjs.org/@uppy/url/-/url-3.6.1.tgz"
  integrity sha512-EqnhNSHv7HYY8T9pQ3Cc7/SlSs1eD9rIoNd0GH7axPrJiR1KNwinN3EKnipIlUQgKvjpmrqAdPpHzkAyUVQqDw==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/utils" "^5.9.0"
    nanoid "^4.0.0"
    preact "^10.5.13"

"@uppy/utils@^5.7.2", "@uppy/utils@^5.7.4", "@uppy/utils@^5.7.5", "@uppy/utils@^5.9.0":
  version "5.9.0"
  resolved "https://registry.npmjs.org/@uppy/utils/-/utils-5.9.0.tgz"
  integrity sha512-9Ubddd3orCOLYjf0KobwgJ+aTrABSxk9t4X/QdM4qJHVZuMIftkaMplrViRUO+kvIBCXEZDIP2AmS060siDNGw==
  dependencies:
    lodash "^4.17.21"
    preact "^10.5.13"

"@uppy/webcam@^3.4.2":
  version "3.4.2"
  resolved "https://registry.npmjs.org/@uppy/webcam/-/webcam-3.4.2.tgz"
  integrity sha512-exp6YZN8eu8hJjJ48/O84htMwwGKLwMzuLTEWrXmu710Z3kU7Zu3+jN1PiajnRcGykXl9iwj3iXR2z1uEu+dbw==
  dependencies:
    "@uppy/utils" "^5.9.0"
    is-mobile "^3.1.1"
    preact "^10.5.13"

"@uppy/xhr-upload@^3.2.0", "@uppy/xhr-upload@^3.6.2", "@uppy/xhr-upload@^3.6.8":
  version "3.6.8"
  resolved "https://registry.npmjs.org/@uppy/xhr-upload/-/xhr-upload-3.6.8.tgz"
  integrity sha512-zr3OHrIdo08jmCqTYKS0C7o3E0XQpjtZI40wmB6VvXYzu4x/aZankG9QqKxLiY0n8KbZ9aCIvO8loxBGoL7Kaw==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/utils" "^5.9.0"

"@uppy/zoom@^2.3.1":
  version "2.3.1"
  resolved "https://registry.npmjs.org/@uppy/zoom/-/zoom-2.3.1.tgz"
  integrity sha512-rhoNua3zAXt2grzHha09N2pGKyZdnI9h/TLHU8X/LQTswdIhxNmr772AVci2R2p/IwO18xGXB/VXIkh4t3/Nww==
  dependencies:
    "@uppy/companion-client" "^3.8.1"
    "@uppy/provider-views" "^3.12.0"
    "@uppy/utils" "^5.9.0"
    preact "^10.5.13"

"@vitejs/plugin-react@^4.0.0":
  version "4.3.4"
  resolved "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.4.tgz"
  integrity sha512-SCCPBJtYLdE8PX/7ZQAs1QAZ8Jqwih+0VBLum1EGqmCCQal+MIUqLCzj3ZUy8ufbC0cAM4LRlSTm7IQJwWT4ug==
  dependencies:
    "@babel/core" "^7.26.0"
    "@babel/plugin-transform-react-jsx-self" "^7.25.9"
    "@babel/plugin-transform-react-jsx-source" "^7.25.9"
    "@types/babel__core" "^7.20.5"
    react-refresh "^0.14.2"

"@wojtekmaj/date-utils@^1.1.3":
  version "1.5.1"
  resolved "https://registry.npmjs.org/@wojtekmaj/date-utils/-/date-utils-1.5.1.tgz"
  integrity sha512-+i7+JmNiE/3c9FKxzWFi2IjRJ+KzZl1QPu6QNrsgaa2MuBgXvUy4gA1TVzf/JMdIIloB76xSKikTWuyYAIVLww==

"@yr/monotone-cubic-spline@^1.0.3":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@yr/monotone-cubic-spline/-/monotone-cubic-spline-1.0.3.tgz"
  integrity sha512-FQXkOta0XBSUPHndIKON2Y9JeQz5ZeMqLYZVVK93FliNBFm7LNMIZmY6FrMEB9XPcDbE2bekMbZD6kzDkxwYjA==

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

abs-svg-path@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/abs-svg-path/-/abs-svg-path-0.1.1.tgz"
  integrity sha512-d8XPSGjfyzlXC3Xx891DJRyZfqk5JU0BJrDQcsWomFIV1/BIzPW5HDH5iDdWpqWaav0YVIEzT1RHTwWr0FFshA==

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.9.0:
  version "8.14.0"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.14.0.tgz"
  integrity sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==

adler-32@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/adler-32/-/adler-32-1.2.0.tgz"
  integrity sha512-/vUqU/UY4MVeFsg+SsK6c+/05RZXIHZMGJA+PX5JyWI0ZRcBpupnRuPLU/NXXoFwMYCPCoxIfElM2eS+DUXCqQ==
  dependencies:
    exit-on-epipe "~1.0.1"
    printj "~1.1.0"

adler-32@~1.3.0:
  version "1.3.1"
  resolved "https://registry.npmjs.org/adler-32/-/adler-32-1.3.1.tgz"
  integrity sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

airbnb-prop-types@^2.14.0, airbnb-prop-types@^2.15.0, airbnb-prop-types@^2.16.0:
  version "2.16.0"
  resolved "https://registry.npmjs.org/airbnb-prop-types/-/airbnb-prop-types-2.16.0.tgz"
  integrity sha512-7WHOFolP/6cS96PhKNrslCLMYAI8yB1Pp6u6XmxozQOiZbsI5ycglZr5cHhBFfuRcQQjzCMith5ZPZdYiJCxUg==
  dependencies:
    array.prototype.find "^2.1.1"
    function.prototype.name "^1.1.2"
    is-regex "^1.1.0"
    object-is "^1.1.2"
    object.assign "^4.1.0"
    object.entries "^1.1.2"
    prop-types "^15.7.2"
    prop-types-exact "^1.2.0"
    react-is "^16.13.1"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

apexcharts@^3.40.0, apexcharts@>=4.0.0:
  version "3.54.1"
  resolved "https://registry.npmjs.org/apexcharts/-/apexcharts-3.54.1.tgz"
  integrity sha512-E4et0h/J1U3r3EwS/WlqJCQIbepKbp6wGUmaAwJOMjHUP4Ci0gxanLa7FR3okx6p9coi4st6J853/Cb1NP0vpA==
  dependencies:
    "@yr/monotone-cubic-spline" "^1.0.3"
    svg.draggable.js "^2.2.2"
    svg.easing.js "^2.0.0"
    svg.filter.js "^2.0.2"
    svg.pathmorphing.js "^0.1.3"
    svg.resize.js "^1.4.3"
    svg.select.js "^3.0.1"

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz"
  integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==

are-we-there-yet@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz"
  integrity sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

aria-hidden@^1.1.1:
  version "1.2.4"
  resolved "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.4.tgz"
  integrity sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==
  dependencies:
    tslib "^2.0.0"

aria-query@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/aria-query/-/aria-query-5.3.2.tgz"
  integrity sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==

array-buffer-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.1.tgz"
  integrity sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==
  dependencies:
    call-bind "^1.0.5"
    is-array-buffer "^3.0.4"

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.8"
  resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz"
  integrity sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

array.prototype.find@^2.1.1:
  version "2.2.3"
  resolved "https://registry.npmjs.org/array.prototype.find/-/array.prototype.find-2.2.3.tgz"
  integrity sha512-fO/ORdOELvjbbeIfZfzrXFMhYHGofRGqd+am9zm3tZ4GlJINj/pA2eITyfd65Vg6+ZbHd/Cys7stpoRSWtQFdA==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz"
  integrity sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.5.tgz"
  integrity sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.2.1, array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz"
  integrity sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.2.tgz"
  integrity sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz"
  integrity sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.3.tgz"
  integrity sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.2.1"
    get-intrinsic "^1.2.3"
    is-array-buffer "^3.0.4"
    is-shared-array-buffer "^1.0.2"

asap@~2.0.3:
  version "2.0.6"
  resolved "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==

ast-types-flow@^0.0.8:
  version "0.0.8"
  resolved "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.8.tgz"
  integrity sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  integrity sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==

attr-accept@^2.2.4:
  version "2.2.5"
  resolved "https://registry.npmjs.org/attr-accept/-/attr-accept-2.2.5.tgz"
  integrity sha512-0bDNnY/u6pPwHDMoF0FieU354oBi0a8rD9FcsLwzcGWbc8KS8KPIi7y+s13OlVY+gMWc/9xEMUgNE6Qm8ZllYQ==

autobind-decorator@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/autobind-decorator/-/autobind-decorator-2.4.0.tgz"
  integrity sha512-OGYhWUO72V6DafbF8PM8rm3EPbfuyMZcJhtm5/n26IDwO18pohE4eNazLoCGhPiXOCD0gEGmrbU3849QvM8bbw==

autoprefixer@^10.4.14:
  version "10.4.20"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.20.tgz"
  integrity sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==
  dependencies:
    browserslist "^4.23.3"
    caniuse-lite "^1.0.30001646"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.1"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

axe-core@^4.10.0:
  version "4.10.2"
  resolved "https://registry.npmjs.org/axe-core/-/axe-core-4.10.2.tgz"
  integrity sha512-RE3mdQ7P3FRSe7eqCWoeQ/Z9QXrtniSjp1wUjt5nRC3WIpz5rSCve6o3fsZ2aCpJtrZjSZgjwXAoTO5k4tEI0w==

axios@^1.4.0:
  version "1.7.9"
  resolved "https://registry.npmjs.org/axios/-/axios-1.7.9.tgz"
  integrity sha512-LhLcE7Hbiryz8oMDdDptSrWowmB4Bl6RCt6sIJKpRB4XtVf0iEgewX3au/pJqm+Py1kCASkb/FFKjxQaLtxJvw==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axobject-query@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/axobject-query/-/axobject-query-4.1.0.tgz"
  integrity sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  integrity sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.12"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.12.tgz"
  integrity sha512-CPWT6BwvhrTO2d8QVorhTCQw9Y43zOu7G9HigcfxvepOU6b8o3tcWad6oVgZIsZCTt42FFv97aA7ZJsbM4+8og==
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.10.6:
  version "0.10.6"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.6.tgz"
  integrity sha512-b37+KR2i/khY5sKmWNVQAnitvquQbNdWy6lJdsr0kmquCKEEUgMKK4SboVM3HtfnZilfjr4MMQ7vY58FVWDtIA==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    core-js-compat "^3.38.0"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.3"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.3.tgz"
  integrity sha512-LiWSbl4CRSIa5x/JAU6jZiG9eit9w6mz+yVMFwDE83LAWvt0AfGBoZ7HS/mkhrKuh2ZlzfVZYKoLjXdqw6Yt7Q==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"

babel-plugin-transform-react-remove-prop-types@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz"
  integrity sha512-eqj0hVcJUR57/Ug2zE1Yswsw4LhuqqHhD+8v120T1cl3kjg76QwtyBrdIk4WVwK+lAhBJVYCd/v+4nc4y+8JsA==

babel-preset-react-app@^10.0.1:
  version "10.0.1"
  resolved "https://registry.npmjs.org/babel-preset-react-app/-/babel-preset-react-app-10.0.1.tgz"
  integrity sha512-b0D9IZ1WhhCWkrTXyFuIIgqGzSkRIH5D5AmB0bXbzYAB1OBAwHcUeyWW2LorutLWF5btNo/N7r/cIdmvvKJlYg==
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/plugin-proposal-class-properties" "^7.16.0"
    "@babel/plugin-proposal-decorators" "^7.16.4"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.0"
    "@babel/plugin-proposal-numeric-separator" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-private-methods" "^7.16.0"
    "@babel/plugin-transform-flow-strip-types" "^7.16.0"
    "@babel/plugin-transform-react-display-name" "^7.16.0"
    "@babel/plugin-transform-runtime" "^7.16.4"
    "@babel/preset-env" "^7.16.4"
    "@babel/preset-react" "^7.16.0"
    "@babel/preset-typescript" "^7.16.0"
    "@babel/runtime" "^7.16.3"
    babel-plugin-macros "^3.1.0"
    babel-plugin-transform-react-remove-prop-types "^0.4.24"

babel-runtime@^6.11.6:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz"
  integrity sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  integrity sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==

base64-js@^1.1.2, base64-js@^1.3.0:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

bidi-js@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/bidi-js/-/bidi-js-1.0.3.tgz"
  integrity sha512-RKshQI1R3YQ+n9YJz2QQ147P66ELpa1FQEg20Dk8oW9t2KgLbpDLLp9aGZ7y8WHSshDknG0bknqGw5/tyCs5tw==
  dependencies:
    require-from-string "^2.0.2"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

blueimp-canvas-to-blob@^3.29.0:
  version "3.29.0"
  resolved "https://registry.npmjs.org/blueimp-canvas-to-blob/-/blueimp-canvas-to-blob-3.29.0.tgz"
  integrity sha512-0pcSSGxC0QxT+yVkivxIqW0Y4VlO2XSDPofBAqoJ1qJxgH9eiUDLv50Rixij2cDuEfx4M6DpD9UGZpRhT5Q8qg==

bootstrap@^5.2.3:
  version "5.3.3"
  resolved "https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.3.tgz"
  integrity sha512-8HLCdWgyoMguSO9o+aH+iuZ+aht+mzW0u3HIMzVu7Srrpv7EBBxTnrFlSCskwdY1+EOFQSm7uMJhNQHkdPcmjg==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

brcast@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/brcast/-/brcast-2.0.2.tgz"
  integrity sha512-Tfn5JSE7hrUlFcOoaLzVvkbgIemIorMIyoMr3TgvszWW7jFt2C9PdeMLtysYD9RU0MmU17b69+XJG1eRY2OBRg==

brotli@^1.3.2:
  version "1.3.3"
  resolved "https://registry.npmjs.org/brotli/-/brotli-1.3.3.tgz"
  integrity sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==
  dependencies:
    base64-js "^1.1.2"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  integrity sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==
  dependencies:
    pako "~1.0.5"

browserslist@^4.23.3, browserslist@^4.24.0, browserslist@^4.24.2, "browserslist@>= 4.21.0":
  version "4.24.2"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.24.2.tgz"
  integrity sha512-ZIc+Q62revdMcqC6aChtW4jz3My3klmCO1fEmINZY/8J3EpBg5/A/D0AKmBveUh6pgoeycoMkVMko84tuYS+Gg==
  dependencies:
    caniuse-lite "^1.0.30001669"
    electron-to-chromium "^1.5.41"
    node-releases "^2.0.18"
    update-browserslist-db "^1.1.1"

btoa@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz"
  integrity sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==

buffer-from@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.7.tgz"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

can-use-dom@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/can-use-dom/-/can-use-dom-0.1.0.tgz"
  integrity sha512-ceOhN1DL7Y4O6M0j9ICgmTYziV89WMd96SvSl0REd8PMgrY0B/WBOPoed5S1KUmJqXgUXh8gzSe6E3ae27upsQ==

caniuse-lite@^1.0.30001646, caniuse-lite@^1.0.30001669:
  version "1.0.30001686"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001686.tgz"
  integrity sha512-Y7deg0Aergpa24M3qLC5xjNklnKnhsmSyR/V89dLZ1n0ucJIFNs7PgR2Yfa/Zf6W79SbBicgtGxZr2juHkEUIA==

canvas@^2.11.2:
  version "2.11.2"
  resolved "https://registry.npmjs.org/canvas/-/canvas-2.11.2.tgz"
  integrity sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==
  dependencies:
    "@mapbox/node-pre-gyp" "^1.0.0"
    nan "^2.17.0"
    simple-get "^3.0.3"

canvg@^3.0.6:
  version "3.0.10"
  resolved "https://registry.npmjs.org/canvg/-/canvg-3.0.10.tgz"
  integrity sha512-qwR2FRNO9NlzTeKIPIKpnTY6fqwuYSequ8Ru8c0YkYU7U0oW+hLUvWadLvAu1Rl72OMNiFhoLu4f8eUjQ7l/+Q==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    core-js "^3.8.3"
    raf "^3.4.1"
    regenerator-runtime "^0.13.7"
    rgbcolor "^1.0.1"
    stackblur-canvas "^2.0.0"
    svg-pathdata "^6.0.3"

cfb@^1.1.4, cfb@~1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/cfb/-/cfb-1.2.2.tgz"
  integrity sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"

chalk@^4.0.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

change-emitter@^0.1.2:
  version "0.1.6"
  resolved "https://registry.npmjs.org/change-emitter/-/change-emitter-0.1.6.tgz"
  integrity sha512-YXzt1cQ4a2jqazhcuSWEOc1K2q8g9H6eWNsyZgi640LDzRWVQ2eDe+Y/kVdftH+vYdPF2rgDb3dLdpxE1jvAxw==

chart.js@^3.7.1, chart.js@>=3.0.0:
  version "3.9.1"
  resolved "https://registry.npmjs.org/chart.js/-/chart.js-3.9.1.tgz"
  integrity sha512-Ro2JbLmvg83gXF5F4sniaQ+lTbSv18E+TIf2cOeiH1Iqd2PGFOtem+DUufMZsCJwFE7ywPOpfXFBwRTGq7dh6w==

chartjs-plugin-datalabels@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/chartjs-plugin-datalabels/-/chartjs-plugin-datalabels-2.2.0.tgz"
  integrity sha512-14ZU30lH7n89oq+A4bWaJPnAG8a7ZTk7dKf48YAzMvJjQtjrgg5Dpk9f+LbjCF6bpx3RAGTeL13IXpKQYyRvlw==

chokidar@^3.5.3, chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

class-variance-authority@^0.7.1:
  version "0.7.1"
  resolved "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz"
  integrity sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==
  dependencies:
    clsx "^2.1.1"

classnames@^2.2.5, classnames@^2.2.6:
  version "2.5.1"
  resolved "https://registry.npmjs.org/classnames/-/classnames-2.5.1.tgz"
  integrity sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==

client-only@^0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz"
  integrity sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==

clone@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==

clsx@^2.0.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
  integrity sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==

cmdk@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/cmdk/-/cmdk-1.0.4.tgz"
  integrity sha512-AnsjfHyHpQ/EFeAnG216WY7A5LiYCoZzCSygiLvfXC3H3LFGCprErteUcszaVluGOhuOTbJS3jWHrSDYPBBygg==
  dependencies:
    "@radix-ui/react-dialog" "^1.1.2"
    "@radix-ui/react-id" "^1.1.0"
    "@radix-ui/react-primitive" "^2.0.0"
    use-sync-external-store "^1.2.2"

codepage@~1.14.0:
  version "1.14.0"
  resolved "https://registry.npmjs.org/codepage/-/codepage-1.14.0.tgz"
  integrity sha512-iz3zJLhlrg37/gYRWgEPkaFTtzmnEv1h+r7NgZum2lFElYQPi0/5bnmuDfODHxfp0INEfnRqyfyeIJDbb7ahRw==
  dependencies:
    commander "~2.14.1"
    exit-on-epipe "~1.0.1"

codepage@~1.15.0:
  version "1.15.0"
  resolved "https://registry.npmjs.org/codepage/-/codepage-1.15.0.tgz"
  integrity sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@^1.1.4, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color-support@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz"
  integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==

combine-errors@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/combine-errors/-/combine-errors-3.0.3.tgz"
  integrity sha512-C8ikRNRMygCwaTx+Ek3Yr+OuZzgZjduCOfSQBjbM8V3MfgcjSTeto/GXP6PAwKvJz/v15b7GHZvx5rOlczFw/Q==
  dependencies:
    custom-error-instance "2.1.1"
    lodash.uniqby "4.5.0"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

commander@~2.14.1:
  version "2.14.1"
  resolved "https://registry.npmjs.org/commander/-/commander-2.14.1.tgz"
  integrity sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw==

commander@~2.17.1:
  version "2.17.1"
  resolved "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz"
  integrity sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==

component-emitter@^1.2.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.1.tgz"
  integrity sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==

compressorjs@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/compressorjs/-/compressorjs-1.2.1.tgz"
  integrity sha512-+geIjeRnPhQ+LLvvA7wxBQE5ddeLU7pJ3FsKFWirDw6veY3s9iLxAQEw7lXGHnhCJvBujEQWuNnGzZcvCvdkLQ==
  dependencies:
    blueimp-canvas-to-blob "^3.29.0"
    is-blob "^2.1.0"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

confusing-browser-globals@^1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"
  integrity sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==

console-control-strings@^1.0.0, console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

"consolidated-events@^1.1.1 || ^2.0.0":
  version "2.0.2"
  resolved "https://registry.npmjs.org/consolidated-events/-/consolidated-events-2.0.2.tgz"
  integrity sha512-2/uRVMdRypf5z/TW/ncD/66l75P5hH2vM/GR8Jf8HLc2xnfJtmina6F6du8+v4Z2vTrMo7jC+W1tmEEuuELgkQ==

convert-source-map@^1.5.0:
  version "1.9.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

core-js-compat@^3.38.0, core-js-compat@^3.38.1:
  version "3.39.0"
  resolved "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.39.0.tgz"
  integrity sha512-VgEUx3VwlExr5no0tXlBt+silBvhTryPwCXRI2Id1PN8WTKu7MreethvddqOubrYxkFdv/RnYrqlv1sFNAUelw==
  dependencies:
    browserslist "^4.24.2"

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz"
  integrity sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA==

core-js@^2.4.0:
  version "2.6.12"
  resolved "https://registry.npmjs.org/core-js/-/core-js-2.6.12.tgz"
  integrity sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==

core-js@^3.28.0, core-js@^3.6.0, core-js@^3.8.3:
  version "3.39.0"
  resolved "https://registry.npmjs.org/core-js/-/core-js-3.39.0.tgz"
  integrity sha512-raM0ew0/jJUqkJ0E6e8UDtl+y/7ktFivgWvqw8dNSQeNWoSDLvQ1H/RN3aPXB9tBd4/FhyR4RDPGhsNIMsAn7g==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

crc-32@~1.2.0, crc-32@~1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/crc-32/-/crc-32-1.2.2.tgz"
  integrity sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==

cropperjs@1.5.7:
  version "1.5.7"
  resolved "https://registry.npmjs.org/cropperjs/-/cropperjs-1.5.7.tgz"
  integrity sha512-sGj+G/ofKh+f6A4BtXLJwtcKJgMUsXYVUubfTo9grERiDGXncttefmue/fyQFvn8wfdyoD1KhDRYLfjkJFl0yw==

cross-fetch@^3.1.5:
  version "3.1.8"
  resolved "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.8.tgz"
  integrity sha512-cvA+JwZoU0Xq+h6WkMvAUqPEYy92Obet6UdKLfW60qn99ftItKjB5T+BkyWOFWe2pUyfQ+IJHmpOTznqk1M6Kg==
  dependencies:
    node-fetch "^2.6.12"

cross-spawn@^7.0.0, cross-spawn@^7.0.2:
  version "7.0.6"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz"
  integrity sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz"
  integrity sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==
  dependencies:
    utrie "^1.0.2"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==

csstype@^3.0.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

custom-error-instance@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/custom-error-instance/-/custom-error-instance-2.1.1.tgz"
  integrity sha512-p6JFxJc3M4OTD2li2qaHkDCw9SfMw82Ldr6OC9Je1aXiGfhx2W8p3GaoeaGrPJTUN9NirTM/KTxHWMUdR1rsUg==

custom-event-polyfill@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/custom-event-polyfill/-/custom-event-polyfill-1.0.7.tgz"
  integrity sha512-TDDkd5DkaZxZFM8p+1I3yAlvM3rSr1wbrOliG4yJiwinMZN8z/iGL7BTlDkrJcYTmgUSb4ywVCc3ZaUtOtC76w==

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
  integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==

data-view-buffer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.1.tgz"
  integrity sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.1.tgz"
  integrity sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.0.tgz"
  integrity sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4, debug@4:
  version "4.3.7"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  integrity sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==
  dependencies:
    ms "^2.1.3"

decompress-response@^4.2.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/decompress-response/-/decompress-response-4.2.1.tgz"
  integrity sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==
  dependencies:
    mimic-response "^2.0.0"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deepmerge@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz"
  integrity sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.2, define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

dequal@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz"
  integrity sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==

detect-autofill@^1.1.3:
  version "1.1.4"
  resolved "https://registry.npmjs.org/detect-autofill/-/detect-autofill-1.1.4.tgz"
  integrity sha512-utCBQwCR/beSnADQmBC7C4tTueBBkYCl6WSpfGUkYKO/+MzPxqYGj6G4MvHzcKmH1gCTK+VunX2vaagvkRXPvA==
  dependencies:
    custom-event-polyfill "^1.0.7"

detect-libc@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.3.tgz"
  integrity sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  integrity sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==

dfa@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/dfa/-/dfa-1.2.0.tgz"
  integrity sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q==

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

direction@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/direction/-/direction-1.0.4.tgz"
  integrity sha512-GYqKi1aH7PJXxdhTeZBFrg8vUBeKXi+cNprXsC1kpJcbcVnV9wBsrOu1cQEdG0WeQwlfHiy3XvnKfIrJ2R0NzQ==

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

document.contains@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/document.contains/-/document.contains-1.0.2.tgz"
  integrity sha512-YcvYFs15mX8m3AO1QNQy3BlIpSMfNRj3Ujk2BEJxsZG+HZf7/hZ6jr7mDpXrF8q+ff95Vef5yjhiZxm8CGJr6Q==
  dependencies:
    define-properties "^1.1.3"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dompurify@^2.5.4:
  version "2.5.7"
  resolved "https://registry.npmjs.org/dompurify/-/dompurify-2.5.7.tgz"
  integrity sha512-2q4bEI+coQM8f5ez7kt2xclg1XsecaV9ASJk/54vwlfRRNQfDqJz2pzQ8t0Ix/ToBpXlVjrRIx7pFC/o8itG2Q==

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

electron-to-chromium@^1.5.41:
  version "1.5.68"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.68.tgz"
  integrity sha512-FgMdJlma0OzUYlbrtZ4AeXjKxKPk6KT8WOP8BjcqxWtlg8qyJQjRzPJzUtUn5GBg1oQ26hFs7HOOHJMYiJRnvQ==

emoji-mart@^5.2, emoji-mart@5.6.0:
  version "5.6.0"
  resolved "https://registry.npmjs.org/emoji-mart/-/emoji-mart-5.6.0.tgz"
  integrity sha512-eJp3QRe79pjwa+duv+n7+5YsNhRcMl812EcFVwrnRvYKoNPoQb5qxU8DG6Bgwji0akHdp6D4Ln6tYLG58MFSow==

emoji-picker-textarea@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/emoji-picker-textarea/-/emoji-picker-textarea-1.0.1.tgz"
  integrity sha512-EVUhcdw9Y7T369NRay4a7XYbAPVZ0DR65x7oTjIt/goAV0pgsE69exMhOnI9C8ckpotAYLmSHG0nkim8c6BwXw==

emoji-regex@^10.3.0:
  version "10.4.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.4.0.tgz"
  integrity sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

encoding@^0.1.0, encoding@^0.1.11:
  version "0.1.13"
  resolved "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz"
  integrity sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==
  dependencies:
    iconv-lite "^0.6.2"

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmjs.org/enquire.js/-/enquire.js-2.1.6.tgz"
  integrity sha512-/KujNpO+PT63F7Hlpu4h3pE3TokKRHN26JYmQpPyjkRD/N57R7bPDNojMXdi7uveAKjYB7yQnartCxZnFWr0Xw==

enzyme-shallow-equal@^1.0.0:
  version "1.0.7"
  resolved "https://registry.npmjs.org/enzyme-shallow-equal/-/enzyme-shallow-equal-1.0.7.tgz"
  integrity sha512-/um0GFqUXnpM9SvKtje+9Tjoz3f1fpBC3eXRFrNs8kpYn69JljciYP7KZTqM/YQbUY9KUjvKB4jo/q+L6WGGvg==
  dependencies:
    hasown "^2.0.0"
    object-is "^1.1.5"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.5, es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.23.0, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5:
  version "1.23.5"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.5.tgz"
  integrity sha512-vlmniQ0WNPwXqA0BnmwV3Ng7HxiGlh6r5U6JcTMNx8OilcAGqVJBHJcPjqOMaczU9fRuRK5Px2BdVyPRnKMMVQ==
  dependencies:
    array-buffer-byte-length "^1.0.1"
    arraybuffer.prototype.slice "^1.0.3"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    data-view-buffer "^1.0.1"
    data-view-byte-length "^1.0.1"
    data-view-byte-offset "^1.0.0"
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.0.3"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.4"
    get-symbol-description "^1.0.2"
    globalthis "^1.0.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    hasown "^2.0.2"
    internal-slot "^1.0.7"
    is-array-buffer "^3.0.4"
    is-callable "^1.2.7"
    is-data-view "^1.0.1"
    is-negative-zero "^2.0.3"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.3"
    is-string "^1.0.7"
    is-typed-array "^1.1.13"
    is-weakref "^1.0.2"
    object-inspect "^1.13.3"
    object-keys "^1.1.1"
    object.assign "^4.1.5"
    regexp.prototype.flags "^1.5.3"
    safe-array-concat "^1.1.2"
    safe-regex-test "^1.0.3"
    string.prototype.trim "^1.2.9"
    string.prototype.trimend "^1.0.8"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.2"
    typed-array-byte-length "^1.0.1"
    typed-array-byte-offset "^1.0.2"
    typed-array-length "^1.0.6"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.15"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.2.1, es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-iterator-helpers@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.2.0.tgz"
  integrity sha512-tpxqxncxnpw3c93u8n3VOzACmRFoVmWJqbWXvX/JfKbkhBw1oslgPrUfeSt2psuqyEJFD6N/9lg5i7bsKpoq+Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    globalthis "^1.0.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    internal-slot "^1.0.7"
    iterator.prototype "^1.1.3"
    safe-array-concat "^1.1.2"

es-object-atoms@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.0.0.tgz"
  integrity sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.1, es-set-tostringtag@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz"
  integrity sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==
  dependencies:
    get-intrinsic "^1.2.4"
    has-tostringtag "^1.0.2"
    hasown "^2.0.1"

es-shim-unscopables@^1.0.0, es-shim-unscopables@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.0.2.tgz"
  integrity sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz"
  integrity sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

esbuild@^0.18.10:
  version "0.18.20"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.18.20.tgz"
  integrity sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==
  optionalDependencies:
    "@esbuild/android-arm" "0.18.20"
    "@esbuild/android-arm64" "0.18.20"
    "@esbuild/android-x64" "0.18.20"
    "@esbuild/darwin-arm64" "0.18.20"
    "@esbuild/darwin-x64" "0.18.20"
    "@esbuild/freebsd-arm64" "0.18.20"
    "@esbuild/freebsd-x64" "0.18.20"
    "@esbuild/linux-arm" "0.18.20"
    "@esbuild/linux-arm64" "0.18.20"
    "@esbuild/linux-ia32" "0.18.20"
    "@esbuild/linux-loong64" "0.18.20"
    "@esbuild/linux-mips64el" "0.18.20"
    "@esbuild/linux-ppc64" "0.18.20"
    "@esbuild/linux-riscv64" "0.18.20"
    "@esbuild/linux-s390x" "0.18.20"
    "@esbuild/linux-x64" "0.18.20"
    "@esbuild/netbsd-x64" "0.18.20"
    "@esbuild/openbsd-x64" "0.18.20"
    "@esbuild/sunos-x64" "0.18.20"
    "@esbuild/win32-arm64" "0.18.20"
    "@esbuild/win32-ia32" "0.18.20"
    "@esbuild/win32-x64" "0.18.20"

escalade@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-config-react-app@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/eslint-config-react-app/-/eslint-config-react-app-7.0.1.tgz"
  integrity sha512-K6rNzvkIeHaTd8m/QEh1Zko0KI7BACWkkneSs6s9cKZC/J27X3eZR6Upt1jkmZ/4FK+XUOPPxMEN7+lbUXfSlA==
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/eslint-parser" "^7.16.3"
    "@rushstack/eslint-patch" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^5.5.0"
    "@typescript-eslint/parser" "^5.5.0"
    babel-preset-react-app "^10.0.1"
    confusing-browser-globals "^1.0.11"
    eslint-plugin-flowtype "^8.0.3"
    eslint-plugin-import "^2.25.3"
    eslint-plugin-jest "^25.3.0"
    eslint-plugin-jsx-a11y "^6.5.1"
    eslint-plugin-react "^7.27.1"
    eslint-plugin-react-hooks "^4.3.0"
    eslint-plugin-testing-library "^5.0.1"

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
  integrity sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.12.0:
  version "2.12.0"
  resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.0.tgz"
  integrity sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==
  dependencies:
    debug "^3.2.7"

eslint-plugin-flowtype@^8.0.3:
  version "8.0.3"
  resolved "https://registry.npmjs.org/eslint-plugin-flowtype/-/eslint-plugin-flowtype-8.0.3.tgz"
  integrity sha512-dX8l6qUL6O+fYPtpNRideCFSpmWOUVx5QcaGLVqe/vlDiBSe4vYljDWDETwnyFzpl7By/WVIu6rcrniCgH9BqQ==
  dependencies:
    lodash "^4.17.21"
    string-natural-compare "^3.0.1"

eslint-plugin-import@^2.25.3:
  version "2.31.0"
  resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.31.0.tgz"
  integrity sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==
  dependencies:
    "@rtsao/scc" "^1.1.0"
    array-includes "^3.1.8"
    array.prototype.findlastindex "^1.2.5"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.12.0"
    hasown "^2.0.2"
    is-core-module "^2.15.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    object.groupby "^1.0.3"
    object.values "^1.2.0"
    semver "^6.3.1"
    string.prototype.trimend "^1.0.8"
    tsconfig-paths "^3.15.0"

eslint-plugin-jest@^25.3.0:
  version "25.7.0"
  resolved "https://registry.npmjs.org/eslint-plugin-jest/-/eslint-plugin-jest-25.7.0.tgz"
  integrity sha512-PWLUEXeeF7C9QGKqvdSbzLOiLTx+bno7/HC9eefePfEb257QFHg7ye3dh80AZVkaa/RQsBB1Q/ORQvg2X7F0NQ==
  dependencies:
    "@typescript-eslint/experimental-utils" "^5.0.0"

eslint-plugin-jsx-a11y@^6.5.1:
  version "6.10.2"
  resolved "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.10.2.tgz"
  integrity sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==
  dependencies:
    aria-query "^5.3.2"
    array-includes "^3.1.8"
    array.prototype.flatmap "^1.3.2"
    ast-types-flow "^0.0.8"
    axe-core "^4.10.0"
    axobject-query "^4.1.0"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    hasown "^2.0.2"
    jsx-ast-utils "^3.3.5"
    language-tags "^1.0.9"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    safe-regex-test "^1.0.3"
    string.prototype.includes "^2.0.1"

eslint-plugin-react-hooks@^4.3.0:
  version "4.6.2"
  resolved "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz"
  integrity sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==

eslint-plugin-react@^7.27.1:
  version "7.37.2"
  resolved "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.37.2.tgz"
  integrity sha512-EsTAnj9fLVr/GZleBLFbj/sSuXeWmp1eXIN60ceYnZveqEaUCyW4X+Vh4WTdUhCkW4xutXYqTXCUSyqD4rB75w==
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.2"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.1.0"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.8"
    object.fromentries "^2.0.8"
    object.values "^1.2.0"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.11"
    string.prototype.repeat "^1.0.0"

eslint-plugin-testing-library@^5.0.1:
  version "5.11.1"
  resolved "https://registry.npmjs.org/eslint-plugin-testing-library/-/eslint-plugin-testing-library-5.11.1.tgz"
  integrity sha512-5eX9e1Kc2PqVRed3taaLnAAqPZGEX75C+M/rXzUAI3wIg/ZxzUm1OVAwfe/O+vE+6YXOLetSe9g5GKD2ecXipw==
  dependencies:
    "@typescript-eslint/utils" "^5.58.0"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint@*, "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0", "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^7.5.0 || ^8.0.0", "eslint@^7.5.0 || ^8.0.0 || ^9.0.0", eslint@^8.0.0, eslint@^8.1.0, eslint@^8.36.0, eslint@>=7:
  version "8.57.1"
  resolved "https://registry.npmjs.org/eslint/-/eslint-8.57.1.tgz"
  integrity sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.2:
  version "1.6.0"
  resolved "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz"
  integrity sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

exifr@^7.0.0:
  version "7.1.3"
  resolved "https://registry.npmjs.org/exifr/-/exifr-7.1.3.tgz"
  integrity sha512-g/aje2noHivrRSLbAUtBPWFbxKdKhgj/xr1vATDdUXPOFYJlQ62Ft0oy+72V6XLIpDJfHs6gXLbBLAolqOXYRw==

exit-on-epipe@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz"
  integrity sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw==

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^3.2.12, fast-glob@^3.2.9, fast-glob@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz"
  integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
  dependencies:
    reusify "^1.0.4"

fbjs@^0.8.1:
  version "0.8.18"
  resolved "https://registry.npmjs.org/fbjs/-/fbjs-0.8.18.tgz"
  integrity sha512-EQaWFK+fEPSoibjNy8IxUtaFOMXcWsY0JaVrQoZR9zC8N2Ygf9iDITPWjUTVIax95b6I742JFLqASHfsag/vKA==
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.30"

fflate@^0.3.8:
  version "0.3.11"
  resolved "https://registry.npmjs.org/fflate/-/fflate-0.3.11.tgz"
  integrity sha512-Rr5QlUeGN1mbOHlaqcSYMKVpPbgLy0AWT/W0EHxA6NGI12yO1jpoui2zBBvU2G824ltM6Ut8BFgfHSBGfkmS0A==

fflate@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz"
  integrity sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A==

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

file-selector@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/file-selector/-/file-selector-2.1.2.tgz"
  integrity sha512-QgXo+mXTe8ljeqUFaX3QVHc5osSItJ/Km+xpocx0aSqWGMSCf6qYs/VnzZgS864Pjn5iceMRFigeAV7AfTlaig==
  dependencies:
    tslib "^2.7.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz"
  integrity sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz"
  integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.2"
  resolved "https://registry.npmjs.org/flatted/-/flatted-3.3.2.tgz"
  integrity sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==

follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

fontkit@^2.0.2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/fontkit/-/fontkit-2.0.4.tgz"
  integrity sha512-syetQadaUEDNdxdugga9CpEYVaQIxOwk7GlwZWWZ19//qW4zE5bknOKeMBDYAASwnpaSHKJITRLMF9m1fp3s6g==
  dependencies:
    "@swc/helpers" "^0.5.12"
    brotli "^1.3.2"
    clone "^2.1.2"
    dfa "^1.2.0"
    fast-deep-equal "^3.1.3"
    restructure "^3.0.0"
    tiny-inflate "^1.0.3"
    unicode-properties "^1.4.0"
    unicode-trie "^2.0.0"

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.0.tgz"
  integrity sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.1.tgz"
  integrity sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

frac@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/frac/-/frac-1.1.2.tgz"
  integrity sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

framer-motion@^10.12.9:
  version "10.18.0"
  resolved "https://registry.npmjs.org/framer-motion/-/framer-motion-10.18.0.tgz"
  integrity sha512-oGlDh1Q1XqYPksuTD/usb0I70hq95OUzmL9+6Zd+Hs4XV0oaISBa/UUMSjYiq6m8EUF32132mOJ8xVZS+I0S6w==
  dependencies:
    tslib "^2.4.0"
  optionalDependencies:
    "@emotion/is-prop-valid" "^0.8.2"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.2, function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.6.tgz"
  integrity sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

gauge@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/gauge/-/gauge-3.0.2.tgz"
  integrity sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.2"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.1"
    object-assign "^4.1.1"
    signal-exit "^3.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.2"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-form-data@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/get-form-data/-/get-form-data-3.0.0.tgz"
  integrity sha512-1d53Kn08wlPuLu31/boF1tW2WRYKw3xAWae3mqcjqpDjoqVBtXolbQnudbbEFyFWL7+2SLGRAFdotxNY06V7MA==

get-intrinsic@^1.2.1, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-nonce@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
  integrity sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==

get-symbol-description@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.2.tgz"
  integrity sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==
  dependencies:
    call-bind "^1.0.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"

get-user-locale@^2.2.1:
  version "2.3.2"
  resolved "https://registry.npmjs.org/get-user-locale/-/get-user-locale-2.3.2.tgz"
  integrity sha512-O2GWvQkhnbDoWFUJfaBlDIKUEdND8ATpBXD6KXcbhxlfktyD/d8w6mkzM/IlQEqGZAMz/PW6j6Hv53BiigKLUQ==
  dependencies:
    mem "^8.0.0"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob@^10.3.10:
  version "10.4.5"
  resolved "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  integrity sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3:
  version "7.2.3"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-cache@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/global-cache/-/global-cache-1.2.1.tgz"
  integrity sha512-EOeUaup5DgWKlCMhA9YFqNRIlZwoxt731jCh47WBV9fQqHgXhr3Fa55hfgIUqilIcPsfdNKN7LHjrNY+Km40KA==
  dependencies:
    define-properties "^1.1.2"
    is-symbol "^1.0.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.19.0:
  version "13.24.0"
  resolved "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3, globalthis@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz"
  integrity sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

google-maps-infobox@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/google-maps-infobox/-/google-maps-infobox-2.0.0.tgz"
  integrity sha512-hTuWmWZZSOxf5D/z7l3/hTF1grgRvLG53BEKMdjiKOG+FcK/kH7vqseUeyIU9Zj2ZIqKTOaro0nknxpAuRq4Vw==

gopd@^1.0.1, gopd@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz"
  integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1, has-proto@^1.0.3:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.1.0.tgz"
  integrity sha512-QLdzI9IIO1Jg7f9GT1gXpPpXArAn6cS31R1eEZqz08Gc+uQ8/XiqHWt17Fiw+2p6oTTIq5GXEpQkAlA88YRl/Q==
  dependencies:
    call-bind "^1.0.7"

has-symbols@^1.0.3:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

hasown@^2.0.0, hasown@^2.0.1, hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hoist-non-react-statics@^2.3.1:
  version "2.5.5"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz"
  integrity sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw==

hoist-non-react-statics@^3.2.1, hoist-non-react-statics@^3.3.1, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

hsl-to-hex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/hsl-to-hex/-/hsl-to-hex-1.0.0.tgz"
  integrity sha512-K6GVpucS5wFf44X0h2bLVRDsycgJmf9FF2elg+CrqD8GcFU8c6vYhgXn8NjUkFCwj+xDFb70qgLbTUm6sxwPmA==
  dependencies:
    hsl-to-rgb-for-reals "^1.1.0"

hsl-to-rgb-for-reals@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/hsl-to-rgb-for-reals/-/hsl-to-rgb-for-reals-1.1.1.tgz"
  integrity sha512-LgOWAkrN0rFaQpfdWBQlv/VhkOxb5AsBjk6NQVx4yEzWS923T07X0M1Y0VNko2H52HeSpZrZNNMJ0aFqsdVzQg==

html-to-image@^1.11.11:
  version "1.11.11"
  resolved "https://registry.npmjs.org/html-to-image/-/html-to-image-1.11.11.tgz"
  integrity sha512-9gux8QhvjRO/erSnDPv28noDZcPZmYE7e1vFsBLKLlRlKDSqNJYebj6Qz1TGd5lsRV+X+xYyjCKjuZdABinWjA==

html2canvas@^1.0.0-rc.5, html2canvas@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz"
  integrity sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

hyphen@^1.6.4:
  version "1.10.6"
  resolved "https://registry.npmjs.org/hyphen/-/hyphen-1.10.6.tgz"
  integrity sha512-fXHXcGFTXOvZTSkPJuGOQf5Lv5T/R2itiiCVPg9LxAje5D00O0pP83yJShFq5V89Ly//Gt6acj7z8pbBr34stw==

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz"
  integrity sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==

immediate@~3.0.5:
  version "3.0.6"
  resolved "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  integrity sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==

import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@~2.0.3, inherits@2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

internal-slot@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.7.tgz"
  integrity sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.0"
    side-channel "^1.0.4"

invariant@^2.2.1:
  version "2.2.4"
  resolved "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
  dependencies:
    loose-envify "^1.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-array-buffer@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.4.tgz"
  integrity sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-async-function@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-async-function/-/is-async-function-2.0.0.tgz"
  integrity sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==
  dependencies:
    has-tostringtag "^1.0.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz"
  integrity sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-blob@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-blob/-/is-blob-2.1.0.tgz"
  integrity sha512-SZ/fTft5eUhQM6oF/ZaASFDEdbFVe89Imltn9uZr03wdKMcWNVYSMjQPFtg05QuNkt5l5c135ElvXEQG0rk4tw==

is-boolean-object@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.0.tgz"
  integrity sha512-kR5g0+dXf/+kXnqI+lu0URKYPKgICtHGGNCDSB10AaUFj3o/HkB3u7WfpRBJGFopxxY0oH3ux7ZsDjLtK7xqvw==
  dependencies:
    call-bind "^1.0.7"
    has-tostringtag "^1.0.2"

is-callable@^1.1.3, is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.13.0, is-core-module@^2.15.1:
  version "2.15.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.15.1.tgz"
  integrity sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.1.tgz"
  integrity sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==
  dependencies:
    is-typed-array "^1.1.13"

is-date-object@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-finalizationregistry@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.0.tgz"
  integrity sha512-qfMdqbAQEwBw78ZyReKnlA8ezmPdb9BemzIIip/JkjaZUhitfXDkkr+3QTboW0JrSXT1QWyYShpvnNHGZ4c4yA==
  dependencies:
    call-bind "^1.0.7"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-function@^1.0.10, is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz"
  integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-map@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz"
  integrity sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==

is-mobile@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/is-mobile/-/is-mobile-3.1.1.tgz"
  integrity sha512-RRoXXR2HNFxNkUnxtaBdGBXtFlUMFa06S0NUKf/LCF+MuGLu13gi9iBCkoEmc6+rpXuwi5Mso5V8Zf7mNynMBQ==

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz"
  integrity sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==

is-network-error@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-network-error/-/is-network-error-1.1.0.tgz"
  integrity sha512-tUdRRAnhT+OtCZR/LxZelH/C7QtjtFrTu5tXCA8pl55eTUElUHT+GPYV8MBMBvea/j+NxQqVt3LbWMRir7Gx9g==

is-number-object@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.0.tgz"
  integrity sha512-KVSZV0Dunv9DTPkhXwcZ3Q+tUc9TsaE1ZwX5J2WMvsSGS6Md8TFPun5uwh0yRdrNerI6vf/tbJxqSx4c1ZI1Lw==
  dependencies:
    call-bind "^1.0.7"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-regex@^1.1.0, is-regex@^1.1.4:
  version "1.2.0"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.2.0.tgz"
  integrity sha512-B6ohK4ZmoftlUe+uvenXSbPJFo6U37BH7oO1B3nQH8f/7h27N56s85MhUtbFJAziz5dcmuR3i8ovUl35zp8pFA==
  dependencies:
    call-bind "^1.0.7"
    gopd "^1.1.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz"
  integrity sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==

is-shallow-equal@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-shallow-equal/-/is-shallow-equal-1.0.1.tgz"
  integrity sha512-lq5RvK+85Hs5J3p4oA4256M1FEffzmI533ikeDHvJd42nouRRx5wBzt36JuviiGe5dIPyHON/d0/Up+PBo6XkQ==

is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.3.tgz"
  integrity sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==
  dependencies:
    call-bind "^1.0.7"

is-stream@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
  integrity sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-string@^1.0.7, is-string@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.1.0.tgz"
  integrity sha512-PlfzajuF9vSo5wErv3MJAKD/nqf9ngAs1NFQYm16nUYFO2IzxJ2hcm+IOCg+EEopdykNNUhVq5cz35cAUxU8+g==
  dependencies:
    call-bind "^1.0.7"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.1, is-symbol@^1.0.4, is-symbol@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.0.tgz"
  integrity sha512-qS8KkNNXUZ/I+nX6QT8ZS1/Yx0A444yhzdTKxCzKkNjQ9sHErBxJnJAgh+f5YhusYECEcjo4XcyH87hn6+ks0A==
  dependencies:
    call-bind "^1.0.7"
    has-symbols "^1.0.3"
    safe-regex-test "^1.0.3"

is-touch-device@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-touch-device/-/is-touch-device-1.0.1.tgz"
  integrity sha512-LAYzo9kMT1b2p19L/1ATGt2XcSilnzNlyvq6c0pbPRVisLbAPpLqr53tIJS00kvrTkj0HtR8U7+u8X0yR8lPSw==

is-typed-array@^1.1.13, is-typed-array@^1.1.3:
  version "1.1.13"
  resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.13.tgz"
  integrity sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==
  dependencies:
    which-typed-array "^1.1.14"

is-url@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/is-url/-/is-url-1.2.4.tgz"
  integrity sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww==

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz"
  integrity sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

is-weakset@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.3.tgz"
  integrity sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz"
  integrity sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA==
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

iterator.prototype@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.3.tgz"
  integrity sha512-FW5iMbeQ6rBGm/oKgzq2aW4KvAGpxPzYES8N4g4xNXUKpL1mclMvOe+76AcLDTvD+Ze+sOpVhgdAQEKF4L9iGQ==
  dependencies:
    define-properties "^1.2.1"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    reflect.getprototypeof "^1.0.4"
    set-function-name "^2.0.1"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jay-peg@^1.0.2:
  version "1.1.1"
  resolved "https://registry.npmjs.org/jay-peg/-/jay-peg-1.1.1.tgz"
  integrity sha512-D62KEuBxz/ip2gQKOEhk/mx14o7eiFRaU+VNNSP4MOiIkwb/D6B3G1Mfas7C/Fit8EsSV2/IWjZElx/Gs6A4ww==
  dependencies:
    restructure "^3.0.0"

jiti@^1.17.2, jiti@^1.21.6:
  version "1.21.6"
  resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.6.tgz"
  integrity sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==

jodit-react@^1.3.39:
  version "1.3.39"
  resolved "https://registry.npmjs.org/jodit-react/-/jodit-react-1.3.39.tgz"
  integrity sha512-uZSRadPlgvcB6Wn54RGZ6XQEN+JHj0dB1SI4OH42yTC5WTeoYavksUwzCgFuQF4+janjSCxn0CXNqzd0MJEhVw==
  dependencies:
    jodit "^3.20.2"

jodit@^3.20.2:
  version "3.24.9"
  resolved "https://registry.npmjs.org/jodit/-/jodit-3.24.9.tgz"
  integrity sha512-t2d73v7GFbGI08ZzdCTwrzi8ZtoOG4icGzD6IIN2e+ghlpUKYxcHZ/Rmy6/mc6055172z+tv7QSr9BcOz6IOFQ==
  dependencies:
    autobind-decorator "^2.4.0"
    core-js "^3.28.0"

jquery@>=1.8.0:
  version "3.7.1"
  resolved "https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz"
  integrity sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg==

js-base64@^3.7.2:
  version "3.7.7"
  resolved "https://registry.npmjs.org/js-base64/-/js-base64-3.7.7.tgz"
  integrity sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2, jsesc@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz"
  integrity sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json2mq@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/json2mq/-/json2mq-0.2.0.tgz"
  integrity sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==
  dependencies:
    string-convert "^0.2.0"

json5@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jspdf-autotable@^3.8.1:
  version "3.8.4"
  resolved "https://registry.npmjs.org/jspdf-autotable/-/jspdf-autotable-3.8.4.tgz"
  integrity sha512-rSffGoBsJYX83iTRv8Ft7FhqfgEL2nLpGAIiqruEQQ3e4r0qdLFbPUB7N9HAle0I3XgpisvyW751VHCqKUVOgQ==

jspdf@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmjs.org/jspdf/-/jspdf-2.5.2.tgz"
  integrity sha512-myeX9c+p7znDWPk0eTrujCzNjT+CXdXyk7YmJq5nD5V7uLLKmSXnlQ/Jn/kuo3X09Op70Apm0rQSnFWyGK8uEQ==
  dependencies:
    "@babel/runtime" "^7.23.2"
    atob "^2.1.2"
    btoa "^1.2.1"
    fflate "^0.8.1"
  optionalDependencies:
    canvg "^3.0.6"
    core-js "^3.6.0"
    dompurify "^2.5.4"
    html2canvas "^1.0.0-rc.5"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
  version "3.3.5"
  resolved "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
  integrity sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

jszip@^3.10.1:
  version "3.10.1"
  resolved "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz"
  integrity sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

language-subtag-registry@^0.3.20:
  version "0.3.23"
  resolved "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.23.tgz"
  integrity sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==

language-tags@^1.0.9:
  version "1.0.9"
  resolved "https://registry.npmjs.org/language-tags/-/language-tags-1.0.9.tgz"
  integrity sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==
  dependencies:
    language-subtag-registry "^0.3.20"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lie@~3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  integrity sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==
  dependencies:
    immediate "~3.0.5"

lilconfig@^2.0.5, lilconfig@^2.0.6:
  version "2.1.0"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz"
  integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz"
  integrity sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash._baseiteratee@~4.7.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/lodash._baseiteratee/-/lodash._baseiteratee-4.7.0.tgz"
  integrity sha512-nqB9M+wITz0BX/Q2xg6fQ8mLkyfF7MU7eE+MNBNjTHFKeKaZAPEzEg+E8LWxKWf1DQVflNEn9N49yAuqKh2mWQ==
  dependencies:
    lodash._stringtopath "~4.8.0"

lodash._basetostring@~4.12.0:
  version "4.12.0"
  resolved "https://registry.npmjs.org/lodash._basetostring/-/lodash._basetostring-4.12.0.tgz"
  integrity sha512-SwcRIbyxnN6CFEEK4K1y+zuApvWdpQdBHM/swxP962s8HIxPO3alBH5t3m/dl+f4CMUug6sJb7Pww8d13/9WSw==

lodash._baseuniq@~4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash._baseuniq/-/lodash._baseuniq-4.6.0.tgz"
  integrity sha512-Ja1YevpHZctlI5beLA7oc5KNDhGcPixFhcqSiORHNsp/1QTv7amAXzw+gu4YOvErqVlMVyIJGgtzeepCnnur0A==
  dependencies:
    lodash._createset "~4.0.0"
    lodash._root "~3.0.0"

lodash._createset@~4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/lodash._createset/-/lodash._createset-4.0.3.tgz"
  integrity sha512-GTkC6YMprrJZCYU3zcqZj+jkXkrXzq3IPBcF/fIPpNEAB4hZEtXU8zp/RwKOvZl43NUmwDbyRk3+ZTbeRdEBXA==

lodash._root@~3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/lodash._root/-/lodash._root-3.0.1.tgz"
  integrity sha512-O0pWuFSK6x4EXhM1dhZ8gchNtG7JMqBtrHdoUFUWXD7dJnNSUze1GuyQr5sOs0aCvgGeI3o/OJW8f4ca7FDxmQ==

lodash._stringtopath@~4.8.0:
  version "4.8.0"
  resolved "https://registry.npmjs.org/lodash._stringtopath/-/lodash._stringtopath-4.8.0.tgz"
  integrity sha512-SXL66C731p0xPDC5LZg4wI5H+dJo/EO4KTqOMwLYCH3+FmmfAKJEZCm6ohGpI+T1xwsDsJCfL4OnhorllvlTPQ==
  dependencies:
    lodash._basetostring "~4.12.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  integrity sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==

lodash.uniqby@4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.uniqby/-/lodash.uniqby-4.5.0.tgz"
  integrity sha512-IRt7cfTtHy6f1aRVA5n7kT8rgN3N1nH6MOWLcHfpWG2SH19E3JksLK38MktLxZDhlAjCP9jpIXkOnRXlu6oByQ==
  dependencies:
    lodash._baseiteratee "~4.7.0"
    lodash._baseuniq "~4.6.0"

lodash@^4.1.1, lodash@^4.16.2, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lucide-react@^0.469.0:
  version "0.469.0"
  resolved "https://registry.npmjs.org/lucide-react/-/lucide-react-0.469.0.tgz"
  integrity sha512-28vvUnnKQ/dBwiCQtwJw7QauYnE7yd2Cyp4tTTJpvglX4EMpbflcdBgrgToX2j71B3YvugK/NH3BGUk+E/p/Fw==

make-cancellable-promise@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/make-cancellable-promise/-/make-cancellable-promise-1.3.2.tgz"
  integrity sha512-GCXh3bq/WuMbS+Ky4JBPW1hYTOU+znU+Q5m9Pu+pI8EoUqIHk9+tviOKC6/qhHh8C4/As3tzJ69IF32kdz85ww==

make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

make-event-props@^1.6.0:
  version "1.6.2"
  resolved "https://registry.npmjs.org/make-event-props/-/make-event-props-1.6.2.tgz"
  integrity sha512-iDwf7mA03WPiR8QxvcVHmVWEPfMY1RZXerDVNCRYW7dUr2ppH3J58Rwb39/WG39yTZdRSxr3x+2v22tvI0VEvA==

map-age-cleaner@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz"
  integrity sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w==
  dependencies:
    p-defer "^1.0.0"

marker-clusterer-plus@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npmjs.org/marker-clusterer-plus/-/marker-clusterer-plus-2.1.4.tgz"
  integrity sha512-4WLZnYCkgsUfSC0pftldd0YrLNupSqVIEdxL979f3sXVMBHTUOF3gDa6cEuOk2z8UGyVGcANiNZgvVc333mrHA==

markerwithlabel@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/markerwithlabel/-/markerwithlabel-2.0.2.tgz"
  integrity sha512-C/cbm1A0h/u54gwHk5ZJNdUU3V3+1BbCpRPMsMyFA7vF4yL+aB4rWpxACz29TpQ+cTg6/iQroExh0PMSRGtQFg==

media-engine@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/media-engine/-/media-engine-1.0.3.tgz"
  integrity sha512-aa5tG6sDoK+k70B9iEX1NeyfT8ObCKhNDs6lJVpwF6r8vhUfuKMslIcirq6HIUYuuUYLefcEQOn9bSBOvawtwg==

mem@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmjs.org/mem/-/mem-8.1.1.tgz"
  integrity sha512-qFCFUDs7U3b8mBDPyz5EToEKoAkgCzqquIgi9nkkR9bixxOVOre+09lbuH7+9Kn2NFpm56M3GUWVbU2hQgdACA==
  dependencies:
    map-age-cleaner "^0.1.3"
    mimic-fn "^3.1.0"

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

"memoize-one@>=3.1.1 <6":
  version "5.2.1"
  resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz"
  integrity sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==

merge-refs@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/merge-refs/-/merge-refs-1.3.0.tgz"
  integrity sha512-nqXPXbso+1dcKDpPCXvwZyJILz+vSLqGGOnDrYHQYE+B8n9JTCekVLC65AfCpR4ggVyA/45Y0iR9LDyS2iI+zA==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromatch@^4.0.4, micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-match@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/mime-match/-/mime-match-1.0.2.tgz"
  integrity sha512-VXp/ugGDVh3eCLOBCiHZMYWQaTNUHv2IJrut+yXA6+JbLPXHglHwfS/5A5L0ll+jkCY7fIzRJcH6OIunF+c6Cg==
  dependencies:
    wildcard "^1.1.0"

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mimic-fn@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-3.1.0.tgz"
  integrity sha512-Ysbi9uYW9hFyfrThdDEQuykN4Ey6BuwPD2kpI5ES/nFTDn/98yxYNLZJcgUAKPT/mcrLLKaGzJR9YVxJrIdASQ==

mimic-response@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-response/-/mimic-response-2.1.0.tgz"
  integrity sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==

mini-svg-data-uri@^1.2.3:
  version "1.4.4"
  resolved "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.4.4.tgz"
  integrity sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg==

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minipass@^3.0.0:
  version "3.3.6"
  resolved "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz"
  integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0, "minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  version "5.0.0"
  resolved "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz"
  integrity sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==

minipass@^7.1.2:
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

minizlib@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

moment-timezone@^0.5.46:
  version "0.5.46"
  resolved "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.46.tgz"
  integrity sha512-ZXm9b36esbe7OmdABqIWJuBBiLLwAjrN7CE+7sYdCCx82Nabt1wHDj8TVseS59QIlfFPbOoiBPm6ca9BioG4hw==
  dependencies:
    moment "^2.29.4"

moment@^2.18.1, moment@^2.29.4, moment@>=1.6.0:
  version "2.30.1"
  resolved "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

namespace-emitter@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/namespace-emitter/-/namespace-emitter-2.0.1.tgz"
  integrity sha512-N/sMKHniSDJBjfrkbS/tpkPj4RAbvW3mr8UAzvlMHyun93XEm83IAvhWtJVHo+RHn/oO8Job5YN4b+wRjSVp5g==

nan@^2.17.0:
  version "2.22.0"
  resolved "https://registry.npmjs.org/nan/-/nan-2.22.0.tgz"
  integrity sha512-nbajikzWTMwsW+eSsNm3QwlOs7het9gGJU5dDZzRTQGk03vyBOauxgI4VakDzE0PtsGTmXPsXTbbjVhRwR5mpw==

nanoid@^3.3.7:
  version "3.3.8"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.8.tgz"
  integrity sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==

nanoid@^4.0.0, nanoid@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-4.0.2.tgz"
  integrity sha512-7ZtY5KTCNheRGfEFxnedV5zFiORN1+Y1N6zvPTnHQd8ENUvfaDBeuJDZb2bN/oXwXxu3qkTXDzy57W5vAmDTBw==

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz"
  integrity sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-1.7.3.tgz"
  integrity sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-fetch@^2.6.12, node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-releases@^2.0.18:
  version "2.0.18"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.18.tgz"
  integrity sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==

nopt@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz"
  integrity sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==
  dependencies:
    abbrev "1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

normalize-svg-path@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/normalize-svg-path/-/normalize-svg-path-1.1.0.tgz"
  integrity sha512-r9KHKG2UUeB5LoTouwDzBy2VxXlHsiM6fyLQvnJa0S5hrhzqElH/CH7TUGhT1fVvIYBIKf3OpY4YJ4CK+iaqHg==
  dependencies:
    svg-arc-to-cubic-bezier "^3.0.0"

npmlog@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/npmlog/-/npmlog-5.0.1.tgz"
  integrity sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==
  dependencies:
    are-we-there-yet "^2.0.0"
    console-control-strings "^1.1.0"
    gauge "^3.0.0"
    set-blocking "^2.0.0"

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-inspect@^1.13.1, object-inspect@^1.13.3:
  version "1.13.3"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.3.tgz"
  integrity sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==

object-is@^1.1.2, object-is@^1.1.5:
  version "1.1.6"
  resolved "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz"
  integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.0, object.assign@^4.1.2, object.assign@^4.1.4, object.assign@^4.1.5:
  version "4.1.5"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.5.tgz"
  integrity sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.2, object.entries@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.8.tgz"
  integrity sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz"
  integrity sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz"
  integrity sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.values@^1.1.0, object.values@^1.1.5, object.values@^1.1.6, object.values@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/object.values/-/object.values-1.2.0.tgz"
  integrity sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

once@^1.3.0, once@^1.3.1:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz"
  integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

p-defer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz"
  integrity sha512-wB3wfAxZpk2AzOfUMJNL+d36xothRSyj8EXOa4f6GMqYDN9BJaaSISbsk+wS9abmnebVw95C2Kb5t85UmpCxuw==

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

p-queue@^7.3.4:
  version "7.4.1"
  resolved "https://registry.npmjs.org/p-queue/-/p-queue-7.4.1.tgz"
  integrity sha512-vRpMXmIkYF2/1hLBKisKeVYJZ8S2tZ0zEAmIJgdVKP2nq0nh4qCdf8bgw+ZgKrkh71AOCaqzwbJJk1WtdcF3VA==
  dependencies:
    eventemitter3 "^5.0.1"
    p-timeout "^5.0.2"

p-retry@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/p-retry/-/p-retry-6.2.1.tgz"
  integrity sha512-hEt02O4hUct5wtwg4H4KcWgDdm+l1bOaEy/hWzd8xtXB9BqxTWBBhb+2ImAtH4Cv4rPjV76xN3Zumqk3k3AhhQ==
  dependencies:
    "@types/retry" "0.12.2"
    is-network-error "^1.0.0"
    retry "^0.13.1"

p-timeout@^5.0.2:
  version "5.1.0"
  resolved "https://registry.npmjs.org/p-timeout/-/p-timeout-5.1.0.tgz"
  integrity sha512-auFDyzzzGZZZdHz3BtET9VEz0SE/uMEAx7uWfGPucfzEwwe/xH0iVeZibQmANYE/hp9T2+UUZT5m+BKyrDp3Ew==

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==

pako@^0.2.5:
  version "0.2.9"
  resolved "https://registry.npmjs.org/pako/-/pako-0.2.9.tgz"
  integrity sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==

pako@~1.0.2, pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-svg-path@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz"
  integrity sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

path2d-polyfill@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/path2d-polyfill/-/path2d-polyfill-2.1.1.tgz"
  integrity sha512-4Rka5lN+rY/p0CdD8+E+BFv51lFaFvJOrlOhyQ+zjzyQrzyh3ozmxd1vVGGDdIbUFSBtIZLSnspxTgPT0iJhvA==
  dependencies:
    path2d "0.1.1"

path2d@0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/path2d/-/path2d-0.1.1.tgz"
  integrity sha512-/+S03c8AGsDYKKBtRDqieTJv2GlkMb0bWjnqOgtF6MkjdUQ9a8ARAtxWf9NgKLGm2+WQr6+/tqJdU8HNGsIDoA==

pdfjs-dist@3.11.174:
  version "3.11.174"
  resolved "https://registry.npmjs.org/pdfjs-dist/-/pdfjs-dist-3.11.174.tgz"
  integrity sha512-TdTZPf1trZ8/UFu5Cx/GXB7GZM30LT+wWUNfsi6Bq8ePLnb+woNKtDymI2mxZYBpMbonNFqKmiz684DIfnd8dA==
  optionalDependencies:
    canvas "^2.11.2"
    path2d-polyfill "^2.0.1"

perfect-scrollbar@^1.5.5:
  version "1.5.6"
  resolved "https://registry.npmjs.org/perfect-scrollbar/-/perfect-scrollbar-1.5.6.tgz"
  integrity sha512-rixgxw3SxyJbCaSpo1n35A/fwI1r2rdwMKOTCg/AcG+xOEyZcE8UHVjpZMFCVImzsFoCZeJTT+M/rdEIQYO2nw==

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  integrity sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==

picocolors@^1.0.0, picocolors@^1.0.1, picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pirates@^4.0.1:
  version "4.0.6"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz"
  integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==

postcss-import@^14.1.0:
  version "14.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-14.1.0.tgz"
  integrity sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.0, postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^3.1.4:
  version "3.1.4"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.4.tgz"
  integrity sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg==
  dependencies:
    lilconfig "^2.0.5"
    yaml "^1.10.2"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz"
  integrity sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-nested@6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.0.0.tgz"
  integrity sha512-0DkamqrPcmkBDsLn+vQDIrtkSbNkv5AD/M322ySo9kqFkCIYklym2xEmWkwo+Y3/qZo34tzEPNUw4y7yMCdv5w==
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-selector-parser@^6.0.10, postcss-selector-parser@^6.0.11, postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.0.0, postcss@^8.0.9, postcss@^8.1.0, postcss@^8.2.14, postcss@^8.4.21, postcss@^8.4.23, postcss@^8.4.27, postcss@^8.4.47, postcss@>=8.0.9:
  version "8.4.49"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz"
  integrity sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

preact@^10.5.13, preact@~10.12.1:
  version "10.12.1"
  resolved "https://registry.npmjs.org/preact/-/preact-10.12.1.tgz"
  integrity sha512-l8386ixSsBdbreOAkqtrwqHwdvR35ID8c3rKPa8lCWuO86dBi32QWHV4vfsZK1utLLFMvw+Z5Ad4XLkZzchscg==

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prettier-plugin-tailwindcss@^0.2.5:
  version "0.2.8"
  resolved "https://registry.npmjs.org/prettier-plugin-tailwindcss/-/prettier-plugin-tailwindcss-0.2.8.tgz"
  integrity sha512-KgPcEnJeIijlMjsA6WwYgRs5rh3/q76oInqtMXBA/EMcamrcYJpyhtRhyX1ayT9hnHlHTuO8sIifHF10WuSDKg==

prettier@^2.8.7, prettier@>=2.2.0:
  version "2.8.8"
  resolved "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz"
  integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==

pretty-rating-react@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/pretty-rating-react/-/pretty-rating-react-2.2.0.tgz"
  integrity sha512-g5j7zZS5gHsPBmVUEjs5IAjdhzTSnq44B9R/h8bvDCBAuWCls7tEwP+8knDJ2P+2lXvshA83MaRedUisTIDfMw==
  dependencies:
    react "^17.0.2 || ^18.0.0-0"
    react-dom "^17.0.2 || ^18.0.0-0"

printj@~1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/printj/-/printj-1.1.2.tgz"
  integrity sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ==

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

promise-queue@^2.2.5:
  version "2.2.5"
  resolved "https://registry.npmjs.org/promise-queue/-/promise-queue-2.2.5.tgz"
  integrity sha512-p/iXrPSVfnqPft24ZdNNLECw/UrtLTpT3jpAAMzl/o5/rDsGCPo3/CQS2611flL6LkoEJ3oQZw7C8Q80ZISXRQ==

promise@^7.1.1:
  version "7.3.1"
  resolved "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz"
  integrity sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==
  dependencies:
    asap "~2.0.3"

prop-types-exact@^1.2.0:
  version "1.2.5"
  resolved "https://registry.npmjs.org/prop-types-exact/-/prop-types-exact-1.2.5.tgz"
  integrity sha512-wHDhA5TSSvU07gdzsdeT/FZg6zay94K4Y7swSK4YsRG3moWB0Qsp9g1Y5BBausP1HF8K4UeVe2Xt7ZFJByKp6A==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    hasown "^2.0.2"
    isarray "^2.0.5"
    object.assign "^4.1.5"
    reflect.ownkeys "^1.1.4"

prop-types@^15.5.0, prop-types@^15.5.8, prop-types@^15.6.0, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proper-lockfile@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/proper-lockfile/-/proper-lockfile-4.1.2.tgz"
  integrity sha512-TjNPblN4BwAWMXU8s9AEz4JmQxnD1NNL7bNOY/AKUzyamc379FWASUhc/K1pL2noVb+XmZKLL68cjzLsiOAMaA==
  dependencies:
    graceful-fs "^4.2.4"
    retry "^0.12.0"
    signal-exit "^3.0.2"

property-expr@^2.0.5:
  version "2.0.6"
  resolved "https://registry.npmjs.org/property-expr/-/property-expr-2.0.6.tgz"
  integrity sha512-SVtmxhRE/CGkn3eZY1T6pC8Nln6Fr/lu1mKSgRud0eC73whjGfoAogbn78LkD8aFL0zz3bAFerKSnOl7NlErBA==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz"
  integrity sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

queue@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmjs.org/queue/-/queue-6.0.2.tgz"
  integrity sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==
  dependencies:
    inherits "~2.0.3"

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz"
  integrity sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==

raf@^3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  integrity sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==
  dependencies:
    performance-now "^2.1.0"

react-apexcharts@^1.4.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/react-apexcharts/-/react-apexcharts-1.7.0.tgz"
  integrity sha512-03oScKJyNLRf0Oe+ihJxFZliBQM9vW3UWwomVn4YVRTN1jsIR58dLWt0v1sb8RwJVHDMbeHiKQueM0KGpn7nOA==
  dependencies:
    prop-types "^15.8.1"

react-calendar@^4.3.0:
  version "4.8.0"
  resolved "https://registry.npmjs.org/react-calendar/-/react-calendar-4.8.0.tgz"
  integrity sha512-qFgwo+p58sgv1QYMI1oGNaop90eJVKuHTZ3ZgBfrrpUb+9cAexxsKat0sAszgsizPMVo7vOXedV7Lqa0GQGMvA==
  dependencies:
    "@wojtekmaj/date-utils" "^1.1.3"
    clsx "^2.0.0"
    get-user-locale "^2.2.1"
    prop-types "^15.6.0"
    warning "^4.0.0"

react-confirm-alert@^3.0.6:
  version "3.0.6"
  resolved "https://registry.npmjs.org/react-confirm-alert/-/react-confirm-alert-3.0.6.tgz"
  integrity sha512-rplP6Ed9ZSNd0KFV5BUzk4EPQ77BxsrayllBXGFuA8xPXc7sbBjgU5KUrNpl7aWFmP7mXRlVXfuy1IT5DbffYw==

react-dates@^21.8.0:
  version "21.8.0"
  resolved "https://registry.npmjs.org/react-dates/-/react-dates-21.8.0.tgz"
  integrity sha512-PPriGqi30CtzZmoHiGdhlA++YPYPYGCZrhydYmXXQ6RAvAsaONcPtYgXRTLozIOrsQ5mSo40+DiA5eOFHnZ6xw==
  dependencies:
    airbnb-prop-types "^2.15.0"
    consolidated-events "^1.1.1 || ^2.0.0"
    enzyme-shallow-equal "^1.0.0"
    is-touch-device "^1.0.1"
    lodash "^4.1.1"
    object.assign "^4.1.0"
    object.values "^1.1.0"
    prop-types "^15.7.2"
    raf "^3.4.1"
    react-moment-proptypes "^1.6.0"
    react-outside-click-handler "^1.2.4"
    react-portal "^4.2.0"
    react-with-direction "^1.3.1"
    react-with-styles "^4.1.0"
    react-with-styles-interface-css "^6.0.0"

"react-dom@^0.14 || ^15 || ^16", "react-dom@^0.14 || ^15.5.4 || ^16.1.1", "react-dom@^0.14 || >=15", "react-dom@^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom@^15.0.0 || ^16.0.0", "react-dom@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^16 || ^17 || ^18", "react-dom@^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom@^16.0.0-0 || ^17.0.0-0 || ^18.0.0-0", "react-dom@^16.7.0 || ^17 || ^18 || ^19", "react-dom@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom@^17.0.2 || ^18.0.0-0", "react-dom@^18 || ^19 || ^19.0.0-rc", react-dom@^18.0.0, react-dom@^18.2.0, "react-dom@>= 16.8.0", react-dom@>=10.0.0, react-dom@>=16.6.0, react-dom@>=16.8, react-dom@>=16.8.0, react-dom@>=17.0.0, react-dom@>=17.0.x, "react-dom@~0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom@0.14 - 19", "react-dom@17 - 19":
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz"
  integrity sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-dropzone@^14.2.3:
  version "14.3.5"
  resolved "https://registry.npmjs.org/react-dropzone/-/react-dropzone-14.3.5.tgz"
  integrity sha512-9nDUaEEpqZLOz5v5SUcFA0CjM4vq8YbqO0WRls+EYT7+DvxUdzDPKNCPLqGfj3YL9MsniCLCD4RFA6M95V6KMQ==
  dependencies:
    attr-accept "^2.2.4"
    file-selector "^2.1.0"
    prop-types "^15.8.1"

react-easy-emoji@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/react-easy-emoji/-/react-easy-emoji-1.8.1.tgz"
  integrity sha512-wAf2x+cjwtOlLfXnjc0J3k5B2PDZdoxd1BjtfKr5tq2AL7t2ygvByJQWgFOyN28xg3qe8EKBM6pKxGP+qrGWuQ==
  dependencies:
    string-replace-to-array "^2.1.0"

react-google-autocomplete@^2.7.3:
  version "2.7.3"
  resolved "https://registry.npmjs.org/react-google-autocomplete/-/react-google-autocomplete-2.7.3.tgz"
  integrity sha512-Nm+7/VDe7/NDWb8p/a39is7ktNqt5bNqAOoQv2Ev/XkuEvjsRk08VAPFmXUH03xKuM8IUuDrk2Lwfge44YEj6Q==
  dependencies:
    lodash.debounce "^4.0.8"
    prop-types "^15.5.0"

react-google-maps@^9.4.5:
  version "9.4.5"
  resolved "https://registry.npmjs.org/react-google-maps/-/react-google-maps-9.4.5.tgz"
  integrity sha512-8z5nX9DxIcBCXuEiurmRT1VXVwnzx0C6+3Es6lxB2/OyY2SLax2/LcDu6Aldxnl3HegefTL7NJzGeaKAJ61pOA==
  dependencies:
    babel-runtime "^6.11.6"
    can-use-dom "^0.1.0"
    google-maps-infobox "^2.0.0"
    invariant "^2.2.1"
    lodash "^4.16.2"
    marker-clusterer-plus "^2.1.4"
    markerwithlabel "^2.0.1"
    prop-types "^15.5.8"
    recompose "^0.26.0"
    scriptjs "^2.5.8"
    warning "^3.0.0"

react-hook-form@^7.0.0, react-hook-form@^7.49.3:
  version "7.53.2"
  resolved "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.53.2.tgz"
  integrity sha512-YVel6fW5sOeedd1524pltpHX+jgU2u3DSDtXEaBORNdqiNrsX/nUI/iGXONegttg0mJVnfrIkiV0cmTU6Oo2xw==

react-icons@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/react-icons/-/react-icons-5.5.0.tgz"
  integrity sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw==

react-infinite-scroll-component@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/react-infinite-scroll-component/-/react-infinite-scroll-component-6.1.0.tgz"
  integrity sha512-SQu5nCqy8DxQWpnUVLx7V7b7LcA37aM7tvoWjTLZp1dk6EJibM5/4EJKzOnl07/BsM1Y40sKLuqjCwwH/xV0TQ==
  dependencies:
    throttle-debounce "^2.1.0"

react-input-emoji@^5.0.2:
  version "5.9.0"
  resolved "https://registry.npmjs.org/react-input-emoji/-/react-input-emoji-5.9.0.tgz"
  integrity sha512-37Bbg4DEI4UXQ/tNSG3shIA0iFqk3vkhvfTZSQxi/oweeB/870G/6lvp65Ebex2rNM+UlwjMmR80rlJQwl78lQ==
  dependencies:
    "@emoji-mart/data" "1.2.1"
    "@emoji-mart/react" "1.1.1"
    emoji-mart "5.6.0"
    react-easy-emoji "^1.8.1"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-loading-skeleton@^3.3.1:
  version "3.5.0"
  resolved "https://registry.npmjs.org/react-loading-skeleton/-/react-loading-skeleton-3.5.0.tgz"
  integrity sha512-gxxSyLbrEAdXTKgfbpBEFZCO/P153DnqSCQau2+o6lNy1jgMRr2MmRmOzMmyrwSaSYLRB8g7b0waYPmUjz7IhQ==

react-moment-proptypes@^1.6.0:
  version "1.8.1"
  resolved "https://registry.npmjs.org/react-moment-proptypes/-/react-moment-proptypes-1.8.1.tgz"
  integrity sha512-Er940DxWoObfIqPrZNfwXKugjxMIuk1LAuEzn23gytzV6hKS/sw108wibi9QubfMN4h+nrlje8eUCSbQRJo2fQ==
  dependencies:
    moment ">=1.6.0"

react-outside-click-handler@^1.2.4, react-outside-click-handler@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/react-outside-click-handler/-/react-outside-click-handler-1.3.0.tgz"
  integrity sha512-Te/7zFU0oHpAnctl//pP3hEAeobfeHMyygHB8MnjP6sX5OR8KHT1G3jmLsV3U9RnIYo+Yn+peJYWu+D5tUS8qQ==
  dependencies:
    airbnb-prop-types "^2.15.0"
    consolidated-events "^1.1.1 || ^2.0.0"
    document.contains "^1.0.1"
    object.values "^1.1.0"
    prop-types "^15.7.2"

react-pdf-tailwind@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/react-pdf-tailwind/-/react-pdf-tailwind-2.3.0.tgz"
  integrity sha512-RREimnynuF0WAsATdYWhJATY5oNAUp9Ib07JE3Pl5oQW07R5T002xjfXk06e9BPh50DvMm9qZkDglZ7u4xzySQ==
  dependencies:
    "@react-pdf/renderer" "^3.1.14"
    tailwindcss "^3.3.6"

react-pdf@^7.7.1:
  version "7.7.3"
  resolved "https://registry.npmjs.org/react-pdf/-/react-pdf-7.7.3.tgz"
  integrity sha512-a2VfDl8hiGjugpqezBTUzJHYLNB7IS7a2t7GD52xMI9xHg8LdVaTMsnM9ZlNmKadnStT/tvX5IfV0yLn+JvYmw==
  dependencies:
    clsx "^2.0.0"
    dequal "^2.0.3"
    make-cancellable-promise "^1.3.1"
    make-event-props "^1.6.0"
    merge-refs "^1.2.1"
    pdfjs-dist "3.11.174"
    prop-types "^15.6.2"
    tiny-invariant "^1.0.0"
    warning "^4.0.0"

react-portal@^4.2.0:
  version "4.2.2"
  resolved "https://registry.npmjs.org/react-portal/-/react-portal-4.2.2.tgz"
  integrity sha512-vS18idTmevQxyQpnde0Td6ZcUlv+pD8GTyR42n3CHUQq9OHi1C4jDE4ZWEbEsrbrLRhSECYiao58cvocwMtP7Q==
  dependencies:
    prop-types "^15.5.8"

react-ratings-declarative@^3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/react-ratings-declarative/-/react-ratings-declarative-3.4.1.tgz"
  integrity sha512-zL3d5PNcDcF/+KNcZlP2KVE5UIp53yLk7EJkbM9bXpvrqUp/S91lp7SkFsWPqv+q8dUg3BOdfEX1RWNQs1ocVw==
  dependencies:
    classnames "^2.2.5"
    prop-types "^15.6.0"
    react "^16.2.0"

react-refresh@^0.14.2:
  version "0.14.2"
  resolved "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz"
  integrity sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==

react-remove-scroll-bar@^2.3.7:
  version "2.3.8"
  resolved "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz"
  integrity sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==
  dependencies:
    react-style-singleton "^2.2.2"
    tslib "^2.0.0"

react-remove-scroll@^2.6.1:
  version "2.6.2"
  resolved "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.6.2.tgz"
  integrity sha512-KmONPx5fnlXYJQqC62Q+lwIeAk64ws/cUw6omIumRzMRPqgnYqhSSti99nbj0Ry13bv7dF+BKn7NB+OqkdZGTw==
  dependencies:
    react-remove-scroll-bar "^2.3.7"
    react-style-singleton "^2.2.1"
    tslib "^2.1.0"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.2"

react-router-dom@^6.11.1:
  version "6.28.0"
  resolved "https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.28.0.tgz"
  integrity sha512-kQ7Unsl5YdyOltsPGl31zOjLrDv+m2VcIEcIHqYYD3Lp0UppLjrzcfJqDJwXxFw3TH/yvapbnUvPlAj7Kx5nbg==
  dependencies:
    "@remix-run/router" "1.21.0"
    react-router "6.28.0"

react-router@^6.11.1, react-router@6.28.0:
  version "6.28.0"
  resolved "https://registry.npmjs.org/react-router/-/react-router-6.28.0.tgz"
  integrity sha512-HrYdIFqdrnhDw0PqG/AKjAqEqM7AvxCz0DQ4h2W8k6nqmc5uRBYDag0SBxx9iYz5G8gnuNVLzUe13wl9eAsXXg==
  dependencies:
    "@remix-run/router" "1.21.0"

react-select@^5.7.3:
  version "5.8.3"
  resolved "https://registry.npmjs.org/react-select/-/react-select-5.8.3.tgz"
  integrity sha512-lVswnIq8/iTj1db7XCG74M/3fbGB6ZaluCzvwPGT5ZOjCdL/k0CLWhEK0vCBLuU5bHTEf6Gj8jtSvi+3v+tO1w==
  dependencies:
    "@babel/runtime" "^7.12.0"
    "@emotion/cache" "^11.4.0"
    "@emotion/react" "^11.8.1"
    "@floating-ui/dom" "^1.0.1"
    "@types/react-transition-group" "^4.4.0"
    memoize-one "^6.0.0"
    prop-types "^15.6.0"
    react-transition-group "^4.3.0"
    use-isomorphic-layout-effect "^1.1.2"

react-signature-canvas@^1.1.0-alpha.1:
  version "1.1.0-alpha.1"
  resolved "https://registry.npmjs.org/react-signature-canvas/-/react-signature-canvas-1.1.0-alpha.1.tgz"
  integrity sha512-EtUuy/e7kjYhgBx9EKjKodeaVlU82p4STKYStUfDgn4eJQ3sxnykIDdqhI1Xp8DNLdL3qMiIRdNCvC7mTcQe1w==
  dependencies:
    "@babel/runtime" "^7.17.9"
    "@types/signature_pad" "^2.3.0"
    signature_pad "^2.3.2"
    trim-canvas "^0.1.0"

react-signature-pad-wrapper@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/react-signature-pad-wrapper/-/react-signature-pad-wrapper-4.1.0.tgz"
  integrity sha512-HtqUrlIKST5RcpKNZs3M9WeT0GOlGc/HmA5OnEVBxK+8MdYbK6BgdjgrNLlme8YinaWLWUFcPdPifGDFXdMY8w==
  dependencies:
    prop-types "^15.8.1"
    signature_pad "^5.0.1"
    throttle-debounce "^5.0.0"

react-slick@^0.29.0:
  version "0.29.0"
  resolved "https://registry.npmjs.org/react-slick/-/react-slick-0.29.0.tgz"
  integrity sha512-TGdOKE+ZkJHHeC4aaoH85m8RnFyWqdqRfAGkhd6dirmATXMZWAxOpTLmw2Ll/jPTQ3eEG7ercFr/sbzdeYCJXA==
  dependencies:
    classnames "^2.2.5"
    enquire.js "^2.1.6"
    json2mq "^0.2.0"
    lodash.debounce "^4.0.8"
    resize-observer-polyfill "^1.5.0"

react-spinners@^0.13.8:
  version "0.13.8"
  resolved "https://registry.npmjs.org/react-spinners/-/react-spinners-0.13.8.tgz"
  integrity sha512-3e+k56lUkPj0vb5NDXPVFAOkPC//XyhKPJjvcGjyMNPWsBKpplfeyialP74G7H7+It7KzhtET+MvGqbKgAqpZA==

react-style-singleton@^2.2.1, react-style-singleton@^2.2.2:
  version "2.2.3"
  resolved "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz"
  integrity sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==
  dependencies:
    get-nonce "^1.0.0"
    tslib "^2.0.0"

react-to-print@^2.15.1:
  version "2.15.1"
  resolved "https://registry.npmjs.org/react-to-print/-/react-to-print-2.15.1.tgz"
  integrity sha512-1foogIFbCpzAVxydkhBiDfMiFYhIMphiagDOfcG4X/EcQ+fBPqJ0rby9Wv/emzY1YLkIQy/rEgOrWQT+rBKhjw==

react-transition-group@^4.3.0:
  version "4.4.5"
  resolved "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react-window@^1.8.11:
  version "1.8.11"
  resolved "https://registry.npmjs.org/react-window/-/react-window-1.8.11.tgz"
  integrity sha512-+SRbUVT2scadgFSWx+R1P754xHPEqvcfSfVX10QYg6POOz+WNgkN48pS+BtZNIMGiL1HYrSEiCkwsMS15QogEQ==
  dependencies:
    "@babel/runtime" "^7.0.0"
    memoize-one ">=3.1.1 <6"

react-with-direction@^1.3.1:
  version "1.4.0"
  resolved "https://registry.npmjs.org/react-with-direction/-/react-with-direction-1.4.0.tgz"
  integrity sha512-ybHNPiAmaJpoWwugwqry9Hd1Irl2hnNXlo/2SXQBwbLn/jGMauMS2y9jw+ydyX5V9ICryCqObNSthNt5R94xpg==
  dependencies:
    airbnb-prop-types "^2.16.0"
    brcast "^2.0.2"
    deepmerge "^1.5.2"
    direction "^1.0.4"
    hoist-non-react-statics "^3.3.2"
    object.assign "^4.1.2"
    object.values "^1.1.5"
    prop-types "^15.7.2"

react-with-styles-interface-css@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/react-with-styles-interface-css/-/react-with-styles-interface-css-6.0.0.tgz"
  integrity sha512-6khSG1Trf4L/uXOge/ZAlBnq2O2PEXlQEqAhCRbvzaQU4sksIkdwpCPEl6d+DtP3+IdhyffTWuHDO9lhe1iYvA==
  dependencies:
    array.prototype.flat "^1.2.1"
    global-cache "^1.2.1"

"react-with-styles@^3.0.0 || ^4.0.0", react-with-styles@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/react-with-styles/-/react-with-styles-4.2.0.tgz"
  integrity sha512-tZCTY27KriRNhwHIbg1NkSdTTOSfXDg6Z7s+Q37mtz0Ym7Sc7IOr3PzVt4qJhJMW6Nkvfi3g34FuhtiGAJCBQA==
  dependencies:
    airbnb-prop-types "^2.14.0"
    hoist-non-react-statics "^3.2.1"
    object.assign "^4.1.0"
    prop-types "^15.7.2"
    react-with-direction "^1.3.1"

react@*, "react@^0.14 || ^15 || ^16", "react@^0.14 || ^15.0.0 || ^16.0.0-alpha", "react@^0.14 || ^15.5.4 || ^16.1.1", "react@^0.14 || >=15", "react@^0.14.0 || ^15.0.0 || ^16.0.0", "react@^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react@^15.0.0 || ^16.0.0", "react@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16 || ^17 || ^18", "react@^16.0.0 || ^17.0.0 || ^18.0.0", "react@^16.0.0-0 || ^17.0.0-0 || ^18.0.0-0", react@^16.14.0, "react@^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.7.0 || ^17 || ^18 || ^19", "react@^16.8 || ^17 || ^18", "react@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^17.0.2 || ^18.0.0-0", "react@^18 || ^19 || ^19.0.0-rc", react@^18.0.0, react@^18.2.0, react@^18.3.1, "react@>= 16 || ^19.0.0-rc", "react@>= 16.8 || 18.0.0", "react@>= 16.8.0", react@>=0.13, react@>=0.14, react@>=0.14.27, react@>=16.0.0, react@>=16.3, react@>=16.6.0, react@>=16.8, react@>=16.8.0, react@>=17.0.0, react@>=17.0.x, react@>=18.0.0, "react@~0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react@0.14 - 19", "react@17 - 19":
  version "18.3.1"
  resolved "https://registry.npmjs.org/react/-/react-18.3.1.tgz"
  integrity sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==
  dependencies:
    loose-envify "^1.1.0"

react@^16.2.0:
  version "16.14.0"
  resolved "https://registry.npmjs.org/react/-/react-16.14.0.tgz"
  integrity sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.3.6:
  version "2.3.8"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

recompose@^0.26.0:
  version "0.26.0"
  resolved "https://registry.npmjs.org/recompose/-/recompose-0.26.0.tgz"
  integrity sha512-KwOu6ztO0mN5vy3+zDcc45lgnaUoaQse/a5yLVqtzTK13czSWnFGmXbQVmnoMgDkI5POd1EwIKSbjU1V7xdZog==
  dependencies:
    change-emitter "^0.1.2"
    fbjs "^0.8.1"
    hoist-non-react-statics "^2.3.1"
    symbol-observable "^1.0.4"

redux@^4.2.1:
  version "4.2.1"
  resolved "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz"
  integrity sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==
  dependencies:
    "@babel/runtime" "^7.9.2"

reflect.getprototypeof@^1.0.4, reflect.getprototypeof@^1.0.6:
  version "1.0.7"
  resolved "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.7.tgz"
  integrity sha512-bMvFGIUKlc/eSfXNX+aZ+EL95/EgZzuwA0OBPTbZZDEJw/0AkentjMuM1oiRfwHrshqk4RzdgiTg5CcDalXN5g==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    which-builtin-type "^1.1.4"

reflect.ownkeys@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/reflect.ownkeys/-/reflect.ownkeys-1.1.4.tgz"
  integrity sha512-iUNmtLgzudssL+qnTUosCmnq3eczlrVd1wXrgx/GhiI/8FvwrTYWtCJ9PNvWIRX+4ftupj2WUfB5mu5s9t6LnA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-set-tostringtag "^2.0.1"
    globalthis "^1.0.3"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz"
  integrity sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  integrity sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  integrity sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==

regenerator-runtime@^0.13.7:
  version "0.13.11"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regenerator-transform@^0.15.2:
  version "0.15.2"
  resolved "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.15.2.tgz"
  integrity sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==
  dependencies:
    "@babel/runtime" "^7.8.4"

regexp.prototype.flags@^1.5.2, regexp.prototype.flags@^1.5.3:
  version "1.5.3"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.3.tgz"
  integrity sha512-vqlC04+RQoFalODCbCumG2xIOvapzVMHwsyIGM/SIE8fRhFFsXeH8/QQ+s0T0kDAhKc4k30s73/0ydkHQz6HlQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.2"

regexpu-core@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz"
  integrity sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz"
  integrity sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==

regjsparser@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz"
  integrity sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==
  dependencies:
    jsesc "~3.0.2"

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

resize-observer-polyfill@^1.5.0:
  version "1.5.1"
  resolved "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.1.7, resolve@^1.14.2, resolve@^1.19.0, resolve@^1.22.1, resolve@^1.22.4, resolve@^1.22.8:
  version "1.22.8"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz"
  integrity sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restructure@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/restructure/-/restructure-3.0.2.tgz"
  integrity sha512-gSfoiOEA0VPE6Tukkrr7I0RBdE0s7H1eFCDBk05l1KIQT1UIKNc5JZy6jdyW6eYH3aR3g5b3PuL77rq0hvwtAw==

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz"
  integrity sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==

retry@^0.13.1:
  version "0.13.1"
  resolved "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rgbcolor@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz"
  integrity sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rollup@^2.77.2:
  version "2.79.2"
  resolved "https://registry.npmjs.org/rollup/-/rollup-2.79.2.tgz"
  integrity sha512-fS6iqSPZDs3dr/y7Od6y5nha8dW1YnbgtsyotCVvoFGKbERG++CVRFv1meyGDE1SNItQA8BrnCw7ScdAhRJ3XQ==
  optionalDependencies:
    fsevents "~2.3.2"

rollup@^3.27.1:
  version "3.29.5"
  resolved "https://registry.npmjs.org/rollup/-/rollup-3.29.5.tgz"
  integrity sha512-GVsDdsbJzzy4S/v3dqWPJ7EfvZJfCHiDqe80IyrF59LYuP+e6U1LJoUqeuqRbwAWoMNoXivMNeNAOf5E22VA1w==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.2.tgz"
  integrity sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-regex-test@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.3.tgz"
  integrity sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-regex "^1.1.4"

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

scheduler@^0.17.0:
  version "0.17.0"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.17.0.tgz"
  integrity sha512-7rro8Io3tnCPuY4la/NuI5F2yfESpnfZyT6TtkXnSWVkcu0BCDJ+8gk5ozUaFaxpIyNuWAPXrH0yFcSi28fnDA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.19.1:
  version "0.19.1"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.19.1.tgz"
  integrity sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.23.2:
  version "0.23.2"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz"
  integrity sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==
  dependencies:
    loose-envify "^1.1.0"

scriptjs@^2.5.8:
  version "2.5.9"
  resolved "https://registry.npmjs.org/scriptjs/-/scriptjs-2.5.9.tgz"
  integrity sha512-qGVDoreyYiP1pkQnbnFAUIS5AjenNwwQBdl7zeos9etl+hYKWahjRTfzAZZYBv5xNHx7vNKCmaLDQZ6Fr2AEXg==

semver@^6.0.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.3.5:
  version "7.6.3"
  resolved "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

semver@^7.3.7:
  version "7.6.3"
  resolved "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.1, set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  integrity sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel@^1.0.4, side-channel@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.6.tgz"
  integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

signature_pad@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npmjs.org/signature_pad/-/signature_pad-2.3.2.tgz"
  integrity sha512-peYXLxOsIY6MES2TrRLDiNg2T++8gGbpP2yaC+6Ohtxr+a2dzoaqWosWDY9sWqTAAk6E/TyQO+LJw9zQwyu5kA==

signature_pad@^5.0.1:
  version "5.0.4"
  resolved "https://registry.npmjs.org/signature_pad/-/signature_pad-5.0.4.tgz"
  integrity sha512-nngOixbwLAUOuH3QnZwlgwmynQblxmo4iWacKFwfymJfiY+Qt+9icNtcIe/okqXKun4hJ5QTFmHyC7dmv6lf2w==

simple-concat@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz"
  integrity sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==

simple-get@^3.0.3:
  version "3.1.1"
  resolved "https://registry.npmjs.org/simple-get/-/simple-get-3.1.1.tgz"
  integrity sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==
  dependencies:
    decompress-response "^4.2.0"
    once "^1.3.1"
    simple-concat "^1.0.0"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

slick-carousel@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/slick-carousel/-/slick-carousel-1.8.1.tgz"
  integrity sha512-XB9Ftrf2EEKfzoQXt3Nitrt/IPbT+f1fgqBdoxO3W/+JYvtEOW6EgxnWfr9GH6nmULv7Y2tPmEX3koxThVmebA==

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==

ssf@~0.11.2:
  version "0.11.2"
  resolved "https://registry.npmjs.org/ssf/-/ssf-0.11.2.tgz"
  integrity sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==
  dependencies:
    frac "~1.1.2"

ssr-window@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/ssr-window/-/ssr-window-4.0.2.tgz"
  integrity sha512-ISv/Ch+ig7SOtw7G2+qkwfVASzazUnvlDTwypdLoPoySv+6MqlOV10VwPSE6EWkGjhW50lUmghPmpYZXMu/+AQ==

stackblur-canvas@^2.0.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.7.0.tgz"
  integrity sha512-yf7OENo23AGJhBriGx0QivY5JP6Y1HbrrDI6WLt6C5auYZXlQrheoY8hD4ibekFKz1HOfE48Ww8kMWMnJD/zcQ==

string_decoder@^1.1.1, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

string-convert@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmjs.org/string-convert/-/string-convert-0.2.1.tgz"
  integrity sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==

string-natural-compare@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/string-natural-compare/-/string-natural-compare-3.0.1.tgz"
  integrity sha512-n3sPwynL1nwKi3WJ6AIsClwBMa0zTi54fn2oLU6ndfTSIO05xaznjSf15PcBZU6FNWbmN5Q6cxT4V5hGvB4taw==

string-replace-to-array@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/string-replace-to-array/-/string-replace-to-array-2.1.1.tgz"
  integrity sha512-i31csTEoIrcB8pusAF72Z0WsF04sbdiH4s0egpcmFj30SgHYdYd1swmZ7Zb1qFryJ5DbZR6z4rSjOzoUcrWUWw==

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.includes@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/string.prototype.includes/-/string.prototype.includes-2.0.1.tgz"
  integrity sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"

string.prototype.matchall@^4.0.11:
  version "4.0.11"
  resolved "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.11.tgz"
  integrity sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.7"
    regexp.prototype.flags "^1.5.2"
    set-function-name "^2.0.2"
    side-channel "^1.0.6"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz"
  integrity sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.9:
  version "1.2.9"
  resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.9.tgz"
  integrity sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.0"
    es-object-atoms "^1.0.0"

string.prototype.trimend@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.8.tgz"
  integrity sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
  integrity sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

stylis@4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/stylis/-/stylis-4.2.0.tgz"
  integrity sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==

sucrase@^3.29.0, sucrase@^3.35.0:
  version "3.35.0"
  resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

suneditor-react@^3.5.0:
  version "3.6.1"
  resolved "https://registry.npmjs.org/suneditor-react/-/suneditor-react-3.6.1.tgz"
  integrity sha512-12f9KLnEB6pAdyHJINTzRBg3UOWVZZ+jVYSEtwdBTDYQW4amUZr0xOnpikbBAlxb9rcTYV5RHAsad3gnNhLsuA==

suneditor@^2.44.10, suneditor@^2.44.12:
  version "2.47.0"
  resolved "https://registry.npmjs.org/suneditor/-/suneditor-2.47.0.tgz"
  integrity sha512-mmPW2NRZ9zzSFQRjHZFnogG0NUK35usP7cBxYSt/FNzdF+FxOveXqRU4RzAdv07ON06+OGbTl4lPYNCmqa5mxg==

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-arc-to-cubic-bezier@^3.0.0, svg-arc-to-cubic-bezier@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/svg-arc-to-cubic-bezier/-/svg-arc-to-cubic-bezier-3.2.0.tgz"
  integrity sha512-djbJ/vZKZO+gPoSDThGNpKDO+o+bAeA4XQKovvkNCqnIS2t+S4qnLAGQhyyrulhCFRl1WWzAp0wUDV8PpTVU3g==

svg-pathdata@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  integrity sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw==

svg.draggable.js@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmjs.org/svg.draggable.js/-/svg.draggable.js-2.2.2.tgz"
  integrity sha512-JzNHBc2fLQMzYCZ90KZHN2ohXL0BQJGQimK1kGk6AvSeibuKcIdDX9Kr0dT9+UJ5O8nYA0RB839Lhvk4CY4MZw==
  dependencies:
    svg.js "^2.0.1"

svg.easing.js@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/svg.easing.js/-/svg.easing.js-2.0.0.tgz"
  integrity sha512-//ctPdJMGy22YoYGV+3HEfHbm6/69LJUTAqI2/5qBvaNHZ9uUFVC82B0Pl299HzgH13rKrBgi4+XyXXyVWWthA==
  dependencies:
    svg.js ">=2.3.x"

svg.filter.js@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/svg.filter.js/-/svg.filter.js-2.0.2.tgz"
  integrity sha512-xkGBwU+dKBzqg5PtilaTb0EYPqPfJ9Q6saVldX+5vCRy31P6TlRCP3U9NxH3HEufkKkpNgdTLBJnmhDHeTqAkw==
  dependencies:
    svg.js "^2.2.5"

svg.js@^2.0.1, svg.js@^2.2.5, svg.js@^2.4.0, svg.js@^2.6.5, svg.js@>=2.3.x:
  version "2.7.1"
  resolved "https://registry.npmjs.org/svg.js/-/svg.js-2.7.1.tgz"
  integrity sha512-ycbxpizEQktk3FYvn/8BH+6/EuWXg7ZpQREJvgacqn46gIddG24tNNe4Son6omdXCnSOaApnpZw6MPCBA1dODA==

svg.pathmorphing.js@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/svg.pathmorphing.js/-/svg.pathmorphing.js-0.1.3.tgz"
  integrity sha512-49HWI9X4XQR/JG1qXkSDV8xViuTLIWm/B/7YuQELV5KMOPtXjiwH4XPJvr/ghEDibmLQ9Oc22dpWpG0vUDDNww==
  dependencies:
    svg.js "^2.4.0"

svg.resize.js@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/svg.resize.js/-/svg.resize.js-1.4.3.tgz"
  integrity sha512-9k5sXJuPKp+mVzXNvxz7U0uC9oVMQrrf7cFsETznzUDDm0x8+77dtZkWdMfRlmbkEEYvUn9btKuZ3n41oNA+uw==
  dependencies:
    svg.js "^2.6.5"
    svg.select.js "^2.1.2"

svg.select.js@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/svg.select.js/-/svg.select.js-2.1.2.tgz"
  integrity sha512-tH6ABEyJsAOVAhwcCjF8mw4crjXSI1aa7j2VQR8ZuJ37H2MBUbyeqYr5nEO7sSN3cy9AR9DUwNg0t/962HlDbQ==
  dependencies:
    svg.js "^2.2.5"

svg.select.js@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/svg.select.js/-/svg.select.js-3.0.1.tgz"
  integrity sha512-h5IS/hKkuVCbKSieR9uQCj9w+zLHoPh+ce19bBYyqF53g6mnPB8sAtIbe1s9dh2S2fCmYX2xel1Ln3PJBbK4kw==
  dependencies:
    svg.js "^2.6.5"

swiper@^9.3.1:
  version "9.4.1"
  resolved "https://registry.npmjs.org/swiper/-/swiper-9.4.1.tgz"
  integrity sha512-1nT2T8EzUpZ0FagEqaN/YAhRj33F2x/lN6cyB0/xoYJDMf8KwTFT3hMOeoB8Tg4o3+P/CKqskP+WX0Df046fqA==
  dependencies:
    ssr-window "^4.0.2"

symbol-observable@^1.0.4:
  version "1.2.0"
  resolved "https://registry.npmjs.org/symbol-observable/-/symbol-observable-1.2.0.tgz"
  integrity sha512-e900nM8RRtGhlV36KGEU9k65K3mPb1WV70OdjfxlG2EAuM1noi/E/BaW/uMhL7bPEssK8QV57vN3esixjUvcXQ==

tabbable@^6.0.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/tabbable/-/tabbable-6.2.0.tgz"
  integrity sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==

tailwind-merge@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz"
  integrity sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==

tailwindcss@^3.3.2, tailwindcss@^3.3.6, "tailwindcss@>=3.0.0 || >= 3.0.0-alpha.1 || >= 4.0.0-alpha.20":
  version "3.4.16"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.16.tgz"
  integrity sha512-TI4Cyx7gDiZ6r44ewaJmt0o6BrMCT5aK5e0rmJ/G9Xq3w7CX/5VXl/zIPEJZFUK5VEqwByyhqNPycPlvcK4ZNw==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

tailwindcss@3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.3.0.tgz"
  integrity sha512-hOXlFx+YcklJ8kXiCAfk/FMyr4Pm9ck477G0m/us2344Vuj355IpoEDB5UmGAsSpTBmr+4ZhjzW04JuFXkb/fw==
  dependencies:
    arg "^5.0.2"
    chokidar "^3.5.3"
    color-name "^1.1.4"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.2.12"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.17.2"
    lilconfig "^2.0.6"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.0.9"
    postcss-import "^14.1.0"
    postcss-js "^4.0.0"
    postcss-load-config "^3.1.4"
    postcss-nested "6.0.0"
    postcss-selector-parser "^6.0.11"
    postcss-value-parser "^4.2.0"
    quick-lru "^5.1.1"
    resolve "^1.22.1"
    sucrase "^3.29.0"

tar@^6.1.11:
  version "6.2.1"
  resolved "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz"
  integrity sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz"
  integrity sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==
  dependencies:
    utrie "^1.0.2"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

throttle-debounce@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-2.3.0.tgz"
  integrity sha512-H7oLPV0P7+jgvrk+6mwwwBDmxTaxnu9HMXmloNLXwnNO0ZxZ31Orah2n8lU1eMPvsaowP2CX+USCgyovXfdOFQ==

throttle-debounce@^5.0.0:
  version "5.0.2"
  resolved "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-5.0.2.tgz"
  integrity sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A==

tiny-case@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-case/-/tiny-case-1.0.3.tgz"
  integrity sha512-Eet/eeMhkO6TX8mnUteS9zgPbUMQa4I6Kkp5ORiBD5476/m+PIRiumP5tmh5ioJpH7k51Kehawy2UDfsnxxY8Q==

tiny-inflate@^1.0.0, tiny-inflate@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-inflate/-/tiny-inflate-1.0.3.tgz"
  integrity sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==

tiny-invariant@^1.0.0:
  version "1.3.3"
  resolved "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz"
  integrity sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==

tippy.js@^6.3.1:
  version "6.3.7"
  resolved "https://registry.npmjs.org/tippy.js/-/tippy.js-6.3.7.tgz"
  integrity sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==
  dependencies:
    "@popperjs/core" "^2.9.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/toposort/-/toposort-2.0.2.tgz"
  integrity sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg==

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

trim-canvas@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmjs.org/trim-canvas/-/trim-canvas-0.1.2.tgz"
  integrity sha512-nd4Ga3iLFV94mdhW9JFMLpQbHUyCQuhFOD71PEAt1NjtMD5wbZctzhX8c3agHNybMR5zXD1XTGoIEWk995E6pQ==

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

tsconfig-paths@^3.15.0:
  version "3.15.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
  integrity sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.0.0, tslib@^2.1.0, tslib@^2.4.0, tslib@^2.7.0, tslib@^2.8.0:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
  integrity sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==
  dependencies:
    tslib "^1.8.1"

tus-js-client@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/tus-js-client/-/tus-js-client-3.1.3.tgz"
  integrity sha512-n9k6rI/nPOuP2TaqPG6Ogz3a3V1cSH9en7N0VH4gh95jmG8JA58TJzLms2lBfb7aKVb3fdUunqYEG3WnQnZRvQ==
  dependencies:
    buffer-from "^1.1.2"
    combine-errors "^3.0.3"
    is-stream "^2.0.0"
    js-base64 "^3.7.2"
    lodash.throttle "^4.1.1"
    proper-lockfile "^4.1.2"
    url-parse "^1.5.7"

tw-elements@^1.0.0-beta2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/tw-elements/-/tw-elements-1.1.0.tgz"
  integrity sha512-IUr9YW2l99oTVZxuVjQg6rpuCpo6VZqbaKGYmZUNVVsoSVU/ljPpkJAY2Pn/morlXwKPhP9MTPNQMlWosqHL4w==
  dependencies:
    "@popperjs/core" "^2.6.0"
    chart.js "^3.7.1"
    chartjs-plugin-datalabels "^2.0.0"
    deepmerge "^4.2.2"
    detect-autofill "^1.1.3"
    perfect-scrollbar "^1.5.5"
    tailwindcss "3.3.0"

twilio-video@^2.27.0:
  version "2.28.2"
  resolved "https://registry.npmjs.org/twilio-video/-/twilio-video-2.28.2.tgz"
  integrity sha512-L4KNnJwOxsnWHCAOkVHzj9KaYVEkn3knuCvM8sc19ob6M1ytfUmAX1M7k8ydbEdf5AaYDqk88khqO71ZBPUm0w==
  dependencies:
    events "^3.3.0"
    util "^0.12.4"
    ws "^7.4.6"
    xmlhttprequest "^1.8.0"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^2.19.0:
  version "2.19.0"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-2.19.0.tgz"
  integrity sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==

typed-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.2.tgz"
  integrity sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-typed-array "^1.1.13"

typed-array-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.1.tgz"
  integrity sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-byte-offset@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.3.tgz"
  integrity sha512-GsvTyUHTriq6o/bHcTd0vM7OQ9JEdlvluu9YISaA7+KzDzPaIzEeDFNkTfhdE3MYcNhNi0vq/LlegYgIs5yPAw==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"
    reflect.getprototypeof "^1.0.6"

typed-array-length@^1.0.6:
  version "1.0.7"
  resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz"
  integrity sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

"typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta":
  version "5.7.2"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.7.2.tgz"
  integrity sha512-i5t66RHxDvVN40HfDd1PsEThGNnlMCMT3jMUuoh9/0TaqWevNontacunWyN02LA9/fIbEWlcHZcgTKb9QoaLfg==

ua-parser-js@^0.7.30:
  version "0.7.39"
  resolved "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.39.tgz"
  integrity sha512-IZ6acm6RhQHNibSt7+c09hhvsKy9WUr4DVbeq9U8o71qxyYtJpQeDxQnMrVqnIFMLcQjHO0I9wgfO2vIahht4w==

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz"
  integrity sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz"
  integrity sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==

unicode-properties@^1.4.0, unicode-properties@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/unicode-properties/-/unicode-properties-1.4.1.tgz"
  integrity sha512-CLjCCLQ6UuMxWnbIylkisbRj31qxHPAurvena/0iwSVbQ2G1VY5/HjV0IRabOEbDHlzZlRdCrD4NhB0JtU40Pg==
  dependencies:
    base64-js "^1.3.0"
    unicode-trie "^2.0.0"

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  integrity sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==

unicode-trie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-trie/-/unicode-trie-2.0.0.tgz"
  integrity sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ==
  dependencies:
    pako "^0.2.5"
    tiny-inflate "^1.0.0"

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

update-browserslist-db@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.1.tgz"
  integrity sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.0"

uppy@^3.9.0:
  version "3.27.4"
  resolved "https://registry.npmjs.org/uppy/-/uppy-3.27.4.tgz"
  integrity sha512-n86GS2wO/KNH6aCZwl6uOHfsC+UJR5g/zozzP8QSbyu4M2VUS5pZjsNmfutHbOEur6BSfoPZhBb3KZV50nS/eA==
  dependencies:
    "@uppy/audio" "^1.1.9"
    "@uppy/aws-s3" "^3.6.2"
    "@uppy/aws-s3-multipart" "^3.12.0"
    "@uppy/box" "^2.4.0"
    "@uppy/companion-client" "^3.8.2"
    "@uppy/compressor" "^1.1.4"
    "@uppy/core" "^3.13.1"
    "@uppy/dashboard" "^3.9.1"
    "@uppy/drag-drop" "^3.1.1"
    "@uppy/drop-target" "^2.1.0"
    "@uppy/dropbox" "^3.4.0"
    "@uppy/facebook" "^3.3.1"
    "@uppy/file-input" "^3.1.2"
    "@uppy/form" "^3.2.2"
    "@uppy/golden-retriever" "^3.2.0"
    "@uppy/google-drive" "^3.6.0"
    "@uppy/google-photos" "^0.1.0"
    "@uppy/image-editor" "^2.4.6"
    "@uppy/informer" "^3.1.0"
    "@uppy/instagram" "^3.3.1"
    "@uppy/onedrive" "^3.4.0"
    "@uppy/progress-bar" "^3.1.1"
    "@uppy/provider-views" "^3.13.0"
    "@uppy/redux-dev-tools" "^3.0.3"
    "@uppy/remote-sources" "^1.3.0"
    "@uppy/screen-capture" "^3.2.0"
    "@uppy/status-bar" "^3.3.3"
    "@uppy/store-default" "^3.2.2"
    "@uppy/store-redux" "^3.0.7"
    "@uppy/thumbnail-generator" "^3.1.0"
    "@uppy/transloadit" "^3.8.1"
    "@uppy/tus" "^3.5.5"
    "@uppy/unsplash" "^3.3.1"
    "@uppy/url" "^3.6.1"
    "@uppy/webcam" "^3.4.2"
    "@uppy/xhr-upload" "^3.6.8"
    "@uppy/zoom" "^2.3.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

url-parse@^1.5.7:
  version "1.5.10"
  resolved "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-callback-ref@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz"
  integrity sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==
  dependencies:
    tslib "^2.0.0"

use-debounce@^9.0.4:
  version "9.0.4"
  resolved "https://registry.npmjs.org/use-debounce/-/use-debounce-9.0.4.tgz"
  integrity sha512-6X8H/mikbrt0XE8e+JXRtZ8yYVvKkdYRfmIhWZYsP8rcNs9hk3APV8Ua2mFkKRLcJKVdnX2/Vwrmg2GWKUQEaQ==

use-isomorphic-layout-effect@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/use-isomorphic-layout-effect/-/use-isomorphic-layout-effect-1.1.2.tgz"
  integrity sha512-49L8yCO3iGT/ZF9QttjwLF/ZD9Iwto5LnH5LmEdk/6cFmXddqi2ulF0edxTwjj+7mqvpVVGQWvbXZdn32wRSHA==

use-sidecar@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz"
  integrity sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1.2.2:
  version "1.4.0"
  resolved "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.4.0.tgz"
  integrity sha512-9WXSPC5fMv61vaupRkCKCxsPxBocVnwakBEkMIHHpkTTg6icbJtg6jzgtLDm4bl3cSHAca52rYWih0k4K3PfHw==

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

util@^0.12.4:
  version "0.12.5"
  resolved "https://registry.npmjs.org/util/-/util-0.12.5.tgz"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utrie@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz"
  integrity sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==
  dependencies:
    base64-arraybuffer "^1.0.2"

vite-compatible-readable-stream@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npmjs.org/vite-compatible-readable-stream/-/vite-compatible-readable-stream-3.6.1.tgz"
  integrity sha512-t20zYkrSf868+j/p31cRIGN28Phrjm3nRSLR2fyc2tiWi4cZGVdv68yNlwnIINTkMTmPoMiSlc0OadaO7DXZaQ==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

vite-plugin-compression@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmjs.org/vite-plugin-compression/-/vite-plugin-compression-0.5.1.tgz"
  integrity sha512-5QJKBDc+gNYVqL/skgFAP81Yuzo9R+EAf19d+EtsMF/i8kFUpNi3J/H01QD3Oo8zBQn+NzoCIFkpPLynoOzaJg==
  dependencies:
    chalk "^4.1.2"
    debug "^4.3.3"
    fs-extra "^10.0.0"

vite-plugin-eslint@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/vite-plugin-eslint/-/vite-plugin-eslint-1.8.1.tgz"
  integrity sha512-PqdMf3Y2fLO9FsNPmMX+//2BF5SF8nEWspZdgl4kSt7UvHDRHVVfHvxsD7ULYzZrJDGRxR81Nq7TOFgwMnUang==
  dependencies:
    "@rollup/pluginutils" "^4.2.1"
    "@types/eslint" "^8.4.5"
    rollup "^2.77.2"

"vite@^4.2.0 || ^5.0.0 || ^6.0.0", vite@^4.3.5, vite@>=2, vite@>=2.0.0:
  version "4.5.5"
  resolved "https://registry.npmjs.org/vite/-/vite-4.5.5.tgz"
  integrity sha512-ifW3Lb2sMdX+WU91s3R0FyQlAyLxOzCSCP37ujw0+r5POeHPwe6udWVIElKQq8gk3t7b8rkmvqC6IHBpCff4GQ==
  dependencies:
    esbuild "^0.18.10"
    postcss "^8.4.27"
    rollup "^3.27.1"
  optionalDependencies:
    fsevents "~2.3.2"

warning@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/warning/-/warning-3.0.0.tgz"
  integrity sha512-jMBt6pUrKn5I+OGgtQ4YZLdhIeJmObddh6CsibPxyQ5yPZm1XExSyzC1LCNX7BzhxWgiHmizBWJTHJIjMjTQYQ==
  dependencies:
    loose-envify "^1.0.0"

warning@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

whatwg-fetch@>=0.10.0:
  version "3.6.20"
  resolved "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz"
  integrity sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.0.tgz"
  integrity sha512-Ei7Miu/AXe2JJ4iNF5j/UphAgRoma4trE6PtisM09bPygb3egMH3YLW/befsWb1A1AxvNSFidOFTB18XtnIIng==
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.0"
    is-number-object "^1.1.0"
    is-string "^1.1.0"
    is-symbol "^1.1.0"

which-builtin-type@^1.1.4:
  version "1.2.0"
  resolved "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.0.tgz"
  integrity sha512-I+qLGQ/vucCby4tf5HsLmGueEla4ZhwTBSqaooS+Y0BuxN4Cp+okmGuV+8mXZ84KDI9BA+oklo+RzKg0ONdSUA==
  dependencies:
    call-bind "^1.0.7"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.0.5"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.1.4"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.0.2"
    which-collection "^1.0.2"
    which-typed-array "^1.1.15"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz"
  integrity sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.14, which-typed-array@^1.1.15, which-typed-array@^1.1.2:
  version "1.1.16"
  resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.16.tgz"
  integrity sha512-g+N+GAWiRj66DngFwHvISJd+ITsyphZvD1vChfVg6cEdnzy53GzB3oy0fUNlvhz7H7+MiqhYr26qxQShCpKTTQ==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.2:
  version "1.1.5"
  resolved "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

wildcard@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/wildcard/-/wildcard-1.1.2.tgz"
  integrity sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng==

wmf@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wmf/-/wmf-1.0.2.tgz"
  integrity sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==

word@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/word/-/word-0.3.0.tgz"
  integrity sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@^7.4.6:
  version "7.5.10"
  resolved "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz"
  integrity sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==

xlsx-js-style@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/xlsx-js-style/-/xlsx-js-style-1.2.0.tgz"
  integrity sha512-DDT4FXFSWfT4DXMSok/m3TvmP1gvO3dn0Eu/c+eXHW5Kzmp7IczNkxg/iEPnImbG9X0Vb8QhROda5eatSR/97Q==
  dependencies:
    adler-32 "~1.2.0"
    cfb "^1.1.4"
    codepage "~1.14.0"
    commander "~2.17.1"
    crc-32 "~1.2.0"
    exit-on-epipe "~1.0.1"
    fflate "^0.3.8"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xlsx@^0.18.5:
  version "0.18.5"
  resolved "https://registry.npmjs.org/xlsx/-/xlsx-0.18.5.tgz"
  integrity sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==
  dependencies:
    adler-32 "~1.3.0"
    cfb "~1.2.1"
    codepage "~1.15.0"
    crc-32 "~1.2.1"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xmlhttprequest@^1.8.0:
  version "1.8.0"
  resolved "https://registry.npmjs.org/xmlhttprequest/-/xmlhttprequest-1.8.0.tgz"
  integrity sha512-58Im/U0mlVBLM38NdZjHyhuMtCqa61469k2YP/AaPbvCoV9aQGUpbJBj1QRm2ytRiVQBD/fsw7L2bJGDVQswBA==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@^1.10.0, yaml@^1.10.2:
  version "1.10.2"
  resolved "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yaml@^2.3.4:
  version "2.6.1"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.6.1.tgz"
  integrity sha512-7r0XPzioN/Q9kXBro/XPnA6kznR73DHq+GXh5ON7ZozRO6aMjbmiBuKste2wslTFkC5d1dw0GooOCepZXJ2SAg==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

yoga-layout@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/yoga-layout/-/yoga-layout-2.0.1.tgz"
  integrity sha512-tT/oChyDXelLo2A+UVnlW9GU7CsvFMaEnd9kVFsaiCQonFAXd3xrHhkLYu+suwwosrAEQ746xBU+HvYtm1Zs2Q==

yup@^1.1.1:
  version "1.5.0"
  resolved "https://registry.npmjs.org/yup/-/yup-1.5.0.tgz"
  integrity sha512-NJfBIHnp1QbqZwxcgl6irnDMIsb/7d1prNhFx02f1kp8h+orpi4xs3w90szNpOh68a/iHPdMsYvhZWoDmUvXBQ==
  dependencies:
    property-expr "^2.0.5"
    tiny-case "^1.0.3"
    toposort "^2.0.2"
    type-fest "^2.19.0"
