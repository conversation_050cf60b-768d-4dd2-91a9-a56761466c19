import"../vendor-3aca5368.js";import{t as p}from"./dashboard-9ed0e038.js";import"./drag-drop-593d2a9d.js";import{y as e}from"../@fullcalendar/core-ff88745d.js";import{U as n}from"./core-5d4a6f29.js";const a={strings:{chooseFiles:"Choose files"}},l={version:"3.1.2"},h={pretty:!0,inputName:"files[]"};class d extends n{constructor(t,s){super(t,{...h,...s}),this.id=this.opts.id||"FileInput",this.title="File Input",this.type="acquirer",this.defaultLocale=a,this.i18nInit(),this.render=this.render.bind(this),this.handleInputChange=this.handleInputChange.bind(this),this.handleClick=this.handleClick.bind(this)}addFiles(t){const s=t.map(i=>({source:this.id,name:i.name,type:i.type,data:i}));try{this.uppy.addFiles(s)}catch(i){this.uppy.log(i)}}handleInputChange(t){this.uppy.log("[FileInput] Something selected through input...");const s=p(t.target.files);this.addFiles(s),t.target.value=null}handleClick(){this.input.click()}render(){const t={width:"0.1px",height:"0.1px",opacity:0,overflow:"hidden",position:"absolute",zIndex:-1},{restrictions:s}=this.uppy.opts,i=s.allowedFileTypes?s.allowedFileTypes.join(","):void 0;return e("div",{className:"uppy-FileInput-container"},e("input",{className:"uppy-FileInput-input",style:this.opts.pretty?t:void 0,type:"file",name:this.opts.inputName,onChange:this.handleInputChange,multiple:s.maxNumberOfFiles!==1,accept:i,ref:r=>{this.input=r}}),this.opts.pretty&&e("button",{className:"uppy-FileInput-btn",type:"button",onClick:this.handleClick},this.i18n("chooseFiles")))}install(){const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.unmount()}}d.VERSION=l.version;const u={version:"3.1.1"},c={target:"body",fixed:!1,hideAfterFinish:!0};class y extends n{constructor(t,s){super(t,{...c,...s}),this.id=this.opts.id||"ProgressBar",this.title="Progress Bar",this.type="progressindicator",this.render=this.render.bind(this)}render(t){const s=t.totalProgress||0,i=(s===0||s===100)&&this.opts.hideAfterFinish;return e("div",{className:"uppy uppy-ProgressBar",style:{position:this.opts.fixed?"fixed":"initial"},"aria-hidden":i},e("div",{className:"uppy-ProgressBar-inner",style:{width:`${s}%`}}),e("div",{className:"uppy-ProgressBar-percentage"},s))}install(){const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.unmount()}}y.VERSION=u.version;
