import { space } from "postcss/lib/list";
import MkdSDK from "../utils/MkdSDK";
import { removeKeysWhenValueIsNull } from "Utils/utils";

let sdk = new MkdSDK();

export const getAllProjectAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/project/get_all`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllProjectAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/project/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllProjectWithMultiFiltersAPI = async (
  page,
  limit,
  filter
) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/project/retrieve_all_multi_filter`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateProjectAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const getProjectDetailsAPI = async (id, filters) => {
  const payload = {
    page: 1,
    ...(filters && Object.keys(filters).length > 0 && { filter: filters }),
  };
  try {
    const uri = `/v3/api/custom/equality_record/project/view/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getProjectDetailsManagerAPI = async (id, filters) => {
  const payload = {
    page: 1,
    ...(filters && Object.keys(filters).length > 0 && { filter: filters }),
  };
  try {
    const uri = `/v3/api/custom/equality_record/project/manager/view/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getProjectDetailsAdminAPI = async (id, filters) => {
  const payload = {
    page: 1,
    ...(filters && Object.keys(filters).length > 0 && { filter: filters }),
  };
  try {
    const uri = `/v3/api/custom/equality_record/project/admin/view/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const addProjectAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/add`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteProjectAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteAllProjectForUserAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/real/cleanup`;
    const res = await sdk.callRawAPI(uri, payload, "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const addSubProjectAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateSubProjectUploadAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/details`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateSubProjectAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateSubProjectDetailsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/details`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteSubProjectsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/delete`;
    const res = await sdk.callRawAPI(uri, payload, "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateSubProjectStatusAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/status`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const getSubProjectsByProjectIdAPI = async (project_id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/${project_id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getSurveyByProjectIdAndUuidAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/survey/view`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllProjectFileAPI = async (project_id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/file/${project_id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const createFileAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/file`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllIdeaAPI = async (project_id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/idea/${project_id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllProjectIdeasBySubProjectIdAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/idea/sub_project/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateIdeaAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/idea/update`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const addIdeaAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/idea`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const addAndAssignIdeaAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/assign/idea`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const addAndAssignIdeaToMultiSubProjectsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/multi/sub_project/assign/idea`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const assignSubProjectIdeaAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/idea`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const assignSubProjectIdeasAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/ideas`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteSubProjectIdeaAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/idea`;
    const res = await sdk.callRawAPI(uri, payload, "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteIdeaAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/unassigned/idea`;
    const res = await sdk.callRawAPI(uri, payload, "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteOneFileAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/file/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllSubProjectIdeaAPI = async (subproject_id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/idea/${subproject_id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getPDF = async (url) => {
  try {
    const uri = `/v3/api/custom/equality_record/pdf/generate`;
    const res = await sdk.callRawAPI(uri, { url }, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllUnAssignedSubProjectsAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/project/unassigned/sub_project`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllUnAssignedSubProjectsByEmployeeIdAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/unassigned/sub_project/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};
// subProjects.find((subproject) => subproject.id == id) || null;

export const getAllUnAssignedSubSongsAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/project/unassigned/songs`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getSurveyByProjectIdAPI = async (project_id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/survey/view/${project_id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllLyricsByProjectIdAPI = async (project_id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/lyrics/${project_id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllMasterFilesByProjectIdAPI = async (project_id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/master_files/${project_id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllMasterProjectsAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/project/master/view`;
    const res = await sdk.callRawAPI(uri, [], "GET");

    return res;
  } catch (error) {
    return error;
  }
};

export const updateSubProjectEightCountAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/eight_count`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateSubProjectEmployeeAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/employee`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const resetSubProjectEmployeeAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/employee/reset`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateSubProjectEmployeeCostAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/employee/cost`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const reassignSongsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/reassign/songs`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const attachProjectsToSong = async (songId, projectId) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/sub_project/${songId}`;
    const res = await sdk.callRawAPI(
      uri,
      {
        project_ids: projectId,
        subproject_id: songId,
      },
      "PUT"
    );
    return res;
  } catch (error) {
    throw error;
  }
};
