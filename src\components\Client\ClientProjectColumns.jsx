import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import moment from "moment";
import React from "react";
import { useNavigate, useParams } from "react-router";
import { AuthContext } from "Src/authContext";

import PaymentStatus from "./PaymentStatus";

const ClientProjectColumns = ({ row, indexe }) => {
  const { state: authState } = React.useContext(AuthContext);

  const navigate = useNavigate();

  const columns = [
    {
      header: "Mix Date",
      accessor: "mix_date",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: false,
      mappings: {},
    },
    {
      header: "Program/Team",
      accessor: "program&team",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: false,
      mappings: {},
    },
    {
      header: "Producer",
      accessor: "producer",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: false,
      mappings: {},
    },

    {
      header: "Mix Type",
      accessor: "mix_type_name",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: false,
      mappings: {},
    },
    {
      header: "Team Type",
      accessor: "team_type",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: true,
      mappings: {
        1: "All Girl",
        2: "Co-ed",
        3: "TBD",
      },
    },
    {
      header: "PAYMENT STATUS",
      accessor: "payment_status",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: false,
      mappings: {},
    },
    {
      header: "TD/ED",
      accessor: "Td&Ed",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: false,
      mappings: {},
    },
    {
      header: "STATUS",
      accessor: "status",
      isSorted: false,
      isSortedDesc: false,
      mappingExist: false,
      mappings: {},
    },
  ];

  const OpenTabInViewPage = async (tabNo) => {
    await localStorage.setItem("ClientSelectedTab", tabNo);
    navigate(`/${authState?.role}/view-project/${row?.id}`);
  };

  function convertDateFormat(dateString) {
    // Split the date string into year, month, and day

    if (dateString) {
      var parts = dateString?.split("-");

      // Rearrange the parts into the desired format
      var formattedDate =
        parts[1].padStart(2, "0") +
        "-" +
        parts[2].padStart(2, "0") +
        "-" +
        parts[0];

      return formattedDate;
    }
  }

  return (
    <tr
      className={`border-b   border-b-[#9ca3ae80] font-medium   ${
        true ? "bg-boxdark hover:bg-primary/5" : "bg-meta-4 hover:bg-meta-4/80"
      }`}
      onClick={async (e) => {
        e.stopPropagation();
        if (!e.target.className.includes("lightup-icons")) {
          navigate(`/${authState.role}/view-project/` + row.id, {
            state: row,
          });
        }
      }}
    >
      {columns.map((cell, index) => {
        if (cell.accessor === "mix_date") {
          return (
            <td
              key={index}
              className="px-4 py-5 text-sm font-medium text-white whitespace-nowrap"
            >
              {moment(row[cell.accessor]).format("MM-DD-YYYY")}
            </td>
          );
        }

        if (cell.accessor === "program&team") {
          return (
            <td
              key={index}
              className="px-4 py-5 text-sm font-medium text-white"
            >
              <div className="flex flex-col gap-[2px]">
                <span className="text-white">{row.program_name}</span>
                <span className="text-white">{row.team_name}</span>
              </div>
            </td>
          );
        }

        if (cell.accessor === "producer") {
          return (
            <td
              key={index}
              className="px-4 py-5 text-sm font-medium text-white"
            >
              {row.producer_name}
            </td>
          );
        }

        if (cell.accessor === "Td&Ed") {
          return (
            <td
              key={index}
              className="px-4 py-5 text-sm font-medium text-white"
            >
              <div className="flex flex-col gap-[2px]">
                <div className="relative group">
                  <span className="text-white whitespace-nowrap">
                    TD:&nbsp;&nbsp;{convertDateFormat(row.team_details_date)}
                  </span>
                  <div className="hidden absolute -top-4 flex-col items-center group-hover:flex">
                    <span className="relative z-10 p-2 text-xs text-white bg-black rounded shadow-lg">
                      Team Details Date
                    </span>
                  </div>
                </div>

                <div className="relative group">
                  <span className="text-white whitespace-nowrap">
                    ED:&nbsp;&nbsp;
                    {convertDateFormat(row.estimated_delivery_date)}
                  </span>
                  <div className="hidden absolute top-5 flex-col items-center group-hover:flex">
                    <span className="relative z-10 p-2 text-xs text-white bg-black rounded shadow-lg">
                      Estimated Delivery Date
                    </span>
                  </div>
                </div>
              </div>
            </td>
          );
        }

        if (cell.accessor === "status") {
          return (
            <td
              key={index}
              className="px-4 py-5 text-sm font-medium text-white"
            >
              <div className="flex gap-4 justify-center items-center">
                <div className="flex flex-col items-center">
                  <div
                    onClick={() => OpenTabInViewPage(0)}
                    className="relative cursor-pointer group"
                  >
                    <div
                      className={`relative h-[2.5rem] w-[2.5rem] rounded-[50%] ${
                        row.team_details_found
                          ? "ring-[4px] ring-primary"
                          : "ring-[4px] ring-[#8c8c8c]"
                      }`}
                    >
                      <div className="absolute inset-[7px] rounded-full border-2 border-white bg-boxdark">
                        <div className="flex absolute inset-0 justify-center items-center">
                          {!row.team_details_found ? (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(315deg) translateY(-2px)", // 8 o'clock when inactive
                              }}
                            ></div>
                          ) : (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(135deg) translateY(-2px)", // 4 o'clock when active
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                      <div
                        className={`bg- absolute bottom-[-10px] left-0 right-0 h-[15px] border-t-0 border-white bg-boxdark ${
                          true
                            ? "bg-boxdark group-hover:bg-[#253247]"
                            : "bg-meta-4"
                        }`}
                      ></div>
                    </div>
                    <div className="hidden absolute -bottom-8 flex-col items-center group-hover:flex">
                      <span className="relative z-10 p-1 text-xs leading-none text-center text-gray-900 whitespace-nowrap bg-gray-100 rounded shadow-lg">
                        Team Details
                      </span>
                    </div>
                  </div>
                  <span className="mt-2 text-xs text-white whitespace-nowrap">
                    Team Details
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <div
                    onClick={() => OpenTabInViewPage(1)}
                    className="relative cursor-pointer group"
                  >
                    <div
                      className={`relative h-[2.5rem] w-[2.5rem] rounded-[50%] ${
                        row.survey_status
                          ? "ring-[4px] ring-primary"
                          : "ring-[4px] ring-[#8c8c8c]"
                      }`}
                    >
                      <div className="absolute inset-[7px] rounded-full border-2 border-white bg-boxdark">
                        <div className="flex absolute inset-0 justify-center items-center">
                          {!row.survey_status ? (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(315deg) translateY(-2px)", // 8 o'clock when inactive
                              }}
                            ></div>
                          ) : (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(135deg) translateY(-2px)", // 4 o'clock when active
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                      <div
                        className={`bg- absolute bottom-[-10px] left-0 right-0 h-[15px] border-t-0 border-white bg-boxdark ${
                          true
                            ? "bg-boxdark group-hover:bg-[#253247]"
                            : "bg-meta-4"
                        }`}
                      ></div>
                    </div>
                    <div className="hidden absolute -bottom-8 flex-col items-center group-hover:flex">
                      <span className="relative z-10 p-1 text-xs leading-none text-center text-gray-900 bg-gray-100 rounded shadow-lg whitespace-no-wrap">
                        Survey
                      </span>
                    </div>
                  </div>
                  <span className="mt-2 text-xs text-white">Survey</span>
                </div>

                <div className="flex flex-col items-center">
                  <div
                    onClick={() => OpenTabInViewPage(2)}
                    className="relative cursor-pointer group"
                  >
                    <div
                      className={`relative h-[2.5rem] w-[2.5rem] rounded-[50%] ${
                        row.eight_count_found
                          ? "ring-[4px] ring-primary"
                          : "ring-[4px] ring-[#8c8c8c]"
                      }`}
                    >
                      <div className="absolute inset-[7px] rounded-full border-2 border-white bg-boxdark">
                        <div className="flex absolute inset-0 justify-center items-center">
                          {!row.eight_count_found ? (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(315deg) translateY(-2px)", // 8 o'clock when inactive
                              }}
                            ></div>
                          ) : (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(135deg) translateY(-2px)", // 4 o'clock when active
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                      <div
                        className={`bg- absolute bottom-[-10px] left-0 right-0 h-[15px] border-t-0 border-white bg-boxdark ${
                          true
                            ? "bg-boxdark group-hover:bg-[#253247]"
                            : "bg-meta-4"
                        }`}
                      ></div>
                    </div>
                    <div className="hidden absolute -bottom-8 flex-col items-center group-hover:flex">
                      <span className="relative z-10 p-1 text-xs leading-none text-center text-gray-900 bg-gray-100 rounded shadow-lg whitespace-no-wrap">
                        8-Count Sheets
                      </span>
                    </div>
                  </div>
                  <span className="mt-2 text-xs text-white whitespace-nowrap">
                    8 Counts
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <div
                    onClick={() => OpenTabInViewPage(3)}
                    className="relative cursor-pointer group"
                  >
                    <div
                      className={`relative h-[2.5rem] w-[2.5rem] rounded-[50%] ${
                        row.media_found
                          ? "ring-[4px] ring-primary"
                          : "ring-[4px] ring-[#8c8c8c]"
                      }`}
                    >
                      <div className="absolute inset-[7px] rounded-full border-2 border-white bg-boxdark">
                        <div className="flex absolute inset-0 justify-center items-center">
                          {!row.media_found ? (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(315deg) translateY(-2px)", // 8 o'clock when inactive
                              }}
                            ></div>
                          ) : (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(135deg) translateY(-2px)", // 4 o'clock when active
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                      <div
                        className={`bg- absolute bottom-[-10px] left-0 right-0 h-[15px] border-t-0 border-white bg-boxdark ${
                          true
                            ? "bg-boxdark group-hover:bg-[#253247]"
                            : "bg-meta-4"
                        }`}
                      ></div>
                    </div>
                    <div className="hidden absolute -bottom-8 flex-col items-center group-hover:flex">
                      <span className="relative z-10 p-1 text-xs leading-none text-center text-gray-900 bg-gray-100 rounded shadow-lg whitespace-no-wrap">
                        Videos
                      </span>
                    </div>
                  </div>
                  <span className="mt-2 text-xs text-white">Video</span>
                </div>

                <div className="flex flex-col items-center">
                  <div
                    onClick={() => OpenTabInViewPage(3)}
                    className="relative cursor-pointer group"
                  >
                    <div
                      className={`relative h-[2.5rem] w-[2.5rem] rounded-[50%] ${
                        row.is_music_found
                          ? "ring-[4px] ring-primary"
                          : "ring-[4px] ring-[#8c8c8c]"
                      }`}
                    >
                      <div className="absolute inset-[7px] rounded-full border-2 border-white bg-boxdark">
                        <div className="flex absolute inset-0 justify-center items-center">
                          {!row.is_music_found ? (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(315deg) translateY(-2px)", // 8 o'clock when inactive
                              }}
                            ></div>
                          ) : (
                            <div
                              className="h-[8px] w-[1.5px] origin-bottom bg-white"
                              style={{
                                transform: "rotate(135deg) translateY(-2px)", // 4 o'clock when active
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                      <div
                        className={`bg- absolute bottom-[-10px] left-0 right-0 h-[15px] border-t-0 border-white bg-boxdark ${
                          true
                            ? "bg-boxdark group-hover:bg-[#253247]"
                            : "bg-meta-4"
                        }`}
                      ></div>
                    </div>
                    <div className="hidden absolute -bottom-8 flex-col items-center group-hover:flex">
                      <span className="relative z-10 p-1 text-xs leading-none text-center text-gray-900 bg-gray-100 rounded shadow-lg whitespace-no-wrap">
                        Music/Licenses Uploaded
                      </span>
                    </div>
                  </div>
                  <span className="mt-2 text-xs text-white">Music</span>
                </div>
              </div>
            </td>
          );
        }
        if (cell.accessor === "payment_status") {
          return (
            <td key={index} className="px-4 py-4 text-white whitespace-nowrap">
              <PaymentStatus status={row.payment_status} />
            </td>
          );
        }
        if (cell.mappingExist) {
          return (
            <td key={index} className="px-4 py-4 whitespace-nowrap">
              {cell.mappings[row[cell.accessor]]}
            </td>
          );
        }
        return (
          <td key={index} className="px-4 py-4 text-white whitespace-nowrap">
            {row[cell.accessor]}
          </td>
        );
      })}
    </tr>
  );
};

export default ClientProjectColumns;
