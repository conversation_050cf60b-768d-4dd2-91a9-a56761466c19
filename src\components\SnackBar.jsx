import React from "react";
import { GlobalContext } from "../globalContext";
const SnackBar = () => {
  console.log(React.useContext(GlobalContext));
  const {
    state: { globalMessage, toastStatus },
    dispatch,
  } = React.useContext(GlobalContext);
  console.log(globalMessage);
  const show = globalMessage?.length > 0;
  return show ? (
    <div
      id="mkd-toast"
      className={`shadow-default fixed left-1/2 top-5 z-[10000] flex -translate-x-1/2 items-center  rounded border border-strokedark p-4 ${
        toastStatus === "success"
          ? "bg-emerald-600"
          : toastStatus === "error"
          ? "bg-danger"
          : "bg-meta-6"
      }`}
      role="alert"
    >
      <div className="text-[1.2rem] font-medium text-white">
        {globalMessage}
      </div>
      <div className="flex items-center ml-4">
        <button
          type="button"
          className="inline-flex h-8 w-8 items-center justify-center rounded-lg border border-strokedark bg-boxdark p-1.5 text-white hover:bg-meta-4"
          aria-label="Close"
          onClick={() => {
            dispatch({ type: "SNACKBAR", payload: { message: "" } });
          }}
        >
          <span className="sr-only">Close</span>
          <svg
            className="w-5 h-5"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            ></path>
          </svg>
        </button>
      </div>
    </div>
  ) : null;
};

export default SnackBar;
