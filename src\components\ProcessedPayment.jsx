import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";

const ProcessedPayment = () => {
  return (
    <section className="mt-10 w-full bg-gray-700 p-5">
      <h2 className="text-xl font-bold">Processed Payments</h2>
      <p className="mt-3">
        {" "}
        View payments made in the ast 5 years. Note that check or money order
        payments may take up to 3 weeks to show here.
      </p>
      <p className="mt-3"> This list does not include tax witholding</p>
      <p className="mt-3">EFT: Electronic Funds Transfer</p>
      <div className="mt-7 w-full overflow-x-auto rounded-md shadow">
        <table className="w-full min-w-[1200px]  divide-y  divide-gray-200  ">
          <thead className="bg-gray-900">
            <th
              scope="col"
              className=" py-3  text-center text-xs font-semibold  uppercase tracking-wider text-white"
            >
              Date
            </th>
            <th
              scope="col"
              className=" py-3  text-center text-xs font-semibold uppercase tracking-wider text-white"
            >
              Tax Year
            </th>
            <th
              scope="col"
              className=" py-3  text-center text-xs font-semibold uppercase tracking-wider text-white"
            >
              Type
            </th>

            <tr className="cursor-pointer hover:bg-gray-800">
              <td className=" py-3  text-center text-xs  uppercase tracking-wider text-white">
                Jan 6,2024
              </td>
              <td className=" py-3  text-center text-xs  capitalize tracking-wider text-white ">
                2023
              </td>
              <td className=" py-3  text-center text-xs font-medium capitalize tracking-wider text-white">
                Estimated Tax Payment
              </td>

              <td className=" py-3 text-center  text-xs  font-medium capitalize tracking-wider text-white">
                Credit/Debit Card
              </td>
              <td className=" py-3  text-center  text-xs font-medium capitalize tracking-wider text-white">
                $5,500.00
              </td>
              <td className=" py-3   text-center  text-xs font-medium capitalize tracking-wider text-white">
                2838838848884841
              </td>
            </tr>
            <th
              scope="col"
              className=" py-3  text-xs  font-semibold uppercase tracking-wider text-white"
            >
              Payment Method
            </th>
            <th
              scope="col"
              className=" py-3  text-xs  font-semibold uppercase tracking-wider text-white"
            >
              Amount
            </th>
            <th
              scope="col"
              className=" py-3  text-xs  font-semibold uppercase tracking-wider text-white"
            >
              EFT Number*
            </th>
          </thead>
          <tbody className="divide-y divide-gray-100 bg-neutral-950  text-white">
            <tr className="cursor-pointer hover:bg-gray-800">
              <td className=" py-3  text-center text-xs  uppercase tracking-wider text-white">
                Jan 30,2023
              </td>
              <td className=" py-3  text-center text-xs  capitalize tracking-wider text-white ">
                2023
              </td>
              <td className=" py-3  text-center text-xs font-medium capitalize tracking-wider text-white">
                Estimated Tax Payment
              </td>

              <td className=" py-3 text-center  text-xs  font-medium capitalize tracking-wider text-white">
                Credit/Debit Card
              </td>
              <td className=" py-3  text-center  text-xs font-medium capitalize tracking-wider text-white">
                $5,500.00
              </td>
              <td className=" py-3   text-center  text-xs font-medium capitalize tracking-wider text-white">
                2838838848884841
              </td>
            </tr>
            <tr className="cursor-pointer hover:bg-gray-800">
              <td className=" py-3  text-center text-xs  uppercase tracking-wider text-white">
                Jan 6,2023
              </td>
              <td className=" py-3  text-center text-xs  capitalize tracking-wider text-white ">
                2023
              </td>
              <td className=" py-3  text-center text-xs font-medium capitalize tracking-wider text-white">
                Estimated Tax Payment
              </td>

              <td className=" py-3 text-center  text-xs  font-medium capitalize tracking-wider text-white">
                Credit/Debit Card
              </td>
              <td className=" py-3  text-center  text-xs font-medium capitalize tracking-wider text-white">
                $5,500.00
              </td>
              <td className=" py-3   text-center  text-xs font-medium capitalize tracking-wider text-white">
                2838838848884841
              </td>
            </tr>
            <tr className="cursor-pointer hover:bg-gray-800">
              <td className=" py-3  text-center text-xs  uppercase tracking-wider text-white">
                Dec 26,2023
              </td>
              <td className=" py-3  text-center text-xs  capitalize tracking-wider text-white ">
                2023
              </td>
              <td className=" py-3  text-center text-xs font-medium capitalize tracking-wider text-white">
                Estimated Tax Payment
              </td>

              <td className=" py-3 text-center  text-xs  font-medium capitalize tracking-wider text-white">
                Credit/Debit Card
              </td>
              <td className=" py-3  text-center  text-xs font-medium capitalize tracking-wider text-white">
                $5,500.00
              </td>
              <td className=" py-3   text-center  text-xs font-medium capitalize tracking-wider text-white">
                2838838848884841
              </td>
            </tr>
            <tr className="cursor-pointer hover:bg-gray-800">
              <td className=" py-3  text-center text-xs  uppercase tracking-wider text-white">
                Mar 12,2024
              </td>
              <td className=" py-3  text-center text-xs  capitalize tracking-wider text-white ">
                2024
              </td>
              <td className=" py-3  text-center text-xs font-medium capitalize tracking-wider text-white">
                Estimated Tax Payment
              </td>

              <td className=" py-3 text-center  text-xs  font-medium capitalize tracking-wider text-white">
                Credit/Debit Card
              </td>
              <td className=" py-3  text-center  text-xs font-medium capitalize tracking-wider text-white">
                $5,500.00
              </td>
              <td className=" py-3   text-center  text-xs font-medium capitalize tracking-wider text-white">
                2838838848884841
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>
  );
};

export default ProcessedPayment;
