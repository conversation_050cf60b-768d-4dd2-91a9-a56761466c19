import React from "react";

const UploadProgressBar = ({ progress, isUploading, error }) => {
  if (!isUploading && !error) return null;

  return (
    <div className="mt-1">
      {isUploading && (
        <div className="relative pt-1">
          <div className="flex gap-1 justify-between items-center mb-2">
            <div>
              <span className="inline-block rounded-full bg-white px-2 py-1 text-[10px] font-semibold uppercase text-blue-600">
                Uploading
              </span>
            </div>
            <div className="text-right">
              <span className="inline-block text-xs font-semibold text-blue-600">
                {progress}%
              </span>
            </div>
          </div>
          <div className="flex overflow-hidden mb-1 h-2 text-xs bg-white rounded">
            <div
              style={{ width: `${progress}%` }}
              className="flex flex-col justify-center text-center text-white whitespace-nowrap bg-blue-500 shadow-none transition-all duration-500 ease-in-out"
            ></div>
          </div>
        </div>
      )}
      {error && (
        <div className="mt-2 text-sm text-red-500">
          {error.message || "An error occurred during upload."}
        </div>
      )}
    </div>
  );
};

export default UploadProgressBar;
