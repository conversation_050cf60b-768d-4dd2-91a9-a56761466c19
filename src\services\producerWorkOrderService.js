// import axios from 'axios';
import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const addProducerWorkOrderAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/producer_work_order/add`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllProducerWorkOrderAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/producer_work_order/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getProducerWorkOrderPublicDetailsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/producer_work_order/public/details`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const updateProducerWorkOrderAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/producer_work_order/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {
    return error;
  }
};
