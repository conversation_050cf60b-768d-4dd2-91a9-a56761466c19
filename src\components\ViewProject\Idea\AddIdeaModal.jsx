import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const AddIdeaModal = ({
  ideas,
  subProjectTypeName = null,
  setModalClose,
  theme,
  setIdeaAddForm,
  projectId = null,
  onUpdateNote = null,
  initialNote = "",
}) => {
  const [newIdea, setNewIdea] = React.useState(null);
  const [activeTab, setActiveTab] = React.useState(projectId ? "idea" : "note");
  const [note, setNote] = React.useState(initialNote || "");

  const handleSubmitNewIdea = (e) => {
    e.preventDefault();
    if (activeTab === "idea" && !newIdea) {
      return;
    }
    if (activeTab === "idea") {
      setIdeaAddForm(newIdea);
    } else {
      onUpdateNote(note);
    }
  };

  const onChangeNewIdea = (e) => {
    if (e.target.value !== "") {
      let new_idea_key =
        ideas.length > 0 ? `idea_${ideas.length + 1}` : "idea_1";

      setNewIdea({
        idea_key: new_idea_key,
        idea_value: e.target.value,
      });
    } else {
      setNewIdea(null);
    }
  };

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center">
      <div
        className="fixed inset-0 backdrop-blur-sm bg-black/50"
        onClick={() => setModalClose(false)}
      />
      <div className="w-full max-w-xl rounded border transition-all transform shadow-default border-strokedark bg-boxdark">
        <form>
          {/* Modal Header */}
          <div className="flex justify-between items-center px-6 py-4 border-b border-stroke">
            <div className="flex gap-3 items-center">
              <FontAwesomeIcon
                icon="fa-solid fa-lightbulb"
                className="text-xl text-primary"
              />
              <h3 className="text-xl font-medium text-white">
                {activeTab === "idea" ? "Add & Assign Idea" : "Add Note"}{" "}
                {subProjectTypeName && `for ${subProjectTypeName}`}
              </h3>
            </div>
            <button
              onClick={() => setModalClose(false)}
              className="hover:text-primary"
              type="button"
            >
              <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-stroke">
            <button
              type="button"
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === "idea"
                  ? "border-b-2 border-primary text-primary"
                  : "text-bodydark2"
              } ${!projectId ? "cursor-not-allowed opacity-50" : ""}`}
              onClick={() => projectId && setActiveTab("idea")}
              disabled={!projectId}
            >
              Idea
            </button>
            <button
              type="button"
              className={`h-[35px] px-6 py-3 text-sm font-medium ${
                activeTab === "note"
                  ? "border-b-2 border-primary text-primary"
                  : "text-bodydark2"
              } ${projectId ? "cursor-not-allowed opacity-50" : ""}`}
              onClick={() => !projectId && setActiveTab("note")}
              disabled={projectId}
            >
              Note
            </button>
          </div>

          {/* Theme Section - Only show for Idea tab */}
          {activeTab === "idea" && (
            <div className="px-6 py-4 border-b border-stroke">
              <div className="flex flex-col gap-2">
                <h4 className="text-sm font-medium text-bodydark2">
                  Theme of the Routine
                </h4>
                <p className="text-base text-white">
                  {theme || "No theme specified"}
                </p>
              </div>
            </div>
          )}

          {/* Content Section */}
          <div className="px-6 py-4">
            <div className="mb-4">
              <label
                htmlFor="content"
                className="mb-2.5 block font-medium text-white"
              >
                {activeTab === "idea" ? "Add New Idea" : "Add Note"}
              </label>
              {activeTab === "idea" ? (
                <textarea
                  id="ideaDescription"
                  rows={5}
                  className="px-4.5 w-full rounded border border-stroke bg-boxdark py-3 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                  placeholder="Enter your idea description here..."
                  onChange={(e) => onChangeNewIdea(e)}
                />
              ) : (
                <textarea
                  id="noteContent"
                  rows={5}
                  className="px-4.5 w-full rounded border border-stroke bg-boxdark py-3 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                  placeholder="Enter your note here..."
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                />
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-stroke">
            <div className="flex gap-2">
              <button
                type="submit"
                onClick={(e) => handleSubmitNewIdea(e)}
                className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-primary hover:bg-opacity-90"
                disabled={activeTab === "idea" ? !newIdea : !note}
              >
                {activeTab === "idea" ? "Save Idea" : "Save Note"}
              </button>
              <button
                type="button"
                onClick={() => setModalClose(false)}
                className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-danger hover:bg-opacity-90"
              >
                Cancel
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddIdeaModal;
