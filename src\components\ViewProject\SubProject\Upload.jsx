import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import AddIdeaModal from "Components/ViewProject/Idea/AddIdeaModal";
import ConfirmModal from "Components/Modal/ConfirmModal";
import {
  uploadS3FilesAPI,
  uploadFilesDataAPI,
} from "Src/services/workOrderService";
import {
  addAndAssignIdeaAPI,
  getAllSubProjectIdeaAPI,
} from "Src/services/projectService";
import { replaceNextLineToBrTag, replaceBrTagToNextLine } from "Utils/utils";
import SubProjectShowWriter from "./SubProjectShowWriter";
import SubProjectShowEngineer from "./SubProjectShowEngineer";
import SubProjectCollapseWriterAndArtist from "./SubProjectCollapseWriterAndArtist";
import SubProjectCollapseEngineer from "./SubProjectCollapseEngineer";
import NoWorkOrder from "./NoWorkOrder";
import AddFileSubProject from "../AddFile/FileSubProject";
import UploadShowEngineer from "./UploadShowEngineer";
import { useEffect } from "react";
import { useS3Upload } from "Src/libs/uploads3Hook";

const Upload = ({
  expandAll,
  setTempUploadCount,
  tempUploadCount,
  UploadCount,
  mixSeasons,
  theme,
  isSong = false,
  isEdit,
  ideas,
  projectId,
  setUpdateSubprojectPayload,
  writers,
  artists,
  engineers,
  writer,
  artist,
  engineer,
  surveySubmitStatus,
  setLyrics,
  setEightCountPayload,
  setWriterPayload,
  setWriterCostPayload,
  setArtistPayload,
  setArtistCostPayload,
  setDeleteIdeaPayload,
  subProject,
  setShowAssignIdeaModal,
  setSelectedSubProjectId,
  setSelectedSubProjectIdForDelete,
  setUnSelectedSubProjectIdForDelete,
  setDeleteFileId,
  setLoadIdeaFromViewProject,
  setResetWriterPayload,
  setResetArtistPayload,
}) => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const {
    dispatch: globalDispatch,
    state: { subproject_update },
  } = React.useContext(GlobalContext);

  const navigate = useNavigate();
  const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

  const [selectedWriterId, setSelectedWriterId] = React.useState("");
  const [selectedArtistId, setSelectedArtistId] = React.useState("");
  const [selectedEngineerId, setSelectedEngineerId] = React.useState("");
  const [tempWriterId, setTempWriterId] = React.useState(null);
  const [tempArtistId, setTempArtistId] = React.useState(null);
  const [tempEngineerId, setTempEngineerId] = React.useState(null);
  const [writerCost, setWriterCost] = React.useState(0);
  const [artistCost, setArtistCost] = React.useState(0);
  const [engineerCost, setEngineerCost] = React.useState(0);
  const [totalCost, setTotalCost] = React.useState(0);
  const [showDeleteIdeaModal, setShowDeleteIdeaModal] = React.useState(false);
  const { uploadS3FilesAPI, progress, error, isUploading } = useS3Upload();
  const [deleteIdeaId, setDeleteIdeaId] = React.useState(null);
  const [localEightCount, setLocalEightCount] = React.useState(0);
  const [lyricsVal, setLyricsVal] = React.useState(
    subProject.lyrics ? replaceBrTagToNextLine(subProject.lyrics) : ""
  );

  useEffect(() => {
    setLyricsVal(
      subProject.lyrics ? replaceBrTagToNextLine(subProject.lyrics) : ""
    );
  }, [subProject.lyrics]);
  console.log(lyricsVal, subProject.lyrics, subProject.type_name);
  const [localSelectedSubProjectId, setLocalSelectedSubProjectId] =
    React.useState(null);

  const [workOrderFound, setWorkOrderFound] = React.useState(false);

  const [showAddIdeaModal, setShowAddIdeaModal] = React.useState(false);

  const [showCheckboxToDelete, setShowCheckboxToDelete] = React.useState(false);

  const [assignedIdeas, setAssignedIdeas] = React.useState([]);

  const [showWriterDetails, setShowWriterDetails] = React.useState(false);
  const [showEngineerDetails, setShowEngineerDetails] = React.useState(false);
  const [FileUploadOpen, setFileUploadOpen] = useState(false);

  const getAllSubProjectIdea = async () => {
    if (subProject.id) {
      const result = await getAllSubProjectIdeaAPI(Number(subProject.id));
      if (!result.error) {
        setAssignedIdeas(result.list);
      }
    }
  };

  const handleSelectedSubProjectId = (id) => {
    setLocalSelectedSubProjectId(null);
    if (localSelectedSubProjectId === id) {
      setLocalSelectedSubProjectId(null);
      setSelectedSubProjectId(null);
    } else {
      setLocalSelectedSubProjectId(id);
      setSelectedSubProjectId(id);
    }
  };

  const handleEightCountChange = (e) => {
    e.preventDefault();
    setLocalEightCount(e.target.value);
    setEightCountPayload({
      eight_count: Number(e.target.value),
      subproject_id: Number(subProject.id),
    });
  };

  const handleWriterChange = (e) => {
    e.preventDefault();
    if (e.target.value === "") {
      setSelectedWriterId("");
      setWriterCost(0);
      setTotalCost(0 + Number(artistCost) + Number(engineerCost));
      setResetWriterPayload({
        subproject_id: Number(subProject.id),
        employee_type: "writer",
      });
      return;
    } else {
      const writer = writers.find((x) => x.id === Number(e.target.value));
      if (writer && writer.is_writer) {
        setSelectedWriterId(writer.id);
        setWriterCost(Number(writer?.writer_cost));
        setTotalCost(
          Number(writer?.writer_cost) +
            Number(artistCost) +
            Number(engineerCost)
        );
        setWriterPayload({
          subproject_id: Number(subProject.id),
          employee_type: "writer",
          old_employee_id: tempWriterId ?? null,
          new_employee_id: Number(writer.id),
          employee_cost: Number(writer.writer_cost),
        });
      } else {
        setSelectedWriterId("");
        setWriterCost(0);
        setTotalCost(
          Number(writer?.writer_cost) +
            Number(artistCost) +
            Number(engineerCost)
        );
      }
    }
  };

  const handleArtistChange = (e) => {
    e.preventDefault();
    if (e.target.value === "") {
      setSelectedArtistId("");
      setArtistCost(0);
      setTotalCost(Number(writerCost) + 0 + Number(engineerCost));
      setResetArtistPayload({
        subproject_id: Number(subProject.id),
        employee_type: "artist",
      });
      return;
    } else {
      const artist = artists.find((x) => x.id === Number(e.target.value));
      if (artist && artist.is_artist) {
        setSelectedArtistId(artist.id);
        setArtistCost(Number(artist?.artist_cost));
        setTotalCost(
          Number(writerCost) +
            Number(artist?.artist_cost) +
            Number(engineerCost)
        );
        setArtistPayload({
          subproject_id: Number(subProject.id),
          employee_type: "artist",
          old_employee_id: tempArtistId ?? null,
          new_employee_id: Number(artist.id),
          employee_cost: Number(artist.artist_cost),
        });
      } else {
        setSelectedArtistId("");
        setArtistCost(0);
        setTotalCost(
          Number(writerCost) +
            Number(artist?.artist_cost) +
            Number(engineerCost)
        );
      }
    }
  };

  const submitLyrics = (e) => {
    e.preventDefault();
    if (lyricsVal === "" || !lyricsVal) {
      showToast(globalDispatch, "Lyrics cannot be empty.", 5000, "error");
      return;
    }
    setLyrics({
      subproject_id: subProject.id,
      lyrics: replaceNextLineToBrTag(lyricsVal),
    });
  };

  const handleDeleteAssignedIdea = async () => {
    setShowDeleteIdeaModal(false);
    await setDeleteIdeaPayload({
      idea_id: deleteIdeaId,
      subproject_id: subProject.id,
    });
    await getAllSubProjectIdea();
  };

  const handleDeleteIdeaModalClose = () => {
    setShowDeleteIdeaModal(false);
  };

  const handleShowAddIdeaModalClose = async () => {
    setShowAddIdeaModal(false);
    await getAllSubProjectIdea();
  };

  const handleShowAddIdeaModalOpen = async () => {
    setShowAddIdeaModal(true);
    await getAllSubProjectIdea();
  };

  const handleAddIdea = async (data) => {
    try {
      const payload = {
        subproject_id: Number(subProject.id),
        project_id: Number(projectId),
        idea_key: data.idea_key,
        idea_value: data.idea_value,
      };
      const result = await addAndAssignIdeaAPI(payload);
      if (!result.error) {
        showToast(globalDispatch, result.message, 5000);
        handleShowAddIdeaModalClose();
        setLoadIdeaFromViewProject(true);
      }
    } catch (error) {
      tokenExpireError(dispatch, error.message);
    }
  };

  const handleInstrumentalUploads = async (formData) => {
    try {
      // setIsLoading(true);
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        const payload = {
          project_id: Number(subProject.project_id),
          subproject_id: Number(subProject.id),
          workorder_id: Number(subProject.workorder_id),
          employee_id: null,
          employee_type: "writer",
          type: "instrumental",
          attachments: result.attachments,
          is_from_admin: 1,
        };

        const fileUploadDataResult = await uploadFilesDataAPI(payload);
        if (!fileUploadDataResult.error) {
          showToast(globalDispatch, "Files updated successfully", 5000);
          globalDispatch({
            type: "SET_SUBPROJECT_UPDATE",
            payload: !subproject_update,
          });
        } else {
          showToast(
            globalDispatch,
            fileUploadDataResult.message,
            5000,
            "error"
          );
        }
        // setIsLoading(false);
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
        // setIsLoading(false);
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };

  const handleOnclickShowWriterDetails = (e) => {
    e.preventDefault();
    setShowWriterDetails(!showWriterDetails);
  };

  const handleOnclickShowEngineerDetails = (e) => {
    e.preventDefault();
    setShowEngineerDetails(!showEngineerDetails);
  };

  React.useEffect(() => {
    if (isEdit) {
      setShowCheckboxToDelete(true);
    } else {
      setShowCheckboxToDelete(false);
    }
  }, [isEdit]);

  React.useEffect(() => {
    if (writer) {
      let localWriterCost =
        writer && writer.length > 0 ? Number(writer[0]?.emp_cost) : 0;
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      //
      setWriterCost(localWriterCost);
      setTotalCost(localWriterCost + Number(artistCost) + Number(engineerCost));
    }

    if (artist) {
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
      setArtistCost(localArtistCost);
      setTotalCost(Number(writerCost) + localArtistCost + Number(engineerCost));
    }

    if (engineer) {
      let localEngineerCost =
        engineer && engineer.length > 0 ? Number(engineer[0]?.emp_cost) : 0;
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");

      setEngineerCost(localEngineerCost);
      setTotalCost(Number(writerCost) + Number(artistCost) + localEngineerCost);
    }

    if (writer && artist) {
      let localWriterCost =
        writer && writer.length > 0 ? Number(writer[0]?.emp_cost) : 0;
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      setTotalCost(localWriterCost + localArtistCost + Number(engineerCost));
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
    }

    if (writer && engineer) {
      let localWriterCost =
        writer && writer.length > 0 ? writer[0]?.emp_cost : 0;
      let localEngineerCost =
        engineer && engineer.length > 0 ? engineer[0]?.emp_cost : 0;
      setTotalCost(localWriterCost + Number(artistCost) + localEngineerCost);
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");
    }

    if (artist && engineer) {
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      let localEngineerCost =
        engineer && engineer.length > 0 ? Number(engineer[0]?.emp_cost) : 0;
      setTotalCost(Number(writerCost) + localArtistCost + localEngineerCost);
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");
    }

    if (writer && artist && engineer) {
      let localWriterCost =
        writer && writer.length > 0 ? Number(writer[0]?.emp_cost) : 0;
      let localArtistCost =
        artist && artist.length > 0 ? Number(artist[0]?.emp_cost) : 0;
      let localEngineerCost =
        engineer && engineer.length > 0 ? Number(engineer[0]?.emp_cost) : 0;
      setTotalCost(localWriterCost + localArtistCost + localEngineerCost);
      setSelectedWriterId(writer[0]?.id ?? "");
      setTempWriterId(writer[0]?.id ?? "");
      setSelectedArtistId(artist[0]?.id ?? "");
      setTempArtistId(artist[0]?.id ?? "");
      setSelectedEngineerId(engineer[0]?.id ?? "");
      setTempEngineerId(engineer[0]?.id ?? "");
    }
  }, [writer, artist, engineer]);

  React.useEffect(() => {
    if (subProject) {
      setLocalEightCount(subProject.eight_count);
    } else {
      setLocalEightCount(0);
    }
    if (subProject && subProject.workorder_id) {
      setWorkOrderFound(true);
    } else {
      setWorkOrderFound(false);
    }
    if (subProject) {
      (async function () {
        await getAllSubProjectIdea();
      })();
    }
  }, [subProject]);

  React.useEffect(() => {
    if (expandAll) {
      handleSelectedSubProjectId(subProject.id);
    } else {
      handleSelectedSubProjectId(null);
    }
  }, [expandAll]);

  const CreateWorkOrder = async () => {
    await localStorage.setItem("workorder-artist", selectedArtistId);
    await localStorage.setItem("workorder-writer", selectedWriterId);
    await localStorage.setItem("workorder-id", subProject.id);
    navigate(`/${authState.role}/add-work-order/`);
  };

  const timeoutRefWC = React.useRef(null);
  const timeoutRefAC = React.useRef(null);

  console.log(subProject);

  return (
    <>
      {FileUploadOpen && (
        <AddFileSubProject
          mixSeasons={mixSeasons}
          tempUploadCount={tempUploadCount}
          setUpdateSubprojectPayload={setUpdateSubprojectPayload}
          UploadCount={UploadCount}
          writers={writers}
          artists={artists}
          engineers={engineers}
          subprojectType={subProject.type}
          setTempUploadCount={setTempUploadCount}
          id={Number(subProject.id)}
          setIsOpen={setFileUploadOpen}
          isOpen={FileUploadOpen}
        />
      )}
      <div className="p-4 my-4 w-full bg-gray-800 rounded-md border border-gray-500 shadow">
        <div className="flex flex-row justify-between">
          <div className="flex flex-row gap-4 ml-4 w-2/3">
            {subProject.is_song ? (
              <>
                <div className="">
                  {subProject.type_name
                    ? subProject.type_name.length > 15
                      ? `${subProject.type_name.substring(0, 15)}...`
                      : subProject.type_name
                    : "Song"}
                  ,
                </div>
                <div className="">{subProject.song_key ?? "Key N/A"},</div>
                <div className="">{subProject.bpm ?? "BPM N/A"}</div>
              </>
            ) : null}
          </div>
          <div className="flex flex-row gap-3 justify-end items-center w-1/3">
            {subProject.workorder_id ? (
              <span
                className={`cursor-pointer text-xs font-semibold ${
                  subProject.workorder_status == 5
                    ? "text-green-500"
                    : "text-blue-500"
                }`}
                onClick={() =>
                  navigate(
                    `/${authState.role}/edit-work-order/${subProject.workorder_id}`
                  )
                }
              >
                View Work order
              </span>
            ) : (
              <span
                className={`text-xs font-semibold text-green-500 cursor-pointer`}
              >
                File Uploaded
              </span>
            )}

            {/* {!subProject.workorder_id && (
              <div
                className={`flex relative flex-col items-center px-1 py-1 bg-blue-500 rounded cursor-pointer group h-[26px] w-[26px] hover:bg-blue-600`}
                onClick={() => {
                  setFileUploadOpen(true);
                  setTempUploadCount(tempUploadCount + 1);
                }}
              >
                <FontAwesomeIcon icon={`upload`} className="w-4 h-4" />
              </div>
            )} */}
          </div>
        </div>

        <div className="flex flex-row flex-wrap gap-2 justify-between w-full">
          <div
            className="cursor-pointer"
            style={{
              position: "relative",

              // right: '10px',
            }}
            onClick={() => {
              handleSelectedSubProjectId(subProject.id);
            }}
          >
            {localSelectedSubProjectId === subProject.id ? (
              <FontAwesomeIcon
                icon="fa-solid fa-chevron-up"
                width={16}
                height={16}
              />
            ) : (
              <FontAwesomeIcon
                icon="fa-solid fa-chevron-down"
                width={16}
                height={16}
              />
            )}
          </div>
          {showCheckboxToDelete && (
            <div className="flex flex-col justify-center items-center">
              <input
                type="checkbox"
                className="ml-2 w-4 h-4 text-blue-600 bg-gray-100 rounded border-gray-300 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedSubProjectIdForDelete(subProject.id);
                  } else {
                    setUnSelectedSubProjectIdForDelete(subProject.id);
                  }
                }}
              />
            </div>
          )}

          <input
            type="text"
            id="eightCountInput"
            class={`border-1 peer block h-[42px]  w-[104px] cursor-default appearance-none  rounded-lg border  border-gray-500  p-2.5 text-sm  font-medium text-white  placeholder-gray-300 focus:border-blue-600 focus:outline-none focus:ring-0 focus:ring-blue-500 dark:border-gray-500 dark:text-white dark:focus:border-blue-500 ${
              subProject.type.includes("Voiceover")
                ? "bg-orange-500"
                : "bg-sky-500"
            }`}
            placeholder="Type"
            value={subProject.type}
            readOnly
          />
          <div class="relative">
            <div
              type="number"
              id="eightCountInput"
              class="border-1 custom-overflow peer flex h-[42px] w-44  appearance-none flex-col gap-[2px] overflow-auto rounded-lg border-gray-600  bg-transparent px-2.5 pb-2.5 pt-2 text-sm text-white  focus:border-blue-500 focus:outline-none focus:ring-0 dark:text-white dark:focus:border-blue-500 "
              placeholder=" "
              min="0"
            >
              {subProject.masters.length > 0 ? (
                subProject.masters.map((file) => {
                  let fileSrc = `${file.url}`;
                  // only keep the file name which is last part of the url
                  let fileSrcTemp = fileSrc.split("/").pop();
                  let fileExtension = fileSrc.split(".").pop();

                  if (audioFileTypes.includes(fileExtension)) {
                    return (
                      <a
                        className="text-[9px] text-white underline"
                        href={fileSrc}
                        rel="noreferrer"
                        target="_blank"
                      >
                        {fileSrcTemp.length > 21
                          ? fileSrcTemp.substring(0, 21)
                          : fileSrcTemp}
                      </a>
                    );
                  } else {
                    return null;
                  }
                })
              ) : (
                <span
                  className="text-[9px] text-white"
                  rel="noreferrer"
                  target="_blank"
                >
                  Master file empty
                </span>
              )}
            </div>
            <label
              for="eightCountInput"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform border-stroke/50 bg-gray-800  px-2 text-sm  font-medium text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Files
            </label>
          </div>
          <div class="relative invisible mt-6">
            <select
              class="border-1 peer block h-[42px] w-28 appearance-none text-ellipsis rounded-lg  border-gray-600 bg-transparent p-2.5 pr-5 text-sm text-white focus:border-blue-500  focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500"
              name="producer"
              id="producer"
            ></select>
            <label
              htmlFor="producer"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Producer
            </label>
          </div>

          <div class="relative invisible mt-6">
            <select
              class="border-1  peer block h-[42px] w-28 appearance-none text-ellipsis rounded-lg  border-gray-600 bg-transparent p-2.5 pr-5   text-sm text-white focus:border-blue-500 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500"
              name="artist"
              id="artist"
            >
              <option className="text-white bg-gray-800" value="">
                Select
              </option>
            </select>
            <label
              htmlFor="artist"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Artist
            </label>
          </div>

          <div class="relative invisible mt-6">
            <input
              type="number"
              id="artist_cost"
              class="border-1 peer invisible block h-[42px] w-20 appearance-none rounded-lg border-gray-600  bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-white  focus:border-blue-500 focus:outline-none focus:ring-0 dark:text-white dark:focus:border-blue-500 "
              placeholder="Cost"
              min="0"
            />
            <label
              for="artist_cost"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Cost
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='artist_cost'
            >
              Cost
            </label>
            <input
              type='number'
              className='w-16 rounded-lg border border-slate-500 bg-transparent p-2.5 text-sm text-white placeholder-slate-300 focus:border-blue-500 focus:ring-blue-500'
            />
          </div> */}
          <div class="relative invisible mt-6">
            <select
              class="border-1 peer invisible block h-[42px] w-28 appearance-none text-ellipsis rounded-lg border-zinc-500 bg-zinc-700  p-2.5 pr-5 text-sm text-white focus:border-blue-500 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500"
              name="engineer"
              id="engineer"
              disabled
            >
              <option className="text-white bg-gray-800" value="">
                Select
              </option>
            </select>
            <label
              htmlFor="engineer"
              class="absolute start-1 top-2 z-[4] origin-[0]  -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Engineer
            </label>
          </div>

          <div class="relative invisible mt-6">
            <input
              type="text"
              id="engineer_cost"
              class="border-1 peer invisible block h-[42px] w-20 appearance-none rounded-lg border-zinc-500 bg-zinc-700 bg-zinc-700 px-2.5 pb-2.5 pt-4 text-sm font-medium text-white focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500   "
              placeholder="Cost"
              disabled
            />
            <label
              for="engineer_cost"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Cost
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 w-16 text-sm font-normal text-gray-100'
              htmlFor='engineer_cost'
            >
              Cost
            </label>
            <input
              type='text'
              className='w-16 rounded-lg border border-zinc-500 bg-zinc-700 p-2.5 text-sm text-white placeholder-zinc-300 focus:border-blue-500 focus:ring-blue-500'
            />
          </div> */}

          {/* <div className='flex flex-col'>
              <label
                className='block mb-1 text-sm font-normal text-gray-100'
                htmlFor='producer_cost'
              >
                Cost
              </label>
              <input
                type='number'
                className='w-16 rounded-lg border border-stone-500 bg-transparent p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500'
                placeholder='Cost'
                value={producerCost}
                min='0'
                onChange={(e) => {
                  if (!selectedProducerId) {
                    showToast(
                      globalDispatch,
                      'Please select a producer',
                      5000,
                      'warning'
                    );
                    return;
                  }
                  setProducerCost(Number(e.target.value));
                  setTotalCost(Number(e.target.value));
                  setProducerCostPayload({
                    subproject_id: Number(subProject.id),
                    employee_type: 'producer',
                    employee_id: Number(selectedProducerId),
                    employee_cost: Number(e.target.value),
                  });
                }}
              />
            </div>
          </div> */}
          {/* <label
                  htmlFor='name'
                  class='absolute start-1 top-2  z-[4] block origin-[0] -translate-y-2.5 scale-75 transform bg-white px-2 text-sm font-normal text-gray-100  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-2.5 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-orange-500 dark:text-white peer-focus:dark:text-blue-500'
                >
                  Type
                </label> */}

          {/* <div className='flex flex-col'>
                <label<div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='total'
            >
              Total
            </label>
            <input
              type='text'
              className='w-20 rounded-lg border border-neutral-500 bg-neutral-700 p-2.5 text-sm text-white placeholder-neutral-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Total'
              value={totalCost ?? 0}
              disabled
            />
          </div>
                  className='block mb-1 text-sm font-normal text-gray-100'
                  htmlFor='name'
                >
                  Type
                </label>
                <input
                  type='text'
                  className={`w-24 cursor-default rounded-lg border border-gray-500 p-2.5 text-sm text-white placeholder-gray-300 focus:border-blue-500 focus:ring-blue-500 ${
                    subProject.type.match(/Voiceover/)
                      ? 'bg-orange-500'
                      : 'bg-sky-500'
                  }`}
                  placeholder='Type'
                  value={subProject.type}
                  readOnly
                />
              </div> */}

          {/* <div className='relative mt-6'>
            <input
              type='number'
              id='eightCountInput'
              className='peer w-16 border-b border-stone-500 bg-transparent p-2.5 text-sm text-white text-white placeholder-stone-300 outline-none transition-all duration-200 focus:border-blue-500 focus:ring-blue-500'
            />
            <label
              htmlFor='eightCountInput'
              className='absolute top-2 left-2 text-xs text-white transition-all duration-200 pointer-events-none peer-placeholder-shown:-top-2 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-focus:-top-2 peer-focus:text-sm peer-focus:text-gray-500'
            ></label>
          </div> */}

          <div class="relative mt-6">
            <input
              type="text"
              id="total"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-zinc-500 bg-zinc-700 bg-zinc-700 px-2.5 pb-2.5 pt-4 text-sm text-white focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500   "
              value={subProject.file_cost}
              disabled
            />
            <label
              for="total"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Total
            </label>
          </div>
          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='total'
            >
              Total
            </label>
            <input
              type='text'
              className='w-20 rounded-lg border border-neutral-500 bg-neutral-700 p-2.5 text-sm text-white placeholder-neutral-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Total'
              value={totalCost}
              disabled
            />
          </div> */}
        </div>
      </div>

      {localSelectedSubProjectId === subProject.id && (
        <div
          className={`flex w-full flex-col items-start justify-start  rounded-md border border-form-strokedark bg-meta-4 p-5 shadow ${
            subProject.masters.length > 0 ? "gap-4" : ""
          }`}
        >
          {/* show contents for sub-project which has no workorder */}
          {!subProject.workorder_id ? (
            <UploadShowEngineer
              subProject={subProject}
              lyricsVal={lyricsVal}
              setDeleteFileId={setDeleteFileId}
              setLyricsVal={setLyricsVal}
              submitLyrics={submitLyrics}
            />
          ) : null}

          {/* expand icon for sub-project workorder stats 5: completed */}
          {subProject.workorder_id && subProject.workorder_status === 5 ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showEngineerDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowEngineerDetails(e)}
              >
                {!showEngineerDetails ? (
                  <>
                    <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                  </>
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for writer -> when writer and artist are same and no auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 1 &&
          subProject.workorder_auto_approve !== 1 &&
          selectedWriterId === selectedArtistId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for writer -> when writer and artist are different and no auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 1 &&
          subProject.workorder_auto_approve !== 1 &&
          selectedWriterId !== selectedArtistId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for writer -> when writer and artist are different and  auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 1 &&
          subProject.workorder_auto_approve === 1 &&
          selectedWriterId !== selectedArtistId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for writer -> when writer, artist and engineer are different and no auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 1 &&
          subProject.workorder_auto_approve === 1 &&
          selectedWriterId !== selectedArtistId &&
          selectedWriterId !== selectedEngineerId &&
          selectedArtistId !== selectedEngineerId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for artist -> when writer and artist are same and no auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 2 &&
          subProject.workorder_auto_approve !== 1 &&
          selectedWriterId === selectedArtistId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for artist -> when writer and artist are different and no auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 2 &&
          subProject.workorder_auto_approve !== 1 &&
          selectedWriterId !== selectedArtistId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for artist -> when writer and artist are same and auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 2 &&
          subProject.workorder_auto_approve === 1 &&
          selectedWriterId === selectedArtistId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for artist -> when writer and artist are different and auto-approve */}
          {subProject.workorder_id &&
          subProject.workorder_status === 2 &&
          subProject.workorder_auto_approve === 1 &&
          selectedWriterId !== selectedArtistId &&
          selectedArtistId !== selectedEngineerId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <>
                    {" "}
                    <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                  </>
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for writer/artist */}
          {subProject.workorder_id &&
          subProject.workorder_auto_approve === 1 &&
          subProject.workorder_status === 1 &&
          selectedWriterId === selectedArtistId &&
          selectedWriterId !== selectedEngineerId ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showWriterDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowWriterDetails(e)}
              >
                {!showWriterDetails ? (
                  <>
                    <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                  </>
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for engineer */}
          {subProject.workorder_id && subProject.workorder_status === 3 ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showEngineerDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowEngineerDetails(e)}
              >
                {!showEngineerDetails ? (
                  <>
                    <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                  </>
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for artist/engineer */}
          {subProject.workorder_id &&
          selectedWriterId !== selectedArtistId &&
          selectedArtistId === selectedEngineerId &&
          subProject.workorder_status !== 5 ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showEngineerDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowEngineerDetails(e)}
              >
                {!showEngineerDetails ? (
                  <>
                    <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                  </>
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* expand icon for Writer/Artist/Engineer */}
          {!subProject.workorder_id && !subProject.type.match(/Upload/) ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showEngineerDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowEngineerDetails(e)}
              >
                {!showEngineerDetails ? (
                  <>
                    <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                  </>
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* when Writer/Artist/Engineer & not auto approved work order  */}
          {subProject.workorder_id &&
          !subProject.workorder_auto_approve &&
          selectedWriterId === selectedArtistId &&
          selectedWriterId === selectedEngineerId &&
          subProject.workorder_status !== 5 ? (
            <div className="flex flex-row justify-end w-full">
              <div
                className={`w-max cursor-pointer rounded-md ${
                  !showEngineerDetails
                    ? "bg-blue-500 hover:bg-blue-700"
                    : "bg-amber-500 hover:bg-amber-700"
                } px-1.5`}
                onClick={(e) => handleOnclickShowEngineerDetails(e)}
              >
                {!showEngineerDetails ? (
                  <>
                    <FontAwesomeIcon icon="fa-solid fa-plus" size="xs" />
                  </>
                ) : (
                  <FontAwesomeIcon icon="fa-solid fa-minus" size="xs" />
                )}
              </div>
            </div>
          ) : null}

          {/* init show component for Writer, Artist when writer & artist are same but no auto-approve */}
          {subProject.workorder_id &&
          (subProject.workorder_status === 1 ||
            subProject.workorder_status === 2) &&
          subProject.workorder_auto_approve !== 1 &&
          selectedWriterId === selectedArtistId ? (
            <SubProjectShowWriter
              subProject={subProject}
              lyricsVal={lyricsVal}
              setDeleteFileId={setDeleteFileId}
              setLyricsVal={setLyricsVal}
              submitLyrics={submitLyrics}
            />
          ) : null}

          {/* init show component for Writer, Artist when writer & artist are not same */}
          {subProject.workorder_id &&
          (subProject.workorder_status === 1 ||
            subProject.workorder_status === 2) &&
          selectedWriterId !== selectedArtistId ? (
            <SubProjectShowWriter
              subProject={subProject}
              lyricsVal={lyricsVal}
              setDeleteFileId={setDeleteFileId}
              setLyricsVal={setLyricsVal}
              submitLyrics={submitLyrics}
            />
          ) : null}

          {/* init show component for Artist */}
          {/* {subProject.workorder_id && subProject.workorder_status === 2 ? (
            <SubProjectShowWriter
              subProject={subProject}
              lyricsVal={lyricsVal}
              setDeleteFileId={setDeleteFileId}
              setLyricsVal={setLyricsVal}
              submitLyrics={submitLyrics}
            />
          ) : null} */}

          {/* init show component for Writer/Artist */}
          {subProject.workorder_id &&
          subProject.workorder_auto_approve === 1 &&
          subProject.workorder_status === 1 &&
          selectedWriterId === selectedArtistId &&
          selectedWriterId !== selectedEngineerId ? (
            <SubProjectShowWriter
              subProject={subProject}
              lyricsVal={lyricsVal}
              setDeleteFileId={setDeleteFileId}
              setLyricsVal={setLyricsVal}
              submitLyrics={submitLyrics}
            />
          ) : null}

          {/* collapsed component of Writer, Artist, Writer/Artist */}
          {showWriterDetails ? (
            <SubProjectCollapseWriterAndArtist
              uploadedFilesProgressData={{ progress, error, isUploading }}
              subProjectId={subProject.id}
              assignedIdeas={assignedIdeas}
              surveySubmitStatus={surveySubmitStatus}
              adminWriterInstrumentals={subProject.admin_writer_instrumentals}
              handleInstrumentalUploads={handleInstrumentalUploads}
              setDeleteFileId={setDeleteFileId}
              setShowAssignIdeaModal={setShowAssignIdeaModal}
              setSelectedSubProjectId={setSelectedSubProjectId}
              handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
            />
          ) : null}

          {/* init show component for workorder completed */}
          {subProject.workorder_id && subProject.workorder_status === 5 ? (
            <SubProjectShowEngineer
              masters={subProject.masters}
              setDeleteFileId={setDeleteFileId}
            />
          ) : null}

          {/* init show component for Engineer */}
          {subProject.workorder_id && subProject.workorder_status === 3 ? (
            <SubProjectShowEngineer
              masters={subProject.masters}
              setDeleteFileId={setDeleteFileId}
            />
          ) : null}

          {/* init show component for Writer/Artist/Engineer */}
          {subProject.workorder_id &&
          subProject.workorder_auto_approve === 1 &&
          selectedWriterId === selectedArtistId &&
          selectedWriterId === selectedEngineerId &&
          subProject.workorder_status !== 5 ? (
            <SubProjectShowEngineer
              masters={subProject.masters}
              setDeleteFileId={setDeleteFileId}
            />
          ) : null}

          {/* init show component for Artist/Engineer */}
          {subProject.workorder_id &&
          selectedWriterId !== selectedArtistId &&
          selectedArtistId === selectedEngineerId &&
          subProject.workorder_status !== 5 ? (
            <SubProjectShowEngineer
              masters={subProject.masters}
              setDeleteFileId={setDeleteFileId}
            />
          ) : null}

          {/* init show component for Writer/Artist/Engineer but not auto approved work order */}
          {subProject.workorder_id &&
          selectedWriterId === selectedArtistId &&
          selectedArtistId === selectedEngineerId &&
          !subProject.workorder_auto_approve &&
          subProject.workorder_status !== 5 ? (
            <SubProjectShowEngineer
              masters={subProject.masters}
              setDeleteFileId={setDeleteFileId}
            />
          ) : null}

          {/* collapsed component of Engineer, Writer/Artist/Engineer, Artist/Engineer */}
          {showEngineerDetails ? (
            <SubProjectCollapseEngineer
              subProjectId={subProject.id}
              assignedIdeas={assignedIdeas}
              demos={subProject.demos}
              lyricsVal={lyricsVal}
              uploadedFilesProgressData={{ progress, error, isUploading }}
              surveySubmitStatus={surveySubmitStatus}
              adminWriterInstrumentals={subProject.admin_writer_instrumentals}
              handleInstrumentalUploads={handleInstrumentalUploads}
              setDeleteFileId={setDeleteFileId}
              setShowAssignIdeaModal={setShowAssignIdeaModal}
              setSelectedSubProjectId={setSelectedSubProjectId}
              handleShowAddIdeaModalOpen={handleShowAddIdeaModalOpen}
              setLyricsVal={setLyricsVal}
              submitLyrics={submitLyrics}
            />
          ) : null}
        </div>
      )}

      {showDeleteIdeaModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to delete this idea?`}
          setModalClose={handleDeleteIdeaModalClose}
          setFormYes={handleDeleteAssignedIdea}
        />
      ) : null}

      {showAddIdeaModal ? (
        <AddIdeaModal
          theme={theme}
          ideas={ideas}
          setModalClose={handleShowAddIdeaModalClose}
          setIdeaAddForm={handleAddIdea}
          projectId={projectId}
        />
      ) : null}
    </>
  );
};

export default Upload;
