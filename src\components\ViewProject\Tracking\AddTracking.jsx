import CustomSelect2 from "Components/CustomSelect2";
import React from "react";

const AddTracking = ({
  typeName,
  producers,
  addTotalCost,
  addProducerCost,
  handleAddTypeChange,
  handleAddProducerChange,
  handleAddProducerCostChange,
  handleAddSubProject,
}) => {
  React.useEffect(() => {
    if (typeName) {
      handleAddTypeChange(typeName);
    }
  }, [typeName]);

  return (
    <div className="mb-4 w-full rounded-md border border-zinc-500 bg-zinc-800 p-5 shadow">
      <div className="flex w-full flex-row flex-wrap items-end justify-start gap-2 xl:justify-between">
        <div className="flex flex-col">
          {/* <label
            className='block mb-1 text-sm font-normal text-gray-100'
            htmlFor='name'
          >
            Type
          </label> */}
          <input
            type="text"
            className="w-24 cursor-default rounded-lg border border-stone-500 bg-purple-600 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500"
            placeholder="Type"
            value={typeName}
            disabled
          />
        </div>

        <div class="relative mt-6">
          <CustomSelect2
            label="Producer"
            class="border-1 peer block h-[42px] w-28 appearance-none text-ellipsis rounded-lg border-gray-600  bg-transparent p-2.5 pr-5 text-sm   text-white focus:border-blue-500 focus:outline-none focus:ring-0 dark:border-gray-600 dark:bg-transparent dark:text-white dark:focus:border-blue-500"
            name="producer"
            id="producer"
            onChange={(value) => {
              handleAddProducerChange(value);
            }}
          >
            <option className="bg-gray-800 text-white" value="">
              Select
            </option>
            {producers?.map((producer) => (
              <option
                className="bg-gray-800 text-white"
                key={producer.id}
                value={producer.id}
              >
                {producer.name}
              </option>
            ))}
          </CustomSelect2>
          <label
            htmlFor="producer"
            class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
          >
            Producer
          </label>
        </div>

        <div class="relative mt-6">
          <input
            type="text"
            id="cost"
            class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-gray-600  bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-white  focus:border-blue-500 focus:outline-none focus:ring-0 dark:text-white dark:focus:border-blue-500 "
            placeholder="Cost"
            value={addProducerCost}
            onChange={(e) => {
              handleAddProducerCostChange(e);
            }}
          />
          <label
            for="cost"
            class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
          >
            Cost
          </label>
        </div>

        {/* <div className='flex flex-col'>
          <label
            className='block mb-1 text-sm font-normal text-gray-100'
            htmlFor='cost'
          >
            Cost
          </label>
          <input
            type='text'
            className='w-16 rounded-lg border border-stone-500 bg-stone-700 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500'
            placeholder='Cost'
            value={addProducerCost}
            onChange={(e) => {
              handleAddProducerCostChange(e);
            }}
          />
        </div> */}

        <div class="relative mt-6">
          <input
            type="text"
            id="total"
            class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-zinc-500 bg-zinc-700 bg-zinc-700 px-2.5 pb-2.5 pt-4 text-sm text-white focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500   "
            placeholder="Total"
            value={addProducerCost}
            disabled
          />
          <label
            for="total"
            class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
          >
            Total
          </label>
        </div>

        {/* <div className='flex flex-col'>
          <label
            className='block mb-1 text-sm font-normal text-gray-100'
            htmlFor='total'
          >
            Total
          </label>
          <input
            type='text'
            className='w-20 rounded-lg border border-neutral-500 bg-neutral-700 p-2.5 text-sm text-white placeholder-neutral-300 focus:border-blue-500 focus:ring-blue-500'
            placeholder='Total'
            value={addProducerCost}
            disabled
          />
        </div> */}

        <div className="flex flex-row items-center">
          <button
            type="button"
            className="rounded-lg border border-blue-600 bg-blue-600 px-4 py-2 font-bold text-white hover:bg-blue-700"
            onClick={handleAddSubProject}
          >
            Add
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddTracking;
