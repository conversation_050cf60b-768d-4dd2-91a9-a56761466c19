{"name": "equality-record", "private": true, "version": "1.0.0", "scripts": {"dev": "vite", "tw": "npx tailwindcss -i ./src/index.css -o ./src/output.css --watch", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@editorjs/attaches": "^1.3.0", "@editorjs/checklist": "^1.5.0", "@editorjs/code": "^2.8.0", "@editorjs/delimiter": "^1.3.0", "@editorjs/editorjs": "^2.26.5", "@editorjs/embed": "^2.5.3", "@editorjs/header": "^2.7.0", "@editorjs/image": "^2.8.1", "@editorjs/inline-code": "^1.4.0", "@editorjs/link": "^2.5.0", "@editorjs/list": "^1.8.0", "@editorjs/marker": "^1.3.0", "@editorjs/nested-list": "^1.3.0", "@editorjs/paragraph": "^2.9.0", "@editorjs/personality": "^2.0.2", "@editorjs/quote": "^2.5.0", "@editorjs/raw": "^2.4.0", "@editorjs/simple-image": "^1.5.1", "@editorjs/table": "^2.2.1", "@editorjs/underline": "^1.1.0", "@editorjs/warning": "^1.3.0", "@ffmpeg/util": "^0.12.1", "@floating-ui/react": "^0.27.8", "@fontsource/poppins": "^4.5.10", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.1.0", "@legendapp/state": "^0.23.4", "@popperjs/core": "^2.11.8", "@radix-ui/react-progress": "^1.1.1", "@react-pdf/renderer": "^3.3.8", "@splidejs/react-splide": "^0.7.12", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.52.1", "@tailwindcss/forms": "^0.5.3", "@tippyjs/react": "^4.2.6", "@uppy/aws-s3": "^3.1.1", "@uppy/core": "^3.2.0", "@uppy/dashboard": "^3.4.0", "@uppy/drag-drop": "^3.0.2", "@uppy/dropbox": "^3.1.1", "@uppy/google-drive": "^3.1.1", "@uppy/onedrive": "^3.1.1", "@uppy/react": "^3.1.2", "@uppy/tus": "^3.1.0", "@uppy/xhr-upload": "^3.2.0", "apexcharts": "^3.40.0", "axios": "^1.4.0", "bootstrap": "^5.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "emoji-picker-textarea": "^1.0.1", "framer-motion": "^10.12.9", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "jodit-react": "^1.3.39", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "moment": "^2.29.4", "moment-timezone": "^0.5.46", "nanoid": "^4.0.2", "pretty-rating-react": "^2.2.0", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-calendar": "^4.3.0", "react-confirm-alert": "^3.0.6", "react-dates": "^21.8.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-google-autocomplete": "^2.7.3", "react-google-maps": "^9.4.5", "react-hook-form": "^7.49.3", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-input-emoji": "^5.0.2", "react-loading-skeleton": "^3.3.1", "react-outside-click-handler": "^1.3.0", "react-pdf": "^7.7.1", "react-pdf-tailwind": "^2.3.0", "react-ratings-declarative": "^3.4.1", "react-router": "^6.11.1", "react-router-dom": "^6.11.1", "react-select": "^5.7.3", "react-signature-canvas": "^1.1.0-alpha.1", "react-signature-pad-wrapper": "^4.1.0", "react-slick": "^0.29.0", "react-spinners": "^0.13.8", "react-to-print": "^2.15.1", "react-window": "^1.8.11", "redux": "^4.2.1", "slick-carousel": "^1.8.1", "suneditor": "^2.44.12", "suneditor-react": "^3.5.0", "swiper": "^9.3.1", "tailwind-merge": "^2.6.0", "tw-elements": "^1.0.0-beta2", "twilio-video": "^2.27.0", "uppy": "^3.9.0", "use-debounce": "^9.0.4", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "yup": "^1.1.1"}, "devDependencies": {"@editorjs/link-autocomplete": "^0.1.0", "@editorjs/opensea": "^1.0.2", "@editorjs/translate-inline": "^1.0.0-rc.0", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.36.0", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.23", "prettier": "^2.8.7", "prettier-plugin-tailwindcss": "^0.2.5", "tailwindcss": "^3.3.2", "vite": "^4.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1"}}