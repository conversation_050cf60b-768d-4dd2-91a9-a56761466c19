import React from "react";
import { StringCaser } from "Utils/utils";

const MkdListTableFilterOptions = ({
  selectedOptions = [],
  columns = [],
  onColumnClick,
  setShowFilterOptions,
}) => {
  // const [showFilterOptions, setShowFilterOptions] = useState(false);

  return (
    <div className="absolute top-[-2000%] z-10 m-auto h-[31.25rem] max-h-[31.25rem] min-h-[31.25rem] w-[12.5rem] min-w-[12.5rem] max-w-[12.5rem] overflow-y-auto bg-white p-2 text-gray-600 opacity-0  shadow-md transition-all hover:top-[80%] hover:opacity-100 focus:top-[80%] focus:opacity-100 peer-focus:top-[80%] peer-focus:opacity-100 peer-focus-visible:top-[80%] peer-focus-visible:opacity-100">
      {/* <div className="flex w-full flex-col gap-2 text-gray-500"> */}
      {columns
        .map((column) => {
          if (
            column?.hasOwnProperty("isFilter") &&
            column.isFilter &&
            column.hasOwnProperty("selected_column") &&
            column?.selected_column
          ) {
            return (
              <button
                type="button"
                key={column?.header}
                className={`h-[2.25rem] w-full cursor-pointer text-left font-inter text-[.875rem] font-[400] capitalize leading-[1.25rem] tracking-[-0.006em] text-black`}
                onClick={() => {
                  // if (!selectedOptions.includes(column.header)) {
                  if (column.join) {
                    onColumnClick(column?.header);
                  } else {
                    onColumnClick(column?.accessor);
                  }
                  // }
                  // setShowFilterOptions(false);
                }}
              >
                {StringCaser(column?.header, {
                  casetype: "capitalize",
                  separator: "",
                })}
              </button>
            );
          } else if (
            !column?.hasOwnProperty("isFilter") &&
            column?.hasOwnProperty("selected_column")
          ) {
            if (
              ![
                "row",
                "action",
                "photo",
                "image",
                "file",
                "note",
                "files",
                "photos",
                "images",
                "image",
                "thumbnial",
                "thumbnails",
              ].includes(column?.header.toLowerCase()) &&
              column?.selected_column
            )
              return (
                <button
                  type="button"
                  key={column.header}
                  className={` h-[2.25rem] w-full cursor-pointer text-left font-inter text-[.875rem] font-[400] capitalize leading-[1.25rem] tracking-[-0.006em] text-black`}
                  onClick={() => {
                    // if (!selectedOptions.includes(column.header)) {
                    if (column.join) {
                      onColumnClick(column?.filter_field || column?.header);
                    } else {
                      onColumnClick(column?.accessor);
                    }
                    // }
                    // setShowFilterOptions(false);
                  }}
                >
                  {StringCaser(column.header, {
                    casetype: "capitalize",
                    separator: "",
                  })}
                </button>
              );
          }
        })
        .filter(Boolean)}
      {/* </div> */}
    </div>
  );
};

export default MkdListTableFilterOptions;
// ${
//   selectedOptions.includes(column.accessor)
//     ? "cursor-not-allowed text-gray-400"
//     : "cursor-pointer"
// }
