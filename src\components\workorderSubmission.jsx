import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  dateTimeToFormattedString,
  dateTimeToFormattedString2,
} from "Utils/utils";

const WorkOrderSubmission = ({ data, setModalClose }) => {
  return (
    <div className="z-999999 fixed inset-0 overflow-y-auto bg-black bg-opacity-80">
      <div className="flex min-h-screen items-center justify-center px-4 py-8">
        <div className="shadow-default w-full max-w-xl transform rounded border border-stroke bg-boxdark transition-all">
          {/* Modal Header */}
          <div className="flex items-center justify-between border-b border-stroke px-6 py-4">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon
                icon="fa-solid fa-clock-rotate-left"
                className="text-xl text-primary"
              />
              <h3 className="text-xl font-medium text-white">
                Work Order Submission History
              </h3>
            </div>
            <button
              onClick={() => setModalClose(false)}
              className="hover:text-primary"
            >
              <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
            </button>
          </div>

          {/* Submission List */}
          <div className="max-h-[60vh] overflow-y-auto p-6">
            {data?.length > 0 ? (
              <div className="rounded border border-stroke">
                <table className="w-full">
                  <thead className="bg-boxdark-2">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-semibold uppercase text-bodydark2">
                        Status
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-semibold uppercase text-bodydark2">
                        Date
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-semibold uppercase text-bodydark2">
                        Employee
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-stroke">
                    {data.map((item, index) => (
                      <tr key={index} className="bg-boxdark">
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <span
                              className={`h-2.5 w-2.5 rounded-full ${
                                item?.status === "Early"
                                  ? "bg-success"
                                  : "bg-danger"
                              }`}
                            />
                            <span className="ml-2 text-sm text-white">
                              {item?.status}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-white">
                          {dateTimeToFormattedString2(item?.date)}
                        </td>
                        <td className="px-4 py-3 text-sm text-white">
                          {item?.employee}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              // Empty State
              <div className="flex flex-col items-center justify-center py-8">
                <div className="mb-3 rounded-full bg-boxdark p-3">
                  <FontAwesomeIcon
                    icon="fa-solid fa-clock"
                    className="h-6 w-6 text-bodydark2"
                  />
                </div>
                <p className="text-sm text-bodydark2">
                  No submission history available
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-stroke px-6 py-4">
            <button
              onClick={() => setModalClose(false)}
              className="flex w-full items-center justify-center rounded-sm bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkOrderSubmission;
