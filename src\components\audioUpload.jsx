import React, { useCallback } from "react";
import { useDropzone } from "react-dropzone";

const AudioUpload = ({
  maxFileSize = 500,

  fileValues,
  setFileValues,
}) => {
  const onDrop = useCallback((acceptedFiles) => {
    // Filter for only audio/mp3 files
    const mp3Files = acceptedFiles.filter(
      (file) =>
        file.type.includes("audio") ||
        file.type.includes("mp3") ||
        file.type.includes("mp4")
    );
    console.log(mp3Files);
    console.log(acceptedFiles);
    // Call your function with the accepted MP3 files
    saveFiles(mp3Files);
  }, []);

  const { getRootProps, getInputProps, open, acceptedFiles } = useDropzone({
    // Disable click and keydown behavior
    onDrop,
    accept: "audio/mp3", // Specify accepted file types
    multiple: false,

    noKeyboard: true,
  });

  const files = acceptedFiles.map((file) => (
    <li key={file.path}>
      {file.path} - {file.size} bytes
    </li>
  ));
  const [maxFileSizeStr, setMaxFileSizeStr] = React.useState(
    `${maxFileSize}MB`
  );
  const convertMBToGB = (mb) => {
    return {
      value: mb / 1024,
      unit: "GB",
    };
  };

  const saveFiles = (files) => {
    if (files.length > 0) {
      setFileValues([...fileValues, ...files]);
    }
  };

  React.useEffect(() => {
    if (maxFileSize > 1024) {
      const { value, unit } = convertMBToGB(maxFileSize);
      let maxFileSizeStr = `${value}${unit}`;
      setMaxFileSizeStr(maxFileSizeStr);
    }
  }, [maxFileSize]);

  return (
    <>
      <div className="mt-5 flex h-[56px] w-full items-center rounded border-2 border-form-strokedark bg-form-input">
        <div className="flex justify-center items-center px-3 min-w-max h-full text-white bg-boxdark-2">
          Choose File
        </div>
        <div {...getRootProps({ className: "dropzone w-full" })}>
          <input {...getInputProps()} onChange={saveFiles} />
          <div className="flex w-full flex-row items-center justify-center gap-1 text-[12px] text-white">
            <p>Drag 'n' drop file here</p> or
            <button
              type="button"
              className="text-[12px] font-semibold"
              onClick={open}
            >
              Open File Dialog
            </button>
          </div>
        </div>

        {/* <button
          type='button'
          className='w-[60px] rounded bg-green-600 p-2 text-sm font-semibold text-white hover:bg-green-700'
        >
          {isUploading ? (
            <ClipLoader size={12} color='white' />
          ) : (
            <span> Upload</span>
          )}
        </button> */}
      </div>
      <div className="flex justify-between items-center">
        <div className="mb-2 text-xs text-gray-500">
          Maximum file size: {maxFileSizeStr}
        </div>
        <div className="mb-2 text-xs font-medium text-white">
          {acceptedFiles[0]?.name}
        </div>
      </div>
    </>
  );
};

export default AudioUpload;
