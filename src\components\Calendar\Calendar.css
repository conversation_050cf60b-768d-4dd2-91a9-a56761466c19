/* Override FullCalendar default styles */
/* .fc {
  --fc-border-color: theme("colors.stroke");
  --fc-daygrid-event-dot-width: 0;
  --fc-today-bg-color: theme("colors.gray.100");
} */

.fc-theme-standard td,
.fc-theme-standard th {
  border-color: theme("colors.strokedark");
}

div.fc-scrollgrid-sync-inner {
  padding: 30px !important;
}

.fc-direction-ltr {
  padding: 30px;
}

.fc-theme-standard th div {
  padding: 30px !important;
}

/* Dark mode support */
.dark .fc {
  --fc-border-color: theme("colors.strokedark");
  --fc-today-bg-color: theme("colors.meta.4");
  color: theme("colors.white");
}

/* Event styling */
.fc-event {
  border-radius: 4px;
  margin: 2px 0;
  padding: 2px 4px;
  transition: all 0.3s ease;
}

.fc-event:hover {
  opacity: 0.9;
}

/* Header buttons */
.fc-button {
  background-color: theme("colors.primary") !important;
  border: none !important;
  border-radius: 4px !important;
}

.fc-button:hover {
  opacity: 0.9;
}

/* Title */
.fc-toolbar-title {
  font-size: 1.25rem !important;
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fc-toolbar-title {
    font-size: 1rem !important;
  }
}
