import { ArrowDownIcon } from "lucide-react";
import { SkeletonLoader } from "Components/Skeleton";
import { StringCaser } from "Utils/utils";
import React from "react";
// import ModalPrompt from "Components/Modal/ModalPrompt";
// import { useNavigate } from "react-router-dom";
// import { EyeIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/solid";
// import { capitalize } from "Utils/utils";
// import { Spinner } from "Assets/svgs";
// import { colors } from "Utils/config";

const MkdListTableHead = ({
  onSort,
  columns,
  actions,
  actionPostion,
  areAllRowsSelected,
  handleSelectAll,
}) => {
  return (
    <>
      <tr className="flex !h-[2.25rem] !max-h-[2.25rem] !min-h-[2.25rem] overflow-hidden">
        {!columns?.length ? (
          <th scope="col" className={`!w-full !min-w-full !max-w-full `}>
            <SkeletonLoader
              count={1}
              counts={[1]}
              className="!m-0 !h-full !p-0"
            />
          </th>
        ) : null}
        {/* {columns.map((column, i) => {
          if (column?.accessor === "") {
            if (
              [actions?.select?.show].includes(true) &&
              actions?.select?.multiple
            ) {
              return (
                <th
                  key={i}
                  scope="col"
                  className={`!w-[2.25rem] !min-w-[2.25rem] !max-w-[2.25rem] cursor-pointer px-[.75rem] py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider text-sub-500 ${
                    column.isSorted ? "cursor-pointer" : ""
                  } `}
                  onClick={column.isSorted ? () => onSort(i) : undefined}
                >
                  {column.header === "Action" && actions?.select?.show ? (
                    <input
                      type="checkbox"
                      disabled={!actions?.select?.multiple}
                      id="select_all_rows"
                      className={`focus:shadow-outline mr-1 !h-4 !w-4 cursor-pointer appearance-none rounded border leading-tight text-primary shadow focus:outline-none focus:ring-0`}
                      checked={areAllRowsSelected}
                      onChange={handleSelectAll}
                    />
                  ) : null}
                </th>
              );
            }
            if (
              [actions?.select?.show].includes(true) &&
              !actions?.select?.multiple
            ) {
              return (
                <th
                  key={i}
                  scope="col"
                  className={`!w-[2.25rem] !min-w-[2.25rem] !max-w-[2.25rem] cursor-pointer px-[.75rem] py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider text-sub-500 ${
                    column.isSorted ? "cursor-pointer" : ""
                  } `}
                >
                  <span></span>
                </th>
              );
            }
          }
        })} */}

        {columns.map((column, i) => {
          // if (["row"].includes(column?.accessor)) {
          //   return (
          //     <th
          //       key={i}
          //       scope="col"
          //       className={`!w-[2.25rem] !min-w-[2.25rem] !max-w-[2.25rem] cursor-pointer py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider text-sub-500 `}
          //     >
          //       Row
          //     </th>
          //   );
          // }

          // if (
          //   column?.accessor === "" &&
          //   (actionPostion.includes("dropdown") ||
          //     actionPostion.includes("buttons"))
          // ) {
          //   if (
          //     [
          //       actions?.edit?.show,
          //       actions?.view?.show,
          //       actions?.delete?.show,
          //       actions?.status?.show,
          //     ].includes(true) ||
          //     Object.keys(actions).filter(
          //       (key) =>
          //         actions[key]?.show &&
          //         actions[key]?.locations &&
          //         (actions[key]?.locations?.includes("dropdown") ||
          //           actions[key]?.locations?.includes("buttons"))
          //     )?.length
          //   ) {
          //     return (
          //       <th
          //         key={i}
          //         scope="col"
          //         className={`!w-[2.25rem] !min-w-[2.25rem] !max-w-[2.25rem] cursor-pointer px-[.75rem] py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider text-sub-500 ${
          //           column.isSorted ? "cursor-pointer" : ""
          //         } `}
          //         // onClick={column.isSorted ? () => onSort(i) : undefined}
          //       ></th>
          //     );
          //   }
          // }

          if (column?.accessor !== "" && column?.selected_column) {
            return (
              <th
                key={i}
                scope="col"
                className={`text-sub-500 flex !w-[6.25rem] !min-w-[6.25rem] max-w-[auto] shrink-0 grow cursor-pointer justify-start px-[.75rem] py-[.5rem] text-left text-sm font-[400] capitalize leading-[1.5rem] tracking-wider ${
                  column.isSorted ? "cursor-pointer" : ""
                } `}
                onClick={column.isSorted ? () => onSort(i) : undefined}
              >
                <div className="flex !w-auto !min-w-fit max-w-[auto] shrink-0  items-center justify-start gap-2">
                  {column.header}
                  <span className="shrink-0">
                    {column.isSorted ? (
                      <ArrowDownIcon
                        className={`h-2 w-2 ${
                          column.isSortedDesc ? "rotate-180" : ""
                        }`}
                      />
                    ) : (
                      ""
                    )}
                  </span>
                </div>
              </th>
            );
          }
        })}
      </tr>
    </>
  );
};

export default MkdListTableHead;
