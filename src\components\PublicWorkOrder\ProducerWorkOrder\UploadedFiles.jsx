import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";
import { Download } from "lucide-react";

const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

const UploadedFiles = ({
  canUpload = true,
  uploadedFiles,
  setDeleteFileId,
  setEmployeeType,
  setFileUploadType,
  setFormData,
}) => {
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);

  const handleDeleteFileModalClose = () => {
    setShowDeleteFileConfirmModal(false);
  };

  const handleDeleteFileSubmit = () => {
    setDeleteFileId(localDeleteFileId);
    setShowDeleteFileConfirmModal(false);
  };

  return (
    <>
      <div
        onClick={() => {
          setEmployeeType("producer");
          setFileUploadType("instrumental");
        }}
        className="block p-5 mb-2 w-full max-w-5xl bg-gray-800 rounded-md border border-gray-500 shadow"
      >
        <div className="flex flex-col">
          <div className="mb-4 text-xl font-semibold text-center text-white">
            Files uploaded
          </div>

          {uploadedFiles &&
            uploadedFiles.length > 0 &&
            uploadedFiles.map((file, index) => {
              let fileSrc = `${file.url}`;
              // only keep the file name which is last part of the url
              let fileSrcTemp = fileSrc.split("/").pop();
              let fileExtension = fileSrc.split(".").pop();
              return (
                <div key={index} className="flex flex-col gap-1 mb-4">
                  {audioFileTypes.includes(fileExtension) && (
                    <div className="flex gap-3 items-center">
                      <a
                        className="text-sm text-white underline truncate"
                        href={fileSrc}
                        rel="noreferrer"
                        target="_blank"
                      >
                        {fileSrcTemp}
                      </a>
                      <Download
                        onClick={() => {
                          const link = document.createElement("a");
                          link.href = fileSrc;
                          link.download = fileSrcTemp;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        className="w-5 h-5 cursor-pointer text-primary"
                      />
                    </div>
                  )}
                  <div className="flex flex-row gap-4 items-center">
                    {audioFileTypes.includes(fileExtension) && (
                      <AudioPlayer fileSource={fileSrc} />
                    )}
                    {!audioFileTypes.includes(fileExtension) && (
                      <a
                        className="text-sm text-white underline truncate"
                        href={fileSrc}
                        rel="noreferrer"
                        target="_blank"
                      >
                        {fileSrcTemp}
                      </a>
                    )}
                    {canUpload && (
                      <span
                        className="cursor-pointer"
                        onClick={() => {
                          setShowDeleteFileConfirmModal(true);
                          setLocalDeleteFileId(file.id);
                        }}
                      >
                        <FontAwesomeIcon
                          icon="fa-solid fa-trash"
                          color="orange"
                          width={24}
                          height={24}
                        />
                      </span>
                    )}
                  </div>
                </div>
              );
            })}

          {canUpload && (
            <div className="flex flex-col justify-center items-center mt-6">
              <span className="my-2 text-lg font-semibold text-white">
                Upload more files
              </span>
              <FileUpload
                justify="center"
                items="center"
                setFormData={setFormData}
                maxFileSize={2048}
              />
            </div>
          )}
        </div>
      </div>

      {showDeleteFileConfirmModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to delete this file?`}
          setModalClose={handleDeleteFileModalClose}
          setFormYes={handleDeleteFileSubmit}
        />
      ) : null}
    </>
  );
};

export default UploadedFiles;
