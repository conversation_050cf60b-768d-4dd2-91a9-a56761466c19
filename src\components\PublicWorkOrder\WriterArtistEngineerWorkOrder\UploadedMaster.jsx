import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";

const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

const UploadedMaster = ({
  canUpload = true,
  uploadedFiles,
  showUploadBtn = true,
  setDeleteFileId,
  setFormData,
  setEmployeeType,
  setFileUploadType,
  uploadedFilesProgressData,
}) => {
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);

  return (
    <>
      <div
        className="flex flex-col"
        onClick={() => {
          setEmployeeType?.("engineer");
          setFileUploadType?.("master");
        }}
      >
        {/* Upload Section */}
        {showUploadBtn && canUpload && (
          <div className="mb-6 rounded border border-stroke bg-boxdark-2 p-6">
            <div className="flex flex-col items-center">
              <div className="mb-4 text-center">
                <h5 className="mb-1 text-sm font-medium text-white">
                  Upload More Files
                </h5>
                <p className="text-xs text-bodydark2">
                  Drag and drop your files or click to browse
                </p>
              </div>

              <FileUpload
                uploadedFilesProgressData={uploadedFilesProgressData}
                justify="center"
                items="center"
                maxFileSize={500}
                setFormData={setFormData}
              />
            </div>
          </div>
        )}

        {/* Files List */}
        <div className="space-y-4">
          {uploadedFiles?.map((file, index) => {
            const fileName = file.url.split("/").pop();
            const fileExt = file.url.split(".").pop().toLowerCase();
            const isAudio = audioFileTypes.includes(fileExt);

            return (
              <div
                key={index}
                className="w-[100%] rounded border border-stroke bg-boxdark p-4 transition-all hover:border-primary"
              >
                <div className="flex items-center gap-4">
                  <div className="min-w-0 flex-1">
                    <div className="mb-2 flex items-center gap-2">
                      <FontAwesomeIcon
                        icon={
                          isAudio ? "fa-solid fa-music" : "fa-solid fa-file"
                        }
                        className="text-bodydark2"
                      />
                      <a
                        href={file.url}
                        rel="noreferrer"
                        target="_blank"
                        className="truncate break-words text-sm font-medium text-white underline hover:text-primary"
                      >
                        {fileName}
                      </a>
                    </div>

                    {isAudio && (
                      <div className="mt-2">
                        <AudioPlayer fileSource={file.url} />
                      </div>
                    )}
                  </div>

                  {canUpload && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowDeleteFileConfirmModal(true);
                        setLocalDeleteFileId(file.id);
                      }}
                      className="group rounded-full p-2 hover:bg-danger/40"
                    >
                      <FontAwesomeIcon
                        icon="fa-solid fa-trash"
                        className="h-4 w-4 text-danger group-hover:text-danger"
                      />
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {showDeleteFileConfirmModal && (
        <ConfirmModal
          confirmText="Are you sure you want to delete this file?"
          setModalClose={() => setShowDeleteFileConfirmModal(false)}
          setFormYes={() => {
            setDeleteFileId(localDeleteFileId);
            setShowDeleteFileConfirmModal(false);
          }}
        />
      )}
    </>
  );
};

export default UploadedMaster;
