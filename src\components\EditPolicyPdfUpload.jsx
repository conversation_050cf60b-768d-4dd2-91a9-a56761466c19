import React from "react";
import { GlobalContext, showToast } from "../globalContext";

const EditPolicyPdfUpload = ({
  setFileUpload,
  maxFileSize = 2,
  minWidth = 500,
  minHeight = 200,
  transparent = false,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const saveFile = async (e) => {
    const file = e.target.files;
    if (file.length > 0) {
      if (!validateFileSize(file[0])) return;
      // const isValidDimensions = await validateImageDimensions(file);
      // if (!isValidDimensions) return;

      const formData = new FormData();
      formData.append("files", file[0]);
      setFileUpload(formData);
    }
  };

  const validateFileSize = (file) => {
    const fileSize = file.size / 1024 / 1024; // in MB
    if (fileSize > maxFileSize) {
      showToast(
        globalDispatch,
        `File size exceeds ${maxFileSize}MB. Please upload a smaller file.`,
        5000,
        "error"
      );
      return false;
    }
    return true;
  };

  return (
    <div className={`flex flex-col`}>
      <div className={`flex flex-row gap-2`}>
        <input
          className="rounded border border-gray-500 bg-gray-800 p-2 text-white shadow placeholder:text-gray-200"
          type="file"
          accept="pdf/*"
          onChange={saveFile}
        />
      </div>

      <div className="text-xs text-warning-600">File should be in .pdf</div>

      <div className="text-xs text-warning-600">
        Max file size limit is {maxFileSize}MB
      </div>
      <div className="text-xs text-warning-600">
        Max file dimension is {minWidth} x {minHeight}&nbsp;pixels
      </div>
    </div>
  );
};

export default EditPolicyPdfUpload;
