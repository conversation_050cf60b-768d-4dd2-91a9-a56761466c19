import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import CustomSelect2 from "Components/CustomSelect2";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import React, { useState } from "react";
import { ClipLoader } from "react-spinners";

const RequestEdit1 = ({
  isOpen,
  getPending,
  setIsOpen,
  setIsOpen2,
  setIsOpen4,
  setSelectedTeam,
  selectedTeam,
  teamList,
}) => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const [loader, setLoader] = useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto overflow-x-hidden bg-black/50 backdrop-blur-sm">
      <div className="shadow-default relative w-full max-w-lg rounded border border-strokedark bg-boxdark p-4 sm:p-8">
        {/* Header */}
        <div className="mb-5.5 flex items-center justify-between border-b border-strokedark pb-4">
          <h3 className="text-xl font-medium text-white">Request Edit</h3>
          <button
            onClick={() => {
              setSelectedTeam(null);
              setIsOpen(false);
            }}
            className="hover:text-primary"
          >
            <FontAwesomeIcon icon="close" className="h-4 w-4 text-white" />
          </button>
        </div>

        {/* Content */}
        <div className="gap-5.5 mt-4 flex flex-col">
          <div>
            <label className="mb-3 block text-sm font-medium text-white">
              Select Team
            </label>
            <div className="relative z-20">
              <CustomSelect2
                label="Select Team"
                value={selectedTeam ? selectedTeam?.value : ""}
                onChange={(value) => {
                  const data = teamList.find((elem) => elem.value == value);
                  setSelectedTeam(data);
                }}
                className="relative z-20 w-full appearance-none rounded border border-strokedark bg-meta-4 px-5 py-3 outline-none transition focus:border-primary active:border-primary"
              >
                <option value="">Select Team</option>
                {teamList.map((data) => (
                  <option key={data.value} value={data.value}>
                    {data.label}
                  </option>
                ))}
              </CustomSelect2>
              <span className="absolute right-4 top-1/2 z-10 -translate-y-1/2">
                <FontAwesomeIcon icon="chevron-down" className="text-white" />
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-4 flex items-center justify-end gap-4">
            <button
              onClick={() => {
                setSelectedTeam(null);
                setIsOpen(false);
              }}
              className="flex justify-center rounded border border-strokedark px-6 py-2 font-medium text-white hover:shadow-1"
            >
              Cancel
            </button>
            <button
              onClick={async () => {
                if (!selectedTeam) {
                  showToast(
                    globalDispatch,
                    "Select a team to proceed",
                    5000,
                    "error"
                  );
                  tokenExpireError(dispatch);
                } else {
                  setLoader(true);
                  const res = await getPending(selectedTeam?.value);
                  console.log(res);
                  if (res) {
                    setIsOpen4(true);
                  } else {
                    setIsOpen2(true);
                  }
                  setLoader(false);
                }
              }}
              className="flex h-[40px] items-center justify-center gap-2 rounded bg-primary px-6 py-2 font-medium text-gray hover:bg-opacity-90"
            >
              {loader ? (
                <ClipLoader color="white" size={13} />
              ) : (
                <>
                  <span>Next</span>
                  <FontAwesomeIcon icon="arrow-right" className="text-xs" />
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestEdit1;
