import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const AudioPlayer = ({ fileSource }) => {
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(0);
  const [duration, setDuration] = React.useState(0);
  const [volume, setVolume] = React.useState(1);
  const [showVolume, setShowVolume] = React.useState(false);
  const audioRef = React.useRef(null);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleLoadedMetadata = () => {
    setDuration(audioRef.current.duration);
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e) => {
    const time = e.target.value;
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    audioRef.current.volume = newVolume;
  };

  const getVolumeIcon = () => {
    if (volume === 0) return "fa-solid fa-volume-xmark";
    if (volume < 0.3) return "fa-solid fa-volume-off";
    if (volume < 0.7) return "fa-solid fa-volume-low";
    return "fa-solid fa-volume-high";
  };

  const toggleVolumeControl = () => {
    setShowVolume(!showVolume);
  };

  return (
    <div className="flex w-full max-w-[400px] items-center gap-3 rounded-lg border border-stroke/40 bg-boxdark-2 p-2">
      {/* Play/Pause Button */}
      <button
        onClick={handlePlayPause}
        className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white hover:bg-opacity-90"
      >
        <FontAwesomeIcon
          icon={isPlaying ? "fa-solid fa-pause" : "fa-solid fa-play"}
          className="h-3 w-3"
        />
      </button>

      {/* Time and Progress */}
      <div className="flex flex-1 flex-col gap-1">
        {/* Time Display */}
        <div className="flex justify-between text-xs text-bodydark2">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>

        {/* Progress Bar */}
        <div className="relative h-1.5 w-full">
          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentTime}
            onChange={handleSeek}
            className="t absolute h-1.5 w-full cursor-pointer appearance-none rounded-full bg-boxdark accent-primary hover:accent-primary/90  [&::-webkit-slider-thumb]:h-3 [&::-webkit-slider-thumb]:w-3 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary"
          />
        </div>
      </div>

      {/* Volume Control */}
      <div className="relative">
        <button
          onClick={toggleVolumeControl}
          className="flex h-8 w-8 items-center justify-center text-bodydark2 hover:text-white"
        >
          <FontAwesomeIcon icon={getVolumeIcon()} className="h-4 w-4" />
        </button>
        {showVolume && (
          <div className="absolute bottom-full left-0 z-50 mb-2">
            <div className="w-32 rounded-lg border border-stroke bg-boxdark-2 p-2 shadow-lg">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon
                  icon={getVolumeIcon()}
                  className="h-4 w-4 text-bodydark2"
                />
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={volume}
                  onChange={handleVolumeChange}
                  className="h-1.5 w-full flex-1 cursor-pointer appearance-none rounded-full bg-boxdark accent-primary hover:accent-primary/90 [&::-webkit-slider-thumb]:h-3 [&::-webkit-slider-thumb]:w-3 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      <audio
        ref={audioRef}
        src={fileSource}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => setIsPlaying(false)}
        className="hidden"
      />
    </div>
  );
};

export default AudioPlayer;
