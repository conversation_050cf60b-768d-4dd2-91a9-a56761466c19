import React from "react";

export const MkdInput = ({
  type = "text",
  page,
  name,
  label,
  errors = null,
  register = null,
  className = "",
  placeholder,
  value = null,
  onChange,
  disabled = false,
  required = false,
  rows = "4",
  cols = "50",
  children,
}) => {
  const inputId = React.useId();

  const inputClasses = `w-full appearance-none rounded-sm border-2 border-gray-800 bg-white px-4 py-2 text-sm font-normal leading-tight text-gray-800 shadow focus:outline-none focus:ring-2 focus:ring-blue-500 ${
    errors && errors?.[name] && errors?.[name]?.message ? "border-red-500" : ""
  } ${disabled ? "bg-gray-100" : ""} ${className}`;

  const renderInput = () => {
    switch (type) {
      case "textarea":
        return (
          <textarea
            id={inputId}
            className={inputClasses}
            disabled={disabled}
            placeholder={placeholder}
            rows={rows}
            cols={cols}
            {...(value ? { value } : {})}
            {...(register
              ? register(name, {
                  ...(required ? { required: true } : null),
                })
              : {
                  onChange,
                })}
          />
        );

      case "select":
        return (
          <select
            id={inputId}
            className={inputClasses}
            disabled={disabled}
            {...(value ? { value } : {})}
            {...(register
              ? register(name, {
                  ...(required ? { required: true } : null),
                })
              : {
                  onChange,
                })}
          >
            {placeholder && <option value="">{placeholder}</option>}
            {children}
          </select>
        );

      case "checkbox":
      case "radio":
        return (
          <input
            id={inputId}
            type={type}
            className={`w-4 h-4 text-blue-600 rounded border-gray-800 focus:ring-2 focus:ring-blue-500 ${className}`}
            disabled={disabled}
            {...(value ? { value } : {})}
            {...(register
              ? register(name, {
                  ...(required ? { required: true } : null),
                })
              : {
                  onChange,
                })}
          />
        );

      default:
        return (
          <input
            id={inputId}
            type={type}
            className={inputClasses}
            disabled={disabled}
            placeholder={placeholder}
            {...(value ? { value } : {})}
            {...(register
              ? register(name, {
                  ...(required ? { required: true } : null),
                })
              : {
                  onChange,
                })}
          />
        );
    }
  };

  return (
    <div className="mb-4 w-full">
      {label && (
        <label
          className="block mb-2 text-base font-bold text-gray-800"
          htmlFor={inputId}
        >
          {label}
          {required && <span className="text-red-600">*</span>}
        </label>
      )}
      {renderInput()}
      {errors && errors[name] && (
        <p className="mt-1 text-sm italic text-red-500">
          {errors[name].message}
        </p>
      )}
    </div>
  );
};
