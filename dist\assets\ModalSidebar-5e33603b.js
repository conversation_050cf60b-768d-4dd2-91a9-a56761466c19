import{j as t}from"./@floating-ui/react-2bb8505c.js";import{r as m}from"./vendor-3aca5368.js";const j={right:"justify-end",left:"justify-start"},c={right:"translate-x-full",left:"-translate-x-full"},u={right:"-translate-x-0",left:"-translate-x-0"},$=({customMinWidthInTw:d="min-w-full",isModalActive:l=!1,closeModalFn:i=()=>{},children:x,showHeader:a=!1,title:p="Modal",headerClassName:h="bg-black",headerContentClassName:s="text-white",headerContent:r=null,closePosition:f=1,side:e="right",classes:o={modalBody:""}})=>{const n=m.useRef(),g=w=>{w.target.id==="modal"&&i()};return m.useEffect(()=>{l&&n&&n.current.focus()},[l]),t.jsx("div",{id:"modal","aria-hidden":"false",autoFocus:!0,style:{zIndex:9999999999},onMouseDown:g,className:`${j[e]} transition-all ${l?u[e]:c[e]} fixed left-0 right-0 top-0 z-[9999999999] flex h-screen w-screen items-center overflow-y-auto overflow-x-hidden bg-[#292828d2] p-3 backdrop:blur-md md:inset-0 md:h-full`,children:t.jsxs("div",{ref:n,autoFocus:!0,className:`${d} scrollable-container relative z-[9999] grid h-full max-h-full min-h-full ${a?"grid-rows-[auto_1fr]":"grid-rows-1"} items-center overflow-y-auto rounded-[.625rem] border-0 bg-weak-100  pt-0 shadow-xl transition-all ${l?u[e]:c[e]}`,children:[a?t.jsxs("div",{className:`sticky top-0 z-[999999999] mb-2 mt-0 flex min-h-[3.25rem] items-center gap-2 px-5 py-2 shadow-md ${h}`,children:[f==1&&t.jsx("div",{onClick:()=>{r||i()},className:`non_print_section text[1rem] w-[3.625rem] cursor-pointer font-inter font-[600] leading-[1.5rem] ${s}`,children:r||"<- Back"}),t.jsx("div",{className:"grow text-center text-lg capitalize text-white",children:p}),f==2&&t.jsx("div",{onClick:()=>{r||i()},className:`non_print_section text[1rem] w-[3.625rem] cursor-pointer font-inter font-[600] leading-[1.5rem] ${s}`,children:r||"<- Back"})]}):null,t.jsx("div",{className:`h-full min-h-full ${o==null?void 0:o.modalBody}`,children:x})]})})};export{$ as default};
