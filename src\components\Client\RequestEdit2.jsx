import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { GlobalContext, showToast } from "Src/globalContext";
import { getAllEditTypesListAPI } from "Src/services/editService";
import React, { useEffect, useState } from "react";
import { ClipLoader } from "react-spinners";

const RequestEdit2 = ({
  isOpen,
  selectedEditType,
  setSelectedTeam,
  setIsOpen1,
  setIsOpen2,
  setIsOpen3,
  setSelectedEditType,
  producer_id = null,
}) => {
  const [SpecialList, setSpecialList] = useState([]);
  const [loader, setLoader] = useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const getSpecialList = async () => {
    setLoader(true);
    const res = producer_id
      ? await getAllEditTypesListAPI({ user_id: producer_id })
      : { list: [] };
    const list = res.list.filter((elem) => elem.request_range === "Special");
    if (list.length <= 0) {
      setIsOpen3(true);
    } else {
      setIsOpen3(false);
    }
    setSpecialList(list);
    setLoader(false);
  };
  useEffect(() => {
    producer_id && getSpecialList();
  }, [producer_id]);
  console.log("dndnnd");
  return (
    <div className="custom-overflow fixed inset-0 z-[51] flex h-full w-full items-center justify-center overflow-y-auto px-4 py-8 backdrop-blur-sm">
      <div className="shadow-default relative w-full max-w-[1200px] rounded border border-strokedark bg-boxdark p-4 sm:p-8 dark:border-strokedark dark:bg-boxdark">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-strokedark pb-4 dark:border-strokedark">
          <h3 className="text-2xl font-semibold text-white dark:text-white">
            Edit Type
          </h3>
          <button
            onClick={() => {
              setSelectedTeam(null);
              setIsOpen2(false);
              setIsOpen1(false);
            }}
            className="text-white hover:text-white dark:text-gray-400 dark:hover:text-white"
          >
            <FontAwesomeIcon icon="close" className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="mt-6">
          <div className="mb-4">
            <label className="mb-2.5 block text-sm font-medium text-white dark:text-white">
              Is this edit for one of the following:
            </label>
            {loader ? (
              <ClipLoader size={15} color="white" />
            ) : (
              <div className="grid grid-cols-2 gap-5 sm:grid-cols-3 lg:grid-cols-5">
                {SpecialList.map((elem) => (
                  <div key={elem.id} className="flex items-center gap-2">
                    <input
                      onChange={(e) => {
                        setSelectedEditType(e.target.checked ? elem.id : null);
                      }}
                      type="radio"
                      checked={elem.id === selectedEditType}
                      className="h-4 w-4"
                      name="special"
                      value={elem.id}
                    />
                    <span className="text-sm text-white">{elem.edit_type}</span>
                    <span className="group relative">
                      <FontAwesomeIcon
                        icon="question-circle"
                        className="h-4 w-4 cursor-pointer text-white"
                      />
                      <p className="whit custom-overflow invisible absolute -top-5 z-[4] max-h-[70px] w-[200px] overflow-y-auto overflow-x-hidden break-words rounded bg-gray-2 p-3 text-center text-xs leading-none text-gray-900 shadow-lg group-hover:visible">
                        {elem.note_keys}
                      </p>
                    </span>
                  </div>
                ))}
                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    className="h-4 w-4"
                    name="special"
                    id=""
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedEditType("");
                      } else {
                        setSelectedEditType(null);
                      }
                    }}
                  />
                  <span className="text-sm text-white">Sound Effects Only</span>
                </div>
              </div>
            )}
          </div>
          <div className="mt-8 flex w-full items-center justify-between">
            {" "}
            <button
              onClick={() => {
                setSelectedEditType("");
                setIsOpen1(true);
                setIsOpen2(false);
              }}
              className="flex w-[90px] items-center justify-center gap-1 rounded bg-primary py-[8px] hover:bg-primary/90"
            >
              <FontAwesomeIcon
                className="text-[13px] text-white"
                icon={"arrow-left"}
              />
              <span className="text-[13px] font-medium text-white">
                {" "}
                Previous
              </span>
            </button>
            <button
              onClick={() => {
                console.log(selectedEditType);
                if (selectedEditType === null) {
                  showToast(
                    globalDispatch,
                    "Make a selection",
                    5000,
                    "warning"
                  );
                } else {
                  setIsOpen3(true);
                }
              }}
              className="flex w-[90px] items-center justify-center gap-1 rounded bg-primary py-[8px] hover:bg-primary/90"
            >
              <span className="text-[13px] font-medium text-white">Next</span>{" "}
              <FontAwesomeIcon
                className="text-[13px] text-white"
                icon={"arrow-right"}
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestEdit2;
