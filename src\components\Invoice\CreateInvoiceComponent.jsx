import React, { useState, useEffect } from "react";
import { GlobalContext, showToast } from "Src/globalContext";
import CustomSelect2 from "Components/CustomSelect2";
import moment from "moment";
import { ArrowLeft, Plus, Trash2, Download, X, PlusCircle } from "lucide-react";
import NewClientModal from "./NewClientModal";
import { SingleDatePicker } from "react-dates";
import { PDFDownloadLink } from "@react-pdf/renderer";
import InvoiceQuotePDF from "./InvoiceQuotePDF";
import { getAllMixSeasonAPI } from "Src/services/mixSeasonService";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import MkdSDK from "Utils/MkdSDK";
// We're not using the utility functions directly anymore

const CreateInvoiceComponent = ({
  onClose,
  userDetails,
  clients,
  mixTypes,
  loading,
  onSubmit,
  fetchAvailableClients,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [selectedClientId, setSelectedClientId] = useState("");
  const [producers, setProducers] = useState([]);
  const [focusedInput, setFocusedInput] = React.useState({
    date: null,
    dueDate: null,
    musicSurveyDue: {},
    submission: {},
    estimatedCompletion: {},
  });
  const [newClientData, setNewClientData] = useState({
    program: "",
    email: "",
  });
  const [invoiceItems, setInvoiceItems] = useState([]);
  const [invoiceDates, setInvoiceDates] = useState({
    invoiceDate: new Date().toISOString().split("T")[0],
    invoiceDueDate: "",
  });
  const [showNewClientModal, setShowNewClientModal] = useState(false);
  const [totalPrice, setTotalPrice] = useState(0);
  const [mixSeasons, setMixSeasons] = useState([]);
  const [companyInfo, setCompanyInfo] = useState(null);
  const [isMainMember, setIsMainMember] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [producerMixTypes, setProducerMixTypes] = useState({});

  const buttonList = {
    complex: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["removeFormat"],
      ["fontColor", "hiliteColor"],
      ["indent", "outdent"],
      ["align", "horizontalRule", "list", "table"],
      ["link"],
      ["fullScreen", "showBlocks", "codeView"],
      ["preview"],
    ],
  };

  // Get SunEditor instance
  const getSunEditorInstance = (sunEditor) => {
    // We keep this function for the SunEditor component but don't need to store the instance
  };

  const [invoiceData, setInvoiceData] = useState({
    notes: "",
    termsAndConditions:
      userDetails?.contract_agreement
        ?.replace(/<[^>]*>/g, "")
        .replace(/&nbsp;/g, " ") || "",
    depositAmount: 0,
    depositPercentage: userDetails?.deposit_percent || 30,
    items: [
      {
        mixDate: "",
        producer: "",
        mixType: "",
        teamName: "Team 1",
        division: "TBD",
        musicSurveyDue: "",
        routineSubmissionDue: "",
        estimatedCompletion: "",
        price: 0,
        quantity: 1,
        isSpecialRow: false,
        isSpecial: false,
        description: "",
        mix_season: "",
        producers: [],
      },
    ],
    specialRows: [],
  });
  const [isQuote, setIsQuote] = useState(false);

  console.log(invoiceData);

  const getInfo = async () => {
    try {
      // Use the SDK directly to get the raw company info
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      if (!response.error) {
        console.log("Company info:", response);
        setCompanyInfo(response);

        // Get current user ID from localStorage
        const userId = parseInt(localStorage.getItem("user"));
        setCurrentUserId(userId);

        // Check if current user is the main member
        const isUserMainMember =
          response.company?.main_member &&
          response.company.main_member.id === userId;
        setIsMainMember(isUserMainMember);

        // Update invoice data with company settings
        // Use manager settings if available, otherwise use main member settings
        const manager = response.company?.manager;
        const mainMember = response.company?.main_member;

        setInvoiceData((prev) => ({
          ...prev,
          termsAndConditions:
            (manager && manager.contract_agreement) ||
            (mainMember && mainMember.contract_agreement) ||
            userDetails?.contract_agreement ||
            "",
          depositPercentage:
            (manager && manager.deposit_percent) ||
            (mainMember && mainMember.deposit_percent) ||
            userDetails?.deposit_percent ||
            30,
        }));

        // Extract members as producers from company info
        const producersList = [];

        // Add main member first if available
        if (response.company?.main_member) {
          producersList.push({
            id: response.company.main_member.id,
            name: `${response.company.main_member.first_name} ${response.company.main_member.last_name}`,
          });
        }

        // Add other members
        if (response.company?.members && response.company.members.length > 0) {
          response.company.members.forEach((member) => {
            // Check if this member is not already in the list
            if (!producersList.some((p) => p.id === member.id)) {
              producersList.push({
                id: member.id,
                name: `${member.first_name} ${member.last_name}`,
              });
            }
          });
        }

        // Note: We're not adding the manager to the producers list
        // as they shouldn't be selectable as a producer

        console.log("Producers list:", producersList);
        setProducers(producersList);

        // Update the invoice items to use producer names in the producers array
        setInvoiceData((prev) => {
          const updatedItems = prev.items.map((item) => {
            // If producer is set, find the producer name and add it to producers array
            if (item.producer) {
              const producerObj = producersList.find(
                (p) => p.id === parseInt(item.producer)
              );
              if (producerObj) {
                return {
                  ...item,
                  producers: [producerObj.name], // Store producer name as string in array
                };
              }
            }
            return item;
          });

          return {
            ...prev,
            items: updatedItems,
          };
        });

        // Fetch mix seasons for the current user
        fetchMixSeasonsForProducer(userId);

        // Fetch mix types for the current user
        fetchMixTypesForProducer(userId);
      } else {
        console.error(
          "Error fetching company info:",
          response.message || "Unknown error"
        );
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    }
  };

  // Fetch mix seasons for a specific producer
  const fetchMixSeasonsForProducer = async (producerId) => {
    if (!producerId) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v4/api/records/mix_season?filter=user_id,eq,${producerId}`,
        {},
        "GET"
      );

      if (!response.error && response.list) {
        // Sort seasons in ascending order
        const sortedSeasons = response.list.sort((a, b) => {
          // First try to sort by name if it contains numbers
          const aNum = parseInt(a.name.replace(/\D/g, ""));
          const bNum = parseInt(b.name.replace(/\D/g, ""));

          if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum;
          }

          // Fall back to alphabetical sort
          return a.name.localeCompare(b.name);
        });

        setMixSeasons(sortedSeasons);
      }
    } catch (error) {
      console.error(
        `Error fetching mix seasons for producer ${producerId}:`,
        error
      );
    }
  };

  // Fetch mix types for a specific producer
  const fetchMixTypesForProducer = async (producerId) => {
    if (!producerId) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v4/api/records/mix_type?filter=user_id,eq,${producerId}`,
        {},
        "GET"
      );

      if (!response.error && response.list) {
        // Update the producer mix types
        setProducerMixTypes((prevState) => ({
          ...prevState,
          [producerId]: response.list,
        }));
      }
    } catch (error) {
      console.error(
        `Error fetching mix types for producer ${producerId}:`,
        error
      );
    }
  };

  useEffect(() => {
    getInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Check if main member has invoice subscription
  // Sub-members inherit access from their main member's subscription
  const checkMainMemberInvoiceSubscription = () => {
    if (!companyInfo || !companyInfo.company) return true;

    // Only check the main member's subscription status
    // Sub-members inherit access from the main member
    if (companyInfo.company.main_member) {
      return companyInfo.company.main_member.has_invoice_subscription === 1;
    }

    return false;
  };

  // Calculate total price whenever invoice items change
  useEffect(() => {
    if (invoiceData.items && invoiceData.items.length > 0) {
      const total = calculateTotalPrice(invoiceData.items);
      setTotalPrice(total);
    }
  }, [invoiceData.items]);

  const calculateDates = (mixDate, index) => {
    if (!mixDate) return;

    // Parse settings with error handling
    let surveySettings = { weeks: 8, day: "Monday" };
    let routineSettings = { weeks: 1, day: "Monday" };
    let deliverySettings = { weeks: 1, day: "Friday" };

    // Use settings from company info if available
    if (companyInfo?.company) {
      // Use manager settings if available, otherwise use main member settings
      const manager = companyInfo.company?.manager;
      const mainMember = companyInfo.company?.main_member;

      if (manager && manager.estimated_delivery) {
        deliverySettings = manager.estimated_delivery;
      } else if (mainMember && mainMember.estimated_delivery) {
        deliverySettings = mainMember.estimated_delivery;
      }

      if (manager && manager.routine_submission_date) {
        routineSettings = manager.routine_submission_date;
      } else if (mainMember && mainMember.routine_submission_date) {
        routineSettings = mainMember.routine_submission_date;
      }

      if (manager && manager.survey) {
        surveySettings = manager.survey;
      } else if (mainMember && mainMember.survey) {
        surveySettings = mainMember.survey;
      }
    }

    // Fallback to user details if company info settings are not available
    try {
      if (userDetails?.survey) {
        // Check if it's already an object
        if (typeof userDetails.survey === "object") {
          surveySettings = userDetails.survey;
        } else {
          surveySettings = JSON.parse(userDetails.survey);
        }
      }
    } catch (error) {
      console.error("Error parsing survey settings:", error);
    }

    try {
      if (userDetails?.routine_submission_date) {
        // Check if it's already an object
        if (typeof userDetails.routine_submission_date === "object") {
          routineSettings = userDetails.routine_submission_date;
        } else {
          routineSettings = JSON.parse(userDetails.routine_submission_date);
        }
      }
    } catch (error) {
      console.error("Error parsing routine settings:", error);
    }

    try {
      if (userDetails?.estimated_delivery && !companyInfo?.settings) {
        // Only use user details if company info settings are not available
        if (typeof userDetails.estimated_delivery === "object") {
          deliverySettings = userDetails.estimated_delivery;
        } else {
          deliverySettings = JSON.parse(userDetails.estimated_delivery);
        }
      }
    } catch (error) {
      console.error("Error parsing delivery settings:", error);
    }

    // Function to find the previous occurrence of a day
    const findPreviousDay = (date, targetDay) => {
      const days = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      const targetDayIndex = days.indexOf(targetDay);
      let currentDate = moment(date);

      while (currentDate.day() !== targetDayIndex) {
        currentDate.subtract(1, "days");
      }
      return currentDate;
    };

    // Calculate survey date (backwards from mix date)
    const surveyDate = findPreviousDay(
      moment(mixDate).subtract(surveySettings.weeks, "weeks"),
      surveySettings.day
    );

    // Calculate routine submission date (backwards from mix date)
    const submissionDate = findPreviousDay(
      moment(mixDate).subtract(routineSettings.weeks, "weeks"),
      routineSettings.day
    );

    // Calculate estimated completion date (forward from mix date)
    const completionDate = findPreviousDay(
      moment(mixDate).add(deliverySettings.weeks, "weeks"),
      deliverySettings.day
    );

    // Update invoiceData with calculated dates
    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      newItems[index] = {
        ...newItems[index],
        mixDate: mixDate,
        musicSurveyDue: surveyDate.format("YYYY-MM-DD"),
        routineSubmissionDue: submissionDate.format("YYYY-MM-DD"),
        estimatedCompletion: completionDate.format("YYYY-MM-DD"),
      };
      return {
        ...prev,
        items: newItems,
      };
    });
  };

  const handleAddItem = () => {
    setInvoiceData((prev) => {
      // Count only non-special items to determine the next team number
      const regularItems = prev.items.filter(
        (item) => !item.isSpecial && !item.isSpecialRow
      );
      const nextTeamNumber = regularItems.length + 1;

      return {
        ...prev,
        items: [
          ...prev.items,
          {
            mixDate: "",
            producer: "",
            producerName: "",
            mixType: "",
            teamName: `Team ${nextTeamNumber}`,
            division: "TBD",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            price: 0,
            quantity: 1,
            discount: 0,
            mix_season: "",
            producers: [],
            description: "", // Initialize description field
            specialType: "",
            isSpecial: false,
            isSpecialRow: false,
            amount: 0,
          },
        ],
      };
    });
  };

  const handleAddSpecialRow = () => {
    setInvoiceData((prev) => {
      return {
        ...prev,
        items: [
          ...prev.items,
          {
            name: "Additional Charge",
            price: 0,
            quantity: 1,
            discount: 0,
            producer: "",
            producers: [],
            description: "Additional Charge", // Initialize description field with the charge name
            specialType: "additional",
            isSpecial: true,
            isSpecialRow: true,
            // Empty values for required fields
            mixDate: "",
            teamName: "",
            division: "",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            amount: 0,
          },
        ],
      };
    });
  };

  const handleRemoveItem = (indexToRemove) => {
    setInvoiceData((prev) => {
      const newItems = prev.items.filter((_, index) => index !== indexToRemove);

      // Count regular items and renumber them
      let regularItemCount = 0;
      const updatedItems = newItems.map((item) => {
        if (!item.isSpecial && !item.isSpecialRow) {
          regularItemCount++;
          if (item.teamName && item.teamName.startsWith("Team ")) {
            return { ...item, teamName: `Team ${regularItemCount}` };
          }
        }
        return item;
      });

      // Recalculate total price
      const total = calculateTotalPrice(updatedItems);
      setTotalPrice(total);

      return { ...prev, items: updatedItems };
    });
  };

  const calculateTotalPrice = (items) => {
    return items.reduce((sum, item) => {
      if (item.isSpecialRow || item.isSpecial) {
        const price = parseFloat(item.price) || 0;
        const discount = parseFloat(item.discount) || 0;
        return sum + Math.max(0, price - discount);
      } else {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;

        // Apply fixed discount to price
        const discountedPrice = Math.max(0, price - discount);

        return sum + discountedPrice * quantity;
      }
    }, 0);
  };

  console.log(invoiceData);

  const handleItemChange = (index, field, value) => {
    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      const item = newItems[index];

      if (field === "teamName") {
        if (value !== "" || !item.teamName?.startsWith("Team ")) {
          item.teamName = value;
        }
      } else if (field === "division") {
        if (value !== "" || item.division !== "TBD") {
          item.division = value;
        }
      } else if (field === "mix_season") {
        item.mix_season = value;
        // Auto-set the discount if a season is selected
        if (value) {
          const selectedSeason = mixSeasons.find(
            (s) => s.id === parseInt(value)
          );
          if (selectedSeason) {
            item.discount = selectedSeason.discount || 0;
          }
        }
      } else if (field === "producer") {
        item.producer = value;

        // Update the producers array with the producer name
        if (value) {
          const producerObj = producers.find((p) => p.id === parseInt(value));
          if (producerObj) {
            item.producers = [producerObj.name]; // Store producer name as string in array
          }
        } else {
          item.producers = []; // Clear producers array if no producer selected
        }
      } else {
        item[field] = value;
      }

      if (
        !item.isSpecialRow &&
        !item.isSpecial &&
        (field === "quantity" || field === "price" || field === "discount")
      ) {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;

        // Apply fixed discount to price
        const discountedPrice = Math.max(0, price - discount);

        item.amount = discountedPrice * quantity;
      }

      if (
        (item.isSpecialRow || item.isSpecial) &&
        (field === "price" || field === "discount")
      ) {
        const price = parseFloat(item.price) || 0;
        const discount = parseFloat(item.discount) || 0;
        item.amount = Math.max(0, price - discount);
      }

      // Calculate subtotal and total
      const subtotal = newItems.reduce(
        (sum, item) => sum + (item.amount || 0),
        0
      );
      const total = subtotal + subtotal * (prev.tax / 100);

      // Update the total price state
      setTotalPrice(total);

      return {
        ...prev,
        items: newItems,
        subtotal,
        total,
      };
    });
  };

  const handleCreateInvoice = async () => {
    try {
      // Check if main member has invoice subscription
      if (!checkMainMemberInvoiceSubscription()) {
        showToast(
          globalDispatch,
          "Invoice subscription required. Please subscribe to create invoices.",
          3000,
          "error"
        );
        return;
      }

      if (
        !selectedClientId &&
        (!newClientData.program || !newClientData.email)
      ) {
        showToast(
          globalDispatch,
          "Please select a client or enter new client details",
          3000,
          "error"
        );
        return;
      }

      // Validate that all required fields have valid values
      for (const item of invoiceData.items) {
        if (!item.isSpecialRow && !item.isSpecial) {
          // For normal items, validate required fields
          if (!item.teamName || item.teamName.trim() === "") {
            showToast(
              globalDispatch,
              "Team name is required for all items",
              3000,
              "error"
            );
            return;
          }

          // We already checked main member subscription in checkMainMemberInvoiceSubscription
          // No need to check again for each item
        }
      }

      // Process items to ensure all required fields are included with exact API structure
      const processedItems = invoiceData.items.map((item) => {
        if (item.isSpecialRow || item.isSpecial) {
          return {
            price: parseFloat(item.price) || 0,
            quantity: parseInt(item.quantity) || 1,
            discount: parseFloat(item.discount) || 0,
            producer: item.producer || "",
            producers: item.producers || [],
            specialType: item.specialType || "additional",
            mixSeasonId: item.mix_season || "",
            isSpecial: true,
            description: item.description,
            name: item.name || "Additional Charge",
            // Include empty values for required fields
            mixDate: "",
            teamName: "",
            division: "",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            mixTypeId: "",
          };
        } else {
          // Get the mix type name for the description
          const itemProducerId = parseInt(item.producer) || currentUserId;
          const producerMixTypesList = producerMixTypes[itemProducerId] || [];
          const selectedMixType = producerMixTypesList.find(
            (mt) => mt.id === parseInt(item.mixType)
          );
          const mixTypeName = selectedMixType ? selectedMixType.name : "";

          return {
            price: parseFloat(item.price) || 0,
            quantity: parseInt(item.quantity) || 1,
            discount: parseFloat(item.discount) || 0,
            producer: item.producer || "",
            producers: item.producers || [],
            description: mixTypeName || "", // Include description with just the mix type name
            specialType: "",
            isSpecial: false,
            mixDate: item.mixDate || "",
            teamName: item.teamName || "",
            division: item.division || "",
            musicSurveyDue: item.musicSurveyDue || "",
            routineSubmissionDue: item.routineSubmissionDue || "",
            estimatedCompletion: item.estimatedCompletion || "",
            mixSeasonId: item.mix_season || "",
            mixTypeId: item.mixType || "",
          };
        }
      });

      const processedInvoiceData = {
        ...invoiceData,
        items: processedItems,
      };

      onSubmit({
        selectedClientId,
        newClientData,
        invoiceDates,
        invoiceData: processedInvoiceData,
        isQuote,
      });
    } catch (error) {
      console.error("Error creating invoice:", error);
      showToast(
        globalDispatch,
        "An error occurred while creating the invoice. Please try again.",
        3000,
        "error"
      );
    }
  };

  // Add function to handle quote download
  const handleQuoteDownload = () => {
    if (!selectedClientId && (!newClientData.program || !newClientData.email)) {
      showToast(
        globalDispatch,
        "Please select a client or enter new client details",
        3000,
        "error"
      );
      return false;
    }
    return true;
  };

  // Using the imported NewClientModal component

  return (
    <div className="rounded-lg bg-boxdark p-6">
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={onClose}
          className="flex items-center gap-2 text-white hover:text-primary"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Invoices
        </button>
      </div>
      {/* Company Information Header */}
      <div className="mb-6 flex items-center justify-between rounded-lg bg-meta-4 p-4">
        <div className="flex items-center gap-4">
          {companyInfo?.company?.manager?.license_company_logo ? (
            <img
              src={companyInfo.company.manager.license_company_logo}
              alt={companyInfo.company.manager.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : companyInfo?.company?.main_member?.license_company_logo ? (
            <img
              src={companyInfo.company.main_member.license_company_logo}
              alt={
                companyInfo.company.main_member.company_name || "Company Logo"
              }
              className="h-16 w-auto object-contain"
            />
          ) : userDetails?.company_logo ? (
            <img
              src={userDetails.company_logo}
              alt={userDetails?.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : null}
          <div>
            <h2 className="text-xl font-bold text-white">
              {companyInfo?.company?.manager?.company_name &&
              companyInfo.company.manager.company_name !== "null"
                ? companyInfo.company.manager.company_name
                : companyInfo?.company?.main_member?.company_name &&
                  companyInfo.company.main_member.company_name !== "null"
                ? companyInfo.company.main_member.company_name
                : userDetails?.company_name || "Your Company"}
            </h2>
            <p className="text-sm text-bodydark2">
              {userDetails?.office_email || ""}
            </p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-bold text-white">
            {isQuote ? "QUOTE" : "INVOICE"}
          </h2>
        </div>
      </div>

      {/* Header with back button */}

      {/* Client Information */}
      <div className="mb-3 pb-2">
        <h3 className="mb-2 text-base font-semibold text-white">
          Client Information
        </h3>
        <div className="flex items-center gap-4">
          <div className="w-[307px]">
            <select
              value={selectedClientId || ""}
              onChange={(e) => {
                const value = e.target.value;
                if (value === "add_new") {
                  setShowNewClientModal(true);
                  setSelectedClientId("");
                } else {
                  setSelectedClientId(value);
                  setNewClientData({ program: "", email: "" });
                }
              }}
              className="h-10 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-3 py-2 text-sm text-white"
            >
              <option value="">Select Client</option>
              <option value="add_new" className="font-semibold text-primary">
                + Add New Client
              </option>
              {clients.map((client) => (
                <option key={client.client_id} value={client.client_id}>
                  {client.client_program}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Show New Client Modal if needed */}
      <NewClientModal
        isOpen={showNewClientModal}
        onClose={() => setShowNewClientModal(false)}
        onAddClient={(clientData) => {
          setNewClientData(clientData);
          setSelectedClientId("");
          fetchAvailableClients();
          setShowNewClientModal(false);
        }}
        showToast={showToast}
        globalDispatch={globalDispatch}
      />

      {/* Invoice Dates Section */}
      <div className="mb-3 flex items-center justify-start pb-3">
        <div className="flex items-center justify-normal gap-3">
          <div>
            <label className="mb-1.5 block font-medium text-white">
              Invoice Date
            </label>
            <input
              type="date"
              value={invoiceDates.invoiceDate}
              onChange={(e) =>
                setInvoiceDates((prev) => ({
                  ...prev,
                  invoiceDate: e.target.value,
                }))
              }
              className="h-8 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-5 py-1.5 text-[12px] text-white"
            />
          </div>
          <div>
            <label className="mb-1.5 block font-medium text-white">
              Invoice Due Date
            </label>
            <input
              type="date"
              value={invoiceDates.invoiceDueDate}
              onChange={(e) =>
                setInvoiceDates((prev) => ({
                  ...prev,

                  invoiceDueDate: e.target.value,
                }))
              }
              className="h-8 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-5 py-1.5 text-[12px] text-white"
            />
          </div>
        </div>
      </div>

      {/* Invoice Items */}

      <div className="mb-3">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">Invoice Items</h3>
        </div>
        <div className="custom-overflow min-h-[140px] overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Producer
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Team Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Division
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Survey Due / Submission Due / Est. Completion
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Discount ($)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Season
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoiceData.items.map((item, index) =>
                !item.isSpecialRow ? (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 text-[12px] hover:bg-primary/5"
                  >
                    <td className="row-date2 whitespace-nowrap px-4 py-3">
                      <SingleDatePicker
                        id={`mixDate_${index}`}
                        date={item.mixDate ? moment(item.mixDate) : null}
                        onDateChange={(date) => {
                          handleItemChange(
                            index,
                            "mixDate",
                            date ? date.format("YYYY-MM-DD") : null
                          );
                          calculateDates(
                            date ? date.format("YYYY-MM-DD") : null,
                            index
                          );
                        }}
                        focused={focusedInput[`mixDate_${index}`]}
                        onFocusChange={({ focused }) =>
                          setFocusedInput((prev) => ({
                            ...prev,
                            [`mixDate_${index}`]: focused,
                          }))
                        }
                        numberOfMonths={1}
                        isOutsideRange={() => false}
                        displayFormat="MM-DD-YYYY"
                        placeholder="Select Mix Date"
                        readOnly={true}
                        customInputIcon={null}
                        noBorder={true}
                        block
                        className="w-full rounded border border-stroke/50"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {isMainMember ? (
                        <CustomSelect2
                          position="top"
                          label="Select Producer"
                          textClass="!text-[10px]"
                          value={item.producer || ""}
                          onChange={(value) => {
                            const producerId = value;
                            const producerName =
                              producers.find(
                                (p) => p.id === parseInt(producerId)
                              )?.name || "";
                            handleItemChange(index, "producer", producerId);
                            handleItemChange(
                              index,
                              "producerName",
                              producerName
                            );

                            // Fetch mix seasons and mix types for the selected producer
                            if (producerId) {
                              fetchMixSeasonsForProducer(parseInt(producerId));
                              fetchMixTypesForProducer(parseInt(producerId));
                            }
                          }}
                          className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                          options={producers.map((producer) => ({
                            value: producer.id.toString(),
                            label: producer.name,
                          }))}
                        />
                      ) : (
                        <input
                          type="text"
                          defaultValue={
                            userDetails?.first_name +
                            " " +
                            userDetails?.last_name
                          }
                          disabled={true}
                          className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        />
                      )}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        textClass="!text-[10px]"
                        value={item.mixType}
                        label="Select MixType"
                        onChange={(value) => {
                          handleItemChange(index, "mixType", value);

                          // Get the mix types for this item's producer
                          const itemProducerId =
                            parseInt(item.producer) || currentUserId;
                          const producerMixTypesList =
                            producerMixTypes[itemProducerId] || [];

                          const selectedMixType = producerMixTypesList.find(
                            (mt) => mt.id === parseInt(value)
                          );
                          if (selectedMixType) {
                            // Set the price if available
                            if (selectedMixType.price) {
                              handleItemChange(
                                index,
                                "price",
                                parseFloat(selectedMixType.price) || 0
                              );
                            }

                            // Set the description field with just the mix type name
                            handleItemChange(
                              index,
                              "description",
                              selectedMixType.name || ""
                            );
                          }
                        }}
                        position="top"
                        className=" h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default  disabled:bg-whiter dark:focus:border-primary"
                        options={(
                          producerMixTypes[
                            parseInt(item.producer) || currentUserId
                          ] || []
                        ).map((mixType) => ({
                          value: mixType.id.toString(),
                          label: mixType.name + " - $" + mixType.price,
                        }))}
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.teamName}
                        onChange={(e) =>
                          handleItemChange(index, "teamName", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value.startsWith("Team ")) {
                            handleItemChange(index, "teamName", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.division}
                        onChange={(e) =>
                          handleItemChange(index, "division", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value === "TBD") {
                            handleItemChange(index, "division", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex flex-col gap-2">
                        <div className="row-date flex flex-row items-center">
                          <SingleDatePicker
                            id={`musicSurveyDue_${index}`}
                            date={
                              item.musicSurveyDue
                                ? moment(item.musicSurveyDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "musicSurveyDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={focusedInput[`musicSurveyDue_${index}`]}
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`musicSurveyDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center">
                          <SingleDatePicker
                            id={`routineSubmissionDue_${index}`}
                            date={
                              item.routineSubmissionDue
                                ? moment(item.routineSubmissionDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "routineSubmissionDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`routineSubmissionDue_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`routineSubmissionDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center">
                          <SingleDatePicker
                            id={`estimatedCompletion_${index}`}
                            date={
                              item.estimatedCompletion
                                ? moment(item.estimatedCompletion)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "estimatedCompletion",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`estimatedCompletion_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`estimatedCompletion_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Completion Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                      </div>
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <div className="flex items-center gap-1">
                        <input
                          type="number"
                          value={item.discount || 0}
                          onChange={(e) =>
                            handleItemChange(
                              index,
                              "discount",
                              parseFloat(e.target.value)
                            )
                          }
                          className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                          min="0"
                          placeholder="Discount $"
                        />
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <div className="flex items-center gap-1">
                        <CustomSelect2
                          textClass="!text-[10px]"
                          label="Select Mix Season"
                          value={item.mix_season}
                          onChange={(value) => {
                            // First set the mix season
                            const newItems = [...invoiceData.items];
                            newItems[index].mix_season = value;

                            // Auto-set the discount if a season is selected
                            if (value) {
                              const selectedSeason = mixSeasons.find(
                                (s) => s.id === parseInt(value)
                              );
                              if (selectedSeason && selectedSeason.discount) {
                                newItems[index].discount =
                                  parseFloat(selectedSeason.discount) || 0;

                                // Recalculate the amount for this item
                                const price =
                                  parseFloat(newItems[index].price) || 0;
                                const quantity =
                                  parseInt(newItems[index].quantity) || 1;
                                const discount =
                                  parseFloat(newItems[index].discount) || 0;

                                // Apply fixed discount to price
                                const discountedPrice = Math.max(
                                  0,
                                  price - discount
                                );
                                newItems[index].amount =
                                  discountedPrice * quantity;
                              }
                            }

                            // Update the state directly to avoid multiple renders
                            setInvoiceData((prev) => ({
                              ...prev,
                              items: newItems,
                            }));

                            // Recalculate total
                            const total = calculateTotalPrice(newItems);
                            setTotalPrice(total);
                          }}
                          options={mixSeasons.map((season) => ({
                            value: season.id.toString(),
                            label: season.name,
                          }))}
                          placeholder="Select Season"
                          className=" h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default  disabled:bg-whiter dark:focus:border-primary"
                        />
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ) : (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 bg-meta-4/20 text-[12px] hover:bg-primary/5"
                  >
                    <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.name}
                        onChange={(e) =>
                          handleItemChange(index, "name", e.target.value)
                        }
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        placeholder="Charge Name"
                      />
                    </td>
                    <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for spacing */}
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.discount || 0}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "discount",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for mix season column */}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </table>
        </div>
        <div className="mt-2 flex justify-end gap-2">
          <button
            onClick={handleAddSpecialRow}
            className="flex items-center gap-2 rounded bg-meta-5 px-4 py-1 text-sm font-medium text-white hover:bg-meta-5/80"
          >
            <PlusCircle className="h-5 w-5" />
            Add Special Charge
          </button>
          <button
            onClick={handleAddItem}
            className="flex items-center gap-2 rounded bg-primary px-4 py-1 text-sm font-medium text-white hover:bg-primary/80"
          >
            <Plus className="h-5 w-5" />
            Add Row
          </button>
        </div>
      </div>

      {/* Invoice Total Section */}
      <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
        <div className="flex justify-end">
          <div className="w-1/3">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">
                Invoice total:
              </span>
              <span className="text-lg font-bold text-white">
                ${isNaN(totalPrice) ? "0.00" : totalPrice.toFixed(2)}
              </span>
            </div>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">Deposit:</span>
              <span className="text-sm text-white">
                $
                {(() => {
                  if (isNaN(totalPrice)) return "0.00";

                  const depositPercentage =
                    parseFloat(invoiceData.depositPercentage) || 0;
                  const depositAmount =
                    parseFloat(invoiceData.depositAmount) || 0;

                  if (depositPercentage > 0) {
                    const calculatedDeposit =
                      (totalPrice * depositPercentage) / 100;
                    return isNaN(calculatedDeposit)
                      ? "0.00"
                      : calculatedDeposit.toFixed(2);
                  } else {
                    return isNaN(depositAmount)
                      ? "0.00"
                      : depositAmount.toFixed(2);
                  }
                })()}
              </span>
            </div>
            <div className="flex items-center justify-between border-t border-stroke/50 pt-2">
              <span className="text-sm font-medium text-white">
                Balance due:
              </span>
              <span className="text-sm font-bold text-white">
                $
                {(() => {
                  if (isNaN(totalPrice)) return "0.00";

                  const depositPercentage =
                    parseFloat(invoiceData.depositPercentage) || 0;
                  const depositAmount =
                    parseFloat(invoiceData.depositAmount) || 0;

                  if (depositPercentage > 0) {
                    const calculatedDeposit =
                      (totalPrice * depositPercentage) / 100;
                    const balance = totalPrice - calculatedDeposit;
                    return isNaN(balance) ? "0.00" : balance.toFixed(2);
                  } else {
                    const balance = totalPrice - depositAmount;
                    return isNaN(balance) ? "0.00" : balance.toFixed(2);
                  }
                })()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Deposit and Terms Section */}
      <div className="mt-6 grid grid-cols-3 gap-6">
        {/* Left Column - Deposit Settings */}
        <div className="space-y-2">
          <h3 className="text-base font-semibold text-white">
            Deposit Settings
          </h3>
          <div className="space-y-2">
            <div>
              <label className="mb-1.5 block text-sm text-white">Type</label>
              <select
                value={
                  invoiceData.depositPercentage > 0 ? "percentage" : "amount"
                }
                onChange={(e) => {
                  const isAmount = e.target.value === "amount";
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isAmount ? 0 : 0,
                    depositPercentage: isAmount
                      ? 0
                      : prev.depositPercentage ||
                        userDetails?.deposit_percent ||
                        30,
                  }));
                }}
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
              >
                <option value="amount">Fixed Amount</option>
                <option value="percentage">Percentage</option>
              </select>
            </div>
            <div>
              <label className="mb-1.5 block text-sm text-white">
                {invoiceData.depositPercentage > 0
                  ? "Percentage (%)"
                  : "Amount ($)"}
              </label>
              <input
                type="number"
                value={
                  invoiceData.depositPercentage > 0
                    ? invoiceData.depositPercentage
                    : invoiceData.depositAmount
                }
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  const isPercentage = invoiceData.depositPercentage > 0;
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isPercentage ? 0 : value,
                    depositPercentage: isPercentage ? value : 0,
                  }));
                }}
                placeholder={
                  invoiceData.depositPercentage > 0
                    ? `Default: ${userDetails?.deposit_percent || 30}%`
                    : "Enter amount"
                }
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
                min="0"
                max={invoiceData.depositPercentage > 0 ? 100 : undefined}
              />
            </div>
          </div>
        </div>

        {/* Middle and Right Columns - Notes and Terms */}
        <div className="col-span-2 space-y-2">
          <h3 className="text-base font-semibold text-white">Notes & Terms</h3>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="mb-1.5 block text-sm text-white">Notes</label>
              <textarea
                value={invoiceData.notes}
                onChange={(e) =>
                  setInvoiceData((prev) => ({ ...prev, notes: e.target.value }))
                }
                className="h-[110px] w-full rounded border border-stroke bg-transparent px-3 py-2 text-sm text-white"
                rows="4"
                placeholder="Add any additional notes..."
              />
            </div>
            <div>
              <label className="mb-1.5 block text-sm text-white">
                Terms and Conditions
              </label>
              <SunEditor
                setContents={invoiceData.termsAndConditions}
                onChange={(content) =>
                  setInvoiceData((prev) => ({
                    ...prev,
                    termsAndConditions: content,
                  }))
                }
                getSunEditorInstance={getSunEditorInstance}
                setOptions={{
                  buttonList: buttonList.complex,
                  height: 110,
                  width: "100%",
                  placeholder: userDetails?.contract_agreement
                    ? "Terms and conditions loaded from your settings"
                    : "Add terms and conditions...",
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-6 flex justify-end gap-4">
        <button
          onClick={onClose}
          className="rounded-lg border border-stroke bg-opacity-80 px-6 py-2 text-bodydark"
        >
          Cancel
        </button>
        {console.log(
          selectedClientId,
          newClientData,
          invoiceDates,
          invoiceData,
          userDetails
        )}
        {/* Add Save as Quote button */}
        <PDFDownloadLink
          document={
            <InvoiceQuotePDF
              data={{
                selectedClientId,
                newClientData,
                invoiceDates,
                invoiceData: { ...invoiceData, isQuote: true },
                clientDetails:
                  clients.find(
                    (client) => client.id === parseInt(selectedClientId)
                  ) || newClientData,
              }}
              userDetails={userDetails}
              companyInfo={companyInfo}
            />
          }
          fileName={`quote-${moment().format("YYYY-MM-DD")}.pdf`}
          className={`flex items-center gap-2 rounded-lg bg-meta-5 px-6 py-2 text-white hover:bg-opacity-90 disabled:opacity-50 ${
            loading ? "cursor-not-allowed opacity-50" : ""
          }`}
          onClick={(e) => {
            if (!handleQuoteDownload()) {
              e.preventDefault();
            }
          }}
        >
          {({ loading: pdfLoading }) => (
            <>
              <Download className="h-4 w-4" />
              {pdfLoading ? "Generating Quote..." : "Save as Quote"}
            </>
          )}
        </PDFDownloadLink>

        <button
          onClick={() => {
            setIsQuote(false);
            handleCreateInvoice();
          }}
          disabled={loading}
          className="rounded-lg bg-primary px-6 py-2 text-white hover:bg-opacity-90 disabled:opacity-50"
        >
          {loading ? "Creating..." : "Create Invoice"}
        </button>
      </div>
    </div>
  );
};

export default CreateInvoiceComponent;
