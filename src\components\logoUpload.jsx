import React, { useState } from "react";
import { GlobalContext, showToast } from "../globalContext";

const LogoUpload = ({
  setFileUpload,
  maxFileSize = 2,
  minWidth = 500,
  minHeight = 200,
  aspectRatioRange = [1.7, 3],
  transparent = false,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [validationError, setValidationError] = useState(null);
  const [selectedFileName, setSelectedFileName] = useState(null);

  const saveFile = async (e) => {
    const file = e.target.files;
    // Reset previous errors
    setValidationError(null);

    if (file.length > 0) {
      setSelectedFileName(file[0].name);
      console.log("File selected in LogoUpload:", file[0].name);

      // Log file details for debugging
      console.log("File details:", {
        name: file[0].name,
        type: file[0].type,
        size: `${(file[0].size / 1024 / 1024).toFixed(2)}MB`,
      });

      if (!validateFileSize(file[0])) {
        setValidationError(
          `File size exceeds ${maxFileSize}MB. Please upload a smaller file.`
        );
        return;
      }

      console.log("Validating image dimensions...");
      const dimensionValidation = await validateImageDimensions(file[0]);

      if (!dimensionValidation.valid) {
        setValidationError(dimensionValidation.message);
        return;
      }

      console.log("All validations passed, proceeding with upload");
      const formData = new FormData();
      formData.append("files", file[0]);
      console.log("LogoUpload: File added to FormData", file[0].name);
      setFileUpload(formData);
    }
  };

  const validateFileSize = (file) => {
    const fileSize = file.size / 1024 / 1024; // in MB
    if (fileSize > maxFileSize) {
      showToast(
        globalDispatch,
        `File size exceeds ${maxFileSize}MB. Please upload a smaller file.`,
        9000,
        "error"
      );
      return false;
    }
    return true;
  };

  const validateImageDimensions = (file) => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const image = new Image();
        image.src = event.target.result;

        image.onload = () => {
          const aspectRatio = image.width / image.height;
          console.log("Image dimensions:", {
            width: image.width,
            height: image.height,
            aspectRatio: aspectRatio.toFixed(2),
            requiredWidth: minWidth,
            requiredHeight: minHeight,
            requiredAspectRatio: `${aspectRatioRange[0]} to ${aspectRatioRange[1]}`,
          });

          // Check width and height
          if (image.width < minWidth || image.height < minHeight) {
            const message = `Image is too small. Required minimum dimensions: ${minWidth}x${minHeight} pixels. Your image: ${image.width}x${image.height} pixels.`;
            showToast(globalDispatch, message, 5000, "error");
            resolve({ valid: false, message });
            return;
          }

          // Check aspect ratio
          if (
            aspectRatio < aspectRatioRange[0] ||
            aspectRatio > aspectRatioRange[1]
          ) {
            const message = `Image aspect ratio (${aspectRatio.toFixed(
              2
            )}) is not within accepted range (${aspectRatioRange[0]} to ${
              aspectRatioRange[1]
            }, roughly 16:9 to 3:1).`;
            showToast(globalDispatch, message, 5000, "error");
            resolve({ valid: false, message });
            return;
          }

          // All checks passed
          resolve({ valid: true });
        };
      };
      reader.readAsDataURL(file);
    });
  };

  return (
    <div className={`flex flex-col`}>
      <div className={`flex flex-row gap-2`}>
        <input
          className="rounded border border-gray-500 bg-gray-800 p-2 text-white shadow placeholder:text-gray-200"
          type="file"
          accept="image/*"
          onChange={saveFile}
        />
      </div>

      {selectedFileName && !validationError && (
        <div className="mt-2 text-xs text-green-500">
          File selected: {selectedFileName}
        </div>
      )}

      {validationError && (
        <div className="mt-2 rounded bg-red-100/10 p-2 text-xs text-red-500">
          Error: {validationError}
        </div>
      )}

      {transparent && (
        <div className="text-warning-600 mt-2 text-xs">
          Logo with Transparent Background is recommended
        </div>
      )}

      <div className="text-warning-600 mt-2 text-xs">
        Max file size limit is {maxFileSize}MB
      </div>

      <div className="text-warning-600 text-xs">
        Required minimum dimensions: {minWidth} x {minHeight} pixels
      </div>

      <div className="text-warning-600 text-xs">
        Required aspect ratio: 16:9 to 3:1 (width/height between{" "}
        {aspectRatioRange[0]} and {aspectRatioRange[1]})
      </div>
    </div>
  );
};

export default LogoUpload;
