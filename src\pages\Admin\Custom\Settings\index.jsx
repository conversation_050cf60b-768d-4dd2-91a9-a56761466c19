import { useContext, useEffect } from "react";
import { GlobalContext } from "Context/Global";
import useSettings from "./useSettings";
import EditSettingButton from "./EditSettingButton";

const CustomAdminSettingsPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  const { loading, settings, refetch } = useSettings();

  const settingObj = settings.reduce((a, c) => {
    a[c.setting_key] = c.setting_value;
    return a;
  }, {});

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "settings",
      },
    });
  }, []);

  return (
    <div className="mx-auto rounded p-5 shadow-md">
      <div className="mt-6 max-w-lg">
        <div className="flex items-center justify-between">
          <p>Number of free reports for a startup</p>
          <div className="flex items-center gap-2">
            <p className="font-medium">
              {settingObj["start_up_free_reports_count"]}
            </p>
            <EditSettingButton
              setting_key={"start_up_free_reports_count"}
              value={settingObj["start_up_free_reports_count"]}
              afterEdit={refetch}
            />
          </div>
        </div>
        <hr className="my-4" />
        <div className="mt-4 flex items-center justify-between">
          <p>Number of invites for a free report</p>
          <div className="flex items-center gap-2">
            <p className="font-medium">
              {settingObj["start_up_free_report_invite_count"]}
            </p>
            <EditSettingButton
              setting_key={"start_up_free_report_invite_count"}
              value={settingObj["start_up_free_report_invite_count"]}
              afterEdit={refetch}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomAdminSettingsPage;
