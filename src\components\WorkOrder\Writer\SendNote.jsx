import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const SendNote = ({ setNoteSubmit, setSendNoteClose }) => {
  const [noteContent, setNoteContent] = React.useState("");
  const [remainingCharCount, setRemainingCharCount] = React.useState(3000);
  const [submitDisabled, setSubmitDisabled] = React.useState(false);

  const handleNoteSubmit = (e) => {
    e.preventDefault();
    setNoteSubmit(noteContent);
    setNoteContent("");
  };

  const handleNoteCancel = (e) => {
    e.preventDefault();
    setNoteContent("");
    setSendNoteClose(true);
  };

  const handleNoteChange = (e) => {
    const length = e.target.value.length;
    if (length > 3000) {
      setSubmitDisabled(true);
      setRemainingCharCount(0);
    } else {
      setSubmitDisabled(false);
      setRemainingCharCount(3000 - length);
    }
    setNoteContent(e.target.value);
  };

  return (
    <div className="shadow-default rounded border border-stroke bg-boxdark p-6">
      <form>
        {/* Header */}
        <div className="mb-4 flex items-center gap-3">
          <FontAwesomeIcon
            icon="fa-solid fa-message"
            className="text-lg text-primary"
          />
          <h3 className="text-lg font-medium text-white">Send Note</h3>
        </div>

        {/* Note Input */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <label
              className="mb-2.5 block text-sm font-medium text-white"
              htmlFor="note"
            >
              Message
            </label>
            <span
              className={`text-xs ${
                remainingCharCount < 300 ? "text-danger" : "text-bodydark2"
              }`}
            >
              {remainingCharCount} characters remaining
            </span>
          </div>
          <textarea
            id="note"
            rows={6}
            className="w-full rounded border border-stroke bg-boxdark-2 px-4 py-3 text-white placeholder:text-bodydark2 focus:border-primary focus:outline-none"
            placeholder="Type your note here..."
            value={noteContent}
            onChange={handleNoteChange}
          />
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end gap-3">
          <button
            type="button"
            onClick={handleNoteCancel}
            className="flex items-center justify-center rounded border border-stroke px-6 py-2 text-sm font-medium text-bodydark2 hover:border-primary hover:text-primary"
          >
            <FontAwesomeIcon icon="fa-solid fa-xmark" className="mr-2" />
            Cancel
          </button>
          <button
            type="button"
            onClick={handleNoteSubmit}
            disabled={submitDisabled || !noteContent}
            className="flex items-center justify-center rounded-sm bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90 disabled:bg-opacity-50"
          >
            <FontAwesomeIcon icon="fa-solid fa-paper-plane" className="mr-2" />
            Send
          </button>
        </div>
      </form>
    </div>
  );
};

export default SendNote;
