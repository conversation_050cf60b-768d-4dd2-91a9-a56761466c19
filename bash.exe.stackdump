Stack trace:
Frame         Function      Args
0007FFFFA110  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFA110, 0007FFFF9010) msys-2.0.dll+0x1FEBA
0007FFFFA110  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3E8) msys-2.0.dll+0x67F9
0007FFFFA110  000210046832 (000210285FF9, 0007FFFF9FC8, 0007FFFFA110, 000000000000) msys-2.0.dll+0x6832
0007FFFFA110  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA110  0002100690B4 (0007FFFFA120, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA3F0  00021006A49D (0007FFFFA120, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE7B740000 ntdll.dll
7FFE79870000 KERNEL32.DLL
7FFE790A0000 KERNELBASE.dll
7FFE7A3E0000 USER32.dll
7FFE794C0000 win32u.dll
7FFE79BA0000 GDI32.dll
7FFE78E50000 gdi32full.dll
7FFE79420000 msvcp_win.dll
7FFE794F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE79AF0000 advapi32.dll
7FFE7A090000 msvcrt.dll
7FFE7A1B0000 sechost.dll
7FFE79BD0000 RPCRT4.dll
7FFE78400000 CRYPTBASE.DLL
7FFE78C60000 bcryptPrimitives.dll
7FFE796C0000 IMM32.DLL
