import React from "react";
import { NavLink } from "react-router-dom";
import { AuthContext } from "Src/authContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { GlobalContext } from "Src/globalContext";

const ProfileDropdown = () => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const handleLogout = () => {
    globalDispatch({
      type: "TOGGLE_PROFILE",
      payload: { showProfile: false },
    });
    dispatch({
      type: "LOGOUT",
    });
  };

  return (
    <div className="absolute right-0 mt-2 w-36 rounded-md border border-gray-500 bg-neutral-950 shadow-lg ring-1 ring-black ring-opacity-5">
      {authState.role === "client" ? (
        <NavLink
          to={`/${authState.role}/projects`}
          className="block px-2 py-2 text-sm text-gray-100 hover:rounded-md hover:bg-gray-600"
          onClick={() =>
            globalDispatch({
              type: "TOGGLE_PROFILE",
              payload: { showProfile: false },
            })
          }
        >
          <FontAwesomeIcon
            icon="fa-solid fa-chart-line"
            width={24}
            height={24}
          />{" "}
          Projects
        </NavLink>
      ) : (
        <NavLink
          to={`/${authState.role}/dashboard`}
          className="block px-2 py-2 text-sm text-gray-100 hover:rounded-md hover:bg-gray-600"
          onClick={() =>
            globalDispatch({
              type: "TOGGLE_PROFILE",
              payload: { showProfile: false },
            })
          }
        >
          <FontAwesomeIcon
            icon="fa-solid fa-chart-line"
            width={24}
            height={24}
          />{" "}
          Dashboard
        </NavLink>
      )}
      <NavLink
        to={`/${authState.role}/profile`}
        className="block px-2 py-2 text-sm text-gray-100 hover:rounded-md hover:bg-gray-600"
        onClick={() =>
          globalDispatch({
            type: "TOGGLE_PROFILE",
            payload: { showProfile: false },
          })
        }
      >
        <FontAwesomeIcon icon="fa-solid fa-user-gear" width={24} height={24} />{" "}
        Profile
      </NavLink>
      <NavLink
        className="block px-2 py-2 text-sm text-gray-100 hover:rounded-md hover:bg-gray-600"
        rel="noreferrer"
        to={`${authState.role}/login`}
        onClick={() => handleLogout()}
      >
        <FontAwesomeIcon
          icon="fa-solid fa-right-from-bracket"
          width={24}
          height={24}
        />{" "}
        Logout
      </NavLink>
    </div>
  );
};

export default ProfileDropdown;
