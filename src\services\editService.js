import MkdSDK from "../utils/MkdSDK";

let sdk = new MkdSDK();

export const CreateEditAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const CreateSpecialEditAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit_type`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const UpdateEditAPI = async (payload, id) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const UpdateEditTypeAPI = async (payload, id) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit_type/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllEditAPI = async (payload) => {
  let data = {
    filter: payload,
  };

  try {
    const uri = "/v3/api/custom/equality_record/edit/retrieve-all";
    const res = await sdk.callRawAPI(uri, data, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllEditTypesListAPI = async (payload) => {
  let data = {
    filter: payload,
  };
  try {
    const uri = "/v3/api/custom/equality_record/edit_type/retrieve-all";
    const res = await sdk.callRawAPI(uri, data, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const viewEditDetails = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit/view/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const viewEditTypesDetails = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit_type/view/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteEditTypeAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit_type/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteEditAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/edit/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};
