import{B as we,t as qt,g as Gt,P as Se,S as fr,n as mr,e as gr,j as Ds,N as vr,h as Cs,k as Ii,R as yr,l as br}from"./@uppy/aws-s3-5653d8cf.js";import{g as wr,t as Fe,P as Ae,S as Sr}from"./@uppy/dashboard-9ed0e038.js";import"./@uppy/drag-drop-593d2a9d.js";import"./@uppy/react-07ca6ec5.js";import{$ as Re,y as h,a5 as Gi,a1 as Yi}from"./@fullcalendar/core-ff88745d.js";import{U as et,m as Ar,f as Rr}from"./@uppy/core-5d4a6f29.js";import{D as Er}from"./@uppy/dropbox-d3ac5536.js";import{G as Dr}from"./@uppy/google-drive-543a142d.js";import{O as Cr}from"./@uppy/onedrive-24a3367e.js";import{g as Os}from"./vendor-3aca5368.js";import{T as xs}from"./@uppy/tus-a7bc7213.js";import"./@uppy/xhr-upload-dc2d48e2.js";const Or={version:"2.1.0"},xr={target:null};function je(s){var t,e;return(t=(e=s.dataTransfer)==null||(e=e.types)==null?void 0:e.some(i=>i==="Files"))!=null?t:!1}class Pr extends we{constructor(t,e){super(t,{...xr,...e}),this.addFiles=i=>{const r=i.map(o=>({source:this.id,name:o.name,type:o.type,data:o,meta:{relativePath:o.relativePath||null}}));try{this.uppy.addFiles(r)}catch(o){this.uppy.log(o)}},this.handleDrop=async i=>{var r,o,a;if(!je(i))return;i.preventDefault(),i.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),(r=i.currentTarget)==null||r.classList.remove("uppy-is-drag-over"),this.setPluginState({isDraggingOver:!1}),this.uppy.iteratePlugins(d=>{d.type==="acquirer"&&(d.handleRootDrop==null||d.handleRootDrop(i))});let n=!1;const l=d=>{this.uppy.log(d,"error"),n||(this.uppy.info(d.message,"error"),n=!0)},c=await wr(i.dataTransfer,{logDropError:l});c.length>0&&(this.uppy.log("[DropTarget] Files were dropped"),this.addFiles(c)),(o=(a=this.opts).onDrop)==null||o.call(a,i)},this.handleDragOver=i=>{var r,o;je(i)&&(i.preventDefault(),i.stopPropagation(),i.dataTransfer.dropEffect="copy",clearTimeout(this.removeDragOverClassTimeout),i.currentTarget.classList.add("uppy-is-drag-over"),this.setPluginState({isDraggingOver:!0}),(r=(o=this.opts).onDragOver)==null||r.call(o,i))},this.handleDragLeave=i=>{var r,o;if(!je(i))return;i.preventDefault(),i.stopPropagation();const{currentTarget:a}=i;clearTimeout(this.removeDragOverClassTimeout),this.removeDragOverClassTimeout=setTimeout(()=>{a.classList.remove("uppy-is-drag-over"),this.setPluginState({isDraggingOver:!1})},50),(r=(o=this.opts).onDragLeave)==null||r.call(o,i)},this.addListeners=()=>{const{target:i}=this.opts;if(i instanceof Element?this.nodes=[i]:typeof i=="string"&&(this.nodes=Fe(document.querySelectorAll(i))),!this.nodes||this.nodes.length===0)throw new Error(`"${i}" does not match any HTML elements`);this.nodes.forEach(r=>{r.addEventListener("dragover",this.handleDragOver,!1),r.addEventListener("dragleave",this.handleDragLeave,!1),r.addEventListener("drop",this.handleDrop,!1)})},this.removeListeners=()=>{this.nodes&&this.nodes.forEach(i=>{i.removeEventListener("dragover",this.handleDragOver,!1),i.removeEventListener("dragleave",this.handleDragLeave,!1),i.removeEventListener("drop",this.handleDrop,!1)})},this.type="acquirer",this.id=this.opts.id||"DropTarget",this.title="Drop Target"}install(){this.setPluginState({isDraggingOver:!1}),this.addListeners()}uninstall(){this.removeListeners()}}Pr.VERSION=Or.version;/*!
 * Cropper.js v1.5.7
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2020-05-23T05:23:00.081Z
 */function Me(s){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Me=function(t){return typeof t}:Me=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Me(s)}function Tr(s,t){if(!(s instanceof t))throw new TypeError("Cannot call a class as a function")}function Xi(s,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(s,i.key,i)}}function Mr(s,t,e){return t&&Xi(s.prototype,t),e&&Xi(s,e),s}function kr(s,t,e){return t in s?Object.defineProperty(s,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):s[t]=e,s}function Ki(s,t){var e=Object.keys(s);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(s);t&&(i=i.filter(function(r){return Object.getOwnPropertyDescriptor(s,r).enumerable})),e.push.apply(e,i)}return e}function Ps(s){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Ki(Object(e),!0).forEach(function(i){kr(s,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(e)):Ki(Object(e)).forEach(function(i){Object.defineProperty(s,i,Object.getOwnPropertyDescriptor(e,i))})}return s}function Ts(s){return Lr(s)||Fr(s)||_r(s)||Ir()}function Lr(s){if(Array.isArray(s))return fi(s)}function Fr(s){if(typeof Symbol<"u"&&Symbol.iterator in Object(s))return Array.from(s)}function _r(s,t){if(s){if(typeof s=="string")return fi(s,t);var e=Object.prototype.toString.call(s).slice(8,-1);if(e==="Object"&&s.constructor&&(e=s.constructor.name),e==="Map"||e==="Set")return Array.from(s);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return fi(s,t)}}function fi(s,t){(t==null||t>s.length)&&(t=s.length);for(var e=0,i=new Array(t);e<t;e++)i[e]=s[e];return i}function Ir(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var _e=typeof window<"u"&&typeof window.document<"u",ct=_e?window:{},Ni=_e&&ct.document.documentElement?"ontouchstart"in ct.document.documentElement:!1,Bi=_e?"PointerEvent"in ct:!1,P="cropper",Ui="all",Ms="crop",ks="move",Ls="zoom",Ct="e",Ot="w",Ut="s",bt="n",Yt="ne",Xt="nw",Kt="se",Qt="sw",mi="".concat(P,"-crop"),Qi="".concat(P,"-disabled"),K="".concat(P,"-hidden"),Ji="".concat(P,"-hide"),Nr="".concat(P,"-invisible"),ke="".concat(P,"-modal"),gi="".concat(P,"-move"),me="".concat(P,"Action"),Ce="".concat(P,"Preview"),zi="crop",Fs="move",_s="none",vi="crop",yi="cropend",bi="cropmove",wi="cropstart",Zi="dblclick",Br=Ni?"touchstart":"mousedown",Ur=Ni?"touchmove":"mousemove",zr=Ni?"touchend touchcancel":"mouseup",ts=Bi?"pointerdown":Br,es=Bi?"pointermove":Ur,is=Bi?"pointerup pointercancel":zr,ss="ready",rs="resize",os="wheel",Si="zoom",as="image/jpeg",jr=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Vr=/^data:/,Wr=/^data:image\/jpeg;base64,/,$r=/^img|canvas$/i,ns={viewMode:0,dragMode:zi,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Hr='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',qr=Number.isNaN||ct.isNaN;function R(s){return typeof s=="number"&&!qr(s)}var ls=function(t){return t>0&&t<1/0};function Ve(s){return typeof s>"u"}function Bt(s){return Me(s)==="object"&&s!==null}var Gr=Object.prototype.hasOwnProperty;function zt(s){if(!Bt(s))return!1;try{var t=s.constructor,e=t.prototype;return t&&e&&Gr.call(e,"isPrototypeOf")}catch{return!1}}function X(s){return typeof s=="function"}var Yr=Array.prototype.slice;function Is(s){return Array.from?Array.from(s):Yr.call(s)}function U(s,t){return s&&X(t)&&(Array.isArray(s)||R(s.length)?Is(s).forEach(function(e,i){t.call(s,e,i,s)}):Bt(s)&&Object.keys(s).forEach(function(e){t.call(s,s[e],e,s)})),s}var T=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return Bt(t)&&i.length>0&&i.forEach(function(o){Bt(o)&&Object.keys(o).forEach(function(a){t[a]=o[a]})}),t},Xr=/\.\d*(?:0|9){12}\d*$/;function Wt(s){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return Xr.test(s)?Math.round(s*t)/t:s}var Kr=/^width|height|left|top|marginLeft|marginTop$/;function wt(s,t){var e=s.style;U(t,function(i,r){Kr.test(r)&&R(i)&&(i="".concat(i,"px")),e[r]=i})}function Qr(s,t){return s.classList?s.classList.contains(t):s.className.indexOf(t)>-1}function j(s,t){if(t){if(R(s.length)){U(s,function(i){j(i,t)});return}if(s.classList){s.classList.add(t);return}var e=s.className.trim();e?e.indexOf(t)<0&&(s.className="".concat(e," ").concat(t)):s.className=t}}function ht(s,t){if(t){if(R(s.length)){U(s,function(e){ht(e,t)});return}if(s.classList){s.classList.remove(t);return}s.className.indexOf(t)>=0&&(s.className=s.className.replace(t,""))}}function jt(s,t,e){if(t){if(R(s.length)){U(s,function(i){jt(i,t,e)});return}e?j(s,t):ht(s,t)}}var Jr=/([a-z\d])([A-Z])/g;function ji(s){return s.replace(Jr,"$1-$2").toLowerCase()}function Ai(s,t){return Bt(s[t])?s[t]:s.dataset?s.dataset[t]:s.getAttribute("data-".concat(ji(t)))}function ge(s,t,e){Bt(e)?s[t]=e:s.dataset?s.dataset[t]=e:s.setAttribute("data-".concat(ji(t)),e)}function Zr(s,t){if(Bt(s[t]))try{delete s[t]}catch{s[t]=void 0}else if(s.dataset)try{delete s.dataset[t]}catch{s.dataset[t]=void 0}else s.removeAttribute("data-".concat(ji(t)))}var Ns=/\s\s*/,Bs=function(){var s=!1;if(_e){var t=!1,e=function(){},i=Object.defineProperty({},"once",{get:function(){return s=!0,t},set:function(o){t=o}});ct.addEventListener("test",e,i),ct.removeEventListener("test",e,i)}return s}();function it(s,t,e){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=e;t.trim().split(Ns).forEach(function(o){if(!Bs){var a=s.listeners;a&&a[o]&&a[o][e]&&(r=a[o][e],delete a[o][e],Object.keys(a[o]).length===0&&delete a[o],Object.keys(a).length===0&&delete s.listeners)}s.removeEventListener(o,r,i)})}function tt(s,t,e){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=e;t.trim().split(Ns).forEach(function(o){if(i.once&&!Bs){var a=s.listeners,n=a===void 0?{}:a;r=function(){delete n[o][e],s.removeEventListener(o,r,i);for(var c=arguments.length,d=new Array(c),u=0;u<c;u++)d[u]=arguments[u];e.apply(s,d)},n[o]||(n[o]={}),n[o][e]&&s.removeEventListener(o,n[o][e],i),n[o][e]=r,s.listeners=n}s.addEventListener(o,r,i)})}function $t(s,t,e){var i;return X(Event)&&X(CustomEvent)?i=new CustomEvent(t,{detail:e,bubbles:!0,cancelable:!0}):(i=document.createEvent("CustomEvent"),i.initCustomEvent(t,!0,!0,e)),s.dispatchEvent(i)}function Us(s){var t=s.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var We=ct.location,to=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function hs(s){var t=s.match(to);return t!==null&&(t[1]!==We.protocol||t[2]!==We.hostname||t[3]!==We.port)}function cs(s){var t="timestamp=".concat(new Date().getTime());return s+(s.indexOf("?")===-1?"?":"&")+t}function he(s){var t=s.rotate,e=s.scaleX,i=s.scaleY,r=s.translateX,o=s.translateY,a=[];R(r)&&r!==0&&a.push("translateX(".concat(r,"px)")),R(o)&&o!==0&&a.push("translateY(".concat(o,"px)")),R(t)&&t!==0&&a.push("rotate(".concat(t,"deg)")),R(e)&&e!==1&&a.push("scaleX(".concat(e,")")),R(i)&&i!==1&&a.push("scaleY(".concat(i,")"));var n=a.length?a.join(" "):"none";return{WebkitTransform:n,msTransform:n,transform:n}}function eo(s){var t=Ps({},s),e=[];return U(s,function(i,r){delete t[r],U(t,function(o){var a=Math.abs(i.startX-o.startX),n=Math.abs(i.startY-o.startY),l=Math.abs(i.endX-o.endX),c=Math.abs(i.endY-o.endY),d=Math.sqrt(a*a+n*n),u=Math.sqrt(l*l+c*c),p=(u-d)/d;e.push(p)})}),e.sort(function(i,r){return Math.abs(i)<Math.abs(r)}),e[0]}function Oe(s,t){var e=s.pageX,i=s.pageY,r={endX:e,endY:i};return t?r:Ps({startX:e,startY:i},r)}function io(s){var t=0,e=0,i=0;return U(s,function(r){var o=r.startX,a=r.startY;t+=o,e+=a,i+=1}),t/=i,e/=i,{pageX:t,pageY:e}}function St(s){var t=s.aspectRatio,e=s.height,i=s.width,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",o=ls(i),a=ls(e);if(o&&a){var n=e*t;r==="contain"&&n>i||r==="cover"&&n<i?e=i/t:i=e*t}else o?e=i/t:a&&(i=e*t);return{width:i,height:e}}function so(s){var t=s.width,e=s.height,i=s.degree;if(i=Math.abs(i)%180,i===90)return{width:e,height:t};var r=i%90*Math.PI/180,o=Math.sin(r),a=Math.cos(r),n=t*a+e*o,l=t*o+e*a;return i>90?{width:l,height:n}:{width:n,height:l}}function ro(s,t,e,i){var r=t.aspectRatio,o=t.naturalWidth,a=t.naturalHeight,n=t.rotate,l=n===void 0?0:n,c=t.scaleX,d=c===void 0?1:c,u=t.scaleY,p=u===void 0?1:u,w=e.aspectRatio,m=e.naturalWidth,y=e.naturalHeight,g=i.fillColor,b=g===void 0?"transparent":g,A=i.imageSmoothingEnabled,O=A===void 0?!0:A,N=i.imageSmoothingQuality,L=N===void 0?"low":N,f=i.maxWidth,D=f===void 0?1/0:f,z=i.maxHeight,Z=z===void 0?1/0:z,dt=i.minWidth,Rt=dt===void 0?0:dt,Et=i.minHeight,yt=Et===void 0?0:Et,nt=document.createElement("canvas"),G=nt.getContext("2d"),Dt=St({aspectRatio:w,width:D,height:Z}),De=St({aspectRatio:w,width:Rt,height:yt},"cover"),Ue=Math.min(Dt.width,Math.max(De.width,m)),ze=Math.min(Dt.height,Math.max(De.height,y)),Wi=St({aspectRatio:r,width:D,height:Z}),$i=St({aspectRatio:r,width:Rt,height:yt},"cover"),Hi=Math.min(Wi.width,Math.max($i.width,o)),qi=Math.min(Wi.height,Math.max($i.height,a)),dr=[-Hi/2,-qi/2,Hi,qi];return nt.width=Wt(Ue),nt.height=Wt(ze),G.fillStyle=b,G.fillRect(0,0,Ue,ze),G.save(),G.translate(Ue/2,ze/2),G.rotate(l*Math.PI/180),G.scale(d,p),G.imageSmoothingEnabled=O,G.imageSmoothingQuality=L,G.drawImage.apply(G,[s].concat(Ts(dr.map(function(pr){return Math.floor(Wt(pr))})))),G.restore(),nt}var zs=String.fromCharCode;function oo(s,t,e){var i="";e+=t;for(var r=t;r<e;r+=1)i+=zs(s.getUint8(r));return i}var ao=/^data:.*,/;function no(s){var t=s.replace(ao,""),e=atob(t),i=new ArrayBuffer(e.length),r=new Uint8Array(i);return U(r,function(o,a){r[a]=e.charCodeAt(a)}),i}function lo(s,t){for(var e=[],i=8192,r=new Uint8Array(s);r.length>0;)e.push(zs.apply(null,Is(r.subarray(0,i)))),r=r.subarray(i);return"data:".concat(t,";base64,").concat(btoa(e.join("")))}function ho(s){var t=new DataView(s),e;try{var i,r,o;if(t.getUint8(0)===255&&t.getUint8(1)===216)for(var a=t.byteLength,n=2;n+1<a;){if(t.getUint8(n)===255&&t.getUint8(n+1)===225){r=n;break}n+=1}if(r){var l=r+4,c=r+10;if(oo(t,l,4)==="Exif"){var d=t.getUint16(c);if(i=d===18761,(i||d===19789)&&t.getUint16(c+2,i)===42){var u=t.getUint32(c+4,i);u>=8&&(o=c+u)}}}if(o){var p=t.getUint16(o,i),w,m;for(m=0;m<p;m+=1)if(w=o+m*12+2,t.getUint16(w,i)===274){w+=8,e=t.getUint16(w,i),t.setUint16(w,1,i);break}}}catch{e=1}return e}function co(s){var t=0,e=1,i=1;switch(s){case 2:e=-1;break;case 3:t=-180;break;case 4:i=-1;break;case 5:t=90,i=-1;break;case 6:t=90;break;case 7:t=90,e=-1;break;case 8:t=-90;break}return{rotate:t,scaleX:e,scaleY:i}}var uo={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,r=this.cropper;j(r,K),ht(t,K);var o={width:Math.max(i.offsetWidth,Number(e.minContainerWidth)||200),height:Math.max(i.offsetHeight,Number(e.minContainerHeight)||100)};this.containerData=o,wt(r,{width:o.width,height:o.height}),j(t,K),ht(r,K)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,r=Math.abs(e.rotate)%180===90,o=r?e.naturalHeight:e.naturalWidth,a=r?e.naturalWidth:e.naturalHeight,n=o/a,l=t.width,c=t.height;t.height*n>t.width?i===3?l=t.height*n:c=t.width/n:i===3?c=t.width/n:l=t.height*n;var d={aspectRatio:n,naturalWidth:o,naturalHeight:a,width:l,height:c};d.left=(t.width-l)/2,d.top=(t.height-c)/2,d.oldLeft=d.left,d.oldTop=d.top,this.canvasData=d,this.limited=i===1||i===2,this.limitCanvas(!0,!0),this.initialImageData=T({},e),this.initialCanvasData=T({},d)},limitCanvas:function(t,e){var i=this.options,r=this.containerData,o=this.canvasData,a=this.cropBoxData,n=i.viewMode,l=o.aspectRatio,c=this.cropped&&a;if(t){var d=Number(i.minCanvasWidth)||0,u=Number(i.minCanvasHeight)||0;n>1?(d=Math.max(d,r.width),u=Math.max(u,r.height),n===3&&(u*l>d?d=u*l:u=d/l)):n>0&&(d?d=Math.max(d,c?a.width:0):u?u=Math.max(u,c?a.height:0):c&&(d=a.width,u=a.height,u*l>d?d=u*l:u=d/l));var p=St({aspectRatio:l,width:d,height:u});d=p.width,u=p.height,o.minWidth=d,o.minHeight=u,o.maxWidth=1/0,o.maxHeight=1/0}if(e)if(n>(c?0:1)){var w=r.width-o.width,m=r.height-o.height;o.minLeft=Math.min(0,w),o.minTop=Math.min(0,m),o.maxLeft=Math.max(0,w),o.maxTop=Math.max(0,m),c&&this.limited&&(o.minLeft=Math.min(a.left,a.left+(a.width-o.width)),o.minTop=Math.min(a.top,a.top+(a.height-o.height)),o.maxLeft=a.left,o.maxTop=a.top,n===2&&(o.width>=r.width&&(o.minLeft=Math.min(0,w),o.maxLeft=Math.max(0,w)),o.height>=r.height&&(o.minTop=Math.min(0,m),o.maxTop=Math.max(0,m))))}else o.minLeft=-o.width,o.minTop=-o.height,o.maxLeft=r.width,o.maxTop=r.height},renderCanvas:function(t,e){var i=this.canvasData,r=this.imageData;if(e){var o=so({width:r.naturalWidth*Math.abs(r.scaleX||1),height:r.naturalHeight*Math.abs(r.scaleY||1),degree:r.rotate||0}),a=o.width,n=o.height,l=i.width*(a/i.naturalWidth),c=i.height*(n/i.naturalHeight);i.left-=(l-i.width)/2,i.top-=(c-i.height)/2,i.width=l,i.height=c,i.aspectRatio=a/n,i.naturalWidth=a,i.naturalHeight=n,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,wt(this.canvas,T({width:i.width,height:i.height},he({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,r=i.naturalWidth*(e.width/e.naturalWidth),o=i.naturalHeight*(e.height/e.naturalHeight);T(i,{width:r,height:o,left:(e.width-r)/2,top:(e.height-o)/2}),wt(this.image,T({width:i.width,height:i.height},he(T({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,r=Number(t.autoCropArea)||.8,o={width:e.width,height:e.height};i&&(e.height*i>e.width?o.height=o.width/i:o.width=o.height*i),this.cropBoxData=o,this.limitCropBox(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.width=Math.max(o.minWidth,o.width*r),o.height=Math.max(o.minHeight,o.height*r),o.left=e.left+(e.width-o.width)/2,o.top=e.top+(e.height-o.height)/2,o.oldLeft=o.left,o.oldTop=o.top,this.initialCropBoxData=T({},o)},limitCropBox:function(t,e){var i=this.options,r=this.containerData,o=this.canvasData,a=this.cropBoxData,n=this.limited,l=i.aspectRatio;if(t){var c=Number(i.minCropBoxWidth)||0,d=Number(i.minCropBoxHeight)||0,u=n?Math.min(r.width,o.width,o.width+o.left,r.width-o.left):r.width,p=n?Math.min(r.height,o.height,o.height+o.top,r.height-o.top):r.height;c=Math.min(c,r.width),d=Math.min(d,r.height),l&&(c&&d?d*l>c?d=c/l:c=d*l:c?d=c/l:d&&(c=d*l),p*l>u?p=u/l:u=p*l),a.minWidth=Math.min(c,u),a.minHeight=Math.min(d,p),a.maxWidth=u,a.maxHeight=p}e&&(n?(a.minLeft=Math.max(0,o.left),a.minTop=Math.max(0,o.top),a.maxLeft=Math.min(r.width,o.left+o.width)-a.width,a.maxTop=Math.min(r.height,o.top+o.height)-a.height):(a.minLeft=0,a.minTop=0,a.maxLeft=r.width-a.width,a.maxTop=r.height-a.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&ge(this.face,me,i.width>=e.width&&i.height>=e.height?ks:Ui),wt(this.cropBox,T({width:i.width,height:i.height},he({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),$t(this.element,vi,this.getData())}},po={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,r=e?this.crossOriginUrl:this.url,o=t.alt||"The image to preview",a=document.createElement("img");if(e&&(a.crossOrigin=e),a.src=r,a.alt=o,this.viewBox.appendChild(a),this.viewBoxImage=a,!!i){var n=i;typeof i=="string"?n=t.ownerDocument.querySelectorAll(i):i.querySelector&&(n=[i]),this.previews=n,U(n,function(l){var c=document.createElement("img");ge(l,Ce,{width:l.offsetWidth,height:l.offsetHeight,html:l.innerHTML}),e&&(c.crossOrigin=e),c.src=r,c.alt=o,c.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',l.innerHTML="",l.appendChild(c)})}},resetPreview:function(){U(this.previews,function(t){var e=Ai(t,Ce);wt(t,{width:e.width,height:e.height}),t.innerHTML=e.html,Zr(t,Ce)})},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,r=i.width,o=i.height,a=t.width,n=t.height,l=i.left-e.left-t.left,c=i.top-e.top-t.top;!this.cropped||this.disabled||(wt(this.viewBoxImage,T({width:a,height:n},he(T({translateX:-l,translateY:-c},t)))),U(this.previews,function(d){var u=Ai(d,Ce),p=u.width,w=u.height,m=p,y=w,g=1;r&&(g=p/r,y=o*g),o&&y>w&&(g=w/o,m=r*g,y=w),wt(d,{width:m,height:y}),wt(d.getElementsByTagName("img")[0],T({width:a*g,height:n*g},he(T({translateX:-l*g,translateY:-c*g},t))))}))}},fo={bind:function(){var t=this.element,e=this.options,i=this.cropper;X(e.cropstart)&&tt(t,wi,e.cropstart),X(e.cropmove)&&tt(t,bi,e.cropmove),X(e.cropend)&&tt(t,yi,e.cropend),X(e.crop)&&tt(t,vi,e.crop),X(e.zoom)&&tt(t,Si,e.zoom),tt(i,ts,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&tt(i,os,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&tt(i,Zi,this.onDblclick=this.dblclick.bind(this)),tt(t.ownerDocument,es,this.onCropMove=this.cropMove.bind(this)),tt(t.ownerDocument,is,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&tt(window,rs,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;X(e.cropstart)&&it(t,wi,e.cropstart),X(e.cropmove)&&it(t,bi,e.cropmove),X(e.cropend)&&it(t,yi,e.cropend),X(e.crop)&&it(t,vi,e.crop),X(e.zoom)&&it(t,Si,e.zoom),it(i,ts,this.onCropStart),e.zoomable&&e.zoomOnWheel&&it(i,os,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&it(i,Zi,this.onDblclick),it(t.ownerDocument,es,this.onCropMove),it(t.ownerDocument,is,this.onCropEnd),e.responsive&&it(window,rs,this.onResize)}},mo={resize:function(){if(!this.disabled){var t=this.options,e=this.container,i=this.containerData,r=e.offsetWidth/i.width;if(r!==1||e.offsetHeight!==i.height){var o,a;t.restore&&(o=this.getCanvasData(),a=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(U(o,function(n,l){o[l]=n*r})),this.setCropBoxData(U(a,function(n,l){a[l]=n*r})))}}},dblclick:function(){this.disabled||this.options.dragMode===_s||this.setDragMode(Qr(this.dragBox,mi)?Fs:zi)},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,r=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){e.wheeling=!1},50),t.deltaY?r=t.deltaY>0?1:-1:t.wheelDelta?r=-t.wheelDelta/120:t.detail&&(r=t.detail>0?1:-1),this.zoom(-r*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(R(e)&&e!==1||R(i)&&i!==0||t.ctrlKey))){var r=this.options,o=this.pointers,a;t.changedTouches?U(t.changedTouches,function(n){o[n.identifier]=Oe(n)}):o[t.pointerId||0]=Oe(t),Object.keys(o).length>1&&r.zoomable&&r.zoomOnTouch?a=Ls:a=Ai(t.target,me),jr.test(a)&&$t(this.element,wi,{originalEvent:t,action:a})!==!1&&(t.preventDefault(),this.action=a,this.cropping=!1,a===Ms&&(this.cropping=!0,j(this.dragBox,ke)))}},cropMove:function(t){var e=this.action;if(!(this.disabled||!e)){var i=this.pointers;t.preventDefault(),$t(this.element,bi,{originalEvent:t,action:e})!==!1&&(t.changedTouches?U(t.changedTouches,function(r){T(i[r.identifier]||{},Oe(r,!0))}):T(i[t.pointerId||0]||{},Oe(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?U(t.changedTouches,function(r){delete i[r.identifier]}):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,jt(this.dragBox,ke,this.cropped&&this.options.modal)),$t(this.element,yi,{originalEvent:t,action:e}))}}},go={change:function(t){var e=this.options,i=this.canvasData,r=this.containerData,o=this.cropBoxData,a=this.pointers,n=this.action,l=e.aspectRatio,c=o.left,d=o.top,u=o.width,p=o.height,w=c+u,m=d+p,y=0,g=0,b=r.width,A=r.height,O=!0,N;!l&&t.shiftKey&&(l=u&&p?u/p:1),this.limited&&(y=o.minLeft,g=o.minTop,b=y+Math.min(r.width,i.width,i.left+i.width),A=g+Math.min(r.height,i.height,i.top+i.height));var L=a[Object.keys(a)[0]],f={x:L.endX-L.startX,y:L.endY-L.startY},D=function(Z){switch(Z){case Ct:w+f.x>b&&(f.x=b-w);break;case Ot:c+f.x<y&&(f.x=y-c);break;case bt:d+f.y<g&&(f.y=g-d);break;case Ut:m+f.y>A&&(f.y=A-m);break}};switch(n){case Ui:c+=f.x,d+=f.y;break;case Ct:if(f.x>=0&&(w>=b||l&&(d<=g||m>=A))){O=!1;break}D(Ct),u+=f.x,u<0&&(n=Ot,u=-u,c-=u),l&&(p=u/l,d+=(o.height-p)/2);break;case bt:if(f.y<=0&&(d<=g||l&&(c<=y||w>=b))){O=!1;break}D(bt),p-=f.y,d+=f.y,p<0&&(n=Ut,p=-p,d-=p),l&&(u=p*l,c+=(o.width-u)/2);break;case Ot:if(f.x<=0&&(c<=y||l&&(d<=g||m>=A))){O=!1;break}D(Ot),u-=f.x,c+=f.x,u<0&&(n=Ct,u=-u,c-=u),l&&(p=u/l,d+=(o.height-p)/2);break;case Ut:if(f.y>=0&&(m>=A||l&&(c<=y||w>=b))){O=!1;break}D(Ut),p+=f.y,p<0&&(n=bt,p=-p,d-=p),l&&(u=p*l,c+=(o.width-u)/2);break;case Yt:if(l){if(f.y<=0&&(d<=g||w>=b)){O=!1;break}D(bt),p-=f.y,d+=f.y,u=p*l}else D(bt),D(Ct),f.x>=0?w<b?u+=f.x:f.y<=0&&d<=g&&(O=!1):u+=f.x,f.y<=0?d>g&&(p-=f.y,d+=f.y):(p-=f.y,d+=f.y);u<0&&p<0?(n=Qt,p=-p,u=-u,d-=p,c-=u):u<0?(n=Xt,u=-u,c-=u):p<0&&(n=Kt,p=-p,d-=p);break;case Xt:if(l){if(f.y<=0&&(d<=g||c<=y)){O=!1;break}D(bt),p-=f.y,d+=f.y,u=p*l,c+=o.width-u}else D(bt),D(Ot),f.x<=0?c>y?(u-=f.x,c+=f.x):f.y<=0&&d<=g&&(O=!1):(u-=f.x,c+=f.x),f.y<=0?d>g&&(p-=f.y,d+=f.y):(p-=f.y,d+=f.y);u<0&&p<0?(n=Kt,p=-p,u=-u,d-=p,c-=u):u<0?(n=Yt,u=-u,c-=u):p<0&&(n=Qt,p=-p,d-=p);break;case Qt:if(l){if(f.x<=0&&(c<=y||m>=A)){O=!1;break}D(Ot),u-=f.x,c+=f.x,p=u/l}else D(Ut),D(Ot),f.x<=0?c>y?(u-=f.x,c+=f.x):f.y>=0&&m>=A&&(O=!1):(u-=f.x,c+=f.x),f.y>=0?m<A&&(p+=f.y):p+=f.y;u<0&&p<0?(n=Yt,p=-p,u=-u,d-=p,c-=u):u<0?(n=Kt,u=-u,c-=u):p<0&&(n=Xt,p=-p,d-=p);break;case Kt:if(l){if(f.x>=0&&(w>=b||m>=A)){O=!1;break}D(Ct),u+=f.x,p=u/l}else D(Ut),D(Ct),f.x>=0?w<b?u+=f.x:f.y>=0&&m>=A&&(O=!1):u+=f.x,f.y>=0?m<A&&(p+=f.y):p+=f.y;u<0&&p<0?(n=Xt,p=-p,u=-u,d-=p,c-=u):u<0?(n=Qt,u=-u,c-=u):p<0&&(n=Yt,p=-p,d-=p);break;case ks:this.move(f.x,f.y),O=!1;break;case Ls:this.zoom(eo(a),t),O=!1;break;case Ms:if(!f.x||!f.y){O=!1;break}N=Us(this.cropper),c=L.startX-N.left,d=L.startY-N.top,u=o.minWidth,p=o.minHeight,f.x>0?n=f.y>0?Kt:Yt:f.x<0&&(c-=u,n=f.y>0?Qt:Xt),f.y<0&&(d-=p),this.cropped||(ht(this.cropBox,K),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}O&&(o.width=u,o.height=p,o.left=c,o.top=d,this.action=n,this.renderCropBox()),U(a,function(z){z.startX=z.endX,z.startY=z.endY})}},vo={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&j(this.dragBox,ke),ht(this.cropBox,K),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=T({},this.initialImageData),this.canvasData=T({},this.initialCanvasData),this.cropBoxData=T({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(T(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),ht(this.dragBox,ke),j(this.cropBox,K)),this},replace:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,U(this.previews,function(i){i.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,ht(this.cropper,Qi)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,j(this.cropper,Qi)),this},destroy:function(){var t=this.element;return t[P]?(t[P]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.canvasData,r=i.left,o=i.top;return this.moveTo(Ve(t)?t:r+Number(t),Ve(e)?e:o+Number(e))},moveTo:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.canvasData,r=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(R(t)&&(i.left=t,r=!0),R(e)&&(i.top=e,r=!0),r&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var r=this.options,o=this.canvasData,a=o.width,n=o.height,l=o.naturalWidth,c=o.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&r.zoomable){var d=l*t,u=c*t;if($t(this.element,Si,{ratio:t,oldRatio:a/l,originalEvent:i})===!1)return this;if(i){var p=this.pointers,w=Us(this.cropper),m=p&&Object.keys(p).length?io(p):{pageX:i.pageX,pageY:i.pageY};o.left-=(d-a)*((m.pageX-w.left-o.left)/a),o.top-=(u-n)*((m.pageY-w.top-o.top)/n)}else zt(e)&&R(e.x)&&R(e.y)?(o.left-=(d-a)*((e.x-o.left)/a),o.top-=(u-n)*((e.y-o.top)/n)):(o.left-=(d-a)/2,o.top-=(u-n)/2);o.width=d,o.height=u,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),R(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,R(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(R(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.imageData,r=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(R(t)&&(i.scaleX=t,r=!0),R(e)&&(i.scaleY=e,r=!0),r&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.options,i=this.imageData,r=this.canvasData,o=this.cropBoxData,a;if(this.ready&&this.cropped){a={x:o.left-r.left,y:o.top-r.top,width:o.width,height:o.height};var n=i.width/i.naturalWidth;if(U(a,function(d,u){a[u]=d/n}),t){var l=Math.round(a.y+a.height),c=Math.round(a.x+a.width);a.x=Math.round(a.x),a.y=Math.round(a.y),a.width=c-a.x,a.height=l-a.y}}else a={x:0,y:0,width:0,height:0};return e.rotatable&&(a.rotate=i.rotate||0),e.scalable&&(a.scaleX=i.scaleX||1,a.scaleY=i.scaleY||1),a},setData:function(t){var e=this.options,i=this.imageData,r=this.canvasData,o={};if(this.ready&&!this.disabled&&zt(t)){var a=!1;e.rotatable&&R(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,a=!0),e.scalable&&(R(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,a=!0),R(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,a=!0)),a&&this.renderCanvas(!0,!0);var n=i.width/i.naturalWidth;R(t.x)&&(o.left=t.x*n+r.left),R(t.y)&&(o.top=t.y*n+r.top),R(t.width)&&(o.width=t.width*n),R(t.height)&&(o.height=t.height*n),this.setCropBoxData(o)}return this},getContainerData:function(){return this.ready?T({},this.containerData):{}},getImageData:function(){return this.sized?T({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&U(["left","top","width","height","naturalWidth","naturalHeight"],function(i){e[i]=t[i]}),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&zt(t)&&(R(t.left)&&(e.left=t.left),R(t.top)&&(e.top=t.top),R(t.width)?(e.width=t.width,e.height=t.width/i):R(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,e;return this.ready&&this.cropped&&(e={left:t.left,top:t.top,width:t.width,height:t.height}),e||{}},setCropBoxData:function(t){var e=this.cropBoxData,i=this.options.aspectRatio,r,o;return this.ready&&this.cropped&&!this.disabled&&zt(t)&&(R(t.left)&&(e.left=t.left),R(t.top)&&(e.top=t.top),R(t.width)&&t.width!==e.width&&(r=!0,e.width=t.width),R(t.height)&&t.height!==e.height&&(o=!0,e.height=t.height),i&&(r?e.height=e.width/i:o&&(e.width=e.height*i)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=ro(this.image,this.imageData,e,t);if(!this.cropped)return i;var r=this.getData(),o=r.x,a=r.y,n=r.width,l=r.height,c=i.width/Math.floor(e.naturalWidth);c!==1&&(o*=c,a*=c,n*=c,l*=c);var d=n/l,u=St({aspectRatio:d,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=St({aspectRatio:d,width:t.minWidth||0,height:t.minHeight||0},"cover"),w=St({aspectRatio:d,width:t.width||(c!==1?i.width:n),height:t.height||(c!==1?i.height:l)}),m=w.width,y=w.height;m=Math.min(u.width,Math.max(p.width,m)),y=Math.min(u.height,Math.max(p.height,y));var g=document.createElement("canvas"),b=g.getContext("2d");g.width=Wt(m),g.height=Wt(y),b.fillStyle=t.fillColor||"transparent",b.fillRect(0,0,m,y);var A=t.imageSmoothingEnabled,O=A===void 0?!0:A,N=t.imageSmoothingQuality;b.imageSmoothingEnabled=O,N&&(b.imageSmoothingQuality=N);var L=i.width,f=i.height,D=o,z=a,Z,dt,Rt,Et,yt,nt;D<=-n||D>L?(D=0,Z=0,Rt=0,yt=0):D<=0?(Rt=-D,D=0,Z=Math.min(L,n+D),yt=Z):D<=L&&(Rt=0,Z=Math.min(n,L-D),yt=Z),Z<=0||z<=-l||z>f?(z=0,dt=0,Et=0,nt=0):z<=0?(Et=-z,z=0,dt=Math.min(f,l+z),nt=dt):z<=f&&(Et=0,dt=Math.min(l,f-z),nt=dt);var G=[D,z,Z,dt];if(yt>0&&nt>0){var Dt=m/n;G.push(Rt*Dt,Et*Dt,yt*Dt,nt*Dt)}return b.drawImage.apply(b,[i].concat(Ts(G.map(function(De){return Math.floor(Wt(De))})))),g},setAspectRatio:function(t){var e=this.options;return!this.disabled&&!Ve(t)&&(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,r=this.face;if(this.ready&&!this.disabled){var o=t===zi,a=e.movable&&t===Fs;t=o||a?t:_s,e.dragMode=t,ge(i,me,t),jt(i,mi,o),jt(i,gi,a),e.cropBoxMovable||(ge(r,me,t),jt(r,mi,o),jt(r,gi,a))}return this}},yo=ct.Cropper,js=function(){function s(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(Tr(this,s),!t||!$r.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=T({},ns,zt(e)&&e),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return Mr(s,[{key:"init",value:function(){var e=this.element,i=e.tagName.toLowerCase(),r;if(!e[P]){if(e[P]=this,i==="img"){if(this.isImg=!0,r=e.getAttribute("src")||"",this.originalUrl=r,!r)return;r=e.src}else i==="canvas"&&window.HTMLCanvasElement&&(r=e.toDataURL());this.load(r)}}},{key:"load",value:function(e){var i=this;if(e){this.url=e,this.imageData={};var r=this.element,o=this.options;if(!o.rotatable&&!o.scalable&&(o.checkOrientation=!1),!o.checkOrientation||!window.ArrayBuffer){this.clone();return}if(Vr.test(e)){Wr.test(e)?this.read(no(e)):this.clone();return}var a=new XMLHttpRequest,n=this.clone.bind(this);this.reloading=!0,this.xhr=a,a.onabort=n,a.onerror=n,a.ontimeout=n,a.onprogress=function(){a.getResponseHeader("content-type")!==as&&a.abort()},a.onload=function(){i.read(a.response)},a.onloadend=function(){i.reloading=!1,i.xhr=null},o.checkCrossOrigin&&hs(e)&&r.crossOrigin&&(e=cs(e)),a.open("GET",e),a.responseType="arraybuffer",a.withCredentials=r.crossOrigin==="use-credentials",a.send()}}},{key:"read",value:function(e){var i=this.options,r=this.imageData,o=ho(e),a=0,n=1,l=1;if(o>1){this.url=lo(e,as);var c=co(o);a=c.rotate,n=c.scaleX,l=c.scaleY}i.rotatable&&(r.rotate=a),i.scalable&&(r.scaleX=n,r.scaleY=l),this.clone()}},{key:"clone",value:function(){var e=this.element,i=this.url,r=e.crossOrigin,o=i;this.options.checkCrossOrigin&&hs(i)&&(r||(r="anonymous"),o=cs(i)),this.crossOrigin=r,this.crossOriginUrl=o;var a=document.createElement("img");r&&(a.crossOrigin=r),a.src=o||i,a.alt=e.alt||"The image to crop",this.image=a,a.onload=this.start.bind(this),a.onerror=this.stop.bind(this),j(a,Ji),e.parentNode.insertBefore(a,e.nextSibling)}},{key:"start",value:function(){var e=this,i=this.image;i.onload=null,i.onerror=null,this.sizing=!0;var r=ct.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(ct.navigator.userAgent),o=function(c,d){T(e.imageData,{naturalWidth:c,naturalHeight:d,aspectRatio:c/d}),e.sizing=!1,e.sized=!0,e.build()};if(i.naturalWidth&&!r){o(i.naturalWidth,i.naturalHeight);return}var a=document.createElement("img"),n=document.body||document.documentElement;this.sizingImage=a,a.onload=function(){o(a.width,a.height),r||n.removeChild(a)},a.src=i.src,r||(a.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",n.appendChild(a))}},{key:"stop",value:function(){var e=this.image;e.onload=null,e.onerror=null,e.parentNode.removeChild(e),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var e=this.element,i=this.options,r=this.image,o=e.parentNode,a=document.createElement("div");a.innerHTML=Hr;var n=a.querySelector(".".concat(P,"-container")),l=n.querySelector(".".concat(P,"-canvas")),c=n.querySelector(".".concat(P,"-drag-box")),d=n.querySelector(".".concat(P,"-crop-box")),u=d.querySelector(".".concat(P,"-face"));this.container=o,this.cropper=n,this.canvas=l,this.dragBox=c,this.cropBox=d,this.viewBox=n.querySelector(".".concat(P,"-view-box")),this.face=u,l.appendChild(r),j(e,K),o.insertBefore(n,e.nextSibling),this.isImg||ht(r,Ji),this.initPreview(),this.bind(),i.initialAspectRatio=Math.max(0,i.initialAspectRatio)||NaN,i.aspectRatio=Math.max(0,i.aspectRatio)||NaN,i.viewMode=Math.max(0,Math.min(3,Math.round(i.viewMode)))||0,j(d,K),i.guides||j(d.getElementsByClassName("".concat(P,"-dashed")),K),i.center||j(d.getElementsByClassName("".concat(P,"-center")),K),i.background&&j(n,"".concat(P,"-bg")),i.highlight||j(u,Nr),i.cropBoxMovable&&(j(u,gi),ge(u,me,Ui)),i.cropBoxResizable||(j(d.getElementsByClassName("".concat(P,"-line")),K),j(d.getElementsByClassName("".concat(P,"-point")),K)),this.render(),this.ready=!0,this.setDragMode(i.dragMode),i.autoCrop&&this.crop(),this.setData(i.data),X(i.ready)&&tt(e,ss,i.ready,{once:!0}),$t(e,ss)}}},{key:"unbuild",value:function(){this.ready&&(this.ready=!1,this.unbind(),this.resetPreview(),this.cropper.parentNode.removeChild(this.cropper),ht(this.element,K))}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=yo,s}},{key:"setDefaults",value:function(e){T(ns,zt(e)&&e)}}]),s}();T(js.prototype,uo,po,fo,mo,go,vo);function bo(s,t){const e=s.width/t.width,i=s.height/t.height,r=Math.min(e,i),o=t.width*r,a=t.height*r,n=(s.width-o)/2,l=(s.height-a)/2;return{width:o,height:a,left:n,top:l}}function wo(s){return s*(Math.PI/180)}function So(s,t,e){const i=Math.abs(wo(e));return Math.max((Math.sin(i)*s+Math.cos(i)*t)/t,(Math.sin(i)*t+Math.cos(i)*s)/s)}function Ao(s,t,e){return t.left<s.left?{left:s.left,width:e.width}:t.top<s.top?{top:s.top,height:e.height}:t.left+t.width>s.left+s.width?{left:s.left+s.width-e.width,width:e.width}:t.top+t.height>s.top+s.height?{top:s.top+s.height-e.height,height:e.height}:null}function Ro(s,t,e){return t.left<s.left?{left:s.left,width:e.left+e.width-s.left}:t.top<s.top?{top:s.top,height:e.top+e.height-s.top}:t.left+t.width>s.left+s.width?{left:e.left,width:s.left+s.width-e.left}:t.top+t.height>s.top+s.height?{top:e.top,height:s.top+s.height-e.top}:null}class Eo extends Re{constructor(t){super(t),this.onRotate90Deg=()=>{const{angle90Deg:e}=this.state,i=e-90;this.setState({angle90Deg:i,angleGranular:0}),this.cropper.scale(1),this.cropper.rotateTo(i);const r=this.cropper.getCanvasData(),o=this.cropper.getContainerData(),a=bo(o,r);this.cropper.setCanvasData(a),this.cropper.setCropBoxData(a)},this.onRotateGranular=e=>{const i=Number(e.target.value);this.setState({angleGranular:i});const{angle90Deg:r}=this.state,o=r+i;this.cropper.rotateTo(o);const a=this.cropper.getImageData(),n=So(a.naturalWidth,a.naturalHeight,i),l=this.cropper.getImageData().scaleX<0?-n:n;this.cropper.scale(l,n)},this.state={angle90Deg:0,angleGranular:0,prevCropboxData:null},this.storePrevCropboxData=this.storePrevCropboxData.bind(this),this.limitCropboxMovement=this.limitCropboxMovement.bind(this)}componentDidMount(){const{opts:t,storeCropperInstance:e}=this.props;this.cropper=new js(this.imgElement,t.cropperOptions),this.imgElement.addEventListener("cropstart",this.storePrevCropboxData),this.imgElement.addEventListener("cropend",this.limitCropboxMovement),e(this.cropper)}componentWillUnmount(){this.cropper.destroy(),this.imgElement.removeEventListener("cropstart",this.storePrevCropboxData),this.imgElement.removeEventListener("cropend",this.limitCropboxMovement)}storePrevCropboxData(){this.setState({prevCropboxData:this.cropper.getCropBoxData()})}limitCropboxMovement(t){const e=this.cropper.getCanvasData(),i=this.cropper.getCropBoxData(),{prevCropboxData:r}=this.state;if(t.detail.action==="all"){const o=Ao(e,i,r);o&&this.cropper.setCropBoxData(o)}else{const o=Ro(e,i,r);o&&this.cropper.setCropBoxData(o)}}renderGranularRotate(){const{i18n:t}=this.props,{angleGranular:e}=this.state;return h("label",{role:"tooltip","aria-label":`${e}º`,"data-microtip-position":"top",className:"uppy-ImageCropper-rangeWrapper"},h("input",{className:"uppy-ImageCropper-range uppy-u-reset",type:"range",onInput:this.onRotateGranular,onChange:this.onRotateGranular,value:e,min:"-45",max:"45","aria-label":t("rotate")}))}renderRevert(){const{i18n:t,opts:e}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button",className:"uppy-u-reset uppy-c-btn","aria-label":t("revert"),onClick:()=>{this.cropper.reset(),this.cropper.setAspectRatio(e.cropperOptions.initialAspectRatio),this.setState({angle90Deg:0,angleGranular:0})}},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M0 0h24v24H0z",fill:"none"}),h("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"})))}renderRotate(){const{i18n:t}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button",className:"uppy-u-reset uppy-c-btn","aria-label":t("rotate"),onClick:this.onRotate90Deg},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M0 0h24v24H0V0zm0 0h24v24H0V0z",fill:"none"}),h("path",{d:"M14 10a2 2 0 012 2v7a2 2 0 01-2 2H6a2 2 0 01-2-2v-7a2 2 0 012-2h8zm0 1.75H6a.25.25 0 00-.243.193L5.75 12v7a.25.25 0 00.193.243L6 19.25h8a.25.25 0 00.243-.193L14.25 19v-7a.25.25 0 00-.193-.243L14 11.75zM12 .76V4c2.3 0 4.61.88 6.36 2.64a8.95 8.95 0 012.634 6.025L21 13a1 1 0 01-1.993.117L19 13h-.003a6.979 6.979 0 00-2.047-4.95 6.97 6.97 0 00-4.652-2.044L12 6v3.24L7.76 5 12 .76z"})))}renderFlip(){const{i18n:t}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button",className:"uppy-u-reset uppy-c-btn","aria-label":t("flipHorizontal"),onClick:()=>this.cropper.scaleX(-this.cropper.getData().scaleX||-1)},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M0 0h24v24H0z",fill:"none"}),h("path",{d:"M15 21h2v-2h-2v2zm4-12h2V7h-2v2zM3 5v14c0 1.1.9 2 2 2h4v-2H5V5h4V3H5c-1.1 0-2 .9-2 2zm16-2v2h2c0-1.1-.9-2-2-2zm-8 20h2V1h-2v22zm8-6h2v-2h-2v2zM15 5h2V3h-2v2zm4 8h2v-2h-2v2zm0 8c1.1 0 2-.9 2-2h-2v2z"})))}renderZoomIn(){const{i18n:t}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button",className:"uppy-u-reset uppy-c-btn","aria-label":t("zoomIn"),onClick:()=>this.cropper.zoom(.1)},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",height:"24",viewBox:"0 0 24 24",width:"24"},h("path",{d:"M0 0h24v24H0V0z",fill:"none"}),h("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}),h("path",{d:"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"})))}renderZoomOut(){const{i18n:t}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button",className:"uppy-u-reset uppy-c-btn","aria-label":t("zoomOut"),onClick:()=>this.cropper.zoom(-.1)},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M0 0h24v24H0V0z",fill:"none"}),h("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"})))}renderCropSquare(){const{i18n:t}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button",className:"uppy-u-reset uppy-c-btn","aria-label":t("aspectRatioSquare"),onClick:()=>this.cropper.setAspectRatio(1)},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M0 0h24v24H0z",fill:"none"}),h("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"})))}renderCropWidescreen(){const{i18n:t}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button",className:"uppy-u-reset uppy-c-btn","aria-label":t("aspectRatioLandscape"),onClick:()=>this.cropper.setAspectRatio(16/9)},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M 19,4.9999992 V 17.000001 H 4.9999998 V 6.9999992 H 19 m 0,-2 H 4.9999998 c -1.0999999,0 -1.9999999,0.9000001 -1.9999999,2 V 17.000001 c 0,1.1 0.9,2 1.9999999,2 H 19 c 1.1,0 2,-0.9 2,-2 V 6.9999992 c 0,-1.0999999 -0.9,-2 -2,-2 z"}),h("path",{fill:"none",d:"M0 0h24v24H0z"})))}renderCropWidescreenVertical(){const{i18n:t}=this.props;return h("button",{role:"button tooltip","data-microtip-position":"top",type:"button","aria-label":t("aspectRatioPortrait"),className:"uppy-u-reset uppy-c-btn",onClick:()=>this.cropper.setAspectRatio(9/16)},h("svg",{"aria-hidden":"true",className:"uppy-c-icon",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M 19.000001,19 H 6.999999 V 5 h 10.000002 v 14 m 2,0 V 5 c 0,-1.0999999 -0.9,-1.9999999 -2,-1.9999999 H 6.999999 c -1.1,0 -2,0.9 -2,1.9999999 v 14 c 0,1.1 0.9,2 2,2 h 10.000002 c 1.1,0 2,-0.9 2,-2 z"}),h("path",{d:"M0 0h24v24H0z",fill:"none"})))}render(){const{currentImage:t,opts:e}=this.props,{actions:i}=e,r=URL.createObjectURL(t.data);return h("div",{className:"uppy-ImageCropper"},h("div",{className:"uppy-ImageCropper-container"},h("img",{className:"uppy-ImageCropper-image",alt:t.name,src:r,ref:o=>{this.imgElement=o}})),h("div",{className:"uppy-ImageCropper-controls"},i.revert&&this.renderRevert(),i.rotate&&this.renderRotate(),i.granularRotate&&this.renderGranularRotate(),i.flip&&this.renderFlip(),i.zoomIn&&this.renderZoomIn(),i.zoomOut&&this.renderZoomOut(),i.cropSquare&&this.renderCropSquare(),i.cropWidescreen&&this.renderCropWidescreen(),i.cropWidescreenVertical&&this.renderCropWidescreenVertical()))}}const Do={strings:{revert:"Reset",rotate:"Rotate 90°",zoomIn:"Zoom in",zoomOut:"Zoom out",flipHorizontal:"Flip horizontally",aspectRatioSquare:"Crop square",aspectRatioLandscape:"Crop landscape (16:9)",aspectRatioPortrait:"Crop portrait (9:16)"}},Co={version:"2.4.6"},Vs={viewMode:0,background:!1,autoCropArea:1,responsive:!0,minCropBoxWidth:70,minCropBoxHeight:70,croppedCanvasOptions:{},initialAspectRatio:0},Ws={revert:!0,rotate:!0,granularRotate:!0,flip:!0,zoomIn:!0,zoomOut:!0,cropSquare:!0,cropWidescreen:!0,cropWidescreenVertical:!0},Oo={quality:.8,actions:Ws,cropperOptions:Vs};class xo extends et{constructor(t,e){super(t,{...Oo,...e,actions:{...Ws,...e==null?void 0:e.actions},cropperOptions:{...Vs,...e==null?void 0:e.cropperOptions}}),this.save=()=>{const i=a=>{const{currentImage:n}=this.getPluginState();this.uppy.setFileState(n.id,{data:new File([a],n.name,{type:a.type}),size:a.size,preview:void 0});const l=this.uppy.getFile(n.id);this.uppy.emit("thumbnail:request",l),this.setPluginState({currentImage:l}),this.uppy.emit("file-editor:complete",l)},{currentImage:r}=this.getPluginState(),o=this.cropper.getCroppedCanvas({});o.width%2!==0&&this.cropper.setData({width:o.width-1}),o.height%2!==0&&this.cropper.setData({height:o.height-1}),this.cropper.getCroppedCanvas(this.opts.cropperOptions.croppedCanvasOptions).toBlob(i,r.type,this.opts.quality)},this.storeCropperInstance=i=>{this.cropper=i},this.selectFile=i=>{this.uppy.emit("file-editor:start",i),this.setPluginState({currentImage:i})},this.id=this.opts.id||"ImageEditor",this.title="Image Editor",this.type="editor",this.defaultLocale=Do,this.i18nInit()}canEditFile(t){if(!t.type||t.isRemote)return!1;const e=t.type.split("/")[1];return!!/^(jpe?g|gif|png|bmp|webp)$/.test(e)}install(){this.setPluginState({currentImage:null});const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){const{currentImage:t}=this.getPluginState();if(t){const e=this.uppy.getFile(t.id);this.uppy.emit("file-editor:cancel",e)}this.unmount()}render(){const{currentImage:t}=this.getPluginState();return t===null||t.isRemote?null:h(Eo,{currentImage:t,storeCropperInstance:this.storeCropperInstance,save:this.save,opts:this.opts,i18n:this.i18n})}}xo.VERSION=Co.version;const Po={__proto__:null,"audio/mp3":"mp3","audio/mp4":"mp4","audio/ogg":"ogg","audio/webm":"webm","image/gif":"gif","image/heic":"heic","image/heif":"heif","image/jpeg":"jpg","image/png":"png","image/svg+xml":"svg","video/mp4":"mp4","video/ogg":"ogv","video/quicktime":"mov","video/webm":"webm","video/x-matroska":"mkv","video/x-msvideo":"avi"};function Ht(s){return[s]=s.split(";",1),Po[s]||null}function To(){var s;return typeof MediaRecorder=="function"&&typeof((s=MediaRecorder.prototype)==null?void 0:s.start)=="function"}function Mo(s){let{recording:t,onStartRecording:e,onStopRecording:i,i18n:r}=s;return t?h("button",{className:"uppy-u-reset uppy-c-btn uppy-Audio-button",type:"button",title:r("stopAudioRecording"),"aria-label":r("stopAudioRecording"),onClick:i,"data-uppy-super-focusable":!0},h("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"100",height:"100",viewBox:"0 0 100 100"},h("rect",{x:"15",y:"15",width:"70",height:"70"}))):h("button",{className:"uppy-u-reset uppy-c-btn uppy-Audio-button",type:"button",title:r("startAudioRecording"),"aria-label":r("startAudioRecording"),onClick:e,"data-uppy-super-focusable":!0},h("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14px",height:"20px",viewBox:"0 0 14 20"},h("path",{d:"M7 14c2.21 0 4-1.71 4-3.818V3.818C11 1.71 9.21 0 7 0S3 1.71 3 3.818v6.364C3 12.29 4.79 14 7 14zm6.364-7h-.637a.643.643 0 0 0-.636.65V9.6c0 3.039-2.565 5.477-5.6 5.175-2.645-.264-4.582-2.692-4.582-5.407V7.65c0-.36-.285-.65-.636-.65H.636A.643.643 0 0 0 0 7.65v1.631c0 3.642 2.544 6.888 6.045 7.382v1.387H3.818a.643.643 0 0 0-.636.65v.65c0 .36.285.65.636.65h6.364c.351 0 .636-.29.636-.65v-.65c0-.36-.285-.65-.636-.65H7.955v-1.372C11.363 16.2 14 13.212 14 9.6V7.65c0-.36-.285-.65-.636-.65z",fill:"#FFF","fill-rule":"nonzero"})))}function ko(s){return`${Math.floor(s/60)}:${String(s%60).padStart(2,"0")}`}function Lo(s){let{recordingLengthSeconds:t,i18n:e}=s;const i=ko(t);return h("span",{"aria-label":e("recordingLength",{recording_length:i})},i)}const Fo=s=>{let{currentDeviceId:t,audioSources:e,onChangeSource:i}=s;return h("div",{className:"uppy-Audio-videoSource"},h("select",{className:"uppy-u-reset uppy-Audio-audioSource-select",onChange:r=>{i(r.target.value)}},e.map(r=>h("option",{key:r.deviceId,value:r.deviceId,selected:r.deviceId===t},r.label))))};function _o(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var Io=0;function No(s){return"__private_"+Io+++"_"+s}function $s(s){return typeof s=="function"}function Jt(s){return $s(s)?s():s}var $e=No("draw");class Bo{constructor(t,e){e===void 0&&(e={}),Object.defineProperty(this,$e,{writable:!0,value:()=>this.draw()});const i=e.canvas||{},r=e.canvasContext||{};this.analyser=null,this.bufferLength=0,this.canvas=t,this.width=Jt(i.width)||this.canvas.width,this.height=Jt(i.height)||this.canvas.height,this.canvas.width=this.width,this.canvas.height=this.height,this.canvasContext=this.canvas.getContext("2d"),this.canvasContext.fillStyle=Jt(r.fillStyle)||"rgb(255, 255, 255)",this.canvasContext.strokeStyle=Jt(r.strokeStyle)||"rgb(0, 0, 0)",this.canvasContext.lineWidth=Jt(r.lineWidth)||1,this.onDrawFrame=$s(e.onDrawFrame)?e.onDrawFrame:()=>{}}addSource(t){this.streamSource=t,this.audioContext=this.streamSource.context,this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=2048,this.bufferLength=this.analyser.frequencyBinCount,this.source=this.audioContext.createBufferSource(),this.dataArray=new Uint8Array(this.bufferLength),this.analyser.getByteTimeDomainData(this.dataArray),this.streamSource.connect(this.analyser)}draw(){const{analyser:t,dataArray:e,bufferLength:i}=this,r=this.canvasContext,o=this.width,a=this.height;t&&t.getByteTimeDomainData(e),r.fillRect(0,0,o,a),r.beginPath();const n=o*1/i;let l=0;i||r.moveTo(0,this.height/2);for(let c=0;c<i;c++){const u=e[c]/128*(a/2);c===0?r.moveTo(l,u):r.lineTo(l,u),l+=n}r.lineTo(o,a/2),r.stroke(),this.onDrawFrame(this),requestAnimationFrame(_o(this,$e)[$e])}}function Uo(s){let{onSubmit:t,i18n:e}=s;return h("button",{className:"uppy-u-reset uppy-c-btn uppy-Audio-button uppy-Audio-button--submit",type:"button",title:e("submitRecordedFile"),"aria-label":e("submitRecordedFile"),onClick:t,"data-uppy-super-focusable":!0},h("svg",{width:"12",height:"9",viewBox:"0 0 12 9",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",className:"uppy-c-icon"},h("path",{fill:"#fff",fillRule:"nonzero",d:"M10.66 0L12 1.31 4.136 9 0 4.956l1.34-1.31L4.136 6.38z"})))}function zo(s){let{onDiscard:t,i18n:e}=s;return h("button",{className:"uppy-u-reset uppy-c-btn uppy-Audio-button",type:"button",title:e("discardRecordedFile"),"aria-label":e("discardRecordedFile"),onClick:t,"data-uppy-super-focusable":!0},h("svg",{width:"13",height:"13",viewBox:"0 0 13 13",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",className:"uppy-c-icon"},h("g",{fill:"#FFF",fillRule:"evenodd"},h("path",{d:"M.496 11.367L11.103.76l1.414 1.414L1.911 12.781z"}),h("path",{d:"M11.104 12.782L.497 2.175 1.911.76l10.607 10.606z"}))))}function jo(s){const{stream:t,recordedAudio:e,onStop:i,recording:r,supportsRecording:o,audioSources:a,showAudioSourceDropdown:n,onSubmit:l,i18n:c,onStartRecording:d,onStopRecording:u,onDiscardRecordedAudio:p,recordingLengthSeconds:w}=s,m=Gi(null),y=Gi();Yi(()=>()=>{y.current=null,i()},[i]),Yi(()=>{if(!e&&(y.current=new Bo(m.current,{canvas:{width:600,height:600},canvasContext:{lineWidth:2,fillStyle:"rgb(0,0,0)",strokeStyle:"green"}}),y.current.draw(),t)){const N=new AudioContext().createMediaStreamSource(t);y.current.addSource(N)}},[e,t]);const g=e!=null,b=!g&&o,A=n&&!g&&a&&a.length>1;return h("div",{className:"uppy-Audio-container"},h("div",{className:"uppy-Audio-audioContainer"},g?h("audio",{className:"uppy-Audio-player",controls:!0,src:e}):h("canvas",{ref:m,className:"uppy-Audio-canvas"})),h("div",{className:"uppy-Audio-footer"},h("div",{className:"uppy-Audio-audioSourceContainer"},A?Fo(s):null),h("div",{className:"uppy-Audio-buttonContainer"},b&&h(Mo,{recording:r,onStartRecording:d,onStopRecording:u,i18n:c}),g&&h(Uo,{onSubmit:l,i18n:c}),g&&h(zo,{onDiscard:p,i18n:c})),h("div",{className:"uppy-Audio-recordingLength"},!g&&h(Lo,{recordingLengthSeconds:w,i18n:c}))))}const Vo=s=>{const{icon:t,hasAudio:e,i18n:i}=s;return h("div",{className:"uppy-Audio-permissons"},h("div",{className:"uppy-Audio-permissonsIcon"},t()),h("h1",{className:"uppy-Audio-title"},i(e?"allowAudioAccessTitle":"noAudioTitle")),h("p",null,i(e?"allowAudioAccessDescription":"noAudioDescription")))},Wo={strings:{pluginNameAudio:"Audio",startAudioRecording:"Begin audio recording",stopAudioRecording:"Stop audio recording",allowAudioAccessTitle:"Please allow access to your microphone",allowAudioAccessDescription:"In order to record audio, please allow microphone access for this site.",noAudioTitle:"Microphone Not Available",noAudioDescription:"In order to record audio, please connect a microphone or another audio input device",recordingStoppedMaxSize:"Recording stopped because the file size is about to exceed the limit",recordingLength:"Recording length %{recording_length}",submitRecordedFile:"Submit recorded file",discardRecordedFile:"Discard recorded file"}};function Ri(){return Ri=Object.assign?Object.assign.bind():function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(s[i]=e[i])}return s},Ri.apply(this,arguments)}function v(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var $o=0;function $(s){return"__private_"+$o+++"_"+s}const Ho={version:"1.1.9"};var V=$("stream"),pt=$("audioActive"),k=$("recordingChunks"),M=$("recorder"),ft=$("capturedMediaFile"),W=$("mediaDevices"),Zt=$("supportsUserMedia"),He=$("hasAudioCheck"),xt=$("start"),qe=$("startRecording"),te=$("stopRecording"),Ge=$("discardRecordedAudio"),Ye=$("submit"),mt=$("stop"),Xe=$("getAudio"),Ke=$("changeSource"),Pt=$("updateSources");class qo extends et{constructor(t,e){super(t,e),Object.defineProperty(this,Xe,{value:Yo}),Object.defineProperty(this,He,{value:Go}),Object.defineProperty(this,V,{writable:!0,value:null}),Object.defineProperty(this,pt,{writable:!0,value:!1}),Object.defineProperty(this,k,{writable:!0,value:null}),Object.defineProperty(this,M,{writable:!0,value:null}),Object.defineProperty(this,ft,{writable:!0,value:null}),Object.defineProperty(this,W,{writable:!0,value:void 0}),Object.defineProperty(this,Zt,{writable:!0,value:void 0}),Object.defineProperty(this,xt,{writable:!0,value:i=>{if(!v(this,Zt)[Zt])return Promise.reject(new Error("Microphone access not supported"));v(this,pt)[pt]=!0,v(this,He)[He]().then(r=>(this.setPluginState({hasAudio:r}),v(this,W)[W].getUserMedia({audio:!0}).then(o=>{v(this,V)[V]=o;let a=null;const n=o.getAudioTracks();i!=null&&i.deviceId?a=n.findLast(l=>l.getSettings().deviceId===i.deviceId):a=n[0].getSettings().deviceId,v(this,Pt)[Pt](),this.setPluginState({currentDeviceId:a,audioReady:!0})}).catch(o=>{this.setPluginState({audioReady:!1,cameraError:o}),this.uppy.info(o.message,"error")})))}}),Object.defineProperty(this,qe,{writable:!0,value:()=>{v(this,M)[M]=new MediaRecorder(v(this,V)[V]),v(this,k)[k]=[];let i=!1;v(this,M)[M].addEventListener("dataavailable",r=>{v(this,k)[k].push(r.data);const{restrictions:o}=this.uppy.opts;if(v(this,k)[k].length>1&&o.maxFileSize!=null&&!i){const a=v(this,k)[k].reduce((d,u)=>d+u.size,0),l=(a-v(this,k)[k][0].size)/(v(this,k)[k].length-1)*3,c=Math.max(0,o.maxFileSize-l);a>c&&(i=!0,this.uppy.info(this.i18n("recordingStoppedMaxSize"),"warning",4e3),v(this,te)[te]())}}),v(this,M)[M].start(500),this.recordingLengthTimer=setInterval(()=>{const r=this.getPluginState().recordingLengthSeconds;this.setPluginState({recordingLengthSeconds:r+1})},1e3),this.setPluginState({isRecording:!0})}}),Object.defineProperty(this,te,{writable:!0,value:()=>new Promise(r=>{v(this,M)[M].addEventListener("stop",()=>{r()}),v(this,M)[M].stop(),clearInterval(this.recordingLengthTimer),this.setPluginState({recordingLengthSeconds:0})}).then(()=>(this.setPluginState({isRecording:!1}),v(this,Xe)[Xe]())).then(r=>{try{v(this,ft)[ft]=r,this.setPluginState({recordedAudio:URL.createObjectURL(r.data)})}catch(o){o.isRestriction||this.uppy.log(o)}}).then(()=>{v(this,k)[k]=null,v(this,M)[M]=null},r=>{throw v(this,k)[k]=null,v(this,M)[M]=null,r})}),Object.defineProperty(this,Ge,{writable:!0,value:()=>{this.setPluginState({recordedAudio:null}),v(this,ft)[ft]=null}}),Object.defineProperty(this,Ye,{writable:!0,value:()=>{try{v(this,ft)[ft]&&this.uppy.addFile(v(this,ft)[ft])}catch(i){i.isRestriction||this.uppy.log(i,"warning")}}}),Object.defineProperty(this,mt,{writable:!0,value:async()=>{v(this,V)[V]&&v(this,V)[V].getAudioTracks().forEach(r=>r.stop()),v(this,M)[M]&&await new Promise(i=>{v(this,M)[M].addEventListener("stop",i,{once:!0}),v(this,M)[M].stop(),clearInterval(this.recordingLengthTimer)}),v(this,k)[k]=null,v(this,M)[M]=null,v(this,pt)[pt]=!1,v(this,V)[V]=null,this.setPluginState({recordedAudio:null,isRecording:!1,recordingLengthSeconds:0})}}),Object.defineProperty(this,Ke,{writable:!0,value:i=>{v(this,mt)[mt](),v(this,xt)[xt]({deviceId:i})}}),Object.defineProperty(this,Pt,{writable:!0,value:()=>{v(this,W)[W].enumerateDevices().then(i=>{this.setPluginState({audioSources:i.filter(r=>r.kind==="audioinput")})})}}),v(this,W)[W]=navigator.mediaDevices,v(this,Zt)[Zt]=v(this,W)[W]!=null,this.id=this.opts.id||"Audio",this.type="acquirer",this.icon=()=>h("svg",{className:"uppy-DashboardTab-iconAudio","aria-hidden":"true",focusable:"false",width:"32px",height:"32px",viewBox:"0 0 32 32"},h("path",{d:"M21.143 12.297c.473 0 .857.383.857.857v2.572c0 3.016-2.24 5.513-5.143 5.931v2.64h2.572a.857.857 0 110 1.714H12.57a.857.857 0 110-1.714h2.572v-2.64C12.24 21.24 10 18.742 10 15.726v-2.572a.857.857 0 111.714 0v2.572A4.29 4.29 0 0016 20.01a4.29 4.29 0 004.286-4.285v-2.572c0-.474.384-.857.857-.857zM16 6.5a3 3 0 013 3v6a3 3 0 01-6 0v-6a3 3 0 013-3z",fill:"currentcolor","fill-rule":"nonzero"})),this.defaultLocale=Wo,this.opts={...e},this.i18nInit(),this.title=this.i18n("pluginNameAudio"),this.setPluginState({hasAudio:!1,audioReady:!1,cameraError:null,recordingLengthSeconds:0,audioSources:[],currentDeviceId:null})}render(){v(this,pt)[pt]||v(this,xt)[xt]();const t=this.getPluginState();return!t.audioReady||!t.hasAudio?h(Vo,{icon:this.icon,i18n:this.i18n,hasAudio:t.hasAudio}):h(jo,Ri({},t,{audioActive:v(this,pt)[pt],onChangeSource:v(this,Ke)[Ke],onStartRecording:v(this,qe)[qe],onStopRecording:v(this,te)[te],onDiscardRecordedAudio:v(this,Ge)[Ge],onSubmit:v(this,Ye)[Ye],onStop:v(this,mt)[mt],i18n:this.i18n,showAudioSourceDropdown:this.opts.showAudioSourceDropdown,supportsRecording:To(),recording:t.isRecording,stream:v(this,V)[V]}))}install(){this.setPluginState({audioReady:!1,recordingLengthSeconds:0});const{target:t}=this.opts;t&&this.mount(t,this),v(this,W)[W]&&(v(this,Pt)[Pt](),v(this,W)[W].ondevicechange=()=>{if(v(this,Pt)[Pt](),v(this,V)[V]){let e=!0;const{audioSources:i,currentDeviceId:r}=this.getPluginState();i.forEach(o=>{r===o.deviceId&&(e=!1)}),e&&(v(this,mt)[mt](),v(this,xt)[xt]())}})}uninstall(){v(this,V)[V]&&v(this,mt)[mt](),this.unmount()}}function Go(){return v(this,W)[W]?v(this,W)[W].enumerateDevices().then(s=>s.some(t=>t.kind==="audioinput")):Promise.resolve(!1)}function Yo(){const s=v(this,k)[k].find(o=>{var a;return((a=o.type)==null?void 0:a.length)>0}).type,t=Ht(s);if(!t)return Promise.reject(new Error(`Could not retrieve recording: Unsupported media type "${s}"`));const e=`audio-${Date.now()}.${t}`,i=new Blob(v(this,k)[k],{type:s}),r={source:this.id,name:e,data:new Blob([i],{type:s}),type:s};return Promise.resolve(r)}qo.VERSION=Ho.version;const Xo={strings:{pluginNameBox:"Box"}},Ko={version:"2.4.0"};class Hs extends et{constructor(t,e){super(t,e),this.id=this.opts.id||"Box",this.type="acquirer",this.storage=this.opts.storage||qt,this.files=[],this.icon=()=>h("svg",{className:"uppy-DashboardTab-iconBox","aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},h("g",{fill:"currentcolor",fillRule:"nonzero"},h("path",{d:"m16.4 13.5c-1.6 0-3 0.9-3.7 2.2-0.7-1.3-2.1-2.2-3.7-2.2-1 0-1.8 0.3-2.5 0.8v-3.6c-0.1-0.3-0.5-0.7-1-0.7s-0.8 0.4-0.8 0.8v7c0 2.3 1.9 4.2 4.2 4.2 1.6 0 3-0.9 3.7-2.2 0.7 1.3 2.1 2.2 3.7 2.2 2.3 0 4.2-1.9 4.2-4.2 0.1-2.4-1.8-4.3-4.1-4.3m-7.5 6.8c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5m7.5 0c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5"}),h("path",{d:"m27.2 20.6l-2.3-2.8 2.3-2.8c0.3-0.4 0.2-0.9-0.2-1.2s-1-0.2-1.3 0.2l-2 2.4-2-2.4c-0.3-0.4-0.9-0.4-1.3-0.2-0.4 0.3-0.5 0.8-0.2 1.2l2.3 2.8-2.3 2.8c-0.3 0.4-0.2 0.9 0.2 1.2s1 0.2 1.3-0.2l2-2.4 2 2.4c0.3 0.4 0.9 0.4 1.3 0.2 0.4-0.3 0.4-0.8 0.2-1.2"}))),this.opts.companionAllowedHosts=Gt(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new Se(t,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionKeysParams:this.opts.companionKeysParams,companionCookiesRule:this.opts.companionCookiesRule,provider:"box",pluginId:this.id,supportsRefreshToken:!1}),this.defaultLocale=Xo,this.i18nInit(),this.title=this.i18n("pluginNameBox"),this.onFirstRender=this.onFirstRender.bind(this),this.render=this.render.bind(this)}install(){this.view=new Ae(this,{provider:this.provider,loadAllFiles:!0,virtualList:!0});const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.view.tearDown(),this.unmount()}async onFirstRender(){await Promise.all([this.provider.fetchPreAuthToken(),this.view.getFolder()])}render(t){return this.view.render(t)}}Hs.VERSION=Ko.version;const Qo={strings:{pluginNameFacebook:"Facebook"}},Jo={version:"3.3.1"};class qs extends et{constructor(t,e){super(t,e),this.id=this.opts.id||"Facebook",this.type="acquirer",this.storage=this.opts.storage||qt,this.files=[],this.icon=()=>h("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},h("g",{fill:"none",fillRule:"evenodd"},h("path",{d:"M27 16c0-6.075-4.925-11-11-11S5 9.925 5 16c0 5.49 4.023 10.041 9.281 10.866V19.18h-2.793V16h2.793v-2.423c0-2.757 1.642-4.28 4.155-4.28 1.204 0 2.462.215 2.462.215v2.707h-1.387c-1.366 0-1.792.848-1.792 1.718V16h3.05l-.487 3.18h-2.563v7.686C22.977 26.041 27 21.49 27 16",fill:"#1777F2"}),h("path",{d:"M20.282 19.18L20.77 16h-3.051v-2.063c0-.87.426-1.718 1.792-1.718h1.387V9.512s-1.258-.215-2.462-.215c-2.513 0-4.155 1.523-4.155 4.28V16h-2.793v3.18h2.793v7.686a11.082 11.082 0 003.438 0V19.18h2.563",fill:"#FFFFFE"}))),this.opts.companionAllowedHosts=Gt(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new Se(t,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionKeysParams:this.opts.companionKeysParams,companionCookiesRule:this.opts.companionCookiesRule,provider:"facebook",pluginId:this.id,supportsRefreshToken:!1}),this.defaultLocale=Qo,this.i18nInit(),this.title=this.i18n("pluginNameFacebook"),this.onFirstRender=this.onFirstRender.bind(this),this.render=this.render.bind(this)}install(){this.view=new Ae(this,{provider:this.provider});const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.view.tearDown(),this.unmount()}async onFirstRender(){await Promise.all([this.provider.fetchPreAuthToken(),this.view.getFolder()])}render(t){const e={};return this.getPluginState().files.length&&!this.getPluginState().folders.length&&(e.viewType="grid",e.showFilter=!1,e.showTitles=!1),this.view.render(t,e)}}qs.VERSION=Jo.version;const Zo={strings:{pluginNameGooglePhotos:"Google Photos"}},ta={version:"0.1.0"};class Gs extends et{constructor(t,e){super(t,e),this.type="acquirer",this.storage=this.opts.storage||qt,this.files=[],this.id=this.opts.id||"GooglePhotos",this.icon=()=>h("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"-7 -7 73 73"},h("g",{fill:"none","fill-rule":"evenodd"},h("path",{d:"M-3-3h64v64H-3z"}),h("g",{"fill-rule":"nonzero"},h("path",{fill:"#FBBC04",d:"M14.8 13.4c8.1 0 14.7 6.6 14.7 14.8v1.3H1.3c-.7 0-1.3-.6-1.3-1.3C0 20 6.6 13.4 14.8 13.4z"}),h("path",{fill:"#EA4335",d:"M45.6 14.8c0 8.1-6.6 14.7-14.8 14.7h-1.3V1.3c0-.7.6-1.3 1.3-1.3C39 0 45.6 6.6 45.6 14.8z"}),h("path",{fill:"#4285F4",d:"M44.3 45.6c-8.2 0-14.8-6.6-14.8-14.8v-1.3h28.2c.7 0 1.3.6 1.3 1.3 0 8.2-6.6 14.8-14.8 14.8z"}),h("path",{fill:"#34A853",d:"M13.4 44.3c0-8.2 6.6-14.8 14.8-14.8h1.3v28.2c0 .7-.6 1.3-1.3 1.3-8.2 0-14.8-6.6-14.8-14.8z"})))),this.opts.companionAllowedHosts=Gt(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new Se(t,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionKeysParams:this.opts.companionKeysParams,companionCookiesRule:this.opts.companionCookiesRule,provider:"googlephotos",pluginId:this.id,supportsRefreshToken:!0}),this.defaultLocale=Zo,this.i18nInit(),this.title=this.i18n("pluginNameGooglePhotos"),this.onFirstRender=this.onFirstRender.bind(this),this.render=this.render.bind(this)}install(){this.view=new Ae(this,{provider:this.provider,loadAllFiles:!0});const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.view.tearDown(),this.unmount()}async onFirstRender(){await Promise.all([this.provider.fetchPreAuthToken(),this.view.getFolder()])}render(t){return this.getPluginState().files.length&&!this.getPluginState().folders.length?this.view.render(t,{viewType:"grid",showFilter:!1,showTitles:!1}):this.view.render(t)}}Gs.VERSION=ta.version;const ea={strings:{pluginNameInstagram:"Instagram"}},ia={version:"3.3.1"};class Ys extends et{constructor(t,e){super(t,e),this.type="acquirer",this.files=[],this.storage=this.opts.storage||qt,this.id=this.opts.id||"Instagram",this.icon=()=>h("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},h("defs",null,h("path",{d:"M16.825 5l.483-.001.799.002c1.168.005 1.598.021 2.407.057 1.17.05 1.97.235 2.67.506.725.28 1.34.655 1.951 1.265.613.61.99 1.223 1.273 1.946.273.7.46 1.498.516 2.67l.025.552.008.205c.029.748.037 1.51.042 3.777l.001.846v.703l-.001.398a50.82 50.82 0 01-.058 2.588c-.05 1.17-.235 1.97-.506 2.67a5.394 5.394 0 01-1.265 1.951c-.61.613-1.222.99-1.946 1.273-.699.273-1.498.46-2.668.516-.243.012-.451.022-.656.03l-.204.007c-.719.026-1.512.034-3.676.038l-.847.001h-1.1a50.279 50.279 0 01-2.587-.059c-1.171-.05-1.971-.235-2.671-.506a5.394 5.394 0 01-1.951-1.265 5.385 5.385 0 01-1.272-1.946c-.274-.699-.46-1.498-.517-2.668a88.15 88.15 0 01-.03-.656l-.007-.205c-.026-.718-.034-1.512-.038-3.674v-2.129c.006-1.168.022-1.597.058-2.406.051-1.171.235-1.971.506-2.672a5.39 5.39 0 011.265-1.95 5.381 5.381 0 011.946-1.272c.699-.274 1.498-.462 2.669-.517l.656-.03.204-.007c.718-.026 1.511-.034 3.674-.038zm.678 1.981h-1.226l-.295.001c-2.307.005-3.016.013-3.777.043l-.21.009-.457.02c-1.072.052-1.654.232-2.042.383-.513.2-.879.44-1.263.825a3.413 3.413 0 00-.82 1.267c-.15.388-.33.97-.375 2.043a48.89 48.89 0 00-.056 2.482v.398 1.565c.006 2.937.018 3.285.073 4.444.05 1.073.231 1.654.382 2.043.2.512.44.878.825 1.263.386.383.753.621 1.267.82.388.15.97.328 2.043.374.207.01.388.017.563.024l.208.007a63.28 63.28 0 002.109.026h1.564c2.938-.006 3.286-.019 4.446-.073 1.071-.051 1.654-.232 2.04-.383.514-.2.88-.44 1.264-.825.384-.386.622-.753.82-1.266.15-.389.328-.971.375-2.044.039-.88.054-1.292.057-2.723v-1.15-.572c-.006-2.936-.019-3.284-.074-4.445-.05-1.071-.23-1.654-.382-2.04-.2-.515-.44-.88-.825-1.264a3.405 3.405 0 00-1.267-.82c-.388-.15-.97-.328-2.042-.375a48.987 48.987 0 00-2.535-.056zm-1.515 3.37a5.65 5.65 0 11.021 11.299 5.65 5.65 0 01-.02-11.3zm.004 1.982a3.667 3.667 0 10.015 7.334 3.667 3.667 0 00-.015-7.334zm5.865-3.536a1.32 1.32 0 11.005 2.64 1.32 1.32 0 01-.005-2.64z",id:"a"})),h("g",{fill:"none","fill-rule":"evenodd"},h("mask",{id:"b",fill:"#fff"},h("use",{xlinkHref:"#a"})),h("image",{mask:"url(#b)",x:"4",y:"4",width:"24",height:"24",xlinkHref:"data:image/png;base64,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"}))),this.defaultLocale=ea,this.i18nInit(),this.title=this.i18n("pluginNameInstagram"),this.opts.companionAllowedHosts=Gt(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new Se(t,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionKeysParams:this.opts.companionKeysParams,companionCookiesRule:this.opts.companionCookiesRule,provider:"instagram",pluginId:this.id,supportsRefreshToken:!1}),this.onFirstRender=this.onFirstRender.bind(this),this.render=this.render.bind(this)}install(){this.view=new Ae(this,{provider:this.provider,viewType:"grid",showTitles:!1,showFilter:!1,showBreadcrumbs:!1});const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.view.tearDown(),this.unmount()}async onFirstRender(){await Promise.all([this.provider.fetchPreAuthToken(),this.view.getFolder("recent")])}render(t){return this.view.render(t)}}Ys.VERSION=ia.version;const sa={version:"3.3.1"};class Xs extends et{constructor(t,e){if(super(t,e),this.type="acquirer",this.files=[],this.storage=this.opts.storage||qt,this.id=this.opts.id||"Unsplash",this.title=this.opts.title||"Unsplash",this.icon=()=>h("svg",{className:"uppy-DashboardTab-iconUnsplash",viewBox:"0 0 32 32",height:"32",width:"32","aria-hidden":"true"},h("g",{fill:"currentcolor"},h("path",{d:"M46.575 10.883v-9h12v9zm12 5h10v18h-32v-18h10v9h12z"}),h("path",{d:"M13 12.5V8h6v4.5zm6 2.5h5v9H8v-9h5v4.5h6z"}))),!this.opts.companionUrl)throw new Error("Companion hostname is required, please consult https://uppy.io/docs/companion");this.hostname=this.opts.companionUrl,this.opts.companionAllowedHosts=Gt(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new fr(t,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionCookiesRule:this.opts.companionCookiesRule,provider:"unsplash",pluginId:this.id})}install(){this.view=new Sr(this,{provider:this.provider,viewType:"unsplash",showFilter:!0});const{target:t}=this.opts;t&&this.mount(t,this)}async onFirstRender(){}render(t){return this.view.render(t)}uninstall(){this.unmount()}}Xs.VERSION=sa.version;function us(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var ra=0;function oa(s){return"__private_"+ra+++"_"+s}var ee=oa("handleSubmit");class aa extends Re{constructor(t){super(t),this.form=document.createElement("form"),Object.defineProperty(this,ee,{writable:!0,value:e=>{e.preventDefault();const{addFile:i}=this.props,r=this.input.value.trim();i(r)}}),this.form.id=mr()}componentDidMount(){this.input.value="",this.form.addEventListener("submit",us(this,ee)[ee]),document.body.appendChild(this.form)}componentWillUnmount(){this.form.removeEventListener("submit",us(this,ee)[ee]),document.body.removeChild(this.form)}render(){const{i18n:t}=this.props;return h("div",{className:"uppy-Url"},h("input",{className:"uppy-u-reset uppy-c-textInput uppy-Url-input",type:"text","aria-label":t("enterUrlToImport"),placeholder:t("enterUrlToImport"),ref:e=>{this.input=e},"data-uppy-super-focusable":!0,form:this.form.id}),h("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Url-importButton",type:"submit",form:this.form.id},t("import")))}}function ds(s,t,e){const i=Fe(s.items);let r;switch(t){case"paste":{if(i.some(a=>a.kind==="file"))return;r=i.filter(a=>a.kind==="string"&&a.type==="text/plain");break}case"drop":{r=i.filter(o=>o.kind==="string"&&o.type==="text/uri-list");break}default:throw new Error(`isDropOrPaste must be either 'drop' or 'paste', but it's ${t}`)}r.forEach(o=>{o.getAsString(a=>e(a))})}const na={strings:{import:"Import",enterUrlToImport:"Enter URL to import a file",failedToFetch:"Companion failed to fetch this URL, please make sure it’s correct",enterCorrectUrl:"Incorrect URL: Please make sure you are entering a direct link to a file"}};var Ks;const la={version:"3.6.1"};function ha(){return h("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},h("path",{d:"M23.637 15.312l-2.474 2.464a3.582 3.582 0 01-.577.491c-.907.657-1.897.986-2.968.986a4.925 4.925 0 01-3.959-1.971c-.248-.329-.164-.902.165-1.149.33-.247.907-.164 1.155.164 1.072 1.478 3.133 1.724 4.618.656a.642.642 0 00.33-.328l2.473-2.463c1.238-1.313 1.238-3.366-.082-4.597a3.348 3.348 0 00-4.618 0l-1.402 1.395a.799.799 0 01-1.154 0 .79.79 0 010-1.15l1.402-1.394a4.843 4.843 0 016.843 0c2.062 1.805 2.144 5.007.248 6.896zm-8.081 5.664l-1.402 1.395a3.348 3.348 0 01-4.618 0c-1.319-1.23-1.319-3.365-.082-4.596l2.475-2.464.328-.328c.743-.492 1.567-.739 2.475-.657.906.165 1.648.574 2.143 1.314.248.329.825.411 1.155.165.33-.248.412-.822.165-1.15-.825-1.068-1.98-1.724-3.216-1.888-1.238-.247-2.556.082-3.628.902l-.495.493-2.474 2.464c-1.897 1.969-1.814 5.09.083 6.977.99.904 2.226 1.396 3.463 1.396s2.473-.492 3.463-1.395l1.402-1.396a.79.79 0 000-1.15c-.33-.328-.908-.41-1.237-.082z",fill:"#FF753E","fill-rule":"nonzero"}))}function ca(s){const t=/^[a-z0-9]+:\/\//,e="http://";return t.test(s)?s:e+s}function ua(s){return Fe(s.dataTransfer.items).filter(i=>i.kind==="string"&&i.type==="text/uri-list").length>0}function da(s){return(s==null?void 0:s.startsWith("http://"))||(s==null?void 0:s.startsWith("https://"))}function pa(s){const{pathname:t}=new URL(s);return t.substring(t.lastIndexOf("/")+1)}class At extends et{constructor(t,e){if(super(t,e),this.getMeta=i=>this.client.post("url/meta",{url:i}).then(r=>{if(r.error)throw this.uppy.log("[URL] Error:"),this.uppy.log(r.error),new Error("Failed to fetch the file");return r}),this.addFile=async(i,r)=>{const o=ca(i);if(!da(o)){this.uppy.log(`[URL] Incorrect URL entered: ${o}`),this.uppy.info(this.i18n("enterCorrectUrl"),"error",4e3);return}try{const a=await this.getMeta(o),n={meta:r,source:this.id,name:a.name||pa(o),type:a.type,data:{size:a.size},isRemote:!0,body:{url:o},remote:{companionUrl:this.opts.companionUrl,url:`${this.hostname}/url/get`,body:{fileId:o,url:o},requestClientId:At.requestClientId}};this.uppy.log("[Url] Adding remote file");try{return this.uppy.addFile(n)}catch(l){return l.isRestriction||this.uppy.log(l),l}}catch(a){return this.uppy.log(a),this.uppy.info({message:this.i18n("failedToFetch"),details:a},"error",4e3),a}},this.handleRootDrop=i=>{ds(i.dataTransfer,"drop",r=>{this.uppy.log(`[URL] Adding file from dropped url: ${r}`),this.addFile(r)})},this.handleRootPaste=i=>{ds(i.clipboardData,"paste",r=>{this.uppy.log(`[URL] Adding file from pasted url: ${r}`),this.addFile(r)})},this.id=this.opts.id||"Url",this.title=this.opts.title||"Link",this.type="acquirer",this.icon=()=>h(ha,null),this.defaultLocale=na,this.i18nInit(),this.hostname=this.opts.companionUrl,!this.hostname)throw new Error("Companion hostname is required, please consult https://uppy.io/docs/companion");this.client=new gr(t,{pluginId:this.id,provider:"url",companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionCookiesRule:this.opts.companionCookiesRule}),this.uppy.registerRequestClient(At.requestClientId,this.client)}render(){return h(aa,{i18n:this.i18n,addFile:this.addFile})}install(){const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.unmount()}}Ks=At;At.VERSION=la.version;At.requestClientId=Ks.name;At.prototype.canHandleRootDrop=ua;const fa={strings:{pluginNameZoom:"Zoom"}},ma={version:"2.3.1"};class Qs extends et{constructor(t,e){super(t,e),this.type="acquirer",this.files=[],this.storage=this.opts.storage||qt,this.id=this.opts.id||"Zoom",this.icon=()=>h("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},h("path",{d:"M24.5 11.125l-2.75 2.063c-.473.353-.75.91-.75 1.5v3.124c0 .59.277 1.147.75 1.5l2.75 2.063a.938.938 0 001.5-.75v-8.75a.938.938 0 00-1.5-.75zm-4.75 9.5c0 1.035-.84 1.875-1.875 1.875H9.75A3.75 3.75 0 016 18.75v-6.875C6 10.84 6.84 10 7.875 10H16a3.75 3.75 0 013.75 3.75v6.875z",fill:"#2E8CFF","fill-rule":"evenodd"})),this.opts.companionAllowedHosts=Gt(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new Se(t,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionKeysParams:this.opts.companionKeysParams,companionCookiesRule:this.opts.companionCookiesRule,provider:"zoom",pluginId:this.id,supportsRefreshToken:!1}),this.defaultLocale=fa,this.i18nInit(),this.title=this.i18n("pluginNameZoom"),this.onFirstRender=this.onFirstRender.bind(this),this.render=this.render.bind(this)}install(){this.view=new Ae(this,{provider:this.provider});const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.view.tearDown(),this.unmount()}async onFirstRender(){await Promise.all([this.provider.fetchPreAuthToken(),this.view.getFolder()])}render(t){return this.view.render(t)}}Qs.VERSION=ma.version;function Qe(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var ga=0;function va(s){return"__private_"+ga+++"_"+s}const ya={version:"1.3.0"},Ei={__proto__:null,Box:Hs,Dropbox:Er,Facebook:qs,GoogleDrive:Dr,GooglePhotos:Gs,Instagram:Ys,OneDrive:Cr,Unsplash:Xs,Url:At,Zoom:Qs},ba={sources:Object.keys(Ei)};var Tt=va("installedPlugins");class wa extends we{constructor(t,e){if(super(t,{...ba,...e}),Object.defineProperty(this,Tt,{writable:!0,value:new Set}),this.id=this.opts.id||"RemoteSources",this.type="preset",this.opts.companionUrl==null)throw new Error("Please specify companionUrl for RemoteSources to work, see https://uppy.io/docs/remote-sources#companionUrl")}setOptions(t){this.uninstall(),super.setOptions(t),this.install()}install(){this.opts.sources.forEach(t=>{const e={...this.opts,sources:void 0},i=Ei[t];if(i==null){const r=Object.keys(Ei),o=new Intl.ListFormat("en",{style:"long",type:"disjunction"});throw new Error(`Invalid plugin: "${t}" is not one of: ${o.format(r)}.`)}this.uppy.use(i,e),Qe(this,Tt)[Tt].add(this.uppy.getPlugin(t))})}uninstall(){for(const t of Qe(this,Tt)[Tt])this.uppy.removePlugin(t);Qe(this,Tt)[Tt].clear()}}wa.VERSION=ya.version;function Sa(){return h("svg",{className:"uppy-DashboardTab-iconScreenRec","aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},h("g",{fill:"currentcolor",fillRule:"evenodd"},h("path",{d:"M24.182 9H7.818C6.81 9 6 9.742 6 10.667v10c0 .916.81 1.666 1.818 1.666h4.546V24h7.272v-1.667h4.546c1 0 1.809-.75 1.809-1.666l.009-10C26 9.742 25.182 9 24.182 9zM24 21H8V11h16v10z"}),h("circle",{cx:"16",cy:"16",r:"2"})))}function Aa(s){let{recording:t,onStartRecording:e,onStopRecording:i,i18n:r}=s;return t?h("button",{className:"uppy-u-reset uppy-c-btn uppy-ScreenCapture-button uppy-ScreenCapture-button--video uppy-ScreenCapture-button--stop-rec",type:"button",title:r("stopCapturing"),"aria-label":r("stopCapturing"),onClick:i,"data-uppy-super-focusable":!0},h("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"100",height:"100",viewBox:"0 0 100 100"},h("rect",{x:"15",y:"15",width:"70",height:"70"}))):h("button",{className:"uppy-u-reset uppy-c-btn uppy-ScreenCapture-button uppy-ScreenCapture-button--video",type:"button",title:r("startCapturing"),"aria-label":r("startCapturing"),onClick:e,"data-uppy-super-focusable":!0},h("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"100",height:"100",viewBox:"0 0 100 100"},h("circle",{cx:"50",cy:"50",r:"40"})))}function Ra(s){let{recording:t,recordedVideo:e,onSubmit:i,i18n:r}=s;return e&&!t?h("button",{className:"uppy-u-reset uppy-c-btn uppy-ScreenCapture-button uppy-ScreenCapture-button--submit",type:"button",title:r("submitRecordedFile"),"aria-label":r("submitRecordedFile"),onClick:i,"data-uppy-super-focusable":!0},h("svg",{width:"12",height:"9",viewBox:"0 0 12 9",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",className:"uppy-c-icon"},h("path",{fill:"#fff",fillRule:"nonzero",d:"M10.66 0L12 1.31 4.136 9 0 4.956l1.34-1.31L4.136 6.38z"}))):null}function Ea(s){return(s-(s%=60))/60+(s>9?":":":0")+s}class Da extends Re{constructor(t){super(t),this.wrapperStyle={width:"100%",height:"100%",display:"flex"},this.overlayStyle={position:"absolute",width:"100%",height:"100%",background:"black",opacity:.7},this.infoContainerStyle={marginLeft:"auto",marginRight:"auto",marginTop:"auto",marginBottom:"auto",zIndex:1,color:"white"},this.infotextStyle={marginLeft:"auto",marginRight:"auto",marginBottom:"1rem",fontSize:"1.5rem"},this.timeStyle={display:"block",fontWeight:"bold",marginLeft:"auto",marginRight:"auto",fontSize:"3rem",fontFamily:"Courier New"},this.state={elapsedTime:0}}startTimer(){this.timerTick(),this.timerRunning=!0}resetTimer(){clearTimeout(this.timer),this.setState({elapsedTime:0}),this.timerRunning=!1}timerTick(){this.timer=setTimeout(()=>{this.setState(t=>({elapsedTime:t.elapsedTime+1})),this.timerTick()},1e3)}render(){const{recording:t,i18n:e}={...this.props},{elapsedTime:i}=this.state,r=Ea(i);return t&&!this.timerRunning&&this.startTimer(),!t&&this.timerRunning&&this.resetTimer(),t?h("div",{style:this.wrapperStyle},h("div",{style:this.overlayStyle}),h("div",{style:this.infoContainerStyle},h("div",{style:this.infotextStyle},e("recording")),h("div",{style:this.timeStyle},r))):null}}function Ca(s){let{streamActive:t,i18n:e}=s;return t?h("div",{title:e("streamActive"),"aria-label":e("streamActive"),className:"uppy-ScreenCapture-icon--stream uppy-ScreenCapture-icon--streamActive"},h("svg",{"aria-hidden":"true",focusable:"false",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M0 0h24v24H0z",opacity:".1",fill:"none"}),h("path",{d:"M0 0h24v24H0z",fill:"none"}),h("path",{d:"M1 18v3h3c0-1.66-1.34-3-3-3zm0-4v2c2.76 0 5 2.24 5 5h2c0-3.87-3.13-7-7-7zm18-7H5v1.63c3.96 1.28 7.09 4.41 8.37 8.37H19V7zM1 10v2c4.97 0 9 4.03 9 9h2c0-6.08-4.93-11-11-11zm20-7H3c-1.1 0-2 .9-2 2v3h2V5h18v14h-7v2h7c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"}))):h("div",{title:e("streamPassive"),"aria-label":e("streamPassive"),className:"uppy-ScreenCapture-icon--stream"},h("svg",{"aria-hidden":"true",focusable:"false",width:"24",height:"24",viewBox:"0 0 24 24"},h("path",{d:"M0 0h24v24H0z",opacity:".1",fill:"none"}),h("path",{d:"M0 0h24v24H0z",fill:"none"}),h("path",{d:"M21 3H3c-1.1 0-2 .9-2 2v3h2V5h18v14h-7v2h7c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM1 18v3h3c0-1.66-1.34-3-3-3zm0-4v2c2.76 0 5 2.24 5 5h2c0-3.87-3.13-7-7-7zm0-4v2c4.97 0 9 4.03 9 9h2c0-6.08-4.93-11-11-11z"})))}function Di(){return Di=Object.assign?Object.assign.bind():function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(s[i]=e[i])}return s},Di.apply(this,arguments)}class Oa extends Re{componentWillUnmount(){const{onStop:t}=this.props;t()}render(){const{recording:t,stream:e,recordedVideo:i}=this.props,r={playsinline:!0};return(t||!i&&!t)&&(r.muted=!0,r.autoplay=!0,r.srcObject=e),i&&!t&&(r.muted=!1,r.controls=!0,r.src=i,this.videoElement&&(this.videoElement.srcObject=null)),h("div",{className:"uppy uppy-ScreenCapture-container"},h("div",{className:"uppy-ScreenCapture-videoContainer"},h(Ca,this.props),h("video",Di({ref:o=>{this.videoElement=o},className:"uppy-ScreenCapture-video"},r)),h(Da,this.props)),h("div",{className:"uppy-ScreenCapture-buttonContainer"},h(Aa,this.props),h(Ra,this.props)))}}const xa={strings:{startCapturing:"Begin screen capturing",stopCapturing:"Stop screen capturing",submitRecordedFile:"Submit recorded file",streamActive:"Stream active",streamPassive:"Stream passive",micDisabled:"Microphone access denied by user",recording:"Recording"}};function Ci(){return Ci=Object.assign?Object.assign.bind():function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(s[i]=e[i])}return s},Ci.apply(this,arguments)}const Pa={version:"3.2.0"};function Ta(){var s;return window.MediaRecorder&&((s=navigator.mediaDevices)==null?void 0:s.getDisplayMedia)}function Ma(){return window.MediaRecorder&&navigator.mediaDevices}const ka={displayMediaConstraints:{video:{width:1280,height:720,frameRate:{ideal:3,max:5},cursor:"motion",displaySurface:"monitor"}},userMediaConstraints:{audio:!0},preferredVideoMimeType:"video/webm"};class La extends et{constructor(t,e){super(t,{...ka,...e}),this.mediaDevices=Ma(),this.protocol=location.protocol==="https:"?"https":"http",this.id=this.opts.id||"ScreenCapture",this.title=this.opts.title||"Screencast",this.type="acquirer",this.icon=Sa,this.defaultLocale=xa,this.i18nInit(),this.install=this.install.bind(this),this.setPluginState=this.setPluginState.bind(this),this.render=this.render.bind(this),this.start=this.start.bind(this),this.stop=this.stop.bind(this),this.startRecording=this.startRecording.bind(this),this.stopRecording=this.stopRecording.bind(this),this.submit=this.submit.bind(this),this.streamInterrupted=this.streamInactivated.bind(this),this.captureActive=!1,this.capturedMediaFile=null}install(){if(!Ta())return this.uppy.log("Screen recorder access is not supported","warning"),null;this.setPluginState({streamActive:!1,audioStreamActive:!1});const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.videoStream&&this.stop(),this.unmount()}start(){return this.mediaDevices?(this.captureActive=!0,this.selectAudioStreamSource(),this.selectVideoStreamSource().then(t=>{t===!1&&this.parent&&this.parent.hideAllPanels&&(this.parent.hideAllPanels(),this.captureActive=!1)})):Promise.reject(new Error("Screen recorder access not supported"))}selectVideoStreamSource(){return this.videoStream?new Promise(t=>t(this.videoStream)):this.mediaDevices.getDisplayMedia(this.opts.displayMediaConstraints).then(t=>(this.videoStream=t,this.videoStream.addEventListener("inactive",()=>{this.streamInactivated()}),this.setPluginState({streamActive:!0}),t)).catch(t=>(this.setPluginState({screenRecError:t}),this.userDenied=!0,setTimeout(()=>{this.userDenied=!1},1e3),!1))}selectAudioStreamSource(){return this.audioStream?new Promise(t=>t(this.audioStream)):this.mediaDevices.getUserMedia(this.opts.userMediaConstraints).then(t=>(this.audioStream=t,this.setPluginState({audioStreamActive:!0}),t)).catch(t=>(t.name==="NotAllowedError"&&(this.uppy.info(this.i18n("micDisabled"),"error",5e3),this.uppy.log(this.i18n("micDisabled"),"warning")),!1))}startRecording(){const t={};this.capturedMediaFile=null,this.recordingChunks=[];const{preferredVideoMimeType:e}=this.opts;this.selectVideoStreamSource().then(i=>{if(i===!1)throw new Error("No video stream available");e&&MediaRecorder.isTypeSupported(e)&&Ht(e)&&(t.mimeType=e);const r=[i.getVideoTracks()[0]];this.audioStream&&r.push(this.audioStream.getAudioTracks()[0]),this.outputStream=new MediaStream(r),this.recorder=new MediaRecorder(this.outputStream,t),this.recorder.addEventListener("dataavailable",o=>{this.recordingChunks.push(o.data)}),this.recorder.start(),this.setPluginState({recording:!0})}).catch(i=>{this.uppy.log(i,"error")})}streamInactivated(){const{recordedVideo:t,recording:e}={...this.getPluginState()};!t&&!e?this.parent&&this.parent.hideAllPanels&&this.parent.hideAllPanels():e&&(this.uppy.log("Capture stream inactive — stop recording"),this.stopRecording()),this.videoStream=null,this.audioStream=null,this.setPluginState({streamActive:!1,audioStreamActive:!1})}stopRecording(){return new Promise(e=>{this.recorder.addEventListener("stop",()=>{e()}),this.recorder.stop()}).then(()=>(this.setPluginState({recording:!1}),this.getVideo())).then(e=>{this.capturedMediaFile=e,this.setPluginState({recordedVideo:URL.createObjectURL(e.data)})}).then(()=>{this.recordingChunks=null,this.recorder=null},e=>{throw this.recordingChunks=null,this.recorder=null,e})}submit(){try{this.capturedMediaFile&&this.uppy.addFile(this.capturedMediaFile)}catch(t){t.isRestriction||this.uppy.log(t,"warning")}}stop(){this.videoStream&&(this.videoStream.getVideoTracks().forEach(t=>{t.stop()}),this.videoStream.getAudioTracks().forEach(t=>{t.stop()}),this.videoStream=null),this.audioStream&&(this.audioStream.getAudioTracks().forEach(t=>{t.stop()}),this.audioStream.getVideoTracks().forEach(t=>{t.stop()}),this.audioStream=null),this.outputStream&&(this.outputStream.getAudioTracks().forEach(t=>{t.stop()}),this.outputStream.getVideoTracks().forEach(t=>{t.stop()}),this.outputStream=null),this.setPluginState({recordedVideo:null}),this.captureActive=!1}getVideo(){const t=this.recordingChunks[0].type,e=Ht(t);if(!e)return Promise.reject(new Error(`Could not retrieve recording: Unsupported media type "${t}"`));const i=`screencap-${Date.now()}.${e}`,r=new Blob(this.recordingChunks,{type:t}),o={source:this.id,name:i,data:new Blob([r],{type:t}),type:t};return Promise.resolve(o)}render(){const t=this.getPluginState();return!t.streamActive&&!this.captureActive&&!this.userDenied&&this.start(),h(Oa,Ci({},t,{onStartRecording:this.startRecording,onStopRecording:this.stopRecording,onStop:this.stop,onSubmit:this.submit,i18n:this.i18n,stream:this.videoStream}))}}La.VERSION=Pa.version;var Ie={exports:{}};Ie.exports=Vi;Ie.exports.isMobile=Vi;Ie.exports.default=Vi;const Fa=/(android|bb\d+|meego).+mobile|armv7l|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series[46]0|samsungbrowser|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i,_a=/CrOS/,Ia=/android|ipad|playbook|silk/i;function Vi(s){s||(s={});let t=s.ua;if(!t&&typeof navigator<"u"&&(t=navigator.userAgent),t&&t.headers&&typeof t.headers["user-agent"]=="string"&&(t=t.headers["user-agent"]),typeof t!="string")return!1;let e=Fa.test(t)&&!_a.test(t)||!!s.tablet&&Ia.test(t);return!e&&s.tablet&&s.featureDetect&&navigator&&navigator.maxTouchPoints>1&&t.indexOf("Macintosh")!==-1&&t.indexOf("Safari")!==-1&&(e=!0),e}var Na=Ie.exports;const Ba=Os(Na);function Ua(s,t,e){return new Promise(i=>{s.toBlob(i,t,e)})}function za(){return typeof MediaRecorder=="function"&&!!MediaRecorder.prototype&&typeof MediaRecorder.prototype.start=="function"}function Js(){return h("svg",{"aria-hidden":"true",focusable:"false",fill:"#0097DC",width:"66",height:"55",viewBox:"0 0 66 55"},h("path",{d:"M57.3 8.433c4.59 0 8.1 3.51 8.1 8.1v29.7c0 4.59-3.51 8.1-8.1 8.1H8.7c-4.59 0-8.1-3.51-8.1-8.1v-29.7c0-4.59 3.51-8.1 8.1-8.1h9.45l4.59-7.02c.54-.54 1.35-1.08 2.16-1.08h16.2c.81 0 1.62.54 2.16 1.08l4.59 7.02h9.45zM33 14.64c-8.62 0-15.393 6.773-15.393 15.393 0 8.62 6.773 15.393 15.393 15.393 8.62 0 15.393-6.773 15.393-15.393 0-8.62-6.773-15.393-15.393-15.393zM33 40c-5.648 0-9.966-4.319-9.966-9.967 0-5.647 4.318-9.966 9.966-9.966s9.966 4.319 9.966 9.966C42.966 35.681 38.648 40 33 40z",fillRule:"evenodd"}))}function ja(s){let{onSnapshot:t,i18n:e}=s;return h("button",{className:"uppy-u-reset uppy-c-btn uppy-Webcam-button uppy-Webcam-button--picture",type:"button",title:e("takePicture"),"aria-label":e("takePicture"),onClick:t,"data-uppy-super-focusable":!0},Js())}function Va(s){let{recording:t,onStartRecording:e,onStopRecording:i,i18n:r}=s;return t?h("button",{className:"uppy-u-reset uppy-c-btn uppy-Webcam-button",type:"button",title:r("stopRecording"),"aria-label":r("stopRecording"),onClick:i,"data-uppy-super-focusable":!0},h("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"100",height:"100",viewBox:"0 0 100 100"},h("rect",{x:"15",y:"15",width:"70",height:"70"}))):h("button",{className:"uppy-u-reset uppy-c-btn uppy-Webcam-button",type:"button",title:r("startRecording"),"aria-label":r("startRecording"),onClick:e,"data-uppy-super-focusable":!0},h("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"100",height:"100",viewBox:"0 0 100 100"},h("circle",{cx:"50",cy:"50",r:"40"})))}function Wa(s){return`${Math.floor(s/60)}:${String(s%60).padStart(2,"0")}`}function $a(s){let{recordingLengthSeconds:t,i18n:e}=s;const i=Wa(t);return h("span",{"aria-label":e("recordingLength",{recording_length:i})},i)}function Ha(s){let{currentDeviceId:t,videoSources:e,onChangeVideoSource:i}=s;return h("div",{className:"uppy-Webcam-videoSource"},h("select",{className:"uppy-u-reset uppy-Webcam-videoSource-select",onChange:r=>{i(r.target.value)}},e.map(r=>h("option",{key:r.deviceId,value:r.deviceId,selected:r.deviceId===t},r.label))))}function qa(s){let{onSubmit:t,i18n:e}=s;return h("button",{className:"uppy-u-reset uppy-c-btn uppy-Webcam-button uppy-Webcam-button--submit",type:"button",title:e("submitRecordedFile"),"aria-label":e("submitRecordedFile"),onClick:t,"data-uppy-super-focusable":!0},h("svg",{width:"12",height:"9",viewBox:"0 0 12 9",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",className:"uppy-c-icon"},h("path",{fill:"#fff",fillRule:"nonzero",d:"M10.66 0L12 1.31 4.136 9 0 4.956l1.34-1.31L4.136 6.38z"})))}function Ga(s){let{onDiscard:t,i18n:e}=s;return h("button",{className:"uppy-u-reset uppy-c-btn uppy-Webcam-button uppy-Webcam-button--discard",type:"button",title:e("discardRecordedFile"),"aria-label":e("discardRecordedFile"),onClick:t,"data-uppy-super-focusable":!0},h("svg",{width:"13",height:"13",viewBox:"0 0 13 13",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",className:"uppy-c-icon"},h("g",{fill:"#FFF",fillRule:"evenodd"},h("path",{d:"M.496 11.367L11.103.76l1.414 1.414L1.911 12.781z"}),h("path",{d:"M11.104 12.782L.497 2.175 1.911.76l10.607 10.606z"}))))}function Oi(){return Oi=Object.assign?Object.assign.bind():function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(s[i]=e[i])}return s},Oi.apply(this,arguments)}function xe(s,t){return s.includes(t)}class Ya extends Re{componentDidMount(){const{onFocus:t}=this.props;t()}componentWillUnmount(){const{onStop:t}=this.props;t()}render(){const{src:t,recordedVideo:e,recording:i,modes:r,supportsRecording:o,videoSources:a,showVideoSourceDropdown:n,showRecordingLength:l,onSubmit:c,i18n:d,mirror:u,onSnapshot:p,onStartRecording:w,onStopRecording:m,onDiscardRecordedVideo:y,recordingLengthSeconds:g}=this.props,b=!!e,A=!b&&o&&(xe(r,"video-only")||xe(r,"audio-only")||xe(r,"video-audio")),O=!b&&xe(r,"picture"),N=o&&l&&!b,L=n&&a&&a.length>1,f={playsInline:!0};return e?(f.muted=!1,f.controls=!0,f.src=e,this.videoElement&&(this.videoElement.srcObject=null)):(f.muted=!0,f.autoPlay=!0,f.srcObject=t),h("div",{className:"uppy uppy-Webcam-container"},h("div",{className:"uppy-Webcam-videoContainer"},h("video",Oi({ref:D=>this.videoElement=D,className:`uppy-Webcam-video  ${u?"uppy-Webcam-video--mirrored":""}`},f))),h("div",{className:"uppy-Webcam-footer"},h("div",{className:"uppy-Webcam-videoSourceContainer"},L?Ha(this.props):null),h("div",{className:"uppy-Webcam-buttonContainer"},O&&h(ja,{onSnapshot:p,i18n:d}),A&&h(Va,{recording:i,onStartRecording:w,onStopRecording:m,i18n:d}),b&&h(qa,{onSubmit:c,i18n:d}),b&&h(Ga,{onDiscard:y,i18n:d})),h("div",{className:"uppy-Webcam-recordingLength"},N&&h($a,{recordingLengthSeconds:g,i18n:d}))))}}function Xa(s){let{icon:t,i18n:e,hasCamera:i}=s;return h("div",{className:"uppy-Webcam-permissons"},h("div",{className:"uppy-Webcam-permissonsIcon"},t()),h("h1",{className:"uppy-Webcam-title"},e(i?"allowAccessTitle":"noCameraTitle")),h("p",null,e(i?"allowAccessDescription":"noCameraDescription")))}const Ka={strings:{pluginNameCamera:"Camera",noCameraTitle:"Camera Not Available",noCameraDescription:"In order to take pictures or record video, please connect a camera device",recordingStoppedMaxSize:"Recording stopped because the file size is about to exceed the limit",submitRecordedFile:"Submit recorded file",discardRecordedFile:"Discard recorded file",smile:"Smile!",takePicture:"Take a picture",startRecording:"Begin video recording",stopRecording:"Stop video recording",recordingLength:"Recording length %{recording_length}",allowAccessTitle:"Please allow access to your camera",allowAccessDescription:"In order to take pictures or record video with your camera, please allow camera access for this site."}};function xi(){return xi=Object.assign?Object.assign.bind():function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(s[i]=e[i])}return s},xi.apply(this,arguments)}function ie(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var Qa=0;function Ja(s){return"__private_"+Qa+++"_"+s}const Za={version:"3.4.2"};function ps(s){return s[0]==="."?Ar[s.slice(1)]:s}function tn(s){return/^video\/[^*]+$/.test(s)}function en(s){return/^image\/[^*]+$/.test(s)}function sn(){return navigator.mediaDevices}function Je(s,t){return s.includes(t)}const rn={onBeforeSnapshot:()=>Promise.resolve(),countdown:!1,modes:["video-audio","video-only","audio-only","picture"],mirror:!0,showVideoSourceDropdown:!1,facingMode:"user",preferredImageMimeType:null,preferredVideoMimeType:null,showRecordingLength:!1,mobileNativeCamera:Ba({tablet:!0})};var st=Ja("enableMirror");class on extends et{constructor(t,e){super(t,{...rn,...e}),Object.defineProperty(this,st,{writable:!0,value:void 0}),this.mediaDevices=sn(),this.supportsUserMedia=!!this.mediaDevices,this.protocol=location.protocol.match(/https/i)?"https":"http",this.id=this.opts.id||"Webcam",this.type="acquirer",this.capturedMediaFile=null,this.icon=()=>h("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},h("path",{d:"M23.5 9.5c1.417 0 2.5 1.083 2.5 2.5v9.167c0 1.416-1.083 2.5-2.5 2.5h-15c-1.417 0-2.5-1.084-2.5-2.5V12c0-1.417 1.083-2.5 2.5-2.5h2.917l1.416-2.167C13 7.167 13.25 7 13.5 7h5c.25 0 .5.167.667.333L20.583 9.5H23.5zM16 11.417a4.706 4.706 0 00-4.75 4.75 4.704 4.704 0 004.75 4.75 4.703 4.703 0 004.75-4.75c0-2.663-2.09-4.75-4.75-4.75zm0 7.825c-1.744 0-3.076-1.332-3.076-3.074 0-1.745 1.333-3.077 3.076-3.077 1.744 0 3.074 1.333 3.074 3.076s-1.33 3.075-3.074 3.075z",fill:"#02B383",fillRule:"nonzero"})),this.defaultLocale=Ka,this.i18nInit(),this.title=this.i18n("pluginNameCamera"),ie(this,st)[st]=this.opts.mirror,this.install=this.install.bind(this),this.setPluginState=this.setPluginState.bind(this),this.render=this.render.bind(this),this.start=this.start.bind(this),this.stop=this.stop.bind(this),this.takeSnapshot=this.takeSnapshot.bind(this),this.startRecording=this.startRecording.bind(this),this.stopRecording=this.stopRecording.bind(this),this.discardRecordedVideo=this.discardRecordedVideo.bind(this),this.submit=this.submit.bind(this),this.oneTwoThreeSmile=this.oneTwoThreeSmile.bind(this),this.focus=this.focus.bind(this),this.changeVideoSource=this.changeVideoSource.bind(this),this.webcamActive=!1,this.opts.countdown&&(this.opts.onBeforeSnapshot=this.oneTwoThreeSmile),this.setPluginState({hasCamera:!1,cameraReady:!1,cameraError:null,recordingLengthSeconds:0,videoSources:[],currentDeviceId:null})}setOptions(t){super.setOptions({...t,videoConstraints:{...this.opts.videoConstraints,...t==null?void 0:t.videoConstraints}})}hasCameraCheck(){return this.mediaDevices?this.mediaDevices.enumerateDevices().then(t=>t.some(e=>e.kind==="videoinput")):Promise.resolve(!1)}isAudioOnly(){return this.opts.modes.length===1&&this.opts.modes[0]==="audio-only"}getConstraints(t){t===void 0&&(t=null);const e=this.opts.modes.indexOf("video-audio")!==-1||this.opts.modes.indexOf("audio-only")!==-1,i=!this.isAudioOnly()&&(this.opts.modes.indexOf("video-audio")!==-1||this.opts.modes.indexOf("video-only")!==-1||this.opts.modes.indexOf("picture")!==-1),r={...this.opts.videoConstraints||{facingMode:this.opts.facingMode},...t?{deviceId:t,facingMode:null}:{}};return{audio:e,video:i?r:!1}}start(t){var e;if(t===void 0&&(t=null),!this.supportsUserMedia)return Promise.reject(new Error("Webcam access not supported"));this.webcamActive=!0,this.opts.mirror&&(ie(this,st)[st]=!0);const i=this.getConstraints((e=t)==null?void 0:e.deviceId);this.hasCameraCheck().then(r=>(this.setPluginState({hasCamera:r}),this.mediaDevices.getUserMedia(i).then(o=>{this.stream=o;let a=null;const n=this.isAudioOnly()?o.getAudioTracks():o.getVideoTracks();!t||!t.deviceId?a=n[0].getSettings().deviceId:n.forEach(l=>{l.getSettings().deviceId===t.deviceId&&(a=l.getSettings().deviceId)}),this.updateVideoSources(),this.setPluginState({currentDeviceId:a,cameraReady:!0})}).catch(o=>{this.setPluginState({cameraReady:!1,cameraError:o}),this.uppy.info(o.message,"error")})))}getMediaRecorderOptions(){const t={};if(MediaRecorder.isTypeSupported){const{restrictions:e}=this.uppy.opts;let i=[];this.opts.preferredVideoMimeType?i=[this.opts.preferredVideoMimeType]:e.allowedFileTypes&&(i=e.allowedFileTypes.map(ps).filter(tn));const r=a=>MediaRecorder.isTypeSupported(a)&&Ht(a),o=i.filter(r);o.length>0&&(t.mimeType=o[0])}return t}startRecording(){this.recorder=new MediaRecorder(this.stream,this.getMediaRecorderOptions()),this.recordingChunks=[];let t=!1;this.recorder.addEventListener("dataavailable",e=>{this.recordingChunks.push(e.data);const{restrictions:i}=this.uppy.opts;if(this.recordingChunks.length>1&&i.maxFileSize!=null&&!t){const r=this.recordingChunks.reduce((l,c)=>l+c.size,0),a=(r-this.recordingChunks[0].size)/(this.recordingChunks.length-1)*3,n=Math.max(0,i.maxFileSize-a);r>n&&(t=!0,this.uppy.info(this.i18n("recordingStoppedMaxSize"),"warning",4e3),this.stopRecording())}}),this.recorder.start(500),this.opts.showRecordingLength&&(this.recordingLengthTimer=setInterval(()=>{const e=this.getPluginState().recordingLengthSeconds;this.setPluginState({recordingLengthSeconds:e+1})},1e3)),this.setPluginState({isRecording:!0})}stopRecording(){return new Promise(e=>{this.recorder.addEventListener("stop",()=>{e()}),this.recorder.stop(),this.opts.showRecordingLength&&(clearInterval(this.recordingLengthTimer),this.setPluginState({recordingLengthSeconds:0}))}).then(()=>(this.setPluginState({isRecording:!1}),this.getVideo())).then(e=>{try{this.capturedMediaFile=e,this.setPluginState({recordedVideo:URL.createObjectURL(e.data)}),ie(this,st)[st]=!1}catch(i){i.isRestriction||this.uppy.log(i)}}).then(()=>{this.recordingChunks=null,this.recorder=null},e=>{throw this.recordingChunks=null,this.recorder=null,e})}discardRecordedVideo(){this.setPluginState({recordedVideo:null}),this.opts.mirror&&(ie(this,st)[st]=!0),this.capturedMediaFile=null}submit(){try{this.capturedMediaFile&&this.uppy.addFile(this.capturedMediaFile)}catch(t){t.isRestriction||this.uppy.log(t,"error")}}async stop(){if(this.stream){const t=this.stream.getAudioTracks(),e=this.stream.getVideoTracks();t.concat(e).forEach(i=>i.stop())}this.recorder&&await new Promise(t=>{this.recorder.addEventListener("stop",t,{once:!0}),this.recorder.stop(),this.opts.showRecordingLength&&clearInterval(this.recordingLengthTimer)}),this.recordingChunks=null,this.recorder=null,this.webcamActive=!1,this.stream=null,this.setPluginState({recordedVideo:null,isRecording:!1,recordingLengthSeconds:0})}getVideoElement(){return this.el.querySelector(".uppy-Webcam-video")}oneTwoThreeSmile(){return new Promise((t,e)=>{let i=this.opts.countdown;const r=setInterval(()=>{if(!this.webcamActive)return clearInterval(r),this.captureInProgress=!1,e(new Error("Webcam is not active"));i?(this.uppy.info(`${i}...`,"warning",800),i--):(clearInterval(r),this.uppy.info(this.i18n("smile"),"success",1500),setTimeout(()=>t(),1500))},1e3)})}takeSnapshot(){this.captureInProgress||(this.captureInProgress=!0,this.opts.onBeforeSnapshot().catch(t=>{const e=typeof t=="object"?t.message:t;return this.uppy.info(e,"error",5e3),Promise.reject(new Error(`onBeforeSnapshot: ${e}`))}).then(()=>this.getImage()).then(t=>{this.captureInProgress=!1;try{this.uppy.addFile(t)}catch(e){e.isRestriction||this.uppy.log(e)}},t=>{throw this.captureInProgress=!1,t}))}getImage(){const t=this.getVideoElement();if(!t)return Promise.reject(new Error("No video element found, likely due to the Webcam tab being closed."));const e=t.videoWidth,i=t.videoHeight,r=document.createElement("canvas");r.width=e,r.height=i,r.getContext("2d").drawImage(t,0,0);const{restrictions:a}=this.uppy.opts;let n=[];this.opts.preferredImageMimeType?n=[this.opts.preferredImageMimeType]:a.allowedFileTypes&&(n=a.allowedFileTypes.map(ps).filter(en));const l=n[0]||"image/jpeg",c=Ht(l)||"jpg",d=`cam-${Date.now()}.${c}`;return Ua(r,l).then(u=>({source:this.id,name:d,data:new Blob([u],{type:l}),type:l}))}getVideo(){const t=this.recordingChunks.find(a=>{var n;return((n=a.type)==null?void 0:n.length)>0}).type,e=Ht(t);if(!e)return Promise.reject(new Error(`Could not retrieve recording: Unsupported media type "${t}"`));const i=`webcam-${Date.now()}.${e}`,r=new Blob(this.recordingChunks,{type:t}),o={source:this.id,name:i,data:new Blob([r],{type:t}),type:t};return Promise.resolve(o)}focus(){this.opts.countdown&&setTimeout(()=>{this.uppy.info(this.i18n("smile"),"success",1500)},1e3)}changeVideoSource(t){this.stop(),this.start({deviceId:t})}updateVideoSources(){this.mediaDevices.enumerateDevices().then(t=>{this.setPluginState({videoSources:t.filter(e=>e.kind==="videoinput")})})}render(){this.webcamActive||this.start();const t=this.getPluginState();return!t.cameraReady||!t.hasCamera?h(Xa,{icon:Js,i18n:this.i18n,hasCamera:t.hasCamera}):h(Ya,xi({},t,{onChangeVideoSource:this.changeVideoSource,onSnapshot:this.takeSnapshot,onStartRecording:this.startRecording,onStopRecording:this.stopRecording,onDiscardRecordedVideo:this.discardRecordedVideo,onSubmit:this.submit,onFocus:this.focus,onStop:this.stop,i18n:this.i18n,modes:this.opts.modes,showRecordingLength:this.opts.showRecordingLength,showVideoSourceDropdown:this.opts.showVideoSourceDropdown,supportsRecording:za(),recording:t.isRecording,mirror:ie(this,st)[st],src:this.stream}))}install(){const{mobileNativeCamera:t,modes:e,facingMode:i,videoConstraints:r}=this.opts,{target:o}=this.opts;if(t&&o){var a;(a=this.getTargetPlugin(o))==null||a.setOptions({showNativeVideoCameraButton:Je(e,"video-only")||Je(e,"video-audio"),showNativePhotoCameraButton:Je(e,"picture"),nativeCameraFacingMode:(r==null?void 0:r.facingMode)||i});return}this.setPluginState({cameraReady:!1,recordingLengthSeconds:0}),o&&this.mount(o,this),this.mediaDevices&&(this.updateVideoSources(),this.mediaDevices.ondevicechange=()=>{if(this.updateVideoSources(),this.stream){let n=!0;const{videoSources:l,currentDeviceId:c}=this.getPluginState();l.forEach(d=>{c===d.deviceId&&(n=!1)}),n&&(this.stop(),this.start())}})}uninstall(){this.stop(),this.unmount()}onUnmount(){this.stop()}}on.VERSION=Za.version;var Zs={exports:{}};(function(s){s.exports=t;function t(i){if(i)return e(i)}function e(i){for(var r in t.prototype)i[r]=t.prototype[r];return i}t.prototype.on=t.prototype.addEventListener=function(i,r){return this._callbacks=this._callbacks||{},(this._callbacks["$"+i]=this._callbacks["$"+i]||[]).push(r),this},t.prototype.once=function(i,r){function o(){this.off(i,o),r.apply(this,arguments)}return o.fn=r,this.on(i,o),this},t.prototype.off=t.prototype.removeListener=t.prototype.removeAllListeners=t.prototype.removeEventListener=function(i,r){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var o=this._callbacks["$"+i];if(!o)return this;if(arguments.length==1)return delete this._callbacks["$"+i],this;for(var a,n=0;n<o.length;n++)if(a=o[n],a===r||a.fn===r){o.splice(n,1);break}return o.length===0&&delete this._callbacks["$"+i],this},t.prototype.emit=function(i){this._callbacks=this._callbacks||{};for(var r=new Array(arguments.length-1),o=this._callbacks["$"+i],a=1;a<arguments.length;a++)r[a-1]=arguments[a];if(o){o=o.slice(0);for(var a=0,n=o.length;a<n;++a)o[a].apply(this,r)}return this},t.prototype.listeners=function(i){return this._callbacks=this._callbacks||{},this._callbacks["$"+i]||[]},t.prototype.hasListeners=function(i){return!!this.listeners(i).length}})(Zs);var an=Zs.exports;const tr=Os(an);function C(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var nn=0;function ut(s){return"__private_"+nn+++"_"+s}const ln="ASSEMBLY_UPLOADING",Pi="ASSEMBLY_EXECUTING",Ti="ASSEMBLY_COMPLETED",fs=[ln,Pi,Ti];function Pe(s,t){return fs.indexOf(s)>=fs.indexOf(t)}var _t=ut("rateLimitedQueue"),ce=ut("fetchWithNetworkError"),Ft=ut("previousFetchStatusStillPending"),I=ut("sse"),Mi=ut("onFinished"),Ze=ut("connectServerSentEvents"),at=ut("onError"),ti=ut("beginPolling"),vt=ut("fetchStatus"),ei=ut("diffStatus");class er extends tr{constructor(t,e){super(),Object.defineProperty(this,ei,{value:fn}),Object.defineProperty(this,vt,{value:pn}),Object.defineProperty(this,ti,{value:dn}),Object.defineProperty(this,at,{value:un}),Object.defineProperty(this,Ze,{value:cn}),Object.defineProperty(this,Mi,{value:hn}),Object.defineProperty(this,_t,{writable:!0,value:void 0}),Object.defineProperty(this,ce,{writable:!0,value:void 0}),Object.defineProperty(this,Ft,{writable:!0,value:!1}),Object.defineProperty(this,I,{writable:!0,value:void 0}),this.status=t,this.pollInterval=null,this.closed=!1,C(this,_t)[_t]=e,C(this,ce)[ce]=e.wrapPromiseFunction(Ds)}connect(){C(this,Ze)[Ze](),C(this,ti)[ti]()}update(){return C(this,vt)[vt]({diff:!0})}updateStatus(t){C(this,ei)[ei](this.status,t),this.status=t}close(){this.closed=!0,C(this,I)[I]&&(C(this,I)[I].close(),C(this,I)[I]=null),clearInterval(this.pollInterval),this.pollInterval=null}}function hn(){this.emit("finished"),this.close()}function cn(){C(this,I)[I]=new EventSource(`${this.status.websocket_url}?assembly=${this.status.assembly_id}`),C(this,I)[I].addEventListener("open",()=>{clearInterval(this.pollInterval),this.pollInterval=null}),C(this,I)[I].addEventListener("message",s=>{s.data==="assembly_finished"&&C(this,Mi)[Mi](),s.data==="assembly_uploading_finished"&&this.emit("executing"),s.data==="assembly_upload_meta_data_extracted"&&(this.emit("metadata"),C(this,vt)[vt]({diff:!1}))}),C(this,I)[I].addEventListener("assembly_upload_finished",s=>{const t=JSON.parse(s.data);this.status.uploads.push(t),this.emit("upload",t)}),C(this,I)[I].addEventListener("assembly_result_finished",s=>{var t,e;const[i,r]=JSON.parse(s.data);((e=(t=this.status.results)[i])!=null?e:t[i]=[]).push(r),this.emit("result",i,r)}),C(this,I)[I].addEventListener("assembly_execution_progress",s=>{const t=JSON.parse(s.data);this.emit("execution-progress",t)}),C(this,I)[I].addEventListener("assembly_error",s=>{try{C(this,at)[at](JSON.parse(s.data))}catch{C(this,at)[at](new Error(s.data))}C(this,vt)[vt]({diff:!1})})}function un(s){this.emit("error",Object.assign(new Error(s.message),s)),this.close()}function dn(){this.pollInterval=setInterval(()=>{C(this,vt)[vt]()},2e3)}async function pn(s){let{diff:t=!0}=s===void 0?{}:s;if(!(this.closed||C(this,_t)[_t].isPaused||C(this,Ft)[Ft]))try{C(this,Ft)[Ft]=!0;const e=await C(this,ce)[ce](this.status.assembly_ssl_url);if(C(this,Ft)[Ft]=!1,this.closed)return;if(e.status===429){C(this,_t)[_t].rateLimit(2e3);return}if(!e.ok){C(this,at)[at](new vr(e.statusText));return}const i=await e.json();if(this.closed)return;this.emit("status",i),t?this.updateStatus(i):this.status=i}catch(e){C(this,at)[at](e)}}function fn(s,t){const e=s.ok,i=t.ok;if(t.error&&!s.error)return C(this,at)[at](t);const r=Pe(i,Pi)&&!Pe(e,Pi);r&&this.emit("executing"),Object.keys(t.uploads).filter(o=>!Cs(s.uploads,o)).forEach(o=>{this.emit("upload",t.uploads[o])}),r&&this.emit("metadata"),Object.keys(t.results).forEach(o=>{const a=t.results[o],n=s.results[o];a.filter(l=>!n||!n.some(c=>c.id===l.id)).forEach(l=>{this.emit("result",o,l)})}),Pe(i,Ti)&&!Pe(e,Ti)&&this.emit("finished")}function B(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var mn=0;function Ne(s){return"__private_"+mn+++"_"+s}const ir="/assemblies";class gn extends Error{constructor(t,e,i){super(t),this.details=e,this.assembly=i}}var Q=Ne("headers"),ue=Ne("fetchWithNetworkError"),q=Ne("fetchJSON"),rt=Ne("reportError");class vn{constructor(t){Object.defineProperty(this,q,{value:yn}),Object.defineProperty(this,Q,{writable:!0,value:{}}),Object.defineProperty(this,ue,{writable:!0,value:void 0}),Object.defineProperty(this,rt,{writable:!0,value:(e,i)=>{if(this.opts.errorReporting===!1)throw e;const r={type:i.type};throw i.assembly&&(r.assembly=i.assembly.assembly_id,r.instance=i.assembly.instance),i.url&&(r.endpoint=i.url),this.submitError(e,r).catch(()=>{}),e}}),this.opts=t,this.opts.client!=null&&(B(this,Q)[Q]["Transloadit-Client"]=this.opts.client),B(this,ue)[ue]=this.opts.rateLimitedQueue.wrapPromiseFunction(Ds)}async createAssembly(t){let{params:e,fields:i,signature:r,expectedFiles:o}=t;const a=new FormData;a.append("params",typeof e=="string"?e:JSON.stringify(e)),r&&a.append("signature",r),Object.keys(i).forEach(l=>{a.append(l,String(i[l]))}),a.append("num_expected_upload_files",String(o));const n=new URL(ir,`${this.opts.service}`).href;return B(this,q)[q](n,{method:"POST",headers:B(this,Q)[Q],body:a}).catch(l=>B(this,rt)[rt](l,{url:n,type:"API_ERROR"}))}async reserveFile(t,e){const i=encodeURIComponent(e.size),r=`${t.assembly_ssl_url}/reserve_file?size=${i}`;return B(this,q)[q](r,{method:"POST",headers:B(this,Q)[Q]}).catch(o=>B(this,rt)[rt](o,{assembly:t,file:e,url:r,type:"API_ERROR"}))}async addFile(t,e){if(!e.uploadURL)return Promise.reject(new Error("File does not have an `uploadURL`."));const i=encodeURIComponent(e.size),r=encodeURIComponent(e.uploadURL),o=encodeURIComponent(e.name),n=`size=${i}&filename=${o}&fieldname=file&s3Url=${r}`,l=`${t.assembly_ssl_url}/add_file?${n}`;return B(this,q)[q](l,{method:"POST",headers:B(this,Q)[Q]}).catch(c=>B(this,rt)[rt](c,{assembly:t,file:e,url:l,type:"API_ERROR"}))}async cancelAssembly(t){const e=t.assembly_ssl_url;return B(this,q)[q](e,{method:"DELETE",headers:B(this,Q)[Q]}).catch(i=>B(this,rt)[rt](i,{url:e,type:"API_ERROR"}))}async getAssemblyStatus(t){return B(this,q)[q](t,{headers:B(this,Q)[Q]}).catch(e=>B(this,rt)[rt](e,{url:t,type:"STATUS_ERROR"}))}async submitError(t,e){let{endpoint:i,instance:r,assembly:o}=e===void 0?{}:e;const a=t.details?`${t.message} (${t.details})`:t.message;return B(this,q)[q]("https://transloaditstatus.com/client_error",{method:"POST",body:JSON.stringify({endpoint:i,instance:r,assembly_id:o,agent:typeof navigator<"u"?navigator.userAgent:"",client:this.opts.client,error:a})})}}async function yn(){const s=await B(this,ue)[ue](...arguments);if(s.status===429)return this.opts.rateLimitedQueue.rateLimit(2e3),B(this,q)[q](...arguments);if(!s.ok){const t=new Error(s.statusText);return t.statusCode=s.status,`${arguments.length<=0?void 0:arguments[0]}`.endsWith(ir)?s.json().then(e=>{if(!e.error)throw t;const i=new gn(e.error,e.message,e);throw e.assembly_id&&(i.details+=` Assembly ID: ${e.assembly_id}`),i},e=>{throw e.cause=t,e}):Promise.reject(t)}return s.json()}function sr(s){if(s==null)throw new Error("Transloadit: The `params` option is required.");if(typeof s=="string")try{s=JSON.parse(s)}catch(t){throw new Ii("Transloadit: The `params` option is a malformed JSON string.",{cause:t})}if(!s.auth||!s.auth.key)throw new Error("Transloadit: The `params.auth.key` option is required. You can find your Transloadit API key at https://transloadit.com/c/template-credentials")}function bn(s){const t=Object.create(null);for(const{fileIDs:e,options:i}of s.filter(Boolean)){const r=JSON.stringify(i);r in t?t[r].fileIDArrays.push(e):t[r]={options:i,fileIDArrays:[e]}}return Object.values(t).map(e=>{let{options:i,fileIDArrays:r}=e;return{options:i,fileIDs:r.flat(1)}})}async function ms(s,t){const e=typeof t.assemblyOptions=="function"?await t.assemblyOptions(s,t):t.assemblyOptions;sr(e.params);const{fields:i}=e;return Array.isArray(i)?e.fields=s==null?{}:Object.fromEntries(i.map(r=>[r,s.meta[r]])):i==null&&(e.fields={}),e}class wn{constructor(t,e){this.files=t,this.opts=e}async build(){const t=this.opts;if(this.files.length>0)return Promise.all(this.files.map(async e=>{if(e==null)return;const i=await ms(e,t);if(e!=null)return{fileIDs:[e.id],options:i}})).then(bn);if(t.alwaysRunAssembly){const e=await ms(null,t);return[{fileIDs:[],options:e}]}return[]}}function S(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var Sn=0;function J(s){return"__private_"+Sn+++"_"+s}var de=J("assemblyIDs"),It=J("remaining"),pe=J("resolve"),ii=J("reject"),x=J("uppy"),gt=J("watching"),ve=J("onAssemblyComplete"),ye=J("onAssemblyCancel"),Nt=J("onAssemblyError"),be=J("onImportError"),Mt=J("checkAllComplete"),ki=J("removeListeners"),si=J("addListeners");class An extends tr{constructor(t,e){super(),Object.defineProperty(this,si,{value:Cn}),Object.defineProperty(this,ki,{value:Dn}),Object.defineProperty(this,Mt,{value:En}),Object.defineProperty(this,gt,{value:Rn}),Object.defineProperty(this,de,{writable:!0,value:void 0}),Object.defineProperty(this,It,{writable:!0,value:void 0}),Object.defineProperty(this,pe,{writable:!0,value:void 0}),Object.defineProperty(this,ii,{writable:!0,value:void 0}),Object.defineProperty(this,x,{writable:!0,value:void 0}),Object.defineProperty(this,ve,{writable:!0,value:i=>{S(this,gt)[gt](i.assembly_id)&&(S(this,x)[x].log(`[Transloadit] AssemblyWatcher: Got Assembly finish ${i.assembly_id}`),this.emit("assembly-complete",i.assembly_id),S(this,Mt)[Mt]())}}),Object.defineProperty(this,ye,{writable:!0,value:i=>{S(this,gt)[gt](i.assembly_id)&&S(this,Mt)[Mt]()}}),Object.defineProperty(this,Nt,{writable:!0,value:(i,r)=>{S(this,gt)[gt](i.assembly_id)&&(S(this,x)[x].log(`[Transloadit] AssemblyWatcher: Got Assembly error ${i.assembly_id}`),S(this,x)[x].log(r),this.emit("assembly-error",i.assembly_id,r),S(this,Mt)[Mt]())}}),Object.defineProperty(this,be,{writable:!0,value:(i,r,o)=>{S(this,gt)[gt](i.assembly_id)&&S(this,Nt)[Nt](i,o)}}),S(this,x)[x]=t,S(this,de)[de]=e,S(this,It)[It]=e.length,this.promise=new Promise((i,r)=>{S(this,pe)[pe]=i,S(this,ii)[ii]=r}),S(this,si)[si]()}}function Rn(s){return S(this,de)[de].indexOf(s)!==-1}function En(){S(this,It)[It]-=1,S(this,It)[It]===0&&(S(this,ki)[ki](),S(this,pe)[pe]())}function Dn(){S(this,x)[x].off("transloadit:complete",S(this,ve)[ve]),S(this,x)[x].off("transloadit:assembly-cancel",S(this,ye)[ye]),S(this,x)[x].off("transloadit:assembly-error",S(this,Nt)[Nt]),S(this,x)[x].off("transloadit:import-error",S(this,be)[be])}function Cn(){S(this,x)[x].on("transloadit:complete",S(this,ve)[ve]),S(this,x)[x].on("transloadit:assembly-cancel",S(this,ye)[ye]),S(this,x)[x].on("transloadit:assembly-error",S(this,Nt)[Nt]),S(this,x)[x].on("transloadit:import-error",S(this,be)[be])}const On={strings:{creatingAssembly:"Preparing upload...",creatingAssemblyFailed:"Transloadit: Could not create Assembly",encoding:"Encoding..."}};function E(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var xn=0;function F(s){return"__private_"+xn+++"_"+s}const Pn={version:"3.8.1"},gs=s=>t=>{const e=new Ii("Failed to send error to the client",{cause:t});console.error(e,s)},rr="https://api2.transloadit.com/companion",or=/\.transloadit\.com$/,Tn=/https?:\/\/api2(?:-\w+)?\.transloadit\.com\/companion/,Mn={service:"https://api2.transloadit.com",errorReporting:!0,waitForEncoding:!1,waitForMetadata:!1,alwaysRunAssembly:!1,importFromUploadURLs:!1,signature:null,params:null,fields:null,getAssemblyOptions:null,limit:20,retryDelays:[7e3,1e4,15e3,2e4],clientName:null};var ot=F("rateLimitedQueue"),ri=F("getClientVersion"),Li=F("attachAssemblyMetadata"),oi=F("createAssembly"),se=F("createAssemblyWatcher"),ai=F("shouldWaitAfterUpload"),ni=F("reserveFiles"),re=F("onFileUploadURLAvailable"),fe=F("findFile"),Fi=F("onFileUploadComplete"),_i=F("onResult"),le=F("onAssemblyFinished"),li=F("cancelAssembly"),hi=F("onCancelAll"),ci=F("getPersistentData"),ui=F("onRestored"),oe=F("connectAssembly"),ae=F("prepareUpload"),kt=F("afterUpload"),di=F("closeAssemblyIfExists"),ne=F("onError"),pi=F("onTusError");class Ee extends we{constructor(t,e){var i,r,o,a;super(t,{...Mn,...e}),i=this,Object.defineProperty(this,oe,{value:Wn}),Object.defineProperty(this,li,{value:Vn}),Object.defineProperty(this,le,{value:jn}),Object.defineProperty(this,_i,{value:zn}),Object.defineProperty(this,Fi,{value:Un}),Object.defineProperty(this,fe,{value:Bn}),Object.defineProperty(this,ni,{value:Nn}),Object.defineProperty(this,ai,{value:In}),Object.defineProperty(this,se,{value:_n}),Object.defineProperty(this,oi,{value:Fn}),Object.defineProperty(this,Li,{value:Ln}),Object.defineProperty(this,ri,{value:kn}),Object.defineProperty(this,ot,{writable:!0,value:void 0}),Object.defineProperty(this,re,{writable:!0,value:n=>{var l;const c=this.uppy.getFile(n.id);if(!(c!=null&&(l=c.transloadit)!=null&&l.assembly))return;const{assemblies:d}=this.getPluginState(),u=d[c.transloadit.assembly];this.client.addFile(u,c).catch(p=>{this.uppy.log(p),this.uppy.emit("transloadit:import-error",u,c.id,p)})}}),Object.defineProperty(this,hi,{writable:!0,value:async function(n){let{reason:l}=n===void 0?{}:n;try{if(l!=="user")return;const{uploadsAssemblies:c}=i.getPluginState(),u=Object.values(c).flat(1).map(p=>i.getAssembly(p));await Promise.all(u.map(p=>E(i,li)[li](p)))}catch(c){i.uppy.log(c)}}}),Object.defineProperty(this,ci,{writable:!0,value:n=>{const{assemblies:l,uploadsAssemblies:c}=this.getPluginState();n({[this.id]:{assemblies:l,uploadsAssemblies:c}})}}),Object.defineProperty(this,ui,{writable:!0,value:n=>{const l=n&&n[this.id]?n[this.id]:{},c=l.assemblies||{},d=l.uploadsAssemblies||{};if(Object.keys(d).length===0)return;const u=m=>{const y={},g=[];for(const[b,A]of Object.entries(m)){A.uploads.forEach(N=>{const L=E(this,fe)[fe](N);y[N.id]={id:L.id,assembly:b,uploadedFile:N}});const O=this.getPluginState();Object.keys(A.results).forEach(N=>{for(const L of A.results[N]){const f=O.files[L.original_id];L.localId=f?f.id:null,g.push({id:L.id,result:L,stepName:N,assembly:b})}})}this.setPluginState({assemblies:m,files:y,results:g,uploadsAssemblies:d})},p=()=>{const{assemblies:m,uploadsAssemblies:y}=this.getPluginState();Object.keys(y).forEach(b=>{const A=y[b];E(this,se)[se](A,b)}),Object.keys(m).forEach(b=>{const A=new er(m[b],E(this,ot)[ot]);E(this,oe)[oe](A)})},w=()=>{const{assemblies:m}=this.getPluginState();return Promise.all(Object.keys(m).map(y=>this.activeAssemblies[y].update()))};this.restored=Promise.resolve().then(()=>{u(c),p(),w()}),this.restored.then(()=>{this.restored=null})}}),Object.defineProperty(this,ae,{writable:!0,value:async(n,l)=>{const d=n.map(m=>this.uppy.getFile(m)).filter(m=>m.error?!1:(this.uppy.emit("preprocess-progress",m,{mode:"indeterminate",message:this.i18n("creatingAssembly")}),!0)),u=async m=>{let{fileIDs:y,options:g}=m;try{const b=await E(this,oi)[oi](y,l,g);return this.opts.importFromUploadURLs&&await E(this,ni)[ni](b,y),y.forEach(A=>{const O=this.uppy.getFile(A);this.uppy.emit("preprocess-complete",O)}),b}catch(b){throw y.forEach(A=>{const O=this.uppy.getFile(A);this.uppy.emit("preprocess-complete",O),this.uppy.emit("upload-error",O,b)}),b}},{uploadsAssemblies:p}=this.getPluginState();this.setPluginState({uploadsAssemblies:{...p,[l]:[]}}),await new wn(d,this.opts).build().then(m=>Promise.all(m.map(u))).then(m=>{const y=m.filter(Boolean),g=y.map(b=>b.status.assembly_id);return E(this,se)[se](g,l),Promise.all(y.map(b=>E(this,oe)[oe](b)))}).catch(m=>{throw d.forEach(y=>{this.uppy.emit("preprocess-complete",y),this.uppy.emit("upload-error",y,m)}),m})}}),Object.defineProperty(this,kt,{writable:!0,value:(n,l)=>{const c=n.map(g=>this.uppy.getFile(g)),d=c.filter(g=>!g.error).map(g=>g.id),u=this.getPluginState();if(this.restored)return this.restored.then(()=>E(this,kt)[kt](d,l));const p=u.uploadsAssemblies[l],w=()=>{p.forEach(g=>{this.activeAssemblies[g].close(),delete this.activeAssemblies[g]})};if(!E(this,ai)[ai]()){w();const g=p.map(b=>this.getAssembly(b));return this.uppy.addResultData(l,{transloadit:g}),Promise.resolve()}return p.length===0?(this.uppy.addResultData(l,{transloadit:[]}),Promise.resolve()):(c.filter(g=>!Cs(this.completedFiles,g.id)).forEach(g=>{this.uppy.emit("postprocess-progress",g,{mode:"indeterminate",message:this.i18n("encoding")})}),this.assemblyWatchers[l].promise.then(()=>{w();const g=p.map(A=>this.getAssembly(A)),b={...this.getPluginState().uploadsAssemblies};delete b[l],this.setPluginState({uploadsAssemblies:b}),this.uppy.addResultData(l,{transloadit:g})}))}}),Object.defineProperty(this,di,{writable:!0,value:n=>{var l;n&&((l=this.activeAssemblies[n])==null||l.close())}}),Object.defineProperty(this,ne,{writable:!0,value:n=>{this.client.submitError(n).catch(gs(n))}}),Object.defineProperty(this,pi,{writable:!0,value:(n,l)=>{var c,d;if(E(this,di)[di](n==null||(c=n.transloadit)==null?void 0:c.assembly),l!=null&&(d=l.message)!=null&&d.startsWith("tus: ")){var u;const p=(u=l.originalRequest)==null||(u=u.getUnderlyingObject())==null?void 0:u.responseURL;this.client.submitError(l,{endpoint:p}).catch(gs(l))}}}),this.type="uploader",this.id=this.opts.id||"Transloadit",this.defaultLocale=On,(o=(r=this.opts).assemblyOptions)!=null||(r.assemblyOptions=(a=this.opts.getAssemblyOptions)!=null?a:{params:this.opts.params,signature:this.opts.signature,fields:this.opts.fields}),(e==null?void 0:e.params)!=null&&e.getAssemblyOptions==null&&e.assemblyOptions==null&&sr(this.opts.assemblyOptions.params),E(this,ot)[ot]=new yr(this.opts.limit),this.i18nInit(),this.client=new vn({service:this.opts.service,client:E(this,ri)[ri](),errorReporting:this.opts.errorReporting,rateLimitedQueue:E(this,ot)[ot]}),this.activeAssemblies={},this.assemblyWatchers={},this.completedFiles=Object.create(null)}install(){this.uppy.addPreProcessor(E(this,ae)[ae]),this.uppy.addPostProcessor(E(this,kt)[kt]),this.uppy.on("error",E(this,ne)[ne]),this.uppy.on("cancel-all",E(this,hi)[hi]),this.uppy.on("upload-error",E(this,pi)[pi]),this.opts.importFromUploadURLs?this.uppy.on("upload-success",E(this,re)[re]):this.uppy.use(xs,{storeFingerprintForResuming:!1,allowedMetaFields:["assembly_url","filename","fieldname"],limit:this.opts.limit,rateLimitedQueue:E(this,ot)[ot],retryDelays:this.opts.retryDelays}),this.uppy.on("restore:get-data",E(this,ci)[ci]),this.uppy.on("restored",E(this,ui)[ui]),this.setPluginState({assemblies:{},uploadsAssemblies:{},files:{},results:[]});const{capabilities:t}=this.uppy.getState();this.uppy.setState({capabilities:{...t,individualCancellation:!1}})}uninstall(){this.uppy.removePreProcessor(E(this,ae)[ae]),this.uppy.removePostProcessor(E(this,kt)[kt]),this.uppy.off("error",E(this,ne)[ne]),this.opts.importFromUploadURLs&&this.uppy.off("upload-success",E(this,re)[re]);const{capabilities:t}=this.uppy.getState();this.uppy.setState({capabilities:{...t,individualCancellation:!0}})}getAssembly(t){const{assemblies:e}=this.getPluginState();return e[t]}getAssemblyFiles(t){return this.uppy.getFiles().filter(e=>{var i;return(e==null||(i=e.transloadit)==null?void 0:i.assembly)===t})}}function kn(){const s=[`uppy-core:${this.uppy.constructor.VERSION}`,`uppy-transloadit:${this.constructor.VERSION}`,`uppy-tus:${xs.VERSION}`],t=(e,i)=>{const r=this.uppy.getPlugin(e);r&&s.push(`${i}:${r.constructor.VERSION}`)};return this.opts.importFromUploadURLs&&(t("XHRUpload","uppy-xhr-upload"),t("AwsS3","uppy-aws-s3"),t("AwsS3Multipart","uppy-aws-s3-multipart")),t("Dropbox","uppy-dropbox"),t("Box","uppy-box"),t("Facebook","uppy-facebook"),t("GoogleDrive","uppy-google-drive"),t("GooglePhotos","uppy-google-photos"),t("Instagram","uppy-instagram"),t("OneDrive","uppy-onedrive"),t("Zoom","uppy-zoom"),t("Url","uppy-url"),this.opts.clientName!=null&&s.push(this.opts.clientName),s.join(",")}function Ln(s,t){const e={...s.meta,assembly_url:t.assembly_url,filename:s.name,fieldname:"file"},i={...s.tus,endpoint:t.tus_url,addRequestId:!0};let{remote:r}=s;if(s.remote&&Tn.test(s.remote.companionUrl)){const a=t.companion_url.replace(/\/$/,""),n=s.remote.url.replace(s.remote.companionUrl,"").replace(/^\//,"");r={...s.remote,companionUrl:a,url:`${a}/${n}`}}const o={...s,transloadit:{assembly:t.assembly_id}};return this.opts.importFromUploadURLs||Object.assign(o,{meta:e,tus:i,remote:r}),o}function Fn(s,t,e){return this.uppy.log("[Transloadit] Create Assembly"),this.client.createAssembly({...e,expectedFiles:s.length}).then(async i=>{const r=this.uppy.getFiles().filter(p=>{let{id:w}=p;return s.includes(w)});if(r.length===0&&s.length!==0)return await this.client.cancelAssembly(i),null;const o=new er(i,E(this,ot)[ot]),{status:a}=o,n=a.assembly_id,{assemblies:l,uploadsAssemblies:c}=this.getPluginState();this.setPluginState({assemblies:{...l,[n]:a},uploadsAssemblies:{...c,[t]:[...c[t],n]}});const d={};r.forEach(p=>{d[p.id]=E(this,Li)[Li](p,a)}),this.uppy.setState({files:{...this.uppy.getState().files,...d}});const u=(p,w)=>{var m;if(((m=o.status)==null?void 0:m.ok)==="ASSEMBLY_COMPLETED"){this.uppy.off("file-removed",u);return}w==="cancel-all"&&(o.close(),this.uppy.off("file-removed",u))};return this.uppy.on("file-removed",u),this.uppy.emit("transloadit:assembly-created",a,s),this.uppy.log(`[Transloadit] Created Assembly ${n}`),o}).catch(i=>{const r=new Ii(`${this.i18n("creatingAssemblyFailed")}: ${i.message}`,{cause:i});throw"details"in i&&(r.details=i.details),"assembly"in i&&(r.assembly=i.assembly),r})}function _n(s,t){const e=Array.isArray(s)?s:[s],i=new An(this.uppy,e);i.on("assembly-complete",r=>{this.getAssemblyFiles(r).forEach(a=>{this.completedFiles[a.id]=!0,this.uppy.emit("postprocess-complete",a)})}),i.on("assembly-error",(r,o)=>{const a=this.getAssemblyFiles(r);a.forEach(l=>{this.uppy.emit("upload-error",l,o),this.uppy.emit("postprocess-complete",l)});const n={...this.uppy.getState().files};a.forEach(l=>delete n[l.id].tus),this.uppy.setState({files:n}),this.uppy.emit("error",o)}),this.assemblyWatchers[t]=i}function In(){return this.opts.waitForEncoding||this.opts.waitForMetadata}function Nn(s,t){return Promise.all(t.map(e=>{const i=this.uppy.getFile(e);return this.client.reserveFile(s.status,i)}))}function Bn(s){const t=this.uppy.getFiles();for(let e=0;e<t.length;e++){const i=t[e];if(i.uploadURL===s.tus_upload_url||i.tus&&i.tus.uploadUrl===s.tus_upload_url||!s.is_tus_file&&i.name===s.name&&i.size===s.size)return i}}function Un(s,t){const e=this.getPluginState(),i=E(this,fe)[fe](t);if(!i){this.uppy.log("[Transloadit] Couldn’t find the file, it was likely removed in the process");return}this.setPluginState({files:{...e.files,[t.id]:{assembly:s,id:i.id,uploadedFile:t}}}),this.uppy.emit("transloadit:upload",t,this.getAssembly(s))}function zn(s,t,e){const i=this.getPluginState(),r=i.files[e.original_id];e.localId=r?r.id:null;const o={result:e,stepName:t,id:e.id,assembly:s};this.setPluginState({results:[...i.results,o]});const a=this.activeAssemblies[s].status;this.uppy.emit("transloadit:result",t,e,a)}function jn(s){const t=s.assembly_ssl_url;this.client.getAssemblyStatus(t).then(e=>{const i=e.assembly_id,r=this.getPluginState();this.setPluginState({assemblies:{...r.assemblies,[i]:e}}),this.uppy.emit("transloadit:complete",e)})}async function Vn(s){await this.client.cancelAssembly(s),this.uppy.emit("transloadit:assembly-cancelled",s)}function Wn(s){const{status:t}=s,e=t.assembly_id;return this.activeAssemblies[e]=s,s.on("status",i=>{const{assemblies:r}=this.getPluginState();this.setPluginState({assemblies:{...r,[e]:i}})}),s.on("upload",i=>{E(this,Fi)[Fi](e,i)}),s.on("error",i=>{i.assembly=s.status,this.uppy.emit("transloadit:assembly-error",s.status,i)}),s.on("executing",()=>{this.uppy.emit("transloadit:assembly-executing",s.status)}),s.on("execution-progress",i=>{if(this.uppy.emit("transloadit:execution-progress",i),i.progress_combined!=null)for(const r of this.uppy.getFiles())this.uppy.emit("postprocess-progress",r,{mode:"determinate",value:i.progress_combined/100,message:this.i18n("encoding")})}),this.opts.waitForEncoding&&s.on("result",(i,r)=>{E(this,_i)[_i](e,i,r)}),this.opts.waitForEncoding?s.on("finished",()=>{E(this,le)[le](s.status)}):this.opts.waitForMetadata&&s.on("metadata",()=>{E(this,le)[le](s.status)}),s.ok==="ASSEMBLY_COMPLETE"||s.connect(),s}Ee.VERSION=Pn.version;Ee.COMPANION=rr;Ee.COMPANION_PATTERN=or;/*!
 * Compressor.js v1.2.1
 * https://fengyuanchen.github.io/compressorjs
 *
 * Copyright 2018-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2023-02-28T14:09:41.732Z
 */var $n={exports:{}};(function(s){typeof window>"u"||function(t){var e=t.HTMLCanvasElement&&t.HTMLCanvasElement.prototype,i=t.Blob&&function(){try{return!!new Blob}catch{return!1}}(),r=i&&t.Uint8Array&&function(){try{return new Blob([new Uint8Array(100)]).size===100}catch{return!1}}(),o=t.BlobBuilder||t.WebKitBlobBuilder||t.MozBlobBuilder||t.MSBlobBuilder,a=/^data:((.*?)(;charset=.*?)?)(;base64)?,/,n=(i||o)&&t.atob&&t.ArrayBuffer&&t.Uint8Array&&function(l){var c,d,u,p,w,m,y,g,b;if(c=l.match(a),!c)throw new Error("invalid data URI");for(d=c[2]?c[1]:"text/plain"+(c[3]||";charset=US-ASCII"),u=!!c[4],p=l.slice(c[0].length),u?w=atob(p):w=decodeURIComponent(p),m=new ArrayBuffer(w.length),y=new Uint8Array(m),g=0;g<w.length;g+=1)y[g]=w.charCodeAt(g);return i?new Blob([r?y:m],{type:d}):(b=new o,b.append(m),b.getBlob(d))};t.HTMLCanvasElement&&!e.toBlob&&(e.mozGetAsFile?e.toBlob=function(l,c,d){var u=this;setTimeout(function(){d&&e.toDataURL&&n?l(n(u.toDataURL(c,d))):l(u.mozGetAsFile("blob",c))})}:e.toDataURL&&n&&(e.msToBlob?e.toBlob=function(l,c,d){var u=this;setTimeout(function(){(c&&c!=="image/png"||d)&&e.toDataURL&&n?l(n(u.toDataURL(c,d))):l(u.msToBlob(c))})}:e.toBlob=function(l,c,d){var u=this;setTimeout(function(){l(n(u.toDataURL(c,d)))})})),s.exports?s.exports=n:t.dataURLtoBlob=n}(window)})($n);function Le(){return Le=Object.assign||function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(s[i]=e[i])}return s},Le.apply(this,arguments)}var Hn={"[object HTMLCollection]":!0,"[object NodeList]":!0,"[object RadioNodeList]":!0},qn={button:!0,fieldset:!0,reset:!0,submit:!0},Gn={checkbox:!0,radio:!0},Yn=/^\s+|\s+$/g,Xn=Array.prototype.slice,vs=Object.prototype.toString;function ar(s,t){if(!s)throw new Error("A form is required by getFormData, was given form="+s);t=Le({includeDisabled:!1,trim:!1},t);for(var e={},i,r=[],o={},a=0,n=s.elements.length;a<n;a++){var l=s.elements[a];qn[l.type]||l.disabled&&!t.includeDisabled||(i=l.name||l.id,i&&!o[i]&&(r.push(i),o[i]=!0))}for(var c=0,d=r.length;c<d;c++){i=r[c];var u=nr(s,i,t);u!=null&&(e[i]=u)}return e}function nr(s,t,e){if(!s)throw new Error("A form is required by getFieldData, was given form="+s);if(!t&&vs.call(t)!=="[object String]")throw new Error("A field name is required by getFieldData, was given fieldName="+t);e=Le({includeDisabled:!1,trim:!1},e);var i=s.elements[t];if(!i||i.disabled&&!e.includeDisabled)return null;if(!Hn[vs.call(i)])return ys(i,e.trim);for(var r=[],o=!0,a=0,n=i.length;a<n;a++)if(!(i[a].disabled&&!e.includeDisabled)){o&&i[a].type!=="radio"&&(o=!1);var l=ys(i[a],e.trim);l!=null&&(r=r.concat(l))}return o&&r.length===1?r[0]:r.length>0?r:null}function ys(s,t){var e=null,i=s.type;if(i==="select-one")return s.options.length&&(e=s.options[s.selectedIndex].value),e;if(i==="select-multiple"){e=[];for(var r=0,o=s.options.length;r<o;r++)s.options[r].selected&&e.push(s.options[r].value);return e.length===0&&(e=null),e}return i==="file"&&"files"in s?(s.multiple?(e=Xn.call(s.files),e.length===0&&(e=null)):e=s.files[0],e):(Gn[i]?s.checked&&(i==="checkbox"&&!s.hasAttribute("value")?e=!0:e=s.value):e=t?s.value.replace(Yn,""):s.value,e)}ar.getFieldData=nr;const Kn={version:"3.2.2"},Qn={resultName:"uppyResult",getMetaFromForm:!0,addResultToForm:!0,submitOnSuccess:!1,triggerUploadOnSubmit:!1};function Jn(s){if(s==null||s.nodeName!=="FORM")throw new Error("ASSERTION FAILED: the target is not a <form> element",{cause:s});return s}class Zn extends we{constructor(t,e){super(t,{...Qn,...e}),this.type="acquirer",this.id=this.opts.id||"Form",this.handleFormSubmit=this.handleFormSubmit.bind(this),this.handleUploadStart=this.handleUploadStart.bind(this),this.handleSuccess=this.handleSuccess.bind(this),this.addResultToForm=this.addResultToForm.bind(this),this.getMetaFromForm=this.getMetaFromForm.bind(this)}handleUploadStart(){this.opts.getMetaFromForm&&this.getMetaFromForm()}handleSuccess(t){this.opts.addResultToForm&&this.addResultToForm(t),this.opts.submitOnSuccess&&this.form.reportValidity()&&this.form.submit()}handleFormSubmit(t){if(this.opts.triggerUploadOnSubmit){t.preventDefault();const e=Fe(t.target.elements),i=[];e.forEach(r=>{(r.tagName==="BUTTON"||r.tagName==="INPUT"&&r.type==="submit")&&!r.disabled&&(r.disabled=!0,i.push(r))}),this.uppy.upload().then(()=>{i.forEach(r=>{r.disabled=!1})},r=>(i.forEach(o=>{o.disabled=!1}),Promise.reject(r))).catch(r=>{this.uppy.log(r.stack||r.message||r)})}}addResultToForm(t){this.uppy.log("[Form] Adding result to the original form:"),this.uppy.log(t);let e=this.form.querySelector(`[name="${this.opts.resultName}"]`);if(e){let i;try{i=JSON.parse(e.value)}catch{}Array.isArray(i)||(i=[]),i.push(t),e.value=JSON.stringify(i);return}e=document.createElement("input"),e.name=this.opts.resultName,e.type="hidden",e.value=JSON.stringify([t]),this.form.appendChild(e)}getMetaFromForm(){const t=ar(this.form);delete t[this.opts.resultName],this.uppy.setMeta(t)}install(){this.form=Jn(Rr(this.opts.target)),this.form.addEventListener("submit",this.handleFormSubmit),this.uppy.on("upload",this.handleUploadStart),this.uppy.on("complete",this.handleSuccess)}uninstall(){this.form.removeEventListener("submit",this.handleFormSubmit),this.uppy.off("upload",this.handleUploadStart),this.uppy.off("complete",this.handleSuccess)}}Zn.VERSION=Kn.version;function Lt(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var tl=0;function el(s){return"__private_"+tl+++"_"+s}const lr=typeof navigator<"u"&&"serviceWorker"in navigator;function il(){return new Promise((s,t)=>{lr?navigator.serviceWorker.controller?s():navigator.serviceWorker.addEventListener("controllerchange",()=>{s()}):t(new Error("Unsupported"))})}var H=el("ready");class hr{constructor(t){Object.defineProperty(this,H,{writable:!0,value:void 0}),Lt(this,H)[H]=il().then(e=>{Lt(this,H)[H]=e}),this.name=t.storeName}get ready(){return Promise.resolve(Lt(this,H)[H])}set ready(t){Lt(this,H)[H]=t}async list(){return await Lt(this,H)[H],new Promise((t,e)=>{const i=r=>{if(r.data.store===this.name)switch(r.data.type){case"uppy/ALL_FILES":t(r.data.files),navigator.serviceWorker.removeEventListener("message",i);break;default:e()}};navigator.serviceWorker.addEventListener("message",i),navigator.serviceWorker.controller.postMessage({type:"uppy/GET_FILES",store:this.name})})}async put(t){await Lt(this,H)[H],navigator.serviceWorker.controller.postMessage({type:"uppy/ADD_FILE",store:this.name,file:t})}async delete(t){await Lt(this,H)[H],navigator.serviceWorker.controller.postMessage({type:"uppy/REMOVE_FILE",store:this.name,fileID:t})}}hr.isSupported=lr;function lt(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var sl=0;function rl(s){return"__private_"+sl+++"_"+s}const cr=typeof window<"u"&&(window.indexedDB||window.webkitIndexedDB||window.mozIndexedDB||window.OIndexedDB||window.msIndexedDB),ol=!!cr,bs="uppy-blobs",Y="files",ur=24*60*60*1e3,al=3,ws=1048576;function nl(s){const t=s.openCursor();t.onsuccess=e=>{const i=e.target.result;if(!i)return;const r=i.value;r.expires=Date.now()+ur,i.update(r)}}function Ss(s){const t=cr.open(s,al);return new Promise((e,i)=>{t.onupgradeneeded=r=>{const o=r.target.result,a=r.currentTarget.transaction;if(r.oldVersion<2&&o.createObjectStore(Y,{keyPath:"id"}).createIndex("store","store",{unique:!1}),r.oldVersion<3){const n=a.objectStore(Y);n.createIndex("expires","expires",{unique:!1}),nl(n)}a.oncomplete=()=>{e(o)}},t.onsuccess=r=>{e(r.target.result)},t.onerror=i})}function Te(s){return new Promise((t,e)=>{s.onsuccess=i=>{t(i.target.result)},s.onerror=e})}let As=!1;var _=rl("ready");class Be{constructor(t){Object.defineProperty(this,_,{writable:!0,value:void 0}),this.opts={dbName:bs,storeName:"default",expires:ur,maxFileSize:10*ws,maxTotalSize:300*ws,...t},this.name=this.opts.storeName;const e=async()=>{const i=await Ss(this.opts.dbName);return lt(this,_)[_]=i,i};As?lt(this,_)[_]=e():(As=!0,lt(this,_)[_]=Be.cleanup().then(e,e))}get ready(){return Promise.resolve(lt(this,_)[_])}set ready(t){lt(this,_)[_]=t}key(t){return`${this.name}!${t}`}async list(){const r=(await lt(this,_)[_]).transaction([Y],"readonly").objectStore(Y).index("store").getAll(IDBKeyRange.only(this.name)),o=await Te(r);return Object.fromEntries(o.map(a=>[a.fileID,a.data]))}async get(t){const r=(await lt(this,_)[_]).transaction([Y],"readonly").objectStore(Y).get(this.key(t)),{data:o}=await Te(r);return{id:o.fileID,data:o.data}}async getSize(){const r=(await lt(this,_)[_]).transaction([Y],"readonly").objectStore(Y).index("store").openCursor(IDBKeyRange.only(this.name));return new Promise((o,a)=>{let n=0;r.onsuccess=l=>{const c=l.target.result;c?(n+=c.value.data.size,c.continue()):o(n)},r.onerror=()=>{a(new Error("Could not retrieve stored blobs size"))}})}async put(t){if(t.data.size>this.opts.maxFileSize)throw new Error("File is too big to store.");if(await this.getSize()>this.opts.maxTotalSize)throw new Error("No space left");const o=(await lt(this,_)[_]).transaction([Y],"readwrite").objectStore(Y).add({id:this.key(t.id),fileID:t.id,store:this.name,expires:Date.now()+this.opts.expires,data:t.data});return Te(o)}async delete(t){const r=(await lt(this,_)[_]).transaction([Y],"readwrite").objectStore(Y).delete(this.key(t));return Te(r)}static async cleanup(){const t=await Ss(bs),r=t.transaction([Y],"readwrite").objectStore(Y).index("expires").openCursor(IDBKeyRange.upperBound(Date.now()));await new Promise((o,a)=>{r.onsuccess=n=>{const l=n.target.result;l?(l.delete(),l.continue()):o()},r.onerror=a}),t.close()}}Be.isSupported=ol;function ll(){const s=[];for(let t=0;t<localStorage.length;t++){const e=localStorage.key(t);e!=null&&e.startsWith("uppyState:")&&s.push(e.slice(10))}return s}function Rs(s){try{return JSON.parse(s)}catch{return null}}let Es=!1;class Vt{constructor(t){this.opts={expires:24*60*60*1e3,...t},this.name=`uppyState:${t.storeName}`,Es||(Es=!0,Vt.cleanup())}load(){const t=localStorage.getItem(this.name);if(!t)return null;const e=Rs(t);return e?e.metadata:null}save(t){const e=Date.now()+this.opts.expires,i=JSON.stringify({metadata:t,expires:e});localStorage.setItem(this.name,i)}static cleanup(t){if(t){localStorage.removeItem(`uppyState:${t}`);return}const e=ll(),i=Date.now();e.forEach(r=>{const o=localStorage.getItem(`uppyState:${r}`);if(!o)return;const a=Rs(o);a&&a.expires&&a.expires<i&&localStorage.removeItem(`uppyState:${r}`)})}}const hl={version:"3.2.0"},cl={expires:24*60*60*1e3,serviceWorker:!1};class ul extends we{constructor(t,e){super(t,{...cl,...e}),this.addBlobToStores=i=>{i.isRemote||(this.ServiceWorkerStore&&this.ServiceWorkerStore.put(i).catch(r=>{this.uppy.log("[GoldenRetriever] Could not store file","warning"),this.uppy.log(r)}),this.IndexedDBStore.put(i).catch(r=>{this.uppy.log("[GoldenRetriever] Could not store file","warning"),this.uppy.log(r)}))},this.removeBlobFromStores=i=>{this.ServiceWorkerStore&&this.ServiceWorkerStore.delete(i.id).catch(r=>{this.uppy.log("[GoldenRetriever] Failed to remove file","warning"),this.uppy.log(r)}),this.IndexedDBStore.delete(i.id).catch(r=>{this.uppy.log("[GoldenRetriever] Failed to remove file","warning"),this.uppy.log(r)})},this.replaceBlobInStores=i=>{this.removeBlobFromStores(i),this.addBlobToStores(i)},this.handleRestoreConfirmed=()=>{this.uppy.log("[GoldenRetriever] Restore confirmed, proceeding...");const{currentUploads:i}=this.uppy.getState();i&&(this.uppy.resumeAll(),Object.keys(i).forEach(r=>{this.uppy.restore(r)})),this.uppy.setState({recoveredState:null})},this.abortRestore=()=>{this.uppy.log("[GoldenRetriever] Aborting restore...");const i=Object.keys(this.uppy.getState().files);this.deleteBlobs(i).then(()=>{this.uppy.log(`[GoldenRetriever] Removed ${i.length} files`)}).catch(r=>{this.uppy.log(`[GoldenRetriever] Could not remove ${i.length} files`,"warning"),this.uppy.log(r)}),this.uppy.cancelAll(),this.uppy.setState({recoveredState:null}),Vt.cleanup(this.uppy.opts.id)},this.handleComplete=i=>{let{successful:r}=i;const o=r.map(a=>a.id);this.deleteBlobs(o).then(()=>{this.uppy.log(`[GoldenRetriever] Removed ${r.length} files that finished uploading`)}).catch(a=>{this.uppy.log(`[GoldenRetriever] Could not remove ${r.length} files that finished uploading`,"warning"),this.uppy.log(a)}),this.uppy.setState({recoveredState:null}),Vt.cleanup(this.uppy.opts.id)},this.restoreBlobs=()=>{this.uppy.getFiles().length>0?Promise.all([this.loadFileBlobsFromServiceWorker(),this.loadFileBlobsFromIndexedDB()]).then(i=>{const r={...i[0],...i[1]};this.onBlobsLoaded(r)}):this.uppy.log("[GoldenRetriever] No files need to be loaded, only restoring processing state...")},this.type="debugger",this.id=this.opts.id||"GoldenRetriever",this.MetaDataStore=new Vt({expires:this.opts.expires,storeName:t.getID()}),this.ServiceWorkerStore=null,this.opts.serviceWorker&&(this.ServiceWorkerStore=new hr({storeName:t.getID()})),this.IndexedDBStore=new Be({expires:this.opts.expires,...this.opts.indexedDB||{},storeName:t.getID()}),this.saveFilesStateToLocalStorage=br(this.saveFilesStateToLocalStorage.bind(this),500,{leading:!0,trailing:!0}),this.restoreState=this.restoreState.bind(this),this.loadFileBlobsFromServiceWorker=this.loadFileBlobsFromServiceWorker.bind(this),this.loadFileBlobsFromIndexedDB=this.loadFileBlobsFromIndexedDB.bind(this),this.onBlobsLoaded=this.onBlobsLoaded.bind(this)}restoreState(){const t=this.MetaDataStore.load();t&&(this.uppy.log("[GoldenRetriever] Recovered some state from Local Storage"),this.uppy.setState({currentUploads:t.currentUploads||{},files:t.files||{},recoveredState:t}),this.savedPluginData=t.pluginData)}getWaitingFiles(){const t={};return this.uppy.getFiles().forEach(e=>{(!e.progress||!e.progress.uploadStarted)&&(t[e.id]=e)}),t}getUploadingFiles(){const t={},{currentUploads:e}=this.uppy.getState();return e&&Object.keys(e).forEach(r=>{e[r].fileIDs.forEach(a=>{t[a]=this.uppy.getFile(a)})}),t}saveFilesStateToLocalStorage(){const t={...this.getWaitingFiles(),...this.getUploadingFiles()},e=Object.entries(t);if(e.length===0){this.uppy.getState().recoveredState!==null&&this.uppy.setState({recoveredState:null}),Vt.cleanup(this.uppy.opts.id);return}const i=Object.fromEntries(e.map(a=>{let[n,l]=a;return[n,l.isRemote?{...l,isRestored:!0}:{...l,isRestored:!0,data:null,preview:null}]})),r={};this.uppy.emit("restore:get-data",a=>{Object.assign(r,a)});const{currentUploads:o}=this.uppy.getState();this.MetaDataStore.save({currentUploads:o,files:i,pluginData:r})}loadFileBlobsFromServiceWorker(){return this.ServiceWorkerStore?this.ServiceWorkerStore.list().then(t=>{const e=Object.keys(t).length;return e>0?(this.uppy.log(`[GoldenRetriever] Successfully recovered ${e} blobs from Service Worker!`),t):(this.uppy.log("[GoldenRetriever] No blobs found in Service Worker, trying IndexedDB now..."),{})}).catch(t=>(this.uppy.log("[GoldenRetriever] Failed to recover blobs from Service Worker","warning"),this.uppy.log(t),{})):Promise.resolve({})}loadFileBlobsFromIndexedDB(){return this.IndexedDBStore.list().then(t=>{const e=Object.keys(t).length;return e>0?(this.uppy.log(`[GoldenRetriever] Successfully recovered ${e} blobs from IndexedDB!`),t):(this.uppy.log("[GoldenRetriever] No blobs found in IndexedDB"),{})}).catch(t=>(this.uppy.log("[GoldenRetriever] Failed to recover blobs from IndexedDB","warning"),this.uppy.log(t),{}))}onBlobsLoaded(t){const e=[],i={...this.uppy.getState().files};Object.keys(t).forEach(r=>{const o=this.uppy.getFile(r);if(!o){e.push(r);return}const n={data:t[r],isRestored:!0,isGhost:!1};i[r]={...o,...n}}),Object.keys(i).forEach(r=>{i[r].data===null&&(i[r]={...i[r],isGhost:!0})}),this.uppy.setState({files:i}),this.uppy.emit("restored",this.savedPluginData),e.length&&this.deleteBlobs(e).then(()=>{this.uppy.log(`[GoldenRetriever] Cleaned up ${e.length} old files`)}).catch(r=>{this.uppy.log(`[GoldenRetriever] Could not clean up ${e.length} old files`,"warning"),this.uppy.log(r)})}async deleteBlobs(t){await Promise.all(t.map(e=>{var i,r,o;return(i=(r=this.ServiceWorkerStore)==null?void 0:r.delete(e))!=null?i:(o=this.IndexedDBStore)==null?void 0:o.delete(e)}))}install(){this.restoreState(),this.restoreBlobs(),this.uppy.on("file-added",this.addBlobToStores),this.uppy.on("file-editor:complete",this.replaceBlobInStores),this.uppy.on("file-removed",this.removeBlobFromStores),this.uppy.on("state-update",this.saveFilesStateToLocalStorage),this.uppy.on("restore-confirmed",this.handleRestoreConfirmed),this.uppy.on("restore-canceled",this.abortRestore),this.uppy.on("complete",this.handleComplete)}uninstall(){this.uppy.off("file-added",this.addBlobToStores),this.uppy.off("file-editor:complete",this.replaceBlobInStores),this.uppy.off("file-removed",this.removeBlobFromStores),this.uppy.off("state-update",this.saveFilesStateToLocalStorage),this.uppy.off("restore-confirmed",this.handleRestoreConfirmed),this.uppy.off("restore-canceled",this.abortRestore),this.uppy.off("complete",this.handleComplete)}}ul.VERSION=hl.version;const dl={version:"3.0.3"};class pl extends et{constructor(t,e){super(t,e),this.type="debugger",this.id=this.opts.id||"ReduxDevTools",this.title="Redux DevTools";const i={};this.opts={...i,...e},this.handleStateChange=this.handleStateChange.bind(this),this.initDevTools=this.initDevTools.bind(this)}handleStateChange(t,e){this.devTools.send("UPPY_STATE_UPDATE",e)}initDevTools(){this.devTools=window.devToolsExtension.connect(),this.devToolsUnsubscribe=this.devTools.subscribe(t=>{if(t.type==="DISPATCH")switch(t.payload.type){case"RESET":this.uppy.cancelAll();return;case"IMPORT_STATE":{const{computedStates:e}=t.payload.nextLiftedState;this.uppy.store.state={...this.uppy.getState(),...e[e.length-1].state},this.uppy.updateAll(this.uppy.getState());return}case"JUMP_TO_STATE":case"JUMP_TO_ACTION":this.uppy.store.state={...this.uppy.getState(),...JSON.parse(t.state)},this.uppy.updateAll(this.uppy.getState());break}})}install(){this.withDevTools=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__,this.withDevTools&&(this.initDevTools(),this.uppy.on("state-update",this.handleStateChange))}uninstall(){this.withDevTools&&(this.devToolsUnsubscribe(),this.uppy.off("state-update",this.handleStateUpdate))}}pl.VERSION=dl.version;Ee.COMPANION_URL=rr;Ee.COMPANION_ALLOWED_HOSTS=or;
