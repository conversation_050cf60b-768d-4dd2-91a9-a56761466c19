import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { GlobalContext, showToast } from "Src/globalContext";
import { editTeamDetailsAPI } from "Src/services/clientProjectDetailsService";
import { ClipLoader } from "react-spinners";

const ClientEditMusicDetailsModal = (props) => {
  const { isOpen, setIsOpen, data, setData, getData } = props;
  const projectId = useParams();

  const [loader, setLoader] = useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [formValues, setFormValues] = React.useState({
    notes: data?.notes || "",
    song_list: data?.song_list || "",
    // competitions: data.competitions || "",
    colors: data?.colors || "",
    mascot: data?.mascot || "",
    twitter:
      data && data.social_media
        ? JSON.parse(data.social_media)?.twitter || ""
        : "",
    instagram:
      data && data.social_media
        ? JSON.parse(data.social_media)?.instagram || ""
        : "",

    // theme: data?.theme || "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const submit = async (e) => {
    e.preventDefault();
    try {
      setLoader(true);
      const { data } = await editTeamDetailsAPI({
        project_id: projectId.id,

        social_media: JSON.stringify({
          instagram: formValues.instagram,
          twitter: formValues.twitter,
        }),
        song_list: formValues.song_list,
        ...formValues,
      });

      await getData();
      setLoader(false);
      showToast(globalDispatch, "Music Updated");

      setIsOpen(false);
    } catch (error) {
      setIsOpen(false);
      setLoader(false);
      showToast(globalDispatch, "Music Update Failed", "error");
    }
  };

  return (
    <>
      <div className="fixed inset-0 z-10 overflow-y-auto">
        <div
          className="fixed inset-0 h-full w-full bg-black opacity-40"
          onClick={() => setIsOpen(false)}
        ></div>
        <div className="flex min-h-screen items-center px-4 py-8">
          <div className="relative mx-auto w-full max-w-lg rounded-md bg-white p-4 shadow-lg">
            <form className="mt-3 flex flex-col" onSubmit={submit}>
              <div className="flex w-full justify-between">
                <h3 className="text-xl font-bold text-black">
                  Edit Team Details
                </h3>
                <FontAwesomeIcon
                  icon="close"
                  className="h-6 w-6 cursor-pointer text-gray-600"
                  onClick={() => setIsOpen(false)}
                />
              </div>
              <div className="mt-7 flex h-[100px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex h-full items-center justify-center bg-gray-400 px-3">
                  Notes
                </div>
                <textarea
                  type="text"
                  name="notes"
                  value={formValues?.notes}
                  onChange={handleChange}
                  className="block h-[100px] max-h-[100px] w-full resize-none border-transparent bg-transparent py-2 pl-3 text-black outline-none focus:ring-0 focus-visible:outline-transparent"
                  placeholder="Full routine.Dance Section"
                />
              </div>

              {/* <div className="mt-5 flex h-[45px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex justify-center items-center px-3 h-full bg-gray-400">
                  Competitions
                </div>
                <input
                  type="text"
                  name="competitions"
                  value={formValues?.competitions}
                  onChange={handleChange}
                  className="block py-2 pl-3 w-full text-black bg-transparent border-transparent outline-none focus:ring-0 focus-visible:outline-transparent"
                />
              </div> */}

              {/* <div className="mt-5 flex h-[45px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex justify-center items-center px-3 h-full bg-gray-400">
                  Theme
                </div>
                <input
                  type="text"
                  name="theme"
                  value={formValues?.theme}
                  onChange={handleChange}
                  className="block py-2 pl-3 w-full text-black bg-transparent border-transparent outline-none focus:ring-0 focus-visible:outline-transparent"
                />
              </div> */}
              <div className="mt-5 flex h-[45px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex h-full items-center justify-center bg-gray-400 px-3">
                  Mascot
                </div>
                <input
                  type="text"
                  onChange={handleChange}
                  name="mascot"
                  value={formValues?.mascot}
                  className="block w-full border-transparent bg-transparent py-2 pl-3 text-black outline-none focus:ring-0 focus-visible:outline-transparent"
                />
              </div>
              <div className="mt-5 flex h-[45px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex h-full items-center justify-center bg-gray-400 px-3">
                  Twitter
                </div>
                <input
                  type="url"
                  name="twitter"
                  placeholder="Twitter Profile Link"
                  value={formValues?.twitter}
                  onChange={handleChange}
                  className="block w-full border-transparent bg-transparent py-2 pl-3 text-black outline-none focus:ring-0 focus-visible:outline-transparent"
                />
              </div>
              <div className="mt-5 flex h-[45px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex h-full items-center justify-center bg-gray-400 px-3">
                  Instagram
                </div>
                <input
                  type="url"
                  placeholder="Instagram Profile Link"
                  onChange={handleChange}
                  name="instagram"
                  value={formValues?.instagram}
                  className="block w-full border-transparent bg-transparent py-2 pl-3 text-black outline-none focus:ring-0 focus-visible:outline-transparent"
                />
              </div>

              <div className="mt-8 flex w-full items-center justify-end gap-5">
                <button className=" w-[71px] rounded bg-blue-600 px-2 py-1 text-sm font-semibold text-white hover:bg-blue-700 lg:px-3 lg:py-2">
                  {loader ? (
                    <ClipLoader size={12} color="white" />
                  ) : (
                    <span>Submit</span>
                  )}
                </button>
                <button
                  className="w-fit rounded bg-gray-500 px-2 py-1 text-sm font-semibold text-white hover:bg-gray-400 lg:px-3 lg:py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Close
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default ClientEditMusicDetailsModal;
