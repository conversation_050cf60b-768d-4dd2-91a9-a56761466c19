import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const VideoUploadBox = ({ setFileValues, fileValues, isUploading }) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = [...e.dataTransfer.files];
    if (files.length > 0) {
      setFileValues(files);
    }
  };

  const handleFileSelect = (e) => {
    const files = [...e.target.files];
    if (files.length > 0) {
      setFileValues(files);
    }
  };

  return (
    <div className="w-full">
      <div
        className={`relative flex min-h-[200px] cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-form-strokedark bg-transparent p-6 transition-all ${
          isDragging ? "border-primary bg-primary/5" : ""
        }`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {fileValues && fileValues.length > 0 ? (
          <div className="flex w-full flex-col items-center gap-3">
            <FontAwesomeIcon
              icon="fa-solid fa-video"
              className="text-4xl text-primary"
            />
            <p className="text-sm text-bodydark2">{fileValues[0].name}</p>
            <button
              onClick={() => setFileValues([])}
              className="text-sm text-danger hover:text-danger/80"
            >
              Remove
            </button>
          </div>
        ) : (
          <>
            <FontAwesomeIcon
              icon="fa-solid fa-cloud-arrow-up"
              className="mb-3 text-4xl text-primary"
            />
            <p className="mb-2 text-center text-sm text-white">
              Drag file to this area to upload
            </p>
            <p className="mb-4 text-center text-xs text-bodydark2">or</p>
            <label className="inline-flex cursor-pointer items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90">
              <input
                type="file"
                className="hidden"
                accept="video/*"
                onChange={handleFileSelect}
                disabled={isUploading}
              />
              Select File
            </label>
            <p className="mt-4 text-center text-xs text-bodydark2">
              Maximum file size: 500MB
            </p>
          </>
        )}
      </div>
    </div>
  );
};

export default VideoUploadBox;
