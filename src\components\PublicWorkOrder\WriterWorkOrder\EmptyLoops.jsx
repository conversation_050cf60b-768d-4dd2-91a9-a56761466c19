import React from "react";
import FileUpload from "Components/FileUpload/FileUpload";

const EmptyLoops = ({
  canUpload = true,
  setEmployeeType,
  setFileUploadType,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  return (
    <div
      onClick={() => {
        setEmployeeType("writer");
        setFileUploadType("instrumental");
      }}
      className="flex h-full flex-col rounded border border-strokedark bg-boxdark p-6"
    >
      {canUpload ? (
        <div className="flex flex-1 flex-col items-center justify-center">
          <div className="mb-6 text-center">
            <h3 className="mb-2 text-xl font-semibold text-white">
              Writer Loops
            </h3>
            <p className="text-sm text-bodydark2">
              Upload loops and sessions for writing
            </p>
          </div>

          <div className="w-full max-w-md">
            <FileUpload
              uploadedFilesProgressData={uploadedFilesProgressData}
              justify="center"
              items="center"
              maxFileSize={500}
              setFormData={setFormData}
            />
          </div>
        </div>
      ) : (
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <svg
              className="mx-auto mb-4 h-12 w-12 text-boxdark-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
            <p className="text-base font-medium text-bodydark">
              No Sessions Uploaded
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmptyLoops;
