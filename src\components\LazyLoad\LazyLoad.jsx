import React, { memo, Suspense } from "react";
import { SkeletonLoader as Skeleton } from "Components/Skeleton";

const LazyLoad = ({
  children,
  counts = [1],
  count = 1,
  // className,
  circle = false,
  brand = false,
}) => {
  const childrenArray = React.Children.toArray(children).filter(Boolean);
  const className = childrenArray.filter(Boolean)[0]?.props?.className
    ? childrenArray[0]?.props?.className
    : "";
  // console.log("childrenArray >>", childrenArray);
  // console.log("className >>", className);

  return (
    <Suspense
      fallback={
        brand ? (
          <div className="flex h-svh max-h-svh min-h-svh w-full min-w-full max-w-full items-center justify-center bg-black">
            <LazyLoad>
              {/* <UpdatestackLogoInvertIcon className="!h-[3.25rem] !w-[23.8519rem]" /> */}
            </LazyLoad>
          </div>
        ) : (
          <Skeleton
            counts={counts}
            count={count}
            className={className}
            circle={circle}
          />
        )
      }
    >
      {children}
    </Suspense>
  );
};

export default memo(LazyLoad);
