import{r as q}from"../vendor-3aca5368.js";var j={exports:{}},S={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Y=q,J=Symbol.for("react.element"),K=Symbol.for("react.fragment"),X=Object.prototype.hasOwnProperty,G=Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Q={key:!0,ref:!0,__self:!0,__source:!0};function I(t,e,n){var o,r={},s=null,i=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(i=e.ref);for(o in e)X.call(e,o)&&!Q.hasOwnProperty(o)&&(r[o]=e[o]);if(t&&t.defaultProps)for(o in e=t.defaultProps,e)r[o]===void 0&&(r[o]=e[o]);return{$$typeof:J,type:t,key:s,ref:i,props:r,_owner:G.current}}S.Fragment=K;S.jsx=I;S.jsxs=I;j.exports=S;var lt=j.exports;const Z=Math.min,P=Math.max,x=Math.round,R=Math.floor,O=t=>({x:t,y:t});function tt(t){const{x:e,y:n,width:o,height:r}=t;return{width:o,height:r,top:n,left:e,right:e+o,bottom:n+r,x:e,y:n}}function L(){return typeof window<"u"}function V(t){return H(t)?(t.nodeName||"").toLowerCase():"#document"}function p(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function k(t){var e;return(e=(H(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function H(t){return L()?t instanceof Node||t instanceof p(t).Node:!1}function T(t){return L()?t instanceof Element||t instanceof p(t).Element:!1}function D(t){return L()?t instanceof HTMLElement||t instanceof p(t).HTMLElement:!1}function W(t){return!L()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof p(t).ShadowRoot}function $(t){const{overflow:e,overflowX:n,overflowY:o,display:r}=M(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(r)}function et(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function nt(t){return["html","body","#document"].includes(V(t))}function M(t){return p(t).getComputedStyle(t)}function ot(t){if(V(t)==="html")return t;const e=t.assignedSlot||t.parentNode||W(t)&&t.host||k(t);return W(e)?e.host:e}function U(t){const e=ot(t);return nt(e)?t.ownerDocument?t.ownerDocument.body:t.body:D(e)&&$(e)?e:U(e)}function _(t,e,n){var o;e===void 0&&(e=[]),n===void 0&&(n=!0);const r=U(t),s=r===((o=t.ownerDocument)==null?void 0:o.body),i=p(r);if(s){const c=F(i);return e.concat(i,i.visualViewport||[],$(r)?r:[],c&&n?_(c):[])}return e.concat(r,_(r,[],n))}function F(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function rt(t){const e=M(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const r=D(t),s=r?t.offsetWidth:n,i=r?t.offsetHeight:o,c=x(n)!==s||x(o)!==i;return c&&(n=s,o=i),{width:n,height:o,$:c}}function z(t){return T(t)?t:t.contextElement}function N(t){const e=z(t);if(!D(e))return O(1);const n=e.getBoundingClientRect(),{width:o,height:r,$:s}=rt(e);let i=(s?x(n.width):n.width)/o,c=(s?x(n.height):n.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const it=O(0);function st(t){const e=p(t);return!et()||!e.visualViewport?it:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function ct(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==p(t)?!1:e}function B(t,e,n,o){e===void 0&&(e=!1),n===void 0&&(n=!1);const r=t.getBoundingClientRect(),s=z(t);let i=O(1);e&&(o?T(o)&&(i=N(o)):i=N(t));const c=ct(s,n,o)?st(s):O(0);let a=(r.left+c.x)/i.x,l=(r.top+c.y)/i.y,v=r.width/i.x,g=r.height/i.y;if(s){const y=p(s),d=o&&T(o)?p(o):o;let w=y,f=F(w);for(;f&&o&&d!==w;){const m=N(f),u=f.getBoundingClientRect(),h=M(f),b=u.left+(f.clientLeft+parseFloat(h.paddingLeft))*m.x,E=u.top+(f.clientTop+parseFloat(h.paddingTop))*m.y;a*=m.x,l*=m.y,v*=m.x,g*=m.y,a+=b,l+=E,w=p(f),f=F(w)}}return tt({width:v,height:g,x:a,y:l})}function ft(t,e){let n=null,o;const r=k(t);function s(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function i(c,a){c===void 0&&(c=!1),a===void 0&&(a=1),s();const{left:l,top:v,width:g,height:y}=t.getBoundingClientRect();if(c||e(),!g||!y)return;const d=R(v),w=R(r.clientWidth-(l+g)),f=R(r.clientHeight-(v+y)),m=R(l),h={rootMargin:-d+"px "+-w+"px "+-f+"px "+-m+"px",threshold:P(0,Z(1,a))||1};let b=!0;function E(A){const C=A[0].intersectionRatio;if(C!==a){if(!b)return i();C?i(!1,C):o=setTimeout(()=>{i(!1,1e-7)},1e3)}b=!1}try{n=new IntersectionObserver(E,{...h,root:r.ownerDocument})}catch{n=new IntersectionObserver(E,h)}n.observe(t)}return i(!0),s}function at(t,e,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:a=!1}=o,l=z(t),v=r||s?[...l?_(l):[],..._(e)]:[];v.forEach(u=>{r&&u.addEventListener("scroll",n,{passive:!0}),s&&u.addEventListener("resize",n)});const g=l&&c?ft(l,n):null;let y=-1,d=null;i&&(d=new ResizeObserver(u=>{let[h]=u;h&&h.target===l&&d&&(d.unobserve(e),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var b;(b=d)==null||b.observe(e)})),n()}),l&&!a&&d.observe(l),d.observe(e));let w,f=a?B(t):null;a&&m();function m(){const u=B(t);f&&(u.x!==f.x||u.y!==f.y||u.width!==f.width||u.height!==f.height)&&n(),f=u,w=requestAnimationFrame(m)}return n(),()=>{var u;v.forEach(h=>{r&&h.removeEventListener("scroll",n),s&&h.removeEventListener("resize",n)}),g==null||g(),(u=d)==null||u.disconnect(),d=null,a&&cancelAnimationFrame(w)}}export{at as a,lt as j};
