import React from 'react';
import SubProject from './SubProject';

const SubProjects = ({
  canUpload = true,
  subProjects,
  setLyrics,
  setDeleteFileId,
  workOrderDetails,
  setSubProjectDetails,
}) => {
  return (
    <>
      {subProjects &&
        subProjects.length > 0 &&
        subProjects.map((subProject, index) => {
          return (
            <SubProject
              key={index}
              canUpload={canUpload}
              workOrderDetails={workOrderDetails}
              subProject={subProject}
              uploadedFiles={subProject.masters}
              setDeleteFileId={setDeleteFileId}
              setLyrics={setLyrics}
              setSubProjectDetails={setSubProjectDetails}
            />
          );
        })}
    </>
  );
};

export default SubProjects;
