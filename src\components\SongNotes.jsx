import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const SongNotes = ({ notes, setSubmitForm }) => {
  const [localNotes, setLocalNotes] = React.useState(notes ?? '');

  const handleSubmitNotes = (e) => {
    e.preventDefault();
    if (localNotes === '' || localNotes === null) {
      alert('Please enter notes');
      return;
    }
    setSubmitForm(localNotes);
  };

  return (
    <span className='flex flex-row items-center gap-2'>
      <input
        className='block w-32 rounded-lg border border-gray-600 bg-gray-700 p-2 text-white placeholder-gray-400 sm:text-xs'
        type='text'
        name='notes'
        value={localNotes}
        onChange={(e) => setLocalNotes(e.target.value)}
        placeholder='Add notes here'
      />
      <FontAwesomeIcon
        className='cursor-pointer text-blue-600 hover:text-blue-700'
        icon='cloud-upload-alt'
        onClick={(e) => handleSubmitNotes(e)}
      />
    </span>
  );
};

export default SongNotes;
