import React from "react";
import { <PERSON>lip<PERSON>oader } from "react-spinners";
import { GlobalContext } from "../../globalContext";
import { AuthContext } from "../../authContext";
import { getAllTracksClientAPI } from "../../services/countTracksService";
import ClientSingleTrack from "Components/Client/ClientSingleTrack";

const CountTracksClient = () => {
  const [videoList, setVideoList] = React.useState([]);
  const [loader, setLoader] = React.useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const getData = async () => {
    setLoader(true);
    const result = await getAllTracksClientAPI(
      localStorage.getItem("userClientId")
    );
    setLoader(false);
    if (!result.error) {
      setVideoList(result.list);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "count-tracks",
      },
    });
    getData();
  }, []);

  return (
    <div className="max-w-screen h-full p-4 md:p-4 lg:p-6 xl:p-8">
      <div className="border-strokedark bg-boxdark">
        <div className="px-4 pt-8 2xl:px-9">
          <h3 className="mb-4 text-2xl font-bold text-white">Count Tracks</h3>
        </div>

        {videoList.map((elem, index) => (
          <div
            key={index}
            className="shadow-default mb-4 rounded border border-strokedark bg-boxdark dark:border-strokedark dark:bg-boxdark"
          >
            {/* Company Info Header */}
            <div className="border-b border-strokedark px-4 py-4 sm:px-6 2xl:px-9 dark:border-strokedark">
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center gap-3">
                  <h4 className="text-2xl font-semibold text-white dark:text-white">
                    {elem.company_name}
                  </h4>
                  <span className="text-white dark:text-white">-</span>
                  <div>
                    <span className="font-medium text-white dark:text-white">
                      {elem.first_name} {elem.last_name}
                    </span>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {elem.office_email}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Tracks Table */}
            <div className="p-4 md:p-6 2xl:p-10">
              <div className="max-h-[380px] overflow-y-auto">
                <table className="w-full table-auto">
                  <thead className="bg-meta-4">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                        Title
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                        Description
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                        Listen
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                        Download
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-strokedark text-white">
                    {loader ? (
                      <tr>
                        <td colSpan="4" className="text-center">
                          <div className="flex items-center justify-start gap-3 px-8 py-6 text-center">
                            <ClipLoader size={20} color="white" />
                            <span className="animate-pulse text-xl font-semibold text-white ease-out">
                              Loading Tracks...
                            </span>
                          </div>
                        </td>
                      </tr>
                    ) : elem.tracks.length === 0 ? (
                      <tr>
                        <td colSpan="4">
                          <div className="p-4 text-center text-white">
                            No Tracks found!
                          </div>
                        </td>
                      </tr>
                    ) : (
                      elem.tracks.map((track, trackIndex) => (
                        <ClientSingleTrack key={trackIndex} track={track} />
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ))}

        {/* Show loader when no companies are loaded yet */}
        {loader && videoList.length === 0 && (
          <div className="shadow-default rounded border border-strokedark bg-boxdark p-4 text-center dark:border-strokedark dark:bg-boxdark">
            <div className="flex items-center justify-center gap-3">
              <ClipLoader size={20} color="white" />
              <span className="animate-pulse text-xl font-semibold text-white ease-out">
                Loading Tracks...
              </span>
            </div>
          </div>
        )}

        {/* Show message when no data is found */}
        {!loader && videoList.length === 0 && (
          <div className="shadow-default rounded border border-strokedark bg-boxdark p-4 text-center text-white dark:border-strokedark dark:bg-boxdark">
            No Tracks found!
          </div>
        )}
      </div>
    </div>
  );
};

export default CountTracksClient;
