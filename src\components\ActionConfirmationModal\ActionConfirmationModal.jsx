import React, { useEffect } from "react";
import { LazyLoad } from "Components/LazyLoad";
import { Modal } from "Components/Modal";
import { ActionConfirmation } from "Components/ActionConfirmation";

export const ActionConfirmationModal = ({
  data = { id: null },
  options = { endpoint: null, method: "GET" },
  onSuccess,
  onClose,
  multiple = false,
  action = "",
  mode = "create",
  table = "",
  title = "",
  input = "input",
  isOpen = false,
  inputConfirmation = true,
  disableCancel = false,
  modalClasses = {
    modalDialog:
      "max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",
    modal: "h-full",
  },
  customMessage = "",
  inputType = "text",
  initialValue = "",
}) => {
  return (
    <LazyLoad>
      <Modal
        isOpen={isOpen}
        modalCloseClick={onClose}
        title={title}
        modalHeader
        classes={modalClasses}
        disableCancel={disableCancel}
      >
        {isOpen && (
          <LazyLoad>
            <ActionConfirmation
              data={data}
              mode={mode}
              input={input}
              table={table}
              action={action}
              onClose={onClose}
              options={options}
              multiple={multiple}
              onSuccess={onSuccess}
              inputType={inputType}
              initialValue={initialValue}
              disableCancel={disableCancel}
              customMessage={customMessage}
              inputConfirmation={inputConfirmation}
            />
          </LazyLoad>
        )}
      </Modal>
    </LazyLoad>
  );
};

export default ActionConfirmationModal;
