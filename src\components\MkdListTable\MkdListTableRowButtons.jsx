import React from "react";
import { LazyLoad } from "Components/LazyLoad";
import AddButton from "Components/AddButton";
import { processBind } from "./MkdListTableBindOperations";

const MkdListTableRowDropdown = ({
  row,
  columns,
  actions,
  actionId = "id",
  setDeleteId = null,
}) => {
  return (
    <>
      <div className="flex relative gap-2 items-center z-3 h-fit w-fit">
        <LazyLoad>
          {Object.keys(actions)
            .filter(
              (key) =>
                actions[key]?.show &&
                actions[key]?.locations &&
                actions[key]?.locations?.length &&
                actions[key]?.locations?.includes("buttons")
            )
            .map((key, keyIndex) => {
              if (actions[key]?.bind) {
                switch (actions[key]?.bind?.action) {
                  case "hide":
                    if (!processBind(actions[key], row)) {
                      return (
                        <LazyLoad key={keyIndex}>
                          <AddButton
                            key={keyIndex}
                            title={actions[key]?.children ?? key}
                            onClick={() => {
                              if (["delete"].includes(key)) {
                                if (setDeleteId) {
                                  setDeleteId(row[actionId]);
                                }
                              } else if (actions[key]?.action) {
                                actions[key]?.action([row[actionId]]);
                              }
                            }}
                            showPlus={false}
                            className={`!border-soft-200 !bg-brown-main-bg  !flex !h-[2rem] !w-[2.0713rem] !justify-center !text-black !shadow-none `}
                          >
                            {actions[key]?.icon ? actions[key]?.icon : null}

                            {actions[key]?.showChildren
                              ? actions[key]?.children
                                ? actions[key]?.children
                                : key
                              : null}
                          </AddButton>
                        </LazyLoad>
                      );
                    }
                }
              }
              if (!actions[key]?.bind) {
                return (
                  <LazyLoad key={keyIndex}>
                    <AddButton
                      key={keyIndex}
                      title={actions[key]?.children ?? key}
                      onClick={() => {
                        if (["delete"].includes(key) && !actions[key]?.action) {
                          if (setDeleteId) {
                            setDeleteId(row[actionId]);
                          }
                        } else if (actions[key]?.action) {
                          actions[key]?.action([row[actionId]]);
                        }
                        // if (actions[key]?.action) {
                        //   actions[key]?.action([row[actionId]]);
                        // }
                      }}
                      showPlus={false}
                      className={`!border-soft-200 !bg-brown-main-bg !flex !h-[2rem] !w-[2.0713rem] !justify-center !text-black !shadow-none `}
                    >
                      {actions[key]?.icon ? actions[key]?.icon : null}

                      {actions[key]?.showChildren
                        ? actions[key]?.children
                          ? actions[key]?.children
                          : key
                        : null}
                    </AddButton>
                  </LazyLoad>
                );
              }
            })}
        </LazyLoad>
      </div>
    </>
  );
};

export default MkdListTableRowDropdown;
