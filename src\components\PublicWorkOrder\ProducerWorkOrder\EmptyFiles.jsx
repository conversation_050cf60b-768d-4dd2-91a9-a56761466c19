import React from 'react';
import FileUpload from 'Components/FileUpload/FileUpload';

const EmptyFiles = ({
  canUpload = true,
  setEmployeeType,
  setFileUploadType,
  setFormData,
}) => {
  return (
    <div
      onClick={() => {
        setEmployeeType('producer');
        setFileUploadType('instrumental');
      }}
      className='mb-2 block w-full max-w-5xl rounded-md border border-gray-500 bg-gray-800 p-5 shadow'
    >
      {canUpload && (
        <div className='flex flex-col'>
          <div className='mb-2 text-center text-xl font-semibold text-white'>
            Upload Files
          </div>

          <FileUpload
            justify='center'
            items='center'
            maxFileSize={2048}
            setFormData={setFormData}
          />
        </div>
      )}
    </div>
  );
};

export default EmptyFiles;
