import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const AudioPlayer2 = ({ fileSource }) => {
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(0);
  const [duration, setDuration] = React.useState(0);
  const audioRef = React.useRef(null);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleLoadedMetadata = () => {
    setDuration(audioRef.current.duration);
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e) => {
    const time = e.target.value;
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  return (
    <div className="flex w-full max-w-[300px] items-center gap-2 rounded border border-stroke bg-boxdark-2 p-1.5">
      {/* Play/Pause Button */}
      <button
        onClick={handlePlayPause}
        className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-white hover:bg-opacity-90"
      >
        <FontAwesomeIcon
          icon={isPlaying ? "fa-solid fa-pause" : "fa-solid fa-play"}
          className="h-2.5 w-2.5"
        />
      </button>

      {/* Time and Progress */}
      <div className="flex flex-1 flex-col gap-0.5">
        {/* Progress Bar */}
        <div className="relative h-1 w-full">
          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentTime}
            onChange={handleSeek}
            className="absolute h-1 w-full cursor-pointer appearance-none rounded-full bg-boxdark accent-primary hover:accent-primary/90"
          />
        </div>

        {/* Time Display */}
        <div className="flex justify-between text-[10px] text-bodydark2">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Volume Control - Simplified */}
      <div className="group relative">
        <button className="flex h-6 w-6 items-center justify-center text-bodydark2 hover:text-white">
          <FontAwesomeIcon icon="fa-solid fa-volume-high" className="h-3 w-3" />
        </button>
        <div className="invisible absolute bottom-full right-0 mb-2 opacity-0 transition-all group-hover:visible group-hover:opacity-100">
          <div className="h-20 w-6 rounded border border-stroke bg-boxdark-2 p-1.5">
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              defaultValue="1"
              onChange={(e) => (audioRef.current.volume = e.target.value)}
              className="h-full w-1 cursor-pointer appearance-none rounded-full bg-boxdark accent-primary hover:accent-primary/90"
              style={{
                writingMode: "bt-lr",
                transform: "rotate(-90deg) translateY(2px)",
              }}
            />
          </div>
        </div>
      </div>

      <audio
        ref={audioRef}
        src={fileSource}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => setIsPlaying(false)}
        className="hidden"
      />
    </div>
  );
};

export default AudioPlayer2;
