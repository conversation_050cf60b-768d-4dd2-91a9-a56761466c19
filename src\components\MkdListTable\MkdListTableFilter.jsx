import React from "react";
import MkdListTableFilterDisplays, {
  displayTypes,
} from "./MkdListTableFilterDisplays";
import { LazyLoad } from "Components/LazyLoad";
import SetColumns from "./SetColumns/SetColumns";
import { Modal } from "Components/Modal";
import { ModalSidebar } from "Components/ModalSidebar";
import { CloseIcon, FilterIcon } from "Assets/svgs";
import { ModalSidebarHeader } from "Components/ModalSidebarHeader";
import MkdListTableFilterDropdownV2 from "./MkdListTableFilterDropdown/MkdListTableFilterDropdownV2";

const MkdListTableFilter = ({
  columns,
  table = "",
  onSubmit,
  columnData,
  columnId = 0,
  setColumns,
  setColumnId,
  searchField,
  setColumnData,
  onColumnClick,
  setOptionValue,
  selectedOptions,
  columnModel = "",
  setSelectedOptions,
  filterDisplays = [],
  setFilterConditions,
  onOptionValueChange,
}) => {
  const [openFilter, setOpenFilter] = React.useState(false);
  const [openColumns, setOpenColumns] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);

  return (
    <>
      <div className="relative flex w-fit items-center justify-between rounded bg-white">
        <div className="flex w-full flex-col items-start justify-between gap-4 text-gray-700 md:flex-row  md:items-center">
          <LazyLoad>
            <MkdListTableFilterDisplays
              columns={columnData?.columns}
              columnData={columnData}
              selectedOptions={selectedOptions}
              display={[displayTypes.FILTER, ...filterDisplays]}
              setOpenColumns={() => setOpenColumns(true)}
              setOpenFilter={() => setOpenFilter((prev) => !prev)}
            />
          </LazyLoad>

          {/*  */}
        </div>

        {/* {openFilter ? ( */}
        <LazyLoad>
          {/* <MkdListTableFilterDropdown
              onSubmit={onSubmit}
              columns={columnData?.columns}
              onColumnClick={onColumnClick}
              setOptionValue={setOptionValue}
              selectedOptions={selectedOptions}
              setSelectedOptions={setSelectedOptions}
              onOptionValueChange={onOptionValueChange}
            /> */}

          <ModalSidebar
            isModalActive={openFilter}
            closeModalFn={() => setOpenFilter(false)}
            customMinWidthInTw={`md:!w-[25%] !w-full `}
            showHeader
            title={
              <div className="flex items-center gap-2 font-inter text-[1.125rem] font-bold leading-[1.5rem] text-[#18181B]">
                <FilterIcon /> Filter
              </div>
            }
            side="left"
            headerClassName={"bg-white text-black"}
            headerContentClassName={"text-black"}
            closePosition={2}
            headerContent={
              <ModalSidebarHeader
                onToggleModal={() => setOpenFilter(false)}
                cancelText={<CloseIcon className="!h-[.755rem] !w-[.755rem]" />}
              />
            }
            classes={{
              modalBody: "bg-white",
            }}
          >
            {/* {openFilter && ( */}
            <LazyLoad>
              <MkdListTableFilterDropdownV2
                onSubmit={onSubmit}
                columns={columnData?.columns}
                onColumnClick={onColumnClick}
                setOptionValue={setOptionValue}
                selectedOptions={selectedOptions}
                setSelectedOptions={setSelectedOptions}
                onOptionValueChange={onOptionValueChange}
                onClose={() => setOpenFilter(false)}
              />
            </LazyLoad>
            {/* )} */}
          </ModalSidebar>
        </LazyLoad>
        {/* ) : null} */}

        <LazyLoad>
          <LazyLoad>
            <SetColumns
              isOpen={openColumns}
              columnModel={columnModel}
              columns={columnData?.columns}
              onClose={() => setOpenColumns(false)}
              columnData={columnData}
              onUpdate={(data) => {
                setColumnData((prev) => ({
                  ...prev,
                  ...data,
                }));

                //  refreshRef.current.click();
              }}
              onSuccess={(data) => {
                setOpenColumns(false);
                // setColumns(() => [...data]);
                // if (colId) {
                //   setColumnId(colId);
                // }
                setColumnData((prev) => ({
                  ...prev,
                  ...data,
                }));

                //  refreshRef.current.click();
              }}
            />
          </LazyLoad>

          {/* </Modal> */}
        </LazyLoad>
      </div>
    </>
  );
};

export default MkdListTableFilter;
