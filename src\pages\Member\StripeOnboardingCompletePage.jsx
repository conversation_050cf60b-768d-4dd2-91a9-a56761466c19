import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import { ClipLoader } from "react-spinners";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const StripeOnboardingCompletePage = () => {
  const navigate = useNavigate();
  const { state } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [message, setMessage] = useState("Verifying your Stripe setup...");

  useEffect(() => {
    const handleStripeComplete = async () => {
      try {
        setIsLoading(true);

        // Get current user ID
        const userId = localStorage.getItem("user");
        if (!userId) {
          showToast(globalDispatch, "User session not found", 4000, "error");
          navigate("/member/login");
          return;
        }

        // Wait a moment for Stripe to process the completion
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Fetch updated user details to check Stripe status
        const userResult = await getUserDetailsByIdAPI(userId);

        if (userResult.error) {
          showToast(
            globalDispatch,
            "Failed to fetch user details",
            4000,
            "error"
          );
          navigate("/member/dashboard");
          return;
        }

        const userDetails = userResult.model;

        // Check if Stripe setup is now complete
        if (userDetails.has_stripe === true) {
          setIsSuccess(true);
          setMessage("Stripe setup completed successfully!");
          showToast(
            globalDispatch,
            "Payment system setup completed successfully!",
            4000,
            "success"
          );

          setTimeout(() => {
            // Check onboarding status and redirect appropriately
            let onboardingComplete = false;
            if (userDetails.steps) {
              try {
                const stepData = JSON.parse(userDetails.steps);
                onboardingComplete = stepData.onboarding_complete === true;
              } catch (e) {
                console.error("Error parsing step data:", e);
              }
            }

            if (onboardingComplete) {
              navigate("/member/dashboard");
            } else {
              navigate("/member/onboarding");
            }
          }, 3000);
        } else {
          // Stripe setup might still be processing or failed
          setMessage("Stripe setup is still processing. Please wait...");

          // Try again after a longer delay
          setTimeout(async () => {
            try {
              const retryResult = await getUserDetailsByIdAPI(userId);
              if (!retryResult.error && retryResult.model.has_stripe === true) {
                setIsSuccess(true);
                setMessage("Stripe setup completed successfully!");
                showToast(
                  globalDispatch,
                  "Payment system setup completed successfully!",
                  4000,
                  "success"
                );

                setTimeout(() => {
                  let onboardingComplete = false;
                  if (retryResult.model.steps) {
                    try {
                      const stepData = JSON.parse(retryResult.model.steps);
                      onboardingComplete =
                        stepData.onboarding_complete === true;
                    } catch (e) {
                      console.error("Error parsing step data:", e);
                    }
                  }

                  if (onboardingComplete) {
                    navigate("/member/dashboard");
                  } else {
                    navigate("/member/onboarding");
                  }
                }, 2000);
              } else {
                setMessage(
                  "Stripe setup incomplete. Redirecting to dashboard..."
                );
                showToast(
                  globalDispatch,
                  "Stripe setup may need additional steps",
                  4000,
                  "warning"
                );
                setTimeout(() => {
                  navigate("/member/dashboard");
                }, 2000);
              }
            } catch (error) {
              console.error("Error on retry:", error);
              navigate("/member/dashboard");
            }
          }, 5000);
        }
      } catch (error) {
        console.error("Error handling Stripe completion:", error);
        showToast(
          globalDispatch,
          "An error occurred during Stripe setup verification",
          4000,
          "error"
        );
        navigate("/member/dashboard");
      } finally {
        setIsLoading(false);
      }
    };

    handleStripeComplete();
  }, [navigate, globalDispatch]);

  return (
    <div className="flex h-screen items-center justify-center bg-boxdark-2">
      <div className="text-center">
        <div className="mb-6">
          {isSuccess ? (
            <FontAwesomeIcon
              icon="fa-solid fa-check-circle"
              className="text-6xl text-primary"
            />
          ) : (
            <ClipLoader size={50} color="#3C50E0" />
          )}
        </div>
        <h2 className="mb-4 text-2xl font-bold text-white">
          {isSuccess ? "Setup Complete!" : "Processing..."}
        </h2>
        <p className="text-lg text-bodydark">{message}</p>
        <div className="mt-6">
          <p className="text-sm text-bodydark2">
            {isSuccess
              ? "You will be redirected shortly..."
              : "Please wait while we verify your Stripe setup..."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default StripeOnboardingCompletePage;
