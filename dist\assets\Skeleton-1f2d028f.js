import{j as o}from"./@floating-ui/react-2bb8505c.js";import"./vendor-3aca5368.js";import{S as m}from"./react-loading-skeleton-a8f73c32.js";const i=({className:t="",count:l=5,counts:r=[2,1,3,1,1],circle:a=!1})=>o.jsx("div",{className:`flex h-fit max-h-screen min-h-fit w-full flex-col gap-5 overflow-hidden p-4 ${t} `,children:Array.from({length:l}).map((f,e)=>o.jsx(m,{count:r[e]??1,height:r[e]&&r[e]>1||e+1===l?25:80,circle:a},`${f}${e}`))});export{i as default};
