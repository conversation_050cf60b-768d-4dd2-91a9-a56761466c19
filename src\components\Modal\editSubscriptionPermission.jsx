import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import CustomSelect2 from "Components/CustomSelect2";
import { GlobalContext, showToast } from "Src/globalContext";
import React, { useState } from "react";

const EditSubscriptionPermission = (props) => {
  const { isOpen, setIsOpen } = props;

  //
  //
  const [currentPermission, setCurrentPermission] = useState("A");
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const submit = () => {
    setTimeout(() => {
      showToast(globalDispatch, "Permission Updated");
      setIsOpen(false);
    }, 1000);
  };

  return (
    <>
      <div className="fixed inset-0 z-10 overflow-y-auto">
        <div
          className="fixed inset-0 h-full w-full bg-black opacity-40"
          onClick={() => setIsOpen(false)}
        ></div>
        <div className="flex min-h-screen items-center px-4 py-8">
          <div className="relative mx-auto w-full max-w-lg rounded-md bg-gray-700 p-4 shadow-lg">
            <div className="mt-3 flex flex-col">
              <div className="flex w-full justify-between">
                <h3 className="text-xl font-bold text-white">
                  Edit Permission
                </h3>
                <FontAwesomeIcon
                  icon="close"
                  className="h-6 w-6 cursor-pointer text-white"
                  onClick={() => setIsOpen(false)}
                />
              </div>

              <CustomSelect2
                className="mt-3 w-full rounded-lg border border-zinc-500 bg-zinc-700 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500"
                name="engineer"
                label="Permission"
                id="engineer"
                value={currentPermission}
                onChange={(value) => setCurrentPermission(value)}
              >
                <option value="A">A</option>
                <option value="B">B</option>
              </CustomSelect2>

              <div className="mt-8 flex w-full items-center justify-end gap-5">
                <button
                  className="w-fit rounded bg-blue-600 px-2 py-1 text-sm font-semibold text-white hover:bg-blue-700 lg:px-3 lg:py-2"
                  onClick={submit}
                >
                  Submit
                </button>
                <button
                  className="w-fit rounded bg-gray-500 px-2 py-1 text-sm font-semibold text-white hover:bg-gray-400 lg:px-3 lg:py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EditSubscriptionPermission;
