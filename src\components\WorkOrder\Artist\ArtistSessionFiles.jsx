import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";
import { Download } from "lucide-react";

const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

const ArtistSessionFiles = ({
  uploadedFiles,
  setDeleteFileId,
  setEmployeeType,
  setFileUploadType,
  setFormData,
}) => {
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);

  const handleDeleteFileModalClose = () => {
    setShowDeleteFileConfirmModal(false);
  };

  const handleDeleteFileSubmit = () => {
    setDeleteFileId(localDeleteFileId);
    setShowDeleteFileConfirmModal(false);
  };

  return (
    <>
      <div
        onClick={() => {
          setEmployeeType("artist");
          setFileUploadType("session");
        }}
        className="mb-2 block w-full rounded-md border border-gray-500 bg-gray-800 p-5 shadow"
      >
        <div className="flex flex-col gap-4">
          <div className="mb-4 text-center text-xl font-semibold text-white">
            Sessions
          </div>

          <div className="flex flex-row flex-wrap items-center justify-center gap-2">
            {uploadedFiles &&
              uploadedFiles.length > 0 &&
              uploadedFiles.map((file, index) => {
                let fileSrc = `${file.url}`;
                // only keep the file name which is last part of the url
                let fileSrcTemp = fileSrc.split("/").pop();
                let fileExtension = fileSrc.split(".").pop();
                return (
                  <div key={index} className="mb-4 flex flex-col gap-1">
                    {audioFileTypes.includes(fileExtension) && (
                      <div className="flex items-center gap-3">
                        <a
                          className="truncate text-sm text-white underline"
                          href={fileSrc}
                          rel="noreferrer"
                          target="_blank"
                        >
                          {fileSrcTemp}
                        </a>
                        <Download
                          onClick={() => {
                            const link = document.createElement("a");
                            link.href = fileSrc;
                            link.download = fileSrcTemp;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                          className="h-5 w-5 text-primary"
                        />
                      </div>
                    )}
                    <div className="flex flex-row items-center gap-4">
                      {audioFileTypes.includes(fileExtension) && (
                        <AudioPlayer fileSource={fileSrc} />
                      )}
                      {!audioFileTypes.includes(fileExtension) && (
                        <a
                          className="truncate text-sm text-white underline"
                          href={fileSrc}
                          rel="noreferrer"
                          target="_blank"
                        >
                          {fileSrcTemp}
                        </a>
                      )}
                      <span
                        className="cursor-pointer"
                        onClick={() => {
                          setShowDeleteFileConfirmModal(true);
                          setLocalDeleteFileId(file.id);
                        }}
                      >
                        <FontAwesomeIcon
                          icon="fa-solid fa-trash"
                          color="orange"
                          width={24}
                          height={24}
                        />
                      </span>
                    </div>
                  </div>
                );
              })}
          </div>

          <div className="mt-6 flex flex-col items-center justify-center">
            <span className="my-2 text-lg font-semibold text-white">
              Upload more files
            </span>
            <FileUpload
              justify="center"
              items="center"
              maxFileSize={2048}
              setFormData={setFormData}
            />
          </div>
        </div>
      </div>

      {showDeleteFileConfirmModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to delete this file?`}
          setModalClose={handleDeleteFileModalClose}
          setFormYes={handleDeleteFileSubmit}
        />
      ) : null}
    </>
  );
};

export default ArtistSessionFiles;
