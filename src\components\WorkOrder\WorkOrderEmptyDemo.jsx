import React from "react";
import FileUpload from "Components/FileUpload/FileUpload";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const WorkOrderEmptyDemo = ({
  setEmployeeType,
  setFileUploadType,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  return (
    <div
      onClick={() => {
        setEmployeeType("writer");
        setFileUploadType("demo");
      }}
      className="rounded border border-strokedark bg-boxdark p-6"
    >
      <div className="flex flex-col items-center">
        {/* Icon and Title */}
        <div className="mb-6 text-center">
          <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-boxdark">
            <FontAwesomeIcon
              icon="fa-solid fa-cloud-arrow-up"
              className="h-8 w-8 text-primary"
            />
          </div>
          <h3 className="mb-1 text-lg font-semibold text-white">
            Upload Demo Files
          </h3>
          <p className="text-sm text-bodydark2">
            Drag and drop your audio files or click to browse
          </p>
        </div>

        {/* Supported Formats */}

        {/* Upload Component */}
        <div className="w-full max-w-md">
          <FileUpload
            justify="center"
            items="center"
            maxFileSize={500}
            setFormData={setFormData}
            uploadedFilesProgressData={uploadedFilesProgressData}
          />
        </div>

        {/* File Size Info */}
        <div className="mt-4 text-center">
          <p className="text-xs text-bodydark2">Maximum file size: 500MB</p>
        </div>
      </div>
    </div>
  );
};

export default WorkOrderEmptyDemo;
