import axios from 'axios';

export const urlToPdfAPI = async (payload) => {
  try {
    const uri = `https://app.equalityrecords.com/v3/api/custom/equality_record/pdf/generate`;
    const response = await axios.post(uri, payload, {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/json',
        'x-project':
          'ZXF1YWxpdHlyZWNvcmQ6cDV3YjJnd2M5aWVyZjhzem5ldDZibnNjcTA3bnVyYQ==',
      },
    });

    // Create a blob from the response data
    const blob = new Blob([response.data], { type: 'application/pdf' });

    // Create a temporary URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a link element and simulate click to start downloading
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'example.pdf'); // Specify file name
    document.body.appendChild(link);
    link.click();

    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.log(error);
    return error;
  }
};
