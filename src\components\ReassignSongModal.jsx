import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Select from "react-select";

const ReassignSongModal = ({
  projects,
  setSelectedReassignProjectId,
  setModalClose,
}) => {
  const [projectsForSelect, setProjectsForSelect] = React.useState([]);
  const [selectedProjectId, setSelectedProjectId] = React.useState(null);

  const submitForm = (e) => {
    e.preventDefault();
    if (selectedProjectId) {
      setSelectedReassignProjectId(selectedProjectId);
    } else {
      alert("Please select a project");
    }
  };

  React.useEffect(() => {
    if (projects) {
      let forSelect = [];
      projects.length > 0 &&
        projects.map((row) => {
          forSelect.push({
            value: row.id,
            label: `${row.program_name} - ${row.team_name}`,
          });
        });
      setProjectsForSelect(forSelect);
    }
  }, [projects]);

  return (
    <div className="overflow-y-auto fixed inset-0 z-10">
      <div className="flex justify-center items-center px-4 pt-4 pb-20 min-h-screen text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span
          className="hidden sm:inline-block sm:h-screen sm:align-middle"
          aria-hidden="true"
        >
          &#8203;
        </span>
        <div
          className="inline-block overflow-hidden text-left align-bottom bg-gray-800 rounded-lg shadow-xl transition-all transform sm:my-8 sm:w-full sm:max-w-lg sm:align-middle"
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          <form>
            <div className="flex justify-between px-6 py-4 w-full border-b-2 border-gray-500 bg-slate-950">
              <h3
                className="text-2xl font-medium leading-6 text-gray-100"
                id="modal-headline"
              >
                Reassign
              </h3>
              <span
                className="absolute right-3 top-4 cursor-pointer"
                onClick={() => setModalClose(false)}
              >
                <FontAwesomeIcon
                  icon="fa-solid fa-circle-xmark"
                  width={32}
                  height={32}
                  color="#fff"
                />
              </span>
            </div>

            <div className="flex justify-start items-start p-6">
              <div className="mt-3 mb-6 w-full sm:mt-0 sm:text-left">
                <div className="mb-6">
                  <label
                    htmlFor="project"
                    className="block mb-2 text-sm font-medium text-gray-100"
                  >
                    Project{" "}
                    <span className="text-red-500">
                      (One selection is required)
                    </span>
                  </label>

                  <Select
                    className="basic-single"
                    classNamePrefix="select"
                    isClearable={true}
                    isSearchable={true}
                    options={projectsForSelect}
                    onChange={(e) => setSelectedProjectId(e.value)}
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-row-reverse px-6 py-4 border-t-2 border-gray-500 bg-slate-950">
              <div
                onClick={(e) => submitForm(e)}
                className="inline-flex justify-center px-2 py-1 ml-3 w-auto text-base font-medium text-white bg-green-600 rounded-md border border-transparent shadow-sm cursor-pointer hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 sm:text-sm"
              >
                Reassign
              </div>
              <div
                className="inline-flex justify-center px-2 py-1 ml-3 w-auto text-base font-medium text-white bg-red-600 rounded-md border border-transparent shadow-sm cursor-pointer hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:text-sm"
                onClick={() => setModalClose(false)}
              >
                Cancel
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ReassignSongModal;
