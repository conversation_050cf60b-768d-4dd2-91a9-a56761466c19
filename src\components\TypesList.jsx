import { AuthContext } from "Src/authContext";
import React from "react";
import { useNavigate } from "react-router";
import { Clip<PERSON>oader } from "react-spinners";

const TypesList = ({ TypeList, loading }) => {
  const navigate = useNavigate();
  const { dispatch, state: authState } = React.useContext(AuthContext);

  return (
    <div className="shadow-default rounded-sm">
      <table className="w-full table-auto">
        <thead className="bg-meta-4">
          <tr>
            <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark md:pl-6 2xl:pl-9">
              Edit type
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark">
              Request Date Range
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark">
              Number of lines
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark">
              Edit Duration
            </th>
          </tr>
        </thead>

        {!loading ? (
          <tbody className="cursor-pointer text-bodydark1">
            {TypeList.map((elem) => (
              <tr
                key={elem.id}
                onClick={() => {
                  navigate(`/${authState?.role}/edits-type-view/${elem.id}`);
                }}
                className="border-b border-strokedark hover:bg-primary/5 dark:border-strokedark dark:hover:bg-primary/5"
              >
                <td className="whitespace-nowrap px-4 py-4 pl-6 text-white 2xl:pl-9">
                  <span>{elem.edit_type}</span>
                </td>
                <td className="whitespace-nowrap px-4 py-4">
                  <span>{elem.request_range}</span>
                </td>
                <td className="whitespace-nowrap px-4 py-4">
                  <span>{elem.number_of_lines}</span>
                </td>
                <td className="whitespace-nowrap px-4 py-4">
                  <span>{elem.edit_duration}</span>
                </td>
              </tr>
            ))}
          </tbody>
        ) : loading && TypeList.length === 0 ? (
          <tbody>
            <tr>
              <td colSpan="6" className="text-center">
                <span className="trans relative top-[30px] m-4 animate-pulse whitespace-nowrap p-4 pb-8 text-xl font-semibold text-white ease-out">
                  <ClipLoader color="#fff" size={20} className="mr-3" /> Loading
                  Edits...
                </span>
              </td>
            </tr>
          </tbody>
        ) : !loading && TypeList.length === 0 ? (
          <tbody>
            <tr>
              <td colSpan="6" className="text-center">
                <span className="relative top-[10px] m-4 p-4 font-semibold text-white">
                  No data found
                </span>
              </td>
            </tr>
          </tbody>
        ) : null}
      </table>
    </div>
  );
};

export default TypesList;
