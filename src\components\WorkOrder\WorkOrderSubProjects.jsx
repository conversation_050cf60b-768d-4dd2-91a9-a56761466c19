import React from 'react';
import WorkOrderSubProject from './WorkOrderSubProject';

const WorkOrderSubProjects = ({ subProjects, setLyrics, workOrderDetails }) => {
  return (
    <>
      {subProjects &&
        subProjects.length > 0 &&
        subProjects.map((subProject, index) => {
          return (
            <WorkOrderSubProject
              key={index}
              workOrderDetails={workOrderDetails}
              subProject={subProject}
              uploadedDemoFiles={subProject.demos}
              uploadedMasterFiles={subProject.masters}
              setLyrics={setLyrics}
            />
          );
        })}
    </>
  );
};

export default WorkOrderSubProjects;
