import React, { useState, useEffect, useContext } from "react";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import { updateRequest, createRequest } from "Src/context/Global/GlobalActions";
import MkdSDK from "Utils/MkdSDK";
import {
  Plus,
  FileText,
  Download,
  Trash2,
  ArrowLeft,
  Pencil,
  MoreVertical,
  Edit,
} from "lucide-react";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import { SingleDatePicker } from "react-dates";
import "react-dates/initialize";
import "react-dates/lib/css/_datepicker.css";
import moment from "moment";
import { ClipLoader } from "react-spinners";
import { getAllClientsAPI } from "Src/services/clientService";
import { getAllMixTypeAPI } from "Src/services/mixTypeServices";
import CustomSelect2 from "Components/CustomSelect2";
import { useNavigate } from "react-router-dom";
import { sendEmailAPIV3 } from "Src/services/emailService";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import EditInvoiceComponent from "Components/Invoice/EditInvoiceComponent";
import ManagerCreateInvoiceComponent from "Components/Invoice/ManagerCreateInvoiceComponent";
import ManagerEditInvoiceComponent from "Components/Invoice/ManagerEditInvoiceComponent";
import PaginationBar from "Components/PaginationBar";

const ManagerInvoicePage = () => {
  const { state: authState, dispatch: authDispatch } =
    React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [showNewInvoice, setShowNewInvoice] = useState(false);
  const [editInvoiceId, setEditInvoiceId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState([]);
  const [userDetails, setUserDetails] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(1);
  const [dataTotal, setDataTotal] = useState(0);
  const [clients, setClients] = useState([]);
  const [mixTypes, setMixTypes] = useState([]);
  const [activeMenu, setActiveMenu] = useState(null);
  const [pageSize, setPageSize] = useState(10);
  const [canPreviousPage, setCanPreviousPage] = useState(false);
  const [canNextPage, setCanNextPage] = useState(false);
  const [companyInfo, setCompanyInfo] = useState(null);
  const [loading2, setLoading2] = useState(true);
  const sdk = new MkdSDK();
  const navigate = useNavigate();

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: { path: "invoices" },
    });

    const userId = localStorage.getItem("user");
    if (userId) {
      getUserDetails(userId);
    }

    fetchCompanyInfo();
    fetchAvailableClients();
    getAllMixTypes();

    // Check if we need to edit an invoice (coming from ManagerViewInvoicePage)
    const storedEditInvoiceId = localStorage.getItem("editInvoiceId");
    if (storedEditInvoiceId) {
      // Set the edit invoice ID and clear from localStorage
      handleEditInvoice(storedEditInvoiceId);
      localStorage.removeItem("editInvoiceId");
    }
  }, []);

  // Separate useEffect for pagination to avoid unnecessary API calls
  useEffect(() => {
    fetchInvoices();
  }, [currentPage, pageSize]);

  const getUserDetails = async (userId) => {
    try {
      const result = await getUserDetailsByIdAPI(userId);
      if (!result.error) {
        setUserDetails(result.model);
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
      showToast(
        globalDispatch,
        "Failed to fetch company details",
        3000,
        "error"
      );
    }
  };

  const fetchCompanyInfo = async () => {
    try {
      setLoading2(true);
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      if (!response.error) {
        setCompanyInfo(response);
      } else {
        console.error(
          "Error fetching company info:",
          response.message || "Unknown error"
        );
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    } finally {
      setLoading2(false);
    }
  };

  const fetchAvailableClients = async () => {
    try {
      const result = await getAllClientsAPI();
      if (!result.error) {
        setClients(result.list);
      }
    } catch (error) {
      showToast(globalDispatch, "Failed to fetch clients", "error");
    }
  };

  const getAllMixTypes = async () => {
    try {
      const result = await getAllMixTypeAPI();
      if (!result.error && result.list.length > 0) {
        setMixTypes(result.list);
      }
    } catch (error) {
      showToast(globalDispatch, "Failed to fetch mix types", "error");
    }
  };

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      // Use currentPage + 1 because API expects 1-based page numbers
      const result = await sdk.getInvoices(currentPage, pageSize);
      setInvoices(result.invoices || []);
      setPageCount(result.pages || 1);
      setDataTotal(result.total || 0);

      // Update pagination state
      setCanPreviousPage(currentPage > 1);
      setCanNextPage(currentPage + 1 < result.pages);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      showToast(globalDispatch, "Failed to fetch invoices", 3000, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateInvoice = async (formData) => {
    try {
      setLoading(true);

      // Find client details
      const client = clients.find(
        (c) => c.client_id.toString() == formData.selectedClientId
      );

      if (!client) {
        showToast(globalDispatch, "Client not found", 3000, "error");
        setLoading(false);
        return;
      }

      // Format items with IDs for the API
      const formattedItems = formData.invoiceData.items.map((item) => {
        if (item.isSpecialRow || item.isSpecial) {
          return {
            id: item.id, // Include ID for existing items
            price: item.price,
            quantity: item.quantity || 1,
            discount: item.discount || 0,
            producer: "",
            producers: [userDetails?.first_name + " " + userDetails?.last_name], // Store producer name as string in array
            description: item.name || "Special Charge", // Add description with special charge name
            specialType: "additional",
            isSpecial: true,
            name: item.name || "Special Charge",
            mixDate: "",
            teamName: "",
            division: "",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
          };
        } else {
          return {
            id: item.id, // Include ID for existing items
            price: item.price,
            quantity: item.quantity || 1,
            discount: item.discount || 0,
            producer: item.producer || "",
            producers: [userDetails?.first_name + " " + userDetails?.last_name], // Store producer name as string in array
            description: `${item.mixType || ""}`, // Add description with mix package name
            specialType: "",
            isSpecial: false,
            mixDate: item.mixDate || "",
            teamName: item.teamName || "",
            division: item.division || "",
            musicSurveyDue: item.musicSurveyDue || "",
            routineSubmissionDue: item.routineSubmissionDue || "",
            estimatedCompletion: item.estimatedCompletion || "",
            mixSeasonId: item.mixSeasonId || item.mixSeason || "",
            mixTypeId: item.mixTypeId || item.mixType || "",
          };
        }
      });

      // Convert deposit amount to cents if using fixed amount
      const depositAmount =
        formData.invoiceData.depositPercentage > 0
          ? 0
          : Math.round(formData.invoiceData.depositAmount * 100); // Convert to cents

      // Prepare payload for update using the new API endpoint
      const payload = {
        clientId: parseInt(formData.selectedClientId),
        clientEmail: client.client_email,
        clientName: client.client_full_name || "",
        programName: client.client_program,
        invoiceDate: formData.invoiceDates.invoiceDate,
        items: formattedItems,
        depositPercentage: formData.invoiceData.depositPercentage || 0,
        depositAmount: depositAmount,
        notes: formData.invoiceData.notes || "",
        termsAndConditions: formData.invoiceData.termsAndConditions || "",
      };

      // Use the new API endpoint for updating invoices
      const result = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/subscription/invoice/${editInvoiceId}`,
        payload,
        "PUT"
      );

      if (!result.error) {
        setEditInvoiceId(null);
        fetchInvoices();
        showToast(
          globalDispatch,
          "Invoice updated successfully",
          3000,
          "success"
        );
      } else {
        showToast(
          globalDispatch,
          result.message || "Failed to update invoice",
          3000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error updating invoice:", error);
      showToast(globalDispatch, "Failed to update invoice", 3000, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateInvoice = async (formData) => {
    try {
      setLoading(true);
      console.log(formData, "form");

      // Get client details
      let clientDetails = null;
      if (formData.selectedClientId) {
        clientDetails = clients.find(
          (c) => c.client_id === parseInt(formData.selectedClientId)
        );
      }
      console.log(clients);
      console.log(clientDetails);

      // Create the correct payload structure for the API
      const payload = {
        clientId: formData.selectedClientId
          ? parseInt(formData.selectedClientId)
          : null,
        programName: formData.selectedClientId
          ? clientDetails.client_program
          : formData.newClientData?.program || null,
        clientEmail: formData.selectedClientId
          ? clientDetails.client_email
          : formData.newClientData?.email || null,
        date: formData.invoiceDates.invoiceDate,
        dueDate: formData.invoiceDates.invoiceDueDate,
        notes: formData.invoiceData.notes,
        termsAndConditions: formData.invoiceData.termsAndConditions,
        depositAmount:
          formData.invoiceData.depositPercentage > 0
            ? 0
            : Math.round(formData.invoiceData.depositAmount * 100), // Convert to cents
        depositPercentage: formData.invoiceData.depositPercentage,
        // is_quote: formData.isQuote || false,
        items: formData.invoiceData.items.map((item) => {
          if (item.isSpecial) {
            return {
              price: parseFloat(item.price) || 0,
              quantity: parseInt(item.quantity) || 1,
              discount: parseFloat(item.discount) || 0,
              name: item.name,
              description: item.name, // Add description with special charge name
              isSpecial: true,
              specialType: item.specialType,
              producer: "",
              producers: [
                userDetails?.first_name + " " + userDetails?.last_name,
              ], // Store producer name as string in array
              mixDate: "",
              teamName: "",
              division: "",
              musicSurveyDue: "",
              routineSubmissionDue: "",
              estimatedCompletion: "",
              mixSeasonId: "",
              mixTypeId: "",
            };
          } else {
            return {
              price: item.price,
              quantity: item.quantity,
              discount: item.discount,
              description: `${item.description || ""}`, // Add description with mix package name
              mixDate: item.mixDate,
              teamName: item.teamName,
              division: item.division,
              musicSurveyDue: item.musicSurveyDue,
              routineSubmissionDue: item.routineSubmissionDue,
              estimatedCompletion: item.estimatedCompletion,
              mixSeasonId: item.mixSeasonId || item.mixSeason || "",
              producer: formData.producer || null,
              producers: [
                userDetails?.first_name + " " + userDetails?.last_name,
              ], // Store producer name as string in array
              specialType: "",
              isSpecial: false,
              mixTypeId: item.mixTypeId || item.mixType || "",
            };
          }
        }),
      };

      const result = await sdk.createInvoice(payload);

      if (!result.error) {
        setShowNewInvoice(false);
        fetchInvoices();
        showToast(
          globalDispatch,
          formData.isQuote
            ? "Quote created successfully"
            : "Invoice created successfully",
          3000,
          "success"
        );
      }
    } catch (error) {
      console.error("Error creating invoice:", error);
      showToast(
        globalDispatch,
        `Failed to create ${formData.isQuote ? "quote" : "invoice"}`,
        3000,
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleResendLink = async (invoiceId) => {
    try {
      const invoiceDetails = await sdk.callRawAPI(
        `/v3/api/custom/equality_record/subscription/invoice/${invoiceId}`,
        {},
        "GET"
      );

      if (!invoiceDetails.error) {
        const emailResult = await sendEmailAPIV3({
          from: "<EMAIL>",
          to: invoiceDetails.invoice.client_email,
          subject: invoiceDetails.invoice.is_quote
            ? "Quote Available - Action Required"
            : "Invoice Available - Action Required",
          body: `
            <p>Hello${
              invoiceDetails.invoice.client_name
                ? ` ${invoiceDetails.invoice.client_name}`
                : ""
            },</p>
            <p>Your ${
              invoiceDetails.invoice.is_quote ? "quote" : "invoice"
            } is ready for your review.</p>
            <p><a href="https://equalityrecords.com/invoice/${invoiceId}/${
            invoiceDetails.invoice.access_token
          }">View ${
            invoiceDetails.invoice.is_quote ? "Quote" : "Invoice"
          }</a></p>
            <p>Thank you!</p>
            <p>${userDetails?.company_name || ""}</p>
          `,
        });

        if (!emailResult.error) {
          showToast(
            globalDispatch,
            "Invoice link resent successfully",
            3000,
            "success"
          );
        } else {
          showToast(
            globalDispatch,
            "Failed to send email notification",
            "error"
          );
        }
      }
    } catch (error) {
      showToast(globalDispatch, "Failed to resend invoice link", 3000, "error");
    }
  };

  const handleEditInvoice = async (invoiceId) => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const result = await sdk.getInvoiceById(invoiceId);

      if (!result.error) {
        setEditInvoiceId(invoiceId);
      }
    } catch (error) {
      console.error("Error fetching invoice details:", error);
      showToast(
        globalDispatch,
        "Failed to fetch invoice details",
        3000,
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  // Check if main member has invoice subscription - sub-members inherit access
  const hasInvoiceSubscription =
    companyInfo?.company?.main_member?.has_invoice_subscription === 1;
  const hasManager = companyInfo?.company?.manager && !companyInfo?.is_manager;
  const isMainMember =
    companyInfo?.company?.main_member?.id ===
    parseInt(localStorage.getItem("user"));

  // Determine if we should show invoice management UI
  const showInvoiceManagement =
    hasInvoiceSubscription && (!hasManager || isMainMember);

  return (
    <div className="p-4 mx-auto max-w-screen md:p-6 2xl:p-10">
      {showNewInvoice && showInvoiceManagement ? (
        <ManagerCreateInvoiceComponent
          onClose={() => {
            window.confirm("Are you sure you want to close this invoice?") &&
              setShowNewInvoice(false);
          }}
          onSubmit={handleCreateInvoice}
          userDetails={userDetails}
          clients={clients}
          mixTypes={mixTypes}
          loading={loading}
        />
      ) : editInvoiceId && showInvoiceManagement ? (
        <ManagerEditInvoiceComponent
          id={editInvoiceId}
          onClose={() => {
            window.confirm("Are you sure you want to close this invoice?") &&
              setEditInvoiceId(null);
          }}
          onSubmit={handleUpdateInvoice}
          onResend={() => handleResendLink(editInvoiceId)}
          clients={clients}
          mixTypes={mixTypes}
          userDetails={userDetails}
        />
      ) : (
        <>
          <div className="mb-5 rounded border shadow-default border-stroke/50 bg-boxdark">
            <div className="flex justify-between items-center px-4 py-5 mb-6 md:px-6 2xl:px-9">
              <div>
                <h2 className="text-2xl font-bold text-white">Invoices</h2>
                <p className="mt-1 text-lg text-bodydark">
                  Manage your invoices
                </p>
              </div>
              {showInvoiceManagement && (
                <button
                  onClick={() => {
                    setEditInvoiceId(null);
                    setShowNewInvoice(!showNewInvoice);
                  }}
                  className="flex gap-2 items-center px-4 py-2 font-medium text-white rounded-lg bg-primary hover:bg-opacity-90"
                >
                  <Plus className="w-5 h-5" />
                  New Invoice
                </button>
              )}
            </div>
          </div>

          {/* Show subscription message if user doesn't have invoice subscription */}
          {!hasInvoiceSubscription && !loading2 && (
            <div className="p-6 mb-5 rounded border shadow-default border-stroke/50 bg-boxdark">
              <div className="flex flex-col justify-center items-center py-8 text-center">
                <h3 className="mb-4 text-xl font-semibold text-white">
                  Invoice Management Requires a Subscription
                </h3>
                <p className="mb-6 text-bodydark">
                  To create and manage invoices, you need to subscribe to our
                  invoice management service.
                </p>
                <button
                  onClick={() => navigate("/member/subscription")}
                  className="flex gap-2 items-center px-6 py-3 font-medium text-white rounded-lg bg-primary hover:bg-opacity-90"
                >
                  Go to Subscription Page
                </button>
              </div>
            </div>
          )}

          {/* Show manager message if user has a manager and is not the main member */}
          {hasInvoiceSubscription &&
            hasManager &&
            !isMainMember &&
            !loading2 && (
              <div className="p-6 mb-5 rounded border shadow-default border-stroke/50 bg-boxdark">
                <div className="flex flex-col justify-center items-center py-8 text-center">
                  <h3 className="mb-4 text-xl font-semibold text-white">
                    Invoice Management Moved to Manager Portal
                  </h3>
                  <p className="mb-6 text-bodydark">
                    Your invoice management has been moved to your manager's
                    portal. Please contact your manager for any invoice-related
                    requests.
                  </p>
                </div>
              </div>
            )}

          {/* Only show invoice table if user has subscription and proper access */}
          {showInvoiceManagement && (
            <div className="rounded border shadow-default border-strokedark bg-boxdark">
              <div className="flex justify-between items-center px-4 border-b border-strokedark md:px-6 2xl:px-9">
                <h4 className="my-3 text-2xl font-semibold text-white">
                  Invoices
                </h4>
              </div>

              <div className="p-4 md:p-6 2xl:p-10">
                <div className="custom-overflow text-[12p min-h-[140px] overflow-x-auto">
                  <table className="w-full table-auto">
                    <thead className="bg-meta-4">
                      <tr>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-left uppercase text-bodydark1">
                          Invoice ID
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-left uppercase text-bodydark1">
                          Client Name
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-left uppercase text-bodydark1">
                          Program
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-left uppercase text-bodydark1">
                          Email
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-right uppercase text-bodydark1">
                          Total
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-center uppercase text-bodydark1">
                          Payment Status
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-center uppercase text-bodydark1">
                          Payment Method
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-left uppercase text-bodydark1">
                          Created
                        </th>
                        <th className="px-4 py-3 text-xs font-medium tracking-wider text-center uppercase text-bodydark1">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    {!loading && invoices.length > 0 ? (
                      <tbody className="text-white">
                        {invoices.map((invoice) => (
                          <tr
                            key={invoice.id}
                            className="text-xs border-b cursor-pointer border-strokedark hover:bg-primary/5"
                            onClick={() =>
                              navigate(`/manager/invoice/${invoice.id}`)
                            }
                          >
                            <td className="px-4 py-4 whitespace-nowrap">
                              #{invoice.id}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              {invoice.client_name}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              {invoice.program}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              {invoice.client_email}
                            </td>
                            <td className="px-4 py-4 text-right whitespace-nowrap">
                              ${parseFloat(invoice.total).toFixed(2)}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <div className="flex justify-center">
                                <span
                                  className={`inline-block rounded px-2.5 py-0.5 text-sm font-medium ${
                                    parseFloat(invoice.payment_amount) ===
                                    parseFloat(invoice.total)
                                      ? "bg-success/10 text-success"
                                      : invoice.payment_amount
                                      ? "bg-warning/10 text-warning"
                                      : "bg-danger/10 text-danger"
                                  }`}
                                >
                                  {parseFloat(invoice.payment_amount) ===
                                  parseFloat(invoice.total)
                                    ? "Paid"
                                    : invoice.payment_amount
                                    ? "In Progress"
                                    : "Unpaid"}
                                </span>
                              </div>
                            </td>
                            <td className="px-4 py-4 text-center whitespace-nowrap">
                              {invoice.payment_method ? (
                                <span className="capitalize">
                                  {invoice.payment_method}
                                  {invoice.payment_method === "check" &&
                                    invoice.attachment_url && (
                                      <a
                                        href={invoice.attachment_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="ml-2 text-primary hover:underline"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        (View)
                                      </a>
                                    )}
                                </span>
                              ) : (
                                <span className="text-bodydark2">-</span>
                              )}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              {moment(new Date(invoice.create_at)).format(
                                "MMM DD, YYYY"
                              )}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <div className="flex relative justify-center items-center">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setActiveMenu(
                                      activeMenu === invoice.id
                                        ? null
                                        : invoice.id
                                    );
                                  }}
                                  className="inline-flex justify-center items-center w-8 h-8 text-white rounded-md border transition border-strokedark hover:border-primary hover:bg-primary/10"
                                >
                                  <MoreVertical className="w-4 h-4" />
                                </button>

                                {activeMenu === invoice.id && (
                                  <div className="absolute right-0 z-50 w-[120px] rounded-md border border-strokedark bg-boxdark shadow-lg">
                                    <div className="py-1">
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleEditInvoice(invoice.id);
                                          setActiveMenu(null);
                                        }}
                                        className="flex gap-2 items-center px-4 py-2 w-full text-sm text-left text-white whitespace-nowrap hover:bg-primary/10"
                                      >
                                        <Edit className="min-h-4 min-w-4" />
                                        Edit
                                      </button>
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleResendLink(invoice.id);
                                          setActiveMenu(null);
                                        }}
                                        className="flex gap-2 items-center px-4 py-2 w-full text-sm text-left whitespace-nowrap text-success hover:bg-primary/10"
                                      >
                                        <Download className="min-h-4 min-w-4" />
                                        Resend
                                      </button>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    ) : loading ? (
                      <tbody>
                        <tr>
                          <td colSpan={8} className="text-center">
                            <div className="flex h-[140px] items-center justify-center">
                              <ClipLoader color="#fff" size={30} />
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    ) : (
                      <tbody>
                        <tr>
                          <td colSpan={8} className="text-center">
                            <div className="flex h-[140px] items-center justify-center">
                              <span className="text-bodydark">
                                No invoices found
                              </span>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    )}
                  </table>
                </div>

                {pageCount > 1 && (
                  <div className="mt-5">
                    <PaginationBar
                      currentPage={currentPage}
                      pageCount={pageCount}
                      pageSize={pageSize}
                      dataTotal={dataTotal}
                      canPreviousPage={canPreviousPage}
                      canNextPage={canNextPage}
                      updatePageSize={(size) => {
                        setPageSize(parseInt(size));
                        setCurrentPage(1); // Reset to first page when changing page size
                      }}
                      previousPage={() =>
                        setCurrentPage((prev) => Math.max(prev - 1, 1))
                      }
                      nextPage={() =>
                        setCurrentPage((prev) => Math.min(prev + 1, pageCount))
                      }
                      setCurrentPage={setCurrentPage}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ManagerInvoicePage;
