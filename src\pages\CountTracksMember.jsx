import ClientAddTrackModal from "Components/AddTrackModal";
import SingleTrackRow from "Components/singleTrackRow";
import React from "react";
import { ClipLoader } from "react-spinners";

import { getAllTracksAPI } from "Src/services/countTracksService";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import { GlobalContext } from "../globalContext";

const CountTracksMember = () => {
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);

  const [isAddVideoModalOpen, setIsAddVideoModalOpen] = React.useState(false);
  const [videoList, setVideoList] = React.useState([]);
  const [loader, setLoader] = React.useState(true);
  const [officeEmail, setOfficeEmail] = React.useState("");

  const userId = localStorage.getItem("user");
  const getOfficeEmail = async (userId) => {
    try {
      const res = await getUserDetailsByIdAPI(userId);
      if (!res.error) {
        setOfficeEmail(res.model?.office_email || "");
      }
    } catch (error) {}
  };

  const getData = async () => {
    setLoader(true);
    const result = await getAllTracksAPI({
      user_id: parseInt(localStorage.getItem("user")),
    });
    setLoader(false);
    if (!result.error) {
      setVideoList(result.list);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "count-tracks",
      },
    });
    getOfficeEmail(userId);
    getData();
  }, []);

  return (
    <div className="max-w-screen h-full p-4 md:p-4 lg:p-6 xl:p-8">
      {isAddVideoModalOpen && (
        <ClientAddTrackModal
          getData={getData}
          setVideoList={setVideoList}
          setIsOpen={setIsAddVideoModalOpen}
          isOpen={isAddVideoModalOpen}
          user_id={localStorage.getItem("user")}
        />
      )}

      <div className="border-strokedark bg-boxdark">
        <div className="px-4 pt-8 2xl:px-9">
          <h3 className="mb-4 text-2xl font-bold text-white">Count Tracks</h3>
        </div>

        <div className="shadow-default rounded border border-strokedark bg-boxdark dark:border-strokedark dark:bg-boxdark">
          <div className="border-b border-strokedark px-4 py-4 2xl:px-9 dark:border-strokedark">
            <div className="flex w-full items-center justify-between">
              <div className="flex items-center gap-3">
                <h4 className="text-lg font-semibold text-white dark:text-white">
                  {localStorage.getItem("companyName")}
                </h4>
                <span className="text-white dark:text-white">-</span>
                <div>
                  <span className="font-medium text-white dark:text-white">
                    {localStorage.getItem("userName")}
                  </span>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {officeEmail}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setIsAddVideoModalOpen(true)}
                className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
              >
                Upload
              </button>
            </div>
          </div>

          <div className="p-4 md:p-6 2xl:p-10">
            <div className="custom-overflow max-h-[380px] overflow-y-auto">
              <table className="w-full table-auto">
                <thead className="bg-meta-4">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1 2xl:pl-9">
                      Title
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                      Description
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                      Listen
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                      Download
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                      Delete
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-strokedark text-white">
                  {loader ? (
                    <tr>
                      <td colSpan="5" className="p-5 text-center">
                        <span className="trans relative m-4 animate-pulse whitespace-nowrap p-4 pb-8 text-xl font-semibold text-white ease-out">
                          <ClipLoader color="#fff" size={20} className="mr-3" />{" "}
                          Loading Tracks...
                        </span>
                      </td>
                    </tr>
                  ) : videoList.length === 0 ? (
                    <tr>
                      <td colSpan="5">
                        <div className="p-4 text-center text-white">
                          No Tracks found!
                        </div>
                      </td>
                    </tr>
                  ) : (
                    videoList.map((video) => (
                      <SingleTrackRow
                        key={video.id}
                        video={video}
                        getData={getData}
                      />
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountTracksMember;
