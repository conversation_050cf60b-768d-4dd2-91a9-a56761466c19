import React from "react";

const StripeOnboardingModal = ({
  isOpen,
  onClose,
  onSetupPayment,
  onSkip,
  isLoading = false,
}) => {
  console.log("🎭 StripeOnboardingModal render - isOpen:", isOpen);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />

      <div className="relative w-full max-w-md rounded-2xl border border-strokedark bg-boxdark p-6 shadow-xl">
        <div className="mb-4 flex items-center justify-center">
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
            <svg
              className="h-8 w-8 text-primary"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
            </svg>
          </div>
        </div>

        <h3 className="mb-2 text-center text-lg font-medium leading-6 text-white">
          Setup Payment System
        </h3>

        <div className="mb-6 mt-2">
          <p className="text-center text-sm text-bodydark">
            To receive invoice subscription payments, you need to setup your
            payment system with Stripe. This will allow you to accept payments
            from clients for your services.
          </p>
        </div>

        <div className="mb-6 rounded-lg bg-boxdark-2 p-4">
          <h4 className="mb-2 font-medium text-white">What you'll need:</h4>
          <ul className="space-y-1 text-sm text-bodydark">
            <li>• Business information</li>
            <li>• Bank account details</li>
            <li>• Tax identification number</li>
            <li>• Business verification documents</li>
          </ul>
        </div>

        <div className="flex flex-col gap-3">
          <button
            type="button"
            className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90 focus:outline-none disabled:opacity-50"
            onClick={onSetupPayment}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <svg
                  className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Setting up...
              </>
            ) : (
              "Setup Payment System"
            )}
          </button>

          <button
            type="button"
            className="inline-flex justify-center rounded-md border border-strokedark bg-transparent px-4 py-2 text-sm font-medium text-bodydark hover:bg-boxdark-2 focus:outline-none"
            onClick={onSkip}
            disabled={isLoading}
          >
            Skip for Now
          </button>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-bodydark2">
            You can setup your payment system later from your dashboard
            settings.
          </p>
        </div>
      </div>
    </div>
  );
};

export default StripeOnboardingModal;
