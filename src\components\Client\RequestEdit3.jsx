import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useState } from "react";
import { getUserDetailsByIdAPI } from "Src/services/userService";

const RequestEdit3 = ({
  isOpen,
  edit_policy,
  producer_id = null,
  setIsOpen2,
  setIsOpen3,
  setIsOpen1,
  setOpenEditView,
  producerName,
  setSelectedTeam,
}) => {
  const [editPolicy, setEditPolicy] = useState("");
  React.useEffect(() => {
    if (producer_id) {
      (async function () {
        try {
          const result = await getUserDetailsByIdAPI(producer_id);

          if (!result?.error) {
            setEditPolicy(result.model.edit_policy_link);
          }
        } catch (error) {}
      })();
    }
  }, [producer_id]);
  return (
    <div className="custom-overflow fixed inset-0 z-[52] flex h-full w-full items-center justify-center overflow-y-auto px-4 py-8 backdrop-blur-sm">
      <div className="shadow-default relative w-full max-w-[800px] rounded border border-strokedark bg-boxdark p-4 sm:p-8 dark:border-strokedark dark:bg-boxdark">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-strokedark pb-4 dark:border-strokedark">
          <h3 className="text-2xl font-semibold text-white dark:text-white">
            8-Count
          </h3>
          <button
            onClick={() => {
              setIsOpen2(false);
              setIsOpen1(false);
              setIsOpen3(false);
            }}
            className="text-white hover:text-white dark:text-gray-400 dark:hover:text-white"
          >
            <FontAwesomeIcon icon="close" className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="mt-6">
          <div className="mb-6 space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">
                Producer Name:
              </span>
              <span className="text-sm text-white">{producerName}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">
                Edit Policy:
              </span>
              {editPolicy ? (
                <a
                  target="_blank"
                  className="text-sm text-primary underline hover:text-primary/90"
                  href={`${editPolicy}`}
                >
                  Edit_Policy.pdf
                </a>
              ) : (
                <span className="text-sm text-white">null</span>
              )}
            </div>
          </div>

          <div className="mb-6 text-center text-sm text-white">
            Would you like to revise the previous version of 8-count sheets or
            start new?
          </div>

          <div className="mb-8 flex justify-center gap-4">
            <button
              onClick={() => {
                setOpenEditView("Previous");
                setIsOpen2(false);
                setIsOpen1(false);
                setIsOpen3(false);
              }}
              className="flex h-[56px] w-[146px] items-center justify-center rounded bg-primary px-6 py-2 text-xs font-medium  text-white hover:bg-opacity-90"
            >
              <span className="text-[13px] leading-[15px]">
                Revise Previous Version
              </span>
            </button>
            <button
              onClick={() => {
                setOpenEditView("Blank");
                setIsOpen2(false);
                setIsOpen1(false);
                setIsOpen3(false);
              }}
              className="flex h-[56px] w-[146px] items-center justify-center rounded bg-primary px-6 py-2 text-xs font-medium leading-[15px] text-white hover:bg-opacity-90"
            >
              <span className="text-[13px] leading-[15px]">
                Start With Blank Count Sheet
              </span>
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-start">
            <button
              onClick={() => {
                setIsOpen2(true);
                setIsOpen3(false);
              }}
              className="flex items-center justify-center gap-2 rounded bg-primary px-6 py-2 font-medium text-white hover:bg-opacity-90"
            >
              <FontAwesomeIcon icon="arrow-left" className="text-xs" />
              <span>Previous</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestEdit3;
