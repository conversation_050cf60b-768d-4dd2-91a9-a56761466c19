import { MkdDebounceInput } from "Components/MkdDebounceInput";
import React, { memo, useState } from "react";
import { AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import MkdListTableFilterOptions from "./MkdListTableFilterOptions";
import FilterJoinDropdown from "./FilterJoinDropdown";
import { LazyLoad } from "Components/LazyLoad";
import AddButton from "Components/AddButton";
import { InteractiveButton } from "Components/InteractiveButton";
import { StringCaser } from "Utils/utils";

const MkdListTableFilterDropdown = ({
  onSubmit,
  columns = [],
  selectedOptions = [],
  onColumnClick = null,
  setOptionValue = null,
  setSelectedOptions = null,
  onOptionValueChange = null,
  onClose,
}) => {
  const [showFilterOptions, setShowFilterOptions] = useState(false);

  // console.log("COLUMNS >>", columns);
  return (
    <div className="filter-form-holder  z-[9999999] grid h-full max-h-full min-h-full w-full min-w-full max-w-full grid-cols-1 grid-rows-[auto_1fr_auto_auto] overflow-hidden rounded-md bg-white p-5 shadow-xl">
      <div className="relative flex items-center justify-end">
        <AddButton
          type="button"
          onClick={() => setShowFilterOptions((prev) => !prev)}
          // disabled={true}
          className={`!shadow-0 !text-sub-500 peer !h-fit !max-h-fit !min-h-fit w-fit  !border-0 !bg-white !p-0 !py-0 font-[700] !underline`}
        >
          Add Filter
        </AddButton>

        <LazyLoad>
          <MkdListTableFilterOptions
            onColumnClick={(data) => onColumnClick && onColumnClick(data)}
            setShowFilterOptions={setShowFilterOptions}
            columns={columns}
            selectedOptions={selectedOptions}
          />
        </LazyLoad>
      </div>
      <div
        // onSubmit={(e) => {
        //   e.preventDefault();
        //   console.log("e >>", e);
        //   if (onSubmit) {
        //     onSubmit();
        //   }
        // }}
        className="overflow-y-auto"
      >
        <div className="!h-full !max-h-full !min-h-full w-full overflow-y-auto">
          {selectedOptions?.map((option, index) => (
            <div
              key={index}
              className="mb-2 grid w-full grid-cols-[1fr_auto]  justify-between gap-2 text-gray-600"
            >
              {/* <div className="h-[2.5rem] rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none">
              {option?.accessor}
            </div>
            <select
              value={option?.operator}
              className="appearance-none rounded-md border-none outline-0"
              onChange={(e) => {
                setOptionValue &&
                  setOptionValue("operator", e.target.value, option?.uid);
              }}
            >
              <option value=""></option>
              <option value="eq">equals</option>
              <option value="cs">contains</option>
              <option value="sw">start with</option>
              <option value="ew">ends with</option>
              <option value="lt">lower than</option>
              <option value="le">lower or equal</option>
              <option value="ge">greater or equal</option>
              <option value="gt">greater than</option>
              <option value="bt">between</option>
              <option value="in">in</option>
              <option value="is">is null</option>
            </select> */}

              {columns?.length ? (
                <>
                  {columns.map((columnData, columnDataIndex) => {
                    if (
                      (columnData?.selected_column &&
                        columnData?.accessor === option?.accessor) ||
                      columnData?.header === option?.accessor ||
                      columnData?.filter_field === option?.accessor
                    ) {
                      if (columnData?.mappingExist) {
                        return (
                          <>
                            <div className="grid w-full grid-cols-1 items-start justify-start">
                              <label
                                className="mb-2 block cursor-pointer text-left text-sm font-bold text-gray-700"
                                htmlFor={option?.uid}
                              >
                                {StringCaser(columnData?.accessor, {
                                  casetype: "capitalize",
                                  separator: "space",
                                })}
                              </label>
                              <select
                                className="!border-soft-200 !h-[3rem] !max-h-[3rem] !min-h-[3rem] appearance-none rounded-md border outline-0 focus:border-primary focus:ring-primary"
                                onChange={(e) => {
                                  setOptionValue &&
                                    setOptionValue(
                                      "value",
                                      e.target.value,
                                      option?.uid
                                    );
                                }}
                                value={option?.value}
                              >
                                <option
                                  value={""}
                                  selected={!option?.value}
                                ></option>
                                {Object.keys(columnData?.mappings).map(
                                  (columnDataKey, index) => (
                                    <option
                                      key={index}
                                      value={columnDataKey}
                                      selected={columnDataKey === option?.value}
                                    >
                                      {columnData?.mappings[columnDataKey]}
                                    </option>
                                  )
                                )}
                              </select>
                            </div>
                          </>
                        );
                      }

                      if (columnData?.join) {
                        return (
                          <div
                            key={columnDataIndex}
                            className="flex w-full items-end justify-start"
                          >
                            <FilterJoinDropdown
                              columnData={columnData}
                              option={option}
                              setOptionValue={setOptionValue}
                            />
                          </div>
                        );
                      }
                      return (
                        <div
                          key={columnDataIndex}
                          className="flex w-full items-end justify-start  !px-[.0625rem]"
                        >
                          <LazyLoad>
                            <MkdDebounceInput
                              type="text"
                              placeholder="Enter value"
                              label={StringCaser(columnData?.accessor, {
                                casetype: "capitalize",
                                separator: " ",
                              })}
                              setValue={(value) => {
                                setOptionValue &&
                                  setOptionValue("value", value, option?.uid);
                              }}
                              value={option?.value}
                              showIcon={false}
                              className="!border-soft-200 !h-[3rem] !max-h-[3rem] !min-h-[3rem] !w-full !min-w-full !max-w-full !rounded-md !border !px-3 !py-2 !leading-tight !text-gray-700 !outline-none focus:border-primary focus:ring-primary"
                              onReady={(value) => {
                                // setOptionValue && setOptionValue("value", value, option?.uid);
                              }}
                            />
                          </LazyLoad>
                        </div>
                      );
                    } else {
                      return null;
                    }
                  })}
                </>
              ) : null}
              {/* <p className="text-xs italic text-red-500">
               {errors.id?.message}
             </p> */}

              <RiDeleteBin5Line
                className="!text-sub-500 cursor-pointer self-end text-2xl"
                onClick={() => {
                  setSelectedOptions((prev) =>
                    prev.filter((op) => op.uid !== option?.uid)
                  );
                }}
              />
            </div>
          ))}
        </div>
      </div>
      <div className="mt-5  flex w-full  gap-5">
        <AddButton
          type="button"
          onClick={() => onClose()}
          // disabled={true}
          className="!border-soft-200 !text-sub-500 !grow self-end !bg-transparent font-bold"
        >
          Cancel
        </AddButton>

        <InteractiveButton
          type="button"
          onClick={() => {
            if (onSubmit) {
              onClose();
              onSubmit();
            }
          }}
          // loading={true}
          // disabled={selectedOptions?.length === 0}
          className={`!grow self-end rounded px-4 py-2 font-bold capitalize text-white`}
        >
          Apply and Close
        </InteractiveButton>
      </div>
      <div className="flex items-center justify-center">
        <AddButton
          type="button"
          onClick={() => setSelectedOptions(() => [])}
          disabled={selectedOptions?.length === 0}
          className={`!shadow-0 !text-sub-500 w-fit !border-0 !bg-white font-[700] !underline`}
        >
          Clear all Filters
        </AddButton>
      </div>
    </div>
  );
};

export default memo(MkdListTableFilterDropdown);
