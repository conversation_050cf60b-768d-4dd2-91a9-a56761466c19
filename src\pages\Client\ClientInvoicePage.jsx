import React, { useState, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";
import { FileText, Download, ArrowLeft } from "lucide-react";
import moment from "moment";
import { ClipLoader } from "react-spinners";
import { useNavigate } from "react-router-dom";

const ClientInvoicePage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(1);
  const [dataTotal, setDataTotal] = useState(0);
  const pageSize = 10;
  const navigate = useNavigate();

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: { path: "invoices" },
    });
    fetchInvoices();
  }, [currentPage]);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const tdk = new TreeSDK();
      const clientId = localStorage.getItem("userClientId");

      // Use TreeQL to get paginated invoices for the client
      const result = await tdk.getPaginate("invoice", {
        page: currentPage,
        size: pageSize,
        filter: [`client_id,eq,${clientId}`],
      });

      if (result) {
        setInvoices(result.list || []);
        setPageCount(result.num_pages || 1);
        setDataTotal(result.total || 0);
      }
    } catch (error) {
      console.error("Error fetching invoices:", error);
      tokenExpireError(dispatch, error.message);
      showToast(globalDispatch, "Failed to fetch invoices", "error");
    } finally {
      setLoading(false);
    }
  };

  const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
      case "paid":
        return "bg-success/10 text-success";
      case "pending":
        return "bg-warning/10 text-warning";
      case "overdue":
        return "bg-danger/10 text-danger";
      default:
        return "bg-primary/10 text-primary";
    }
  };

  return (
    <div className="max-w-screen mx-auto p-4 md:p-6 2xl:p-10">
      <div className="shadow-default mb-5 rounded border border-stroke/50 bg-boxdark">
        <div className="mb-6 flex items-center justify-between px-4 py-5 md:px-6 2xl:px-9">
          <div>
            <h2 className="text-2xl font-bold text-white">My Invoices</h2>
            <p className="mt-1 text-lg text-bodydark">
              View and manage your invoices
            </p>
          </div>
        </div>
      </div>

      <div className="shadow-default rounded border border-strokedark bg-boxdark">
        <div className="flex items-center justify-between border-b border-strokedark px-4 md:px-6 2xl:px-9">
          <h4 className="my-3 text-2xl font-semibold text-white">Invoices</h4>
        </div>

        <div className="p-4 md:p-6 2xl:p-10">
          <div className="custom-overflow text-[12p min-h-[140px] overflow-x-auto">
            <table className="w-full table-auto">
              <thead className="bg-meta-4">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Invoice ID
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Date
                  </th>

                  <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Total
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Status
                  </th>
                  {/* <th className="px-4 py-3 text-xs font-medium tracking-wider text-center uppercase text-bodydark1">
                    Payment Status
                  </th> */}
                  <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Payment Date
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-bodydark1">
                    Actions
                  </th>
                </tr>
              </thead>
              {!loading && invoices.length > 0 ? (
                <tbody className="text-white">
                  {invoices.map((invoice) => (
                    <tr
                      key={invoice.id}
                      className="cursor-pointer border-b border-strokedark text-xs hover:bg-primary/5"
                      onClick={() => navigate(`/client/invoice/${invoice.id}`)}
                    >
                      <td className="whitespace-nowrap px-4 py-4">
                        #{invoice.id}
                      </td>
                      <td className="whitespace-nowrap px-4 py-4">
                        {moment(
                          new Date(invoice.create_at || invoice.created_at)
                        ).format("MMM DD, YYYY")}
                      </td>

                      <td className="whitespace-nowrap px-4 py-4 text-right">
                        ${parseFloat(invoice.total || 0).toFixed(2)}
                      </td>
                      <td className="whitespace-nowrap px-4 py-4">
                        <div className="flex justify-center">
                          <span
                            className={`inline-block rounded px-2.5 py-0.5 text-sm font-medium ${getStatusClass(
                              invoice.status
                            )}`}
                          >
                            {invoice.status || "pending"}
                          </span>
                        </div>
                      </td>
                      {/* <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex justify-center">
                          <span
                            className={`inline-block rounded px-2.5 py-0.5 text-sm font-medium ${
                              invoice.payment_status === "succeeded"
                                ? "bg-success/10 text-success"
                                : "bg-warning/10 text-warning"
                            }`}
                          >
                            {invoice.payment_status || "Not Paid"}
                          </span>
                        </div>
                      </td> */}
                      <td className="whitespace-nowrap px-4 py-4 text-center">
                        {invoice.payment_date
                          ? moment(new Date(invoice.payment_date)).format(
                              "MMM DD, YYYY"
                            )
                          : "N/A"}
                      </td>
                      <td className="whitespace-nowrap px-4 py-4">
                        <div className="flex items-center justify-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/client/invoice/${invoice.id}`);
                            }}
                            className="bg-primary px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-90"
                          >
                            View
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              ) : loading ? (
                <tbody>
                  <tr>
                    <td colSpan={8} className="text-center">
                      <div className="flex h-[140px] items-center justify-center">
                        <ClipLoader color="#fff" size={30} />
                      </div>
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody>
                  <tr>
                    <td colSpan={8} className="text-center">
                      <div className="flex h-[140px] items-center justify-center">
                        <span className="text-bodydark">No invoices found</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              )}
            </table>
          </div>

          {pageCount > 1 && (
            <div className="flex items-center justify-between px-4 py-5">
              <div className="flex items-center gap-4">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="flex items-center justify-center rounded bg-primary px-4 py-2 text-white hover:bg-opacity-90 disabled:bg-opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, pageCount))
                  }
                  disabled={currentPage === pageCount}
                  className="flex items-center justify-center rounded bg-primary px-4 py-2 text-white hover:bg-opacity-90 disabled:bg-opacity-50"
                >
                  Next
                </button>
              </div>
              <span className="text-sm text-bodydark2">
                Page {currentPage} of {pageCount} ({dataTotal} total)
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientInvoicePage;
