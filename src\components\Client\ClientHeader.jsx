import React, { useEffect } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  <PERSON>old<PERSON><PERSON><PERSON>ban,
  Edit3,
  Disc3,
  UserCog2,
  LogOut,
  PanelLeftClose,
  FileText,
} from "lucide-react";
import { AuthContext } from "../../authContext";
import { GlobalContext } from "../../globalContext";
import { getAllEditAPI } from "Src/services/editService";

export const ClientHeader = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);
  const location = useLocation();

  const handleLogout = () => {
    globalDispatch({
      type: "TOGGLE_PROFILE",
      payload: { showProfile: false },
    });
    dispatch({
      type: "LOGOUT",
    });
  };

  const getPendingEdits = async () => {
    const res = await getAllEditAPI({
      user_id: localStorage.getItem("user"),
      page: 1,
      limit: 50000,

      edit_status: 2,
    });

    globalDispatch({
      type: "SET_CURRENT_PENDING_LENGTH",
      payload: { pendingLength: res?.list?.length || 0 },
    });
  };

  useEffect(() => {
    getPendingEdits();
  }, [location.pathname]);

  let { isOpen, showProfile } = state;
  let toggleOpen = (open) =>
    globalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });

  return (
    <aside
      className={`absolute  left-0 top-0 z-[9] flex h-screen w-[180px] flex-col overflow-y-hidden border-r border-strokedark bg-boxdark text-bodydark1 duration-300 ease-linear lg:static lg:translate-x-0 dark:bg-boxdark ${
        !state.isOpen ? "-translate-x-full" : ""
      }`}
    >
      {/* Sidebar Header */}
      <div className="py-5.5 lg:py-6.5 flex items-center justify-between gap-2 px-3 pl-6  lg:pt-[20px]">
        <div className="flex items-center justify-center lg:flex">
          <img
            crossOrigin="anonymous"
            src={
              state.siteLogo ??
              `${window.location.origin}/new/cheerEQ-2-Ed2.png`
            }
            className="h-auto w-[170px]"
            alt="logo"
          />
        </div>
        {/* <button onClick={() => toggleOpen(!state.isOpen)} className="lg:block">
          <PanelLeftClose className="text-bodydark" />
        </button> */}
      </div>

      {/* Sidebar Menu */}
      <div className="no-scrollbar custom-overflow flex flex-col overflow-y-auto duration-300 ease-linear">
        <nav className="mt-2 px-2 py-2 lg:mt-3 lg:px-3">
          <div>
            <h3 className="mb-4 ml-4 text-sm font-semibold text-bodydark2">
              MENU
            </h3>

            <ul className="mb-6 flex flex-col gap-1.5">
              {/* Projects */}
              <li>
                <NavLink
                  to="/client/projects"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "projects"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <FolderKanban className="h-4 w-4" />
                  <span className="text-[15px]">Projects</span>
                </NavLink>
              </li>

              {/* Edits */}
              <li>
                <NavLink
                  to="/client/edits"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "edits" ? "bg-graydark dark:bg-meta-4" : ""
                  }`}
                >
                  <Edit3 className="h-4 w-4" />
                  <span className="text-[15px]">Edits</span>
                  {state?.pendingLength?.pendingLength > 0 && (
                    <span className="absolute right-4 top-1/2 flex h-5 w-5 -translate-y-1/2 items-center justify-center rounded-[2px] bg-red text-xs text-white">
                      {state?.pendingLength?.pendingLength}
                    </span>
                  )}
                </NavLink>
              </li>

              {/* Count Tracks */}
              <li>
                <NavLink
                  to="/client/count-tracks"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "count-tracks"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <Disc3 className="h-4 w-4" />
                  <span className="text-[15px]">Count Tracks</span>
                </NavLink>
              </li>

              {/* Invoices */}
              <li>
                <NavLink
                  to="/client/invoices"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "invoices"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <FileText className="h-4 w-4" />
                  <span className="text-[15px]">Invoices</span>
                </NavLink>
              </li>

              {/* Profile */}
              <li>
                <NavLink
                  to="/client/profile"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "profile" ? "bg-graydark dark:bg-meta-4" : ""
                  }`}
                >
                  <UserCog2 className="h-4 w-4" />
                  <span className="text-[15px]">Profile</span>
                </NavLink>
              </li>

              {/* Logout */}
              <li>
                <NavLink
                  to="/client/login"
                  onClick={handleLogout}
                  className="group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4"
                >
                  <LogOut className="h-4 w-4 text-[#CD4631]" />
                  <span className="text-[#CD4631]">Logout</span>
                </NavLink>
              </li>
            </ul>
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default ClientHeader;
