import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import CustomSelect from "./CustomSelect";
import CustomSelect2 from "./CustomSelect2";

const PaginationBar = ({
  currentPage,
  pageCount,
  callDataAgain = false,
  pageSize = 10,
  dataTotal,
  canPreviousPage,
  canNextPage,
  updatePageSize,
  previousPage,
  nextPage,
  setCurrentPage = false,
  customPage = false,
}) => {
  console.log(customPage);
  console.log(currentPage, pageCount, pageSize);
  const startEntry = currentPage === 0 ? 0 : (currentPage - 1) * pageSize + 1;
  const endEntry = Math.min(startEntry + pageSize - 1, dataTotal);

  // Function to generate page numbers with ellipsis
  const getPageNumbers = () => {
    const delta = 2; // Number of pages to show before and after current page
    const range = [];
    const rangeWithDots = [];

    // Always show first page
    range.push(1);

    for (let i = currentPage - delta; i <= currentPage + delta; i++) {
      if (i > 1 && i < pageCount) {
        range.push(i);
      }
    }

    // Always show last page
    if (pageCount !== 1) {
      range.push(pageCount);
    }

    // Add the page numbers with dots
    let l;
    for (let i of range) {
      if (l) {
        if (i - l === 2) {
          rangeWithDots.push(l + 1);
        } else if (i - l !== 1) {
          rangeWithDots.push("...");
        }
      }
      rangeWithDots.push(i);
      l = i;
    }

    return rangeWithDots;
  };

  console.log(getPageNumbers());
  return (
    <div className="flex justify-between items-center">
      <div className="flex gap-4 items-center">
        <span className="text-sm font-medium text-white whitespace-nowrap">
          {dataTotal > 0
            ? `Showing ${startEntry} to ${endEntry} of ${dataTotal} entries`
            : "No entries to show"}
        </span>
        <CustomSelect2
          className="!w-20"
          position="up"
          label="Size"
          value={pageSize}
          onChange={updatePageSize}
        >
          {customPage ||
            [
              { value: 5, label: 5 },
              { value: 10, label: 10 },
              { value: 20, label: 20 },
              { value: 30, label: 30 },
              { value: 40, label: 40 },
              { value: 50, label: 50 },
            ].map((elem) => {
              return <option value={elem.value}>{elem.label}</option>;
            })}
        </CustomSelect2>
      </div>

      <div className="flex gap-2 items-center">
        <button
          onClick={previousPage}
          disabled={!canPreviousPage}
          className={`flex h-[25px] min-w-[25px] items-center justify-center rounded-md border border-strokedark bg-meta-4  text-sm font-medium text-white transition hover:bg-opacity-90 ${
            !canPreviousPage && "cursor-not-allowed opacity-50"
          }`}
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
        {/* Page Numbers with Ellipsis */}
        <div className="flex gap-1 items-center">
          {getPageNumbers().map((pageNum, idx) => (
            <button
              key={idx}
              onClick={() => {
                if (pageNum !== "..." && pageNum !== currentPage) {
                  console.log(currentPage);
                  if (setCurrentPage) {
                    setCurrentPage(pageNum);
                  }
                  if (callDataAgain) {
                    callDataAgain(pageNum);
                  }
                }
              }}
              disabled={pageNum === "..."}
              className={`flex h-[32px] min-w-[32px] items-center justify-center rounded-md border border-strokedark text-sm font-medium transition ${
                pageNum === currentPage
                  ? "bg-primary text-white"
                  : pageNum === "..."
                  ? "cursor-default bg-meta-4 text-white"
                  : "bg-meta-4 text-white hover:bg-opacity-90"
              }`}
            >
              {pageNum}
            </button>
          ))}
        </div>
        <button
          onClick={nextPage}
          disabled={!canNextPage}
          className={`flex h-[25px] min-w-[25px] items-center justify-center rounded-md border border-strokedark bg-meta-4  text-sm font-medium text-white transition hover:bg-opacity-90 ${
            !canNextPage && "cursor-not-allowed opacity-50"
          }`}
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default PaginationBar;
