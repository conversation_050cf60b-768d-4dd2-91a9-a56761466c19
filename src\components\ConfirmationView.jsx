import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router";
import moment from "moment";
import { getAllEditAPI } from "Src/services/editService";
import { GlobalContext } from "Src/globalContext";
import { Dashboard } from "uppy";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const ConfirmationView = ({
  isOpen,
  setIsOpen,
  setisOpenRequestEdit,
  setOpenEditView,
  editData,
}) => {
  console.log(editData);
  const navigate = useNavigate();
  const BoxRef = useRef(null);
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);

  const getPendingEdits = async () => {
    const res = await getAllEditAPI({
      user_id: localStorage.getItem("user"),
      page: 1,
      limit: 50000,

      edit_status: 2,
    });

    globalDispatch({
      type: "SET_CURRENT_PENDING_LENGTH",
      payload: { pendingLength: res?.list?.length || 0 },
    });
  };

  useEffect(() => {
    document.querySelector("#mainContainer").scrollTo({ top: 0 });
    getPendingEdits();
    window.scrollTo({ top: 0 });
  }, [isOpen]);

  useEffect(() => {
    const handleClick = (event) => {
      if (BoxRef.current && !BoxRef.current.contains(event.target)) {
        setIsOpen(false);
        setOpenEditView("");
      }
    };

    if (isOpen) {
      document.addEventListener("click", handleClick);
    }

    // Cleanup function to remove the event listener
    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, [isOpen]);

  function convertDateFormat(dateString) {
    // Split the date string into year, month, and day

    if (dateString) {
      var parts = dateString?.split("-");

      // Rearrange the parts into the desired format
      var formattedDate =
        parts[1].padStart(2, "0") +
        "-" +
        parts[2].padStart(2, "0") +
        "-" +
        parts[0];

      return formattedDate;
    }
  }

  return (
    <div
      ref={BoxRef}
      className="custom-overflow fixed inset-0 z-[52] flex h-full w-full items-center justify-center overflow-y-auto px-4 py-8 backdrop-blur-sm"
    >
      <div className="shadow-default relative w-full max-w-[800px] rounded border border-strokedark bg-boxdark p-4 sm:p-8 dark:border-strokedark dark:bg-boxdark">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-strokedark pb-4 dark:border-strokedark">
          <div className="flex items-center gap-2">
            <h3 className="text-2xl font-semibold text-white dark:text-white">
              Edit Request Confirmed
            </h3>
          </div>
          <button
            onClick={() => {
              setOpenEditView("");
              setIsOpen(false);
            }}
            className="text-white hover:text-white dark:text-gray-400 dark:hover:text-white"
          >
            <FontAwesomeIcon icon="close" className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="mt-6">
          <div className="mb-6 space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">
                Program & Team:
              </span>
              <span className="text-sm text-white">
                {editData?.program_name} - {editData?.team_name}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">Edit Type:</span>
              <span className="text-sm text-white">{editData?.edit_type}</span>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">
                Date Requested:
              </span>
              <span className="text-sm text-white">
                {moment(editData?.request_date, "YYYY-MM-DD").format(
                  "MM-DD-YYYY"
                )}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">Due Date:</span>
              <span className="text-sm text-white">
                {convertDateFormat(editData.due_date)}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex flex-wrap items-center gap-4">
            <button
              onClick={() => {
                navigate("/client/projects");
                setOpenEditView("");
                setIsOpen(false);
              }}
              className="flex items-center justify-center gap-2 rounded bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
            >
              <FontAwesomeIcon icon="arrow-left" className="text-xs" />
              <span>Back to Projects</span>
            </button>

            <button
              onClick={() => {
                navigate("/client/edits");
                setOpenEditView("");
                setIsOpen(false);
              }}
              className="flex items-center justify-center gap-2 rounded bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
            >
              <FontAwesomeIcon icon="arrow-left" className="text-xs" />
              <span>Back to Edits</span>
            </button>

            <button
              onClick={() => {
                setIsOpen(false);
                setisOpenRequestEdit(true);
                setOpenEditView("");
              }}
              className="flex items-center justify-center gap-2 rounded bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
            >
              <span>Request Edit</span>
              <FontAwesomeIcon icon="plus" className="text-xs" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationView;
