export const REQUEST_FAILED = "REQUEST_FAILED";
export const REQUEST_SUCCESS = "REQUEST_SUCCESS";
export const REQUEST_LOADING = "REQUEST_LOADING";
export const SET_GLOBAL_PROPERTY = "SET_GLOBAL_PROPERTY";
export const ADD_BACKGROUND_PROCESS = "ADD_BACKGROUND_PROCESS";
export const SET_BACKGROUND_PROCESSES = "SET_BACKGROUND_PROCESSES";
export const REMOVE_BACKGROUND_PROCESS = "REMOVE_BACKGROUND_PROCESS";

const RequestItems = {
  viewModel: "viewModel",
  createModel: "createModel",
  updateModel: "updateModel",
  listModel: "listModel",
  deleteModel: "deleteModel",
  customRequest: "customRequest",
};

const BackgroundProcessStatus = {
  PENDING: "PENDING",
  REJECTED: "REJECTED",
  FULFILLED: "FULFILLED",
};

Object.freeze(RequestItems);
Object.freeze(BackgroundProcessStatus);

export { RequestItems, BackgroundProcessStatus };
