import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";

const NewEightCount = ({
  setIsOpen,
  isDuplicate = false,
  handleDuplicate,
  handleNewEightCount,
}) => {
  return (
    <div className="custom-overflow fixed inset-0 z-[52] flex h-full w-full items-center justify-center overflow-y-auto px-4 py-8 backdrop-blur-sm">
      <div className="shadow-default relative w-full max-w-md rounded border border-strokedark bg-boxdark p-4 sm:p-8 dark:border-strokedark dark:bg-boxdark">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-strokedark pb-4 dark:border-strokedark">
          <h3 className="text-2xl font-semibold text-white dark:text-white">
            New 8 Count - Intro
          </h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-white hover:text-white dark:text-gray-400 dark:hover:text-white"
          >
            <FontAwesomeIcon icon="close" className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        {!isDuplicate && (
          <div className="mt-6">
            <h5 className="mb-6 text-center text-base font-medium text-white">
              What count would you like your music to start on?
            </h5>
            <div className="mb-8 grid grid-cols-8 gap-4">
              {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => (
                <button
                  key={num}
                  className="flex h-12 items-center justify-center rounded bg-primary text-xl font-medium text-white hover:bg-opacity-90"
                  onClick={(e) => {
                    !isDuplicate ? handleNewEightCount(e) : handleDuplicate(e);
                  }}
                >
                  {num}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewEightCount;
