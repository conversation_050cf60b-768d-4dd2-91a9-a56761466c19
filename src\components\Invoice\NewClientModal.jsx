import React, { useState } from "react";
import { X } from "lucide-react";
import { addClientAPI } from "Src/services/clientService";

const NewClientModal = ({
  isOpen,
  onClose,
  onAddClient,
  showToast,
  globalDispatch,
}) => {
  // Local state for form inputs - always declare hooks at the top level
  const [program, setProgram] = useState("");
  const [email, setEmail] = useState("");

  // Only render if the modal is open
  if (!isOpen) return null;

  // Handle close
  const handleClose = () => {
    console.log("Close button clicked");
    onClose();
  };

  // Handle add client
  const handleAddClient = async () => {
    console.log("Add client button clicked");
    if (program && email) {
      await addClientAPI({
        program: program,
        email: email,
        user_id: parseInt(localStorage.getItem("user")),
      });
      onAddClient({
        program: program,
        email: email,
      });
    } else {
      showToast(globalDispatch, "Please fill in all client details", "error");
    }
  };

  return (
    <div
      className="flex fixed inset-0 justify-center items-center"
      style={{ zIndex: 9999 }}
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={handleClose}
      ></div>

      {/* Modal content */}
      <div
        className="relative p-6 w-96 rounded-lg bg-boxdark"
        style={{ zIndex: 10000 }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-medium text-white">Add New Client</h3>
          <button
            type="button"
            onClick={handleClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium text-white">
            Program Name
          </label>
          <input
            type="text"
            value={program}
            onChange={(e) => setProgram(e.target.value)}
            className="px-3 py-2 w-full text-white bg-transparent rounded border border-stroke"
            placeholder="Enter client program name"
          />
        </div>

        <div className="mb-6">
          <label className="block mb-2 text-sm font-medium text-white">
            Email
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="px-3 py-2 w-full text-white bg-transparent rounded border border-stroke"
            placeholder="Enter client email"
          />
        </div>

        <div className="flex gap-3">
          <button
            type="button"
            onClick={handleAddClient}
            className="px-4 py-2 w-full text-sm font-medium text-white rounded bg-primary"
          >
            Add Client
          </button>
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 w-full text-sm font-medium text-white rounded bg-danger"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default NewClientModal;
