import React, { useContext, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";
import { Download, MoreVertical } from "lucide-react";
import { GlobalContext, showToast } from "Src/globalContext";

const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

const UploadedDemo = ({
  uploadedFiles,
  isSong,
  setDeleteFileId,
  setEmployeeType,
  setFileUploadType,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  const [activeMenu, setActiveMenu] = useState(null);
  const { dispatch } = useContext(GlobalContext);
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);

  const handleDownload = async (url, filename) => {
    try {
      showToast(dispatch, "File Downloading", 3000);
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("Download failed:", error);
    }
    setActiveMenu(null); // Close menu after download starts
  };

  // Close menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = () => setActiveMenu(null);
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  const handleDeleteFileModalClose = () => {
    setShowDeleteFileConfirmModal(false);
  };

  const handleDeleteFileSubmit = () => {
    setDeleteFileId(localDeleteFileId);
    setShowDeleteFileConfirmModal(false);
  };

  return (
    <>
      <div
        onClick={() => {
          setEmployeeType("writer");
          setFileUploadType("demo");
        }}
        className="mb-2 block w-full rounded-md border border-gray-500 bg-gray-800 p-5 shadow"
      >
        <div className="mb-4 text-center text-xl font-semibold text-white">
          Demo Files
        </div>

        <div className="flex w-full flex-row flex-wrap items-center gap-4">
          {uploadedFiles &&
            uploadedFiles.length > 0 &&
            uploadedFiles.map((file, index) => {
              let fileSrc = `${file.url}`;
              let fileSrcTemp = fileSrc.split("/").pop();
              let fileExtension = fileSrc.split(".").pop();
              return (
                <div key={index} className="flex w-[32%] flex-col gap-1">
                  {audioFileTypes.includes(fileExtension) && (
                    <div className="flex items-center gap-3">
                      <a
                        className="max-w-[85%] truncate text-sm text-white underline"
                        href={fileSrc}
                        rel="noreferrer"
                        target="_blank"
                      >
                        {fileSrcTemp}
                      </a>
                      <div className="relative">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setActiveMenu(activeMenu === index ? null : index);
                          }}
                          className="rounded-full p-1 hover:bg-gray-700"
                        >
                          <MoreVertical className="h-5 w-5 text-primary" />
                        </button>

                        {activeMenu === index && (
                          <div className="absolute right-0 z-50 mt-1 w-48 rounded-md bg-boxdark shadow-lg ring-1 ring-black ring-opacity-5">
                            <div className="py-1">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDownload(fileSrc, fileSrcTemp);
                                }}
                                className="flex w-full items-center px-4 py-2 text-sm text-white hover:bg-gray-700"
                              >
                                <Download className="mr-2 h-4 w-4" />
                                Download
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setShowDeleteFileConfirmModal(true);
                                  setLocalDeleteFileId(file.id);
                                }}
                                className="flex w-full items-center px-4 py-2 text-sm text-danger hover:bg-gray-700"
                              >
                                <FontAwesomeIcon
                                  icon="fa-solid fa-trash"
                                  className="mr-2 h-4 w-4"
                                />
                                Delete
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  <div className="flex flex-row items-center gap-4">
                    {audioFileTypes.includes(fileExtension) && (
                      <AudioPlayer fileSource={fileSrc} />
                    )}
                    {!audioFileTypes.includes(fileExtension) && (
                      <a
                        className="truncate text-sm text-white underline"
                        href={fileSrc}
                        rel="noreferrer"
                        target="_blank"
                      >
                        {fileSrcTemp}
                      </a>
                    )}
                  </div>
                </div>
              );
            })}
        </div>

        <div className="mt-6 flex flex-col items-center justify-center">
          <span className="my-2 text-lg font-semibold text-white">
            Upload more files
          </span>
          <FileUpload
            uploadedFilesProgressData={uploadedFilesProgressData}
            justify="center"
            items="center"
            maxFileSize={500}
            setFormData={setFormData}
          />
        </div>
      </div>

      {showDeleteFileConfirmModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to delete this file?`}
          setModalClose={handleDeleteFileModalClose}
          setFormYes={handleDeleteFileSubmit}
        />
      ) : null}
    </>
  );
};

export default UploadedDemo;
