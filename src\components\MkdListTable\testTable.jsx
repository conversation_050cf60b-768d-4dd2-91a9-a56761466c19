import React, { useState } from "react";

const TestTable = () => {
  const [selectedItems, setSelectedItems] = useState([]);
  const [allSelected, setAllSelected] = useState(false);

  const data = Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    name: `Name ${i + 1}`,
    age: 20 + i,
    address: `Address ${i + 1}`,
    city: `City ${i + 1}`,
    state: `State ${i + 1}`,
    country: `Country ${i + 1}`,
    postalCode: `Company Postal ${i + 1}`,
    phone: `Phone ${i + 1}`,
    email: `Email ${i + 1}`,
    company: `Company ${i + 1}`,
    department: `Company Department ${i + 1}`,
    position: `Position ${i + 1}`,
    salary: `$${(i + 1) * 1000}`,
    hireDate: `2021-01-${i + 1}`,
    manager: `Manager ${i + 1}`,
    status: `Status ${i + 1}`,
    project: `Project ${i + 1}`,
    team: `Team ${i + 1}`,
  }));

  const columns = [
    { header: "Name", accessor: "name" },
    { header: "Age", accessor: "age" },
    { header: "Address", accessor: "address" },
    { header: "City", accessor: "city" },
    { header: "State", accessor: "state" },
    { header: "Country", accessor: "country" },
    { header: "Postal Code", accessor: "postalCode" },
    { header: "Phone", accessor: "phone" },
    { header: "Email", accessor: "email" },
    { header: "member", accessor: "member" },
    { header: "Department", accessor: "department" },
    { header: "Position", accessor: "position" },
    { header: "Salary", accessor: "salary" },
    { header: "Hire Date", accessor: "hireDate" },
    { header: "Manager", accessor: "manager" },
    { header: "Status", accessor: "status" },
    { header: "Project", accessor: "project" },
    { header: "Team", accessor: "team" },
  ];

  const actions = [
    {
      label: "Edit",
      onClick: (row) => alert(`Edit ${row.name}`),
    },
    {
      label: "Delete",
      onClick: (row) => alert(`Delete ${row.name}`),
    },
  ];

  const handleSelectAll = () => {
    if (allSelected) {
      setSelectedItems([]);
    } else {
      setSelectedItems(data.map((item) => item.id));
    }
    setAllSelected(!allSelected);
  };

  const handleSelectRow = (id) => {
    if (selectedItems.includes(id)) {
      setSelectedItems(selectedItems.filter((item) => item !== id));
    } else {
      setSelectedItems([...selectedItems, id]);
    }
  };

  return (
    <div className="relative max-h-[500px] w-full  min-w-full max-w-full overflow-auto">
      <table className="h-fit min-w-full table-auto border-collapse divide-y divide-gray-200 rounded-md">
        <thead className="">
          <tr className="!h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem]">
            <th className="$ sticky -left-[0.05rem] -top-[0.05rem] z-20 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] !max-w-[2.65rem] bg-gray-100 px-[.75rem] py-[.5rem] text-xs font-medium capitalize tracking-wider text-gray-500">
              <input
                type="checkbox"
                checked={allSelected}
                className={`focus:shadow-outline !h-4 !w-4 cursor-pointer appearance-none rounded border leading-tight text-primary shadow focus:outline-none focus:ring-0`}
                onChange={handleSelectAll}
              />
            </th>
            <th className="$ sticky -top-[0.05rem] left-10 z-20 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] max-w-[auto] bg-gray-100 px-[.75rem] py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500">
              Row
            </th>
            {columns.map((col, index) => (
              <th
                key={index}
                className="$ sticky -top-[0.05rem] z-10 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[6.25rem]  !min-w-[6.25rem] !max-w-[auto] shrink-0 grow border bg-gray-100 px-6 py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500"
              >
                {col.header}
              </th>
            ))}
            <th className="$ sticky -right-[0.05rem] -top-[0.05rem] z-20 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[6.25rem]  !min-w-[6.25rem] max-w-[auto] shrink-0 grow border bg-gray-100 px-[.75rem] py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {data.map((row, rowIndex) => (
            <tr
              className="!h-[3rem] !max-h-[3rem] !min-h-[3rem] border-b"
              key={rowIndex}
            >
              <td className="text-sub-500 sticky -left-[0.05rem] z-10 !h-full !max-h-full !min-h-full  !w-[2.65rem] !min-w-[2.65rem] !max-w-[2.65rem] cursor-pointer whitespace-nowrap border border-b bg-white px-[.75rem] py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(row.id)}
                  className={`focus:shadow-outline mr-1 !h-4 !w-4 cursor-pointer appearance-none rounded border leading-tight text-primary shadow focus:outline-none focus:ring-0`}
                  onChange={() => handleSelectRow(row.id)}
                />
              </td>
              <td className="sticky left-10 z-10 flex h-full w-[auto] !min-w-[2.65rem] !max-w-[auto] items-center whitespace-nowrap bg-white px-[.75rem] py-[.5rem] text-sm">
                {rowIndex + 1}
              </td>
              {columns.map((col, colIndex) => (
                <td key={colIndex} className="whitespace-nowrap border-b px-6">
                  {row[col.accessor]}
                </td>
              ))}
              <td className="sticky -right-[0.0] z-10 whitespace-nowrap border border-b bg-white px-[.75rem] py-[.5rem]">
                {actions.map((action, actionIndex) => (
                  <button
                    key={actionIndex}
                    onClick={() => action.onClick(row)}
                    className="mr-2 rounded bg-blue-500 p-1 text-white"
                  >
                    {action.label}
                  </button>
                ))}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TestTable;
