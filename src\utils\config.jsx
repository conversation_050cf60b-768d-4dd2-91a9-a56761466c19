export const packagingListMap = {
  each: "each",
  carton: "carton",
  pallet: "pallet",
};
export const optionTypes = {
  STATIC: "static",
  DROPDOWN: "dropdown",
};
export const actionsDefault = {
  export: {
    show: true,
    multiple: true,
    action: null,
    showText: false,
    className: "!bg-transparent !border-0 !p-0",
  },
  add: {
    show: true,
    action: null,
    multiple: true,
    showChildren: true,
    children: "+ Add",
  },
};
export const operations = {
  EQUAL: "eq",
  NOT_EQUAL: "neq",
  CONTAINS: "cs",
  NOT_CONTAINS: "ncs",
  START_WITH: "sw",
  NOT_START_WITH: "nsw",
  END_WITH: "ew",
  NOT_END_WITH: "new",
  LESS_THAN: "lt",
  NOT_LESS_THAN: "nlt",
  GREATER_THAN: "gt",
  BETWEEN: "bt",
  NOT_BETWEEN: "nbt",
  LESS_THAN_OR_EQUAL: "le",
  NOT_LESS_THAN_OR_EQUAL: "nle",
  GREATER_THAN_OR_EQUAL: "ge",
  NOT_GREATER_THAN_OR_EQUAL: "nge",
  NOT_GREATER_THAN: "ngt",
  IS_NULL: "is",
  IS_NOT_NULL: "nis",
  IN: "in",
  NOT_IN: "nin",
};

export const operationActions = {
  VISIBILITY: "hide",
  INTERACTION: "disable",
};

/**
 *@deprecated
 *  */
export const operationMap = {
  eq: "===",
  neq: "!==",
  isn: "isn",
  isnn: "isnn",
  cs: "cs",
  sw: "sw",
  ew: "ew",
  lt: "lt",
  gt: "gt",
  bt: "bt",
  in: "in",
};

export const formType = {
  login: "login",
  signup: "signup",
  add: "add",
  edit: "edit",
  search: "search",
  custom: "custom",
};

export const colors = {
  primary: "#0ea5e9",
  signup: "signup",
  add: "add",
  edit: "edit",
  search: "search",
  custom: "custom",
  secondary: "#F594C9",
  lightInfo: "#29282980",
};

export const RoleMap = {
  member: "member",
  admin: "admin",
};
export const RoleType = {
  member: "Corporate Manager",
  admin: "Admin",
};
export const Role = {
  STAKEHOLDER: "stakeholder",
  MEMBER: "member",
  ADMIN: "admin",
};

export const UpdateCardViews = {
  MY_UPDATES: "my_updates",
  TEAM_UPDATES: "team_updates",
};

export const ReactionMap = {
  SMILE: "smile",
  SAD: "sad",
  GLAD: "glad",
  LIKE: "like",
  DISLIKE: "dislike",
  INSIGHTFUL: "insightful",
  ROCKET: "mingcuterocket",
};
export const AlternateReactionMap = {
  smile: "SMILE",
  sad: "SAD",
  glad: "GLAD",
  like: "LIKE",
  dislike: "DISLIKE",
  insightful: "INSIGHTFUL",
  mingcuterocket: "ROCKET",
};

export const planUsage = {
  pro: 5,
  "pro yearly": 5,
  business: 10,
  "business yearly": 10,
  enterprise: Infinity,
};

export const PlanFundManagerAccess = {
  pro: 5,
  "pro yearly": 5,
  business: 25,
  "business yearly": 25,
  enterprise: 100,
  "enterprise yearly": 100,
};
