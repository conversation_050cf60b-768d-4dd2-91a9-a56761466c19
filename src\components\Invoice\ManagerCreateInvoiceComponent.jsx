import React, { useState, useEffect } from "react";
import { GlobalContext, showToast } from "Src/globalContext";
import CustomSelect2 from "Components/CustomSelect2";
import moment from "moment";
import { ArrowLeft, Plus, Trash2, PlusCircle, Download } from "lucide-react";
import { PDFDownloadLink } from "@react-pdf/renderer";
import { SingleDatePicker } from "react-dates";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import {
  getAllMembersForManager,
  retrieveAllForClientForManager,
  retrieveAllForMixTypeForManager,
  retrieveAllForMixSeasonForManager,
} from "Src/services/managerServices";
import { AssignClient } from "Src/services/clientService";
import MkdSDK from "Utils/MkdSDK";
import NewClientModal from "./NewClientModal";
import InvoiceQuotePDF from "./InvoiceQuotePDF";
// We're not using the utility functions directly anymore

const ManagerCreateInvoiceComponent = ({ onClose, userDetails, onSubmit }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [selectedClientId, setSelectedClientId] = useState("");
  const [newClientData, setNewClientData] = useState({
    program: "",
    email: "",
  });
  const [producers, setProducers] = useState([]);
  const [clients, setClients] = useState([]);
  const [mixTypes, setMixTypes] = useState([]);
  const [selectedProducer, setSelectedProducer] = useState("");
  // Store producer-specific mix types
  const [, setProducerMixTypes] = useState({});
  const [producerMixSeasons, setProducerMixSeasons] = useState({});
  const [mixSeasons, setMixSeasons] = useState([]);
  const [focusedInput, setFocusedInput] = React.useState({
    date: null,
    dueDate: null,
    musicSurveyDue: {},
    submission: {},
    estimatedCompletion: {},
  });
  const [invoiceDates, setInvoiceDates] = useState({
    invoiceDate: new Date().toISOString().split("T")[0],
    invoiceDueDate: "",
  });
  const [showNewClientModal, setShowNewClientModal] = useState(false);
  // Total price is used in the UI and for calculations
  // eslint-disable-next-line no-unused-vars
  const [totalPrice, setTotalPrice] = useState(0);
  // Loading state for API calls
  const [loading, setLoading] = useState(false);

  const [invoiceData, setInvoiceData] = useState({
    notes: "",
    termsAndConditions:
      userDetails?.contract_agreement
        ?.replace(/<[^>]*>/g, "")
        .replace(/&nbsp;/g, " ") || "",
    depositAmount: 0,
    depositPercentage: userDetails?.deposit_percent || 30,
    items: [
      {
        mixDate: "",
        producer: "",
        mixType: "",
        teamName: "Team 1",
        division: "TBD",
        musicSurveyDue: "",
        routineSubmissionDue: "",
        estimatedCompletion: "",
        price: 0,
        quantity: 1,
        discount: 0,
        mixSeason: "",
        mixSeasonId: "",
        mixTypeId: "",
        producers: [],
        description: "", // Initialize description field
        specialType: "",
        isSpecial: false,
        isSpecialRow: false,
      },
    ],
  });
  const [isQuote, setIsQuote] = useState(false);

  // SunEditor button list
  const buttonList = {
    complex: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["removeFormat"],
      ["fontColor", "hiliteColor"],
      ["indent", "outdent"],
      ["align", "horizontalRule", "list", "table"],
      ["link"],
      ["fullScreen", "showBlocks", "codeView"],
      ["preview"],
    ],
  };

  // Get SunEditor instance
  const [editorInstance, setEditorInstance] = useState(null);

  const getSunEditorInstance = (sunEditor) => {
    setEditorInstance(sunEditor);
  };

  // Add function to handle quote download
  const handleQuoteDownload = () => {
    if (!selectedClientId && (!newClientData.program || !newClientData.email)) {
      showToast(
        globalDispatch,
        "Please select a client or enter new client details",
        3000,
        "error"
      );
      return false;
    }
    return true;
  };

  // Fetch company info directly
  const [companyInfo, setCompanyInfo] = useState(null);

  const getInfo = async () => {
    try {
      // Use the SDK directly to get the raw company info
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      if (!response.error) {
        console.log("Company info:", response);
        setCompanyInfo(response);

        // Update invoice data with company settings
        // Use manager settings if available, otherwise use main member settings
        const manager = response.company?.manager;
        const mainMember = response.company?.main_member;

        setInvoiceData((prev) => ({
          ...prev,
          termsAndConditions:
            (manager && manager.contract_agreement) ||
            (mainMember && mainMember.contract_agreement) ||
            userDetails?.contract_agreement ||
            "",
          depositPercentage:
            (manager && manager.deposit_percent) ||
            (mainMember && mainMember.deposit_percent) ||
            userDetails?.deposit_percent ||
            30,
        }));
      } else {
        console.error(
          "Error fetching company info:",
          response.message || "Unknown error"
        );
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    }
  };

  useEffect(() => {
    getInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Check if producers in invoice items have invoice subscription
  const checkProducerInvoiceSubscription = () => {
    if (!companyInfo || !companyInfo.company) return true;

    // Get all producers used in invoice items
    const producersInItems = invoiceData.items
      .filter((item) => !item.isSpecialRow && !item.isSpecial && item.producer)
      .map((item) => parseInt(item.producer));

    // If no producers are selected in items, return true (allow creation)
    if (producersInItems.length === 0) return true;

    // Check if any of the selected producers don't have invoice subscription
    const producersWithoutSubscription = companyInfo.company.members.filter(
      (member) =>
        producersInItems.includes(member.id) &&
        member.has_invoice_subscription !== 1
    );

    // Also check main member if they're included in the producers
    if (
      companyInfo.company.main_member &&
      producersInItems.includes(companyInfo.company.main_member.id) &&
      companyInfo.company.main_member.has_invoice_subscription !== 1
    ) {
      producersWithoutSubscription.push(companyInfo.company.main_member);
    }

    return producersWithoutSubscription.length === 0;
  };

  // Fetch producers, their clients, mix types, and mix seasons
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch producers
        const result = await getAllMembersForManager(
          localStorage.getItem("user")
        );
        if (!result.error && result.list.length > 0) {
          const producerList = result.list.map((producer) => ({
            value: producer.id,
            label: producer.first_name + " " + producer.last_name,
          }));
          setProducers(producerList);

          // Fetch clients for all producers
          const clientResult = await retrieveAllForClientForManager(1, 1000, {
            member_ids: producerList.map((p) => p.value),
          });

          if (!clientResult.error) {
            const uniqueClients = Array.from(
              new Map(
                clientResult.list.map((client) => [client.id, client])
              ).values()
            );
            setClients(uniqueClients);
          }

          // Fetch mix types for all producers
          const mixTypeResult = await retrieveAllForMixTypeForManager(1, 1000, {
            member_ids: producerList.map((p) => p.value),
          });

          if (!mixTypeResult.error) {
            // Create a map of producer IDs to their mix types
            const mixTypesByProducer = {};
            mixTypeResult.list.forEach((mixType) => {
              if (!mixTypesByProducer[mixType.user_id]) {
                mixTypesByProducer[mixType.user_id] = [];
              }
              mixTypesByProducer[mixType.user_id].push(mixType);
            });
            setProducerMixTypes(mixTypesByProducer);

            // Create a flat list of all mix types with producer labels
            const allMixTypes = mixTypeResult.list.map((mixType) => {
              const producer = producerList.find(
                (p) => p.value === mixType.user_id
              );
              return {
                ...mixType,
                member_id: mixType.user_id,
                displayName: `${mixType.name} > ${
                  producer?.label || "Unknown Producer"
                }`,
                price: parseFloat(mixType.price) || 0,
              };
            });
            setMixTypes(allMixTypes);
          }

          // Fetch mix seasons for all producers
          try {
            // Get all mix seasons for all producers
            const mixSeasonResult = await retrieveAllForMixSeasonForManager(
              1,
              1000,
              {
                member_ids: producerList.map((p) => p.value),
              }
            );

            if (!mixSeasonResult.error) {
              // Create a map of producer IDs to their mix seasons
              const seasonsByProducer = {};
              const allSeasons = [];

              // Process the mix seasons and organize by producer
              mixSeasonResult.list.forEach((season) => {
                const producerId = season.user_id;

                // Only include active seasons (status === 1)
                if (season.status === 1) {
                  if (!seasonsByProducer[producerId]) {
                    seasonsByProducer[producerId] = [];
                  }

                  seasonsByProducer[producerId].push(season);
                  allSeasons.push(season);
                }
              });

              setProducerMixSeasons(seasonsByProducer);
              setMixSeasons(allSeasons);
            }
          } catch (error) {
            console.error("Error fetching mix seasons:", error);
            showToast(
              globalDispatch,
              "Failed to fetch mix seasons",
              3000,
              "error"
            );
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        showToast(globalDispatch, "Failed to fetch data", 3000, "error");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [globalDispatch]);

  // Calculate total price whenever invoice items change
  useEffect(() => {
    if (invoiceData.items && invoiceData.items.length > 0) {
      const total = calculateTotalPrice(invoiceData.items);
      setTotalPrice(total);
    }
  }, [invoiceData.items]);

  // Update available mix types when producer is selected
  const getAvailableMixTypes = (producerId) => {
    if (!producerId) return [];
    return mixTypes.filter((mt) => mt.user_id === parseInt(producerId));
  };

  // Get available mix seasons for a specific producer
  const getAvailableMixSeasons = (producerId) => {
    if (!producerId || !producerMixSeasons[producerId]) return [];
    return producerMixSeasons[producerId];
  };

  // Calculate total price for all items
  const calculateTotalPrice = (items) => {
    return items.reduce((sum, item) => {
      if (item.isSpecialRow || item.isSpecial) {
        const price = parseFloat(item.price) || 0;
        const discount = parseFloat(item.discount) || 0;
        return sum + (price - (price * discount) / 100);
      } else {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;

        // Apply discount to price
        const discountedPrice = price - (price * discount) / 100;

        return sum + discountedPrice * quantity;
      }
    }, 0);
  };

  const calculateDates = (mixDate, index) => {
    if (!mixDate) return;

    // Parse settings with error handling
    let surveySettings = { weeks: 8, day: "Monday" };
    let routineSettings = { weeks: 1, day: "Monday" };
    let deliverySettings = { weeks: 1, day: "Friday" };

    // Use settings from company info if available
    if (companyInfo?.company) {
      // Use manager settings if available, otherwise use main member settings
      const manager = companyInfo.company?.manager;
      const mainMember = companyInfo.company?.main_member;

      if (manager && manager.estimated_delivery) {
        deliverySettings = manager.estimated_delivery;
      } else if (mainMember && mainMember.estimated_delivery) {
        deliverySettings = mainMember.estimated_delivery;
      }

      if (manager && manager.routine_submission_date) {
        routineSettings = manager.routine_submission_date;
      } else if (mainMember && mainMember.routine_submission_date) {
        routineSettings = mainMember.routine_submission_date;
      }

      if (manager && manager.survey) {
        surveySettings = manager.survey;
      } else if (mainMember && mainMember.survey) {
        surveySettings = mainMember.survey;
      }
    }

    // Function to find the previous occurrence of a day
    const findPreviousDay = (date, targetDay) => {
      const days = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      const targetDayIndex = days.indexOf(targetDay);
      let currentDate = moment(date);

      while (currentDate.day() !== targetDayIndex) {
        currentDate.subtract(1, "days");
      }
      return currentDate;
    };

    // Calculate survey date (backwards from mix date)
    const surveyDate = findPreviousDay(
      moment(mixDate).subtract(surveySettings.weeks, "weeks"),
      surveySettings.day
    );

    // Calculate routine submission date (backwards from mix date)
    const submissionDate = findPreviousDay(
      moment(mixDate).subtract(routineSettings.weeks, "weeks"),
      routineSettings.day
    );

    // Calculate estimated completion date (forward from mix date)
    const completionDate = findPreviousDay(
      moment(mixDate).add(deliverySettings.weeks, "weeks"),
      deliverySettings.day
    );

    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      newItems[index] = {
        ...newItems[index],
        musicSurveyDue: surveyDate.format("YYYY-MM-DD"),
        routineSubmissionDue: submissionDate.format("YYYY-MM-DD"),
        estimatedCompletion: completionDate.format("YYYY-MM-DD"),
      };
      return { ...prev, items: newItems };
    });
  };

  const handleAddItem = () => {
    setInvoiceData((prev) => {
      // Count only non-special items to determine the next team number
      const regularItems = prev.items.filter(
        (item) => !item.isSpecial && !item.isSpecialRow
      );
      const nextTeamNumber = regularItems.length + 1;
      return {
        ...prev,
        items: [
          ...prev.items,
          {
            mixDate: "",
            producer: "",
            mixType: "",
            teamName: `Team ${nextTeamNumber}`,
            division: "TBD",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            price: 0,
            quantity: 1,
            discount: 0,
            mixSeason: "",
            mixSeasonId: "",
            mixTypeId: "",
            producers: [],
            description: "", // Initialize description field
            specialType: "",
            isSpecial: false,
            isSpecialRow: false,
          },
        ],
      };
    });
  };

  const handleAddSpecialRow = () => {
    setInvoiceData((prev) => {
      return {
        ...prev,
        items: [
          ...prev.items,
          {
            name: "Additional Charge",
            price: 0,
            quantity: 1,
            discount: 0,
            // No producer field for special charges
            producer: "",
            producers: [],
            description: "Additional Charge", // Initialize description field with the charge name
            specialType: "additional",
            isSpecial: true,
            isSpecialRow: true,
            // Empty values for required fields
            mixDate: "",
            teamName: "",
            division: "",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            mixSeasonId: "",
            mixTypeId: "",
          },
        ],
      };
    });
  };

  const handleRemoveItem = (indexToRemove) => {
    setInvoiceData((prev) => {
      const newItems = prev.items.filter((_, index) => index !== indexToRemove);

      // Count regular items and renumber them
      let regularItemCount = 0;
      const updatedItems = newItems.map((item) => {
        if (!item.isSpecial && !item.isSpecialRow) {
          regularItemCount++;
          if (item.teamName && item.teamName.startsWith("Team ")) {
            return { ...item, teamName: `Team ${regularItemCount}` };
          }
        }
        return item;
      });

      // Recalculate total price
      const total = calculateTotalPrice(updatedItems);
      setTotalPrice(total);

      return { ...prev, items: updatedItems };
    });
  };

  const handleItemChange = (index, field, value) => {
    setInvoiceData((prev) => {
      const newItems = [...prev.items];
      const item = newItems[index];

      if (field === "teamName") {
        if (value !== "" || !item.teamName?.startsWith("Team ")) {
          item.teamName = value;
        }
      } else if (field === "division") {
        if (value !== "" || item.division !== "TBD") {
          item.division = value;
        }
      } else if (field === "mixSeason") {
        item.mixSeason = value;
        // Auto-set the discount if a season is selected
        if (value) {
          const selectedSeason = mixSeasons.find(
            (s) => s.id === parseInt(value)
          );
          if (selectedSeason) {
            item.discount = selectedSeason.discount || 0;
          }
        }
      } else if (field === "producer") {
        item.producer = value;

        // Update the producers array with the producer name
        if (value) {
          const producerObj = producers.find(
            (p) => p.value === parseInt(value)
          );
          if (producerObj) {
            item.producers = [producerObj.label]; // Store producer name as string in array
          }
        } else {
          item.producers = []; // Clear producers array if no producer selected
        }
      } else if (field === "name" && (item.isSpecialRow || item.isSpecial)) {
        // Update both name and description for special charges
        item.name = value;
        item.description = value;
      } else {
        item[field] = value;
      }

      if (
        !item.isSpecialRow &&
        !item.isSpecial &&
        (field === "quantity" || field === "price" || field === "discount")
      ) {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const discount = parseFloat(item.discount) || 0;

        // Apply fixed discount to price
        const discountedPrice = Math.max(0, price - discount);

        item.amount = discountedPrice * quantity;
      }

      if (
        (item.isSpecialRow || item.isSpecial) &&
        (field === "price" || field === "discount")
      ) {
        const price = parseFloat(item.price) || 0;
        const discount = parseFloat(item.discount) || 0;
        item.amount = Math.max(0, price - discount);
      }

      // Calculate subtotal and total
      const subtotal = newItems.reduce(
        (sum, item) => sum + (item.amount || 0),
        0
      );
      const total = subtotal + subtotal * (prev.tax / 100);

      // Update the total price state
      setTotalPrice(total);

      return {
        ...prev,
        items: newItems,
        subtotal,
        total,
      };
    });
  };

  const handleCreateInvoice = async () => {
    try {
      // Check if all producers have invoice subscription
      if (!checkProducerInvoiceSubscription()) {
        showToast(
          globalDispatch,
          "Some members don't have invoice subscription. Please subscribe to create invoices.",
          5000,
          "error"
        );
        return;
      }

      if (
        !selectedClientId &&
        (!newClientData.program || !newClientData.email)
      ) {
        showToast(
          globalDispatch,
          "Please select a client or enter new client details",
          3000,
          "error"
        );
        return;
      }

      // Validate that all required fields have valid values
      for (const item of invoiceData.items) {
        if (!item.isSpecialRow && !item.isSpecial) {
          // For normal items, validate required fields
          if (!item.teamName || item.teamName.trim() === "") {
            showToast(
              globalDispatch,
              "Team name is required for all items",
              3000,
              "error"
            );
            return;
          }

          // We already checked producer subscription in checkProducerInvoiceSubscription
          // No need to check again for each item
        }
      }

      // Process items to ensure all required fields are included with exact API structure
      const processedItems = invoiceData.items.map((item) => {
        if (item.isSpecialRow || item.isSpecial) {
          return {
            price: parseFloat(item.price) || 0,
            quantity: parseInt(item.quantity) || 1,
            discount: parseFloat(item.discount) || 0,
            producers: item.producers || [],
            description: item.description || item.name || "Additional Charge", // Add description with special charge name
            specialType: item.specialType || "additional",
            isSpecial: true,
            name: item.name || "Additional Charge",
            // Include empty values for required fields
            mixDate: "",
            teamName: "",
            division: "",
            musicSurveyDue: "",
            routineSubmissionDue: "",
            estimatedCompletion: "",
            mixSeasonId: item.mixSeason || "",
            mixTypeId: "",
          };
        } else {
          // Get the mix type name for the description
          const selectedMixType = mixTypes.find(
            (mt) => mt.id === parseInt(item.mixType)
          );
          const mixTypeName = selectedMixType ? selectedMixType.name : "";

          return {
            price: parseFloat(item.price) || 0,
            quantity: parseInt(item.quantity) || 1,
            discount: parseFloat(item.discount) || 0,
            producer: item.producer || "",
            producers: item.producers || [],
            description: mixTypeName || "", // Include description with just the mix type name
            specialType: "",
            isSpecial: false,
            mixDate: item.mixDate || "",
            teamName: item.teamName || "",
            division: item.division || "",
            musicSurveyDue: item.musicSurveyDue || "",
            routineSubmissionDue: item.routineSubmissionDue || "",
            estimatedCompletion: item.estimatedCompletion || "",
            mixSeasonId: item.mixSeason || "",
            mixTypeId: item.mixType || "",
          };
        }
      });

      // Create the payload in the correct format
      const payload = {
        selectedClientId,
        newClientData,
        invoiceDates,
        invoiceData: {
          notes: invoiceData.notes || "",
          termsAndConditions: invoiceData.termsAndConditions || "",
          depositAmount: parseFloat(invoiceData.depositAmount) || 0,
          depositPercentage: parseFloat(invoiceData.depositPercentage) || 0,
          items: processedItems,
          subtotal: calculateTotalPrice(processedItems),
          total: calculateTotalPrice(processedItems),
        },
      };

      // Set the isQuote flag separately (not inside invoiceData)
      if (isQuote) {
        payload.isQuote = true;
      }

      // Set the producer ID if available
      if (selectedProducer) {
        payload.producer = selectedProducer;
      }

      // If creating new client, assign to all producers
      if (!selectedClientId && newClientData.program && newClientData.email) {
        try {
          // Create client and get its ID
          const clientResult = await onSubmit(payload);

          if (clientResult && clientResult.client_id) {
            // Assign new client to all producers
            await Promise.all(
              producers.map((producer) =>
                AssignClient(
                  { member_ids: [producer.value] },
                  clientResult.client_id
                )
              )
            );
          }
        } catch (error) {
          console.error("Error creating client:", error);
          showToast(globalDispatch, "Failed to create client", 3000, "error");
          return;
        }
      } else {
        // Using existing client
        onSubmit(payload);
      }
    } catch (error) {
      console.error("Error creating invoice:", error);
      showToast(
        globalDispatch,
        "An error occurred while creating the invoice. Please try again.",
        3000,
        "error"
      );
    }
  };

  return (
    <div className="rounded-lg bg-boxdark p-6">
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={onClose}
          className="flex items-center gap-2 text-white hover:text-primary"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Invoices
        </button>
      </div>

      {/* Company Information Header */}
      <div className="mb-6 flex items-center justify-between rounded-lg bg-meta-4 p-4">
        <div className="flex items-center gap-4">
          {companyInfo?.company?.manager?.license_company_logo ? (
            <img
              src={companyInfo.company.manager.license_company_logo}
              alt={companyInfo.company.manager.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : companyInfo?.company?.main_member?.license_company_logo ? (
            <img
              src={companyInfo.company.main_member.license_company_logo}
              alt={
                companyInfo.company.main_member.company_name || "Company Logo"
              }
              className="h-16 w-auto object-contain"
            />
          ) : userDetails?.company_logo ? (
            <img
              src={userDetails.company_logo}
              alt={userDetails?.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : null}
          <div>
            <h2 className="text-xl font-bold text-white">
              {companyInfo?.company?.manager?.company_name &&
              companyInfo.company.manager.company_name !== "null"
                ? companyInfo.company.manager.company_name
                : companyInfo?.company?.main_member?.company_name &&
                  companyInfo.company.main_member.company_name !== "null"
                ? companyInfo.company.main_member.company_name
                : userDetails?.company_name || "Your Company"}
            </h2>
            <p className="text-sm text-bodydark2">
              {userDetails?.office_email || ""}
            </p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-bold text-white">
            {isQuote ? "QUOTE" : "INVOICE"}
          </h2>
        </div>
      </div>

      {/* Client Information */}
      <div className="mb-3 pb-2">
        <h3 className="mb-2 text-base font-semibold text-white">
          Client Information
        </h3>
        <div className="flex items-center gap-4">
          <div className="w-[307px]">
            <select
              value={selectedClientId || ""}
              onChange={(e) => {
                const value = e.target.value;
                if (value === "add_new") {
                  setShowNewClientModal(true);
                  setSelectedClientId("");
                } else {
                  setSelectedClientId(value);
                  setNewClientData({ program: "", email: "" });
                }
              }}
              className="h-10 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-3 py-2 text-sm text-white"
            >
              <option value="">Select Client</option>
              <option value="add_new" className="font-semibold text-primary">
                + Add New Client
              </option>
              {clients.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.program}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Show New Client Modal if needed */}
      <NewClientModal
        isOpen={showNewClientModal}
        onClose={() => setShowNewClientModal(false)}
        onAddClient={(clientData) => {
          setNewClientData(clientData);
          setSelectedClientId("");
          setShowNewClientModal(false);
        }}
        showToast={showToast}
        globalDispatch={globalDispatch}
      />

      {/* Invoice Dates Section */}
      <div className="mb-3 flex items-center justify-start pb-3">
        <div className="flex items-center justify-normal gap-3">
          <div>
            <label className="mb-1.5 block font-medium text-white">
              Invoice Date
            </label>
            <input
              type="date"
              value={invoiceDates.invoiceDate}
              onChange={(e) =>
                setInvoiceDates((prev) => ({
                  ...prev,
                  invoiceDate: e.target.value,
                }))
              }
              className="h-8 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-5 py-1.5 text-[12px] text-white"
            />
          </div>
          <div>
            <label className="mb-1.5 block font-medium text-white">
              Invoice Due Date
            </label>
            <input
              type="date"
              value={invoiceDates.invoiceDueDate}
              onChange={(e) =>
                setInvoiceDates((prev) => ({
                  ...prev,
                  invoiceDueDate: e.target.value,
                }))
              }
              className="h-8 w-full rounded border-[1.5px] border-stroke/50 bg-form-input px-5 py-1.5 text-[12px] text-white"
            />
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="mb-3">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">Invoice Items</h3>
        </div>
        <div className="custom-overflow min-h-[140px] overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Producer
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Team Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Division
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Survey Due / Submission Due / Est. Completion
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Discount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Season
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoiceData.items.map((item, index) =>
                !item.isSpecialRow && !item.isSpecial ? (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 text-[12px] hover:bg-primary/5"
                  >
                    <td className="row-date2 whitespace-nowrap px-4 py-3">
                      <SingleDatePicker
                        id={`mixDate_${index}`}
                        date={item.mixDate ? moment(item.mixDate) : null}
                        onDateChange={(date) => {
                          handleItemChange(
                            index,
                            "mixDate",
                            date ? date.format("YYYY-MM-DD") : null
                          );
                          calculateDates(
                            date ? date.format("YYYY-MM-DD") : null,
                            index
                          );
                        }}
                        focused={focusedInput[`mixDate_${index}`]}
                        onFocusChange={({ focused }) =>
                          setFocusedInput((prev) => ({
                            ...prev,
                            [`mixDate_${index}`]: focused,
                          }))
                        }
                        numberOfMonths={1}
                        isOutsideRange={() => false}
                        displayFormat="MM-DD-YYYY"
                        placeholder="Select Mix Date"
                        readOnly={true}
                        customInputIcon={null}
                        noBorder={true}
                        block
                        className="w-full rounded border border-stroke/50"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        position="top"
                        label="Select Producer"
                        textClass="!text-[10px]"
                        value={item.producer}
                        onChange={(value) => {
                          handleItemChange(index, "producer", value);
                          // Update the selected producer for the invoice
                          if (value && !selectedProducer) {
                            setSelectedProducer(value);
                          }
                        }}
                        className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                        options={
                          producers && producers.length > 0
                            ? producers.map((producer) => ({
                                value: producer.value.toString(),
                                label: producer.label,
                              }))
                            : []
                        }
                      />
                      {/* <option value="">Select Producer</option>
                        {producers.map((producer) => (
                          <option key={producer.value} value={producer.value}>
                            {producer.label}
                          </option>
                        ))} */}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <CustomSelect2
                        label="Select MixType"
                        textClass="!text-[10px]"
                        value={item.mixType}
                        onChange={(value) => {
                          handleItemChange(index, "mixType", value);
                          // Also update the mixTypeId field for API consistency
                          handleItemChange(index, "mixTypeId", value);
                          const selectedMixType = mixTypes.find(
                            (mt) => mt.id === parseInt(value)
                          );
                          if (selectedMixType) {
                            // Set the price if available
                            handleItemChange(
                              index,
                              "price",
                              selectedMixType.price
                            );

                            // Set the description field with just the mix type name
                            handleItemChange(
                              index,
                              "description",
                              selectedMixType.name || ""
                            );
                          }
                        }}
                        className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                        options={
                          item.producer &&
                          getAvailableMixTypes(item.producer).map(
                            (mixType) => ({
                              value: mixType.id.toString(),
                              label: mixType.name + " - $" + mixType.price,
                            })
                          )
                        }
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.teamName}
                        onChange={(e) =>
                          handleItemChange(index, "teamName", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value.startsWith("Team ")) {
                            handleItemChange(index, "teamName", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.division}
                        onChange={(e) =>
                          handleItemChange(index, "division", e.target.value)
                        }
                        onFocus={(e) => {
                          if (e.target.value === "TBD") {
                            handleItemChange(index, "division", "");
                          }
                        }}
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex flex-col gap-2">
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`musicSurveyDue_${index}`}
                            date={
                              item.musicSurveyDue
                                ? moment(item.musicSurveyDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "musicSurveyDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={focusedInput[`musicSurveyDue_${index}`]}
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`musicSurveyDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`routineSubmissionDue_${index}`}
                            date={
                              item.routineSubmissionDue
                                ? moment(item.routineSubmissionDue)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "routineSubmissionDue",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`routineSubmissionDue_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`routineSubmissionDue_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Due Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                        <div className="row-date flex flex-row items-center justify-between gap-1">
                          <SingleDatePicker
                            id={`estimatedCompletion_${index}`}
                            date={
                              item.estimatedCompletion
                                ? moment(item.estimatedCompletion)
                                : null
                            }
                            onDateChange={(date) =>
                              handleItemChange(
                                index,
                                "estimatedCompletion",
                                date ? date.format("YYYY-MM-DD") : null
                              )
                            }
                            focused={
                              focusedInput[`estimatedCompletion_${index}`]
                            }
                            onFocusChange={({ focused }) =>
                              setFocusedInput((prev) => ({
                                ...prev,
                                [`estimatedCompletion_${index}`]: focused,
                              }))
                            }
                            numberOfMonths={1}
                            isOutsideRange={() => false}
                            displayFormat="MM-DD-YYYY"
                            placeholder="Select Completion Date"
                            readOnly={true}
                            customInputIcon={null}
                            noBorder={true}
                            block
                            className="w-full rounded border border-stroke/50"
                          />
                        </div>
                      </div>
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <div className="flex items-center gap-1">
                        <input
                          type="number"
                          value={item.discount || 0}
                          onChange={(e) =>
                            handleItemChange(
                              index,
                              "discount",
                              parseFloat(e.target.value)
                            )
                          }
                          className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                          min="0"
                          max="100"
                          placeholder="0%"
                        />
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <div className="flex items-center gap-1">
                        <CustomSelect2
                          label="Select Mix Season"
                          value={item.mixSeason}
                          onChange={(value) => {
                            handleItemChange(index, "mixSeason", value);
                            // Also update the mixSeasonId field for API consistency
                            handleItemChange(index, "mixSeasonId", value);
                            // Auto-set the discount if a season is selected
                            if (value && item.producer) {
                              const producerId = item.producer;
                              const producerSeasons =
                                producerMixSeasons[producerId] || [];
                              const selectedSeason = producerSeasons.find(
                                (s) => s.id === parseInt(value)
                              );
                              if (selectedSeason) {
                                handleItemChange(
                                  index,
                                  "discount",
                                  selectedSeason.discount || 0
                                );
                              }
                            }
                          }}
                          options={getAvailableMixSeasons(item.producer).map(
                            (season) => ({
                              value: season.id.toString(),
                              label: season.name,
                            })
                          )}
                          placeholder="Select Season"
                          className="h-[36px] !w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-5 py-3 !text-[12px] outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:focus:border-primary"
                        />
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ) : (
                  <tr
                    key={index}
                    className="border-b border-stroke/50 bg-meta-4/20 text-[12px] hover:bg-primary/5"
                  >
                    <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                      <input
                        type="text"
                        value={item.name || "Additional Charge"}
                        onChange={(e) =>
                          handleItemChange(index, "name", e.target.value)
                        }
                        className="w-full min-w-[120px] rounded border-[1.5px] border-stroke/50 bg-transparent px-2 py-1 text-[12px] text-white"
                        placeholder="Charge Name"
                      />
                    </td>
                    <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for spacing */}
                    </td>
                    <td className="row-date whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.price}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "price",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-24 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <input
                        type="number"
                        value={item.discount || 0}
                        onChange={(e) =>
                          handleItemChange(
                            index,
                            "discount",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 rounded border border-stroke/50 bg-transparent px-2 py-1 text-white"
                        min="0"
                        placeholder="$0.00"
                      />
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {/* Empty cell for mix season column */}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="p-1 text-danger hover:text-danger/80"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </table>
        </div>
        <div className="mt-2 flex justify-end gap-2">
          <button
            onClick={handleAddSpecialRow}
            className="flex items-center gap-2 rounded bg-meta-5 px-4 py-1 text-sm font-medium text-white hover:bg-meta-5/80"
          >
            <PlusCircle className="h-5 w-5" />
            Add Special Charge
          </button>
          <button
            onClick={handleAddItem}
            className="flex items-center gap-2 rounded bg-primary px-4 py-1 text-sm font-medium text-white hover:bg-primary/80"
          >
            <Plus className="h-5 w-5" />
            Add Row
          </button>
        </div>
      </div>

      {/* Invoice Total Section */}
      <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
        <div className="flex justify-end">
          <div className="w-1/3">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">
                Invoice total:
              </span>
              <span className="text-lg font-bold text-white">
                ${isNaN(totalPrice) ? "0.00" : totalPrice.toFixed(2)}
              </span>
            </div>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">Deposit:</span>
              <span className="text-sm text-white">
                $
                {(() => {
                  if (isNaN(totalPrice)) return "0.00";

                  const depositPercentage =
                    parseFloat(invoiceData.depositPercentage) || 0;
                  const depositAmount =
                    parseFloat(invoiceData.depositAmount) || 0;

                  if (depositPercentage > 0) {
                    const calculatedDeposit =
                      (totalPrice * depositPercentage) / 100;
                    return isNaN(calculatedDeposit)
                      ? "0.00"
                      : calculatedDeposit.toFixed(2);
                  } else {
                    return isNaN(depositAmount)
                      ? "0.00"
                      : depositAmount.toFixed(2);
                  }
                })()}
              </span>
            </div>
            <div className="flex items-center justify-between border-t border-stroke/50 pt-2">
              <span className="text-sm font-medium text-white">
                Balance due:
              </span>
              <span className="text-sm font-bold text-white">
                $
                {(() => {
                  if (isNaN(totalPrice)) return "0.00";

                  const depositPercentage =
                    parseFloat(invoiceData.depositPercentage) || 0;
                  const depositAmount =
                    parseFloat(invoiceData.depositAmount) || 0;

                  if (depositPercentage > 0) {
                    const calculatedDeposit =
                      (totalPrice * depositPercentage) / 100;
                    const balance = totalPrice - calculatedDeposit;
                    return isNaN(balance) ? "0.00" : balance.toFixed(2);
                  } else {
                    const balance = totalPrice - depositAmount;
                    return isNaN(balance) ? "0.00" : balance.toFixed(2);
                  }
                })()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Deposit and Terms Section */}
      <div className="mt-6 grid grid-cols-3 gap-6">
        {/* Left Column - Deposit Settings */}
        <div className="space-y-2">
          <h3 className="text-base font-semibold text-white">
            Deposit Settings
          </h3>
          <div className="space-y-2">
            <div>
              <label className="mb-1.5 block text-sm text-white">Type</label>
              <select
                value={
                  invoiceData.depositPercentage > 0 ? "percentage" : "amount"
                }
                onChange={(e) => {
                  const isAmount = e.target.value === "amount";
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isAmount ? 0 : 0,
                    depositPercentage: isAmount
                      ? 0
                      : prev.depositPercentage ||
                        userDetails?.deposit_percent ||
                        30,
                  }));
                }}
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
              >
                <option value="amount">Fixed Amount</option>
                <option value="percentage">Percentage</option>
              </select>
            </div>
            <div>
              <label className="mb-1.5 block text-sm text-white">
                {invoiceData.depositPercentage > 0
                  ? "Percentage (%)"
                  : "Amount ($)"}
              </label>
              <input
                type="number"
                value={
                  invoiceData.depositPercentage > 0
                    ? invoiceData.depositPercentage
                    : invoiceData.depositAmount
                }
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  const isPercentage = invoiceData.depositPercentage > 0;
                  setInvoiceData((prev) => ({
                    ...prev,
                    depositAmount: isPercentage ? 0 : value,
                    depositPercentage: isPercentage ? value : 0,
                  }));
                }}
                placeholder={
                  invoiceData.depositPercentage > 0
                    ? `Default: ${userDetails?.deposit_percent || 30}%`
                    : "Enter amount"
                }
                className="h-9 w-full rounded border border-stroke bg-transparent px-3 py-1 text-sm text-white"
                min="0"
                max={invoiceData.depositPercentage > 0 ? 100 : undefined}
              />
            </div>
          </div>
        </div>

        {/* Middle and Right Columns - Notes and Terms */}
        <div className="col-span-2 space-y-2">
          <h3 className="text-base font-semibold text-white">Notes </h3>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="mb-1.5 block text-sm text-white">Notes</label>
              <textarea
                value={invoiceData.notes}
                onChange={(e) =>
                  setInvoiceData((prev) => ({ ...prev, notes: e.target.value }))
                }
                className="h-[110px] w-full rounded border border-stroke bg-transparent px-3 py-2 text-sm text-white"
                rows="4"
                placeholder="Add any additional notes..."
              />
            </div>
          </div>
          <div>
            <label className="mb-1.5 block text-sm text-white">
              Terms and Conditions
            </label>
            <SunEditor
              setContents={invoiceData.termsAndConditions}
              onChange={(content) =>
                setInvoiceData((prev) => ({
                  ...prev,
                  termsAndConditions: content,
                }))
              }
              getSunEditorInstance={getSunEditorInstance}
              setOptions={{
                buttonList: buttonList.complex,
                height: 110,
                width: "100%",
                placeholder: userDetails?.contract_agreement
                  ? "Terms and conditions loaded from your settings"
                  : "Add terms and conditions...",
              }}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-6 flex justify-end gap-4">
        <button
          onClick={onClose}
          className="rounded-lg border border-stroke bg-opacity-80 px-6 py-2 text-bodydark"
        >
          Cancel
        </button>

        <PDFDownloadLink
          document={
            <InvoiceQuotePDF
              data={{
                selectedClientId,
                newClientData,
                invoiceDates,
                invoiceData: { ...invoiceData, isQuote: true },
                clientDetails:
                  clients.find(
                    (client) => client.id === parseInt(selectedClientId)
                  ) || newClientData,
              }}
              userDetails={userDetails}
              companyInfo={companyInfo}
            />
          }
          fileName={`quote-${moment().format("YYYY-MM-DD")}.pdf`}
          className={`flex items-center gap-2 rounded-lg bg-meta-5 px-6 py-2 text-white hover:bg-opacity-90 disabled:opacity-50 ${
            loading ? "cursor-not-allowed opacity-50" : ""
          }`}
          onClick={(e) => {
            if (!handleQuoteDownload()) {
              e.preventDefault();
            }
          }}
        >
          {({ loading: pdfLoading }) => (
            <>
              <Download className="h-4 w-4" />
              {pdfLoading ? "Generating Quote..." : "Save as Quote"}
            </>
          )}
        </PDFDownloadLink>

        <button
          onClick={() => {
            setIsQuote(false);
            handleCreateInvoice();
          }}
          className="rounded-lg bg-primary px-6 py-2 text-white hover:bg-opacity-90"
        >
          Create Invoice
        </button>
      </div>
    </div>
  );
};

export default ManagerCreateInvoiceComponent;
