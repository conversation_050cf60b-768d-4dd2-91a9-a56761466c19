import React from 'react';
import EngineerSubProject from './EngineerSubProject';

const EngineerSubProjects = ({
  canUpload = true,
  isPublic,
  subProjects,
  workOrderDetails,
  setDeleteFileId,
}) => {
  return (
    <>
      {subProjects &&
        subProjects.length > 0 &&
        subProjects.map((subProject, index) => {
          return (
            <EngineerSubProject
              key={index}
              isPublic={isPublic}
              canUpload={canUpload}
              workOrderDetails={workOrderDetails}
              subProject={subProject}
              uploadedFiles={subProject.masters}
              setDeleteFileId={setDeleteFileId}
            />
          );
        })}
    </>
  );
};

export default EngineerSubProjects;
