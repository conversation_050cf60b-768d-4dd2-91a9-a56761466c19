import{c as ve}from"./@react-pdf/renderer-15eed3d8.js";const Fu="modulepreload",Iu=function(r){return"/"+r},ic={},gs=function(e,n,a){if(!n||n.length===0)return e();const c=document.getElementsByTagName("link");return Promise.all(n.map(o=>{if(o=Iu(o),o in ic)return;ic[o]=!0;const l=o.endsWith(".css"),h=l?'[rel="stylesheet"]':"";if(!!a)for(let b=c.length-1;b>=0;b--){const y=c[b];if(y.href===o&&(!l||y.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${h}`))return;const g=document.createElement("link");if(g.rel=l?"stylesheet":Fu,l||(g.as="script",g.crossOrigin=""),g.href=o,document.head.appendChild(g),l)return new Promise((b,y)=>{g.addEventListener("load",b),g.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>e()).catch(o=>{const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=o,window.dispatchEvent(l),!l.defaultPrevented)throw o})};var tn=Uint8Array,vn=Uint16Array,Ps=Int32Array,go=new tn([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),mo=new tn([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),ms=new tn([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Sc=function(r,e){for(var n=new vn(31),a=0;a<31;++a)n[a]=e+=1<<r[a-1];for(var c=new Ps(n[30]),a=1;a<30;++a)for(var o=n[a];o<n[a+1];++o)c[o]=o-n[a]<<5|a;return{b:n,r:c}},_c=Sc(go,2),Pc=_c.b,vs=_c.r;Pc[28]=258,vs[258]=28;var kc=Sc(mo,0),Cu=kc.b,ac=kc.r,bs=new vn(32768);for(var we=0;we<32768;++we){var kr=(we&43690)>>1|(we&21845)<<1;kr=(kr&52428)>>2|(kr&13107)<<2,kr=(kr&61680)>>4|(kr&3855)<<4,bs[we]=((kr&65280)>>8|(kr&255)<<8)>>1}var tr=function(r,e,n){for(var a=r.length,c=0,o=new vn(e);c<a;++c)r[c]&&++o[r[c]-1];var l=new vn(e);for(c=1;c<e;++c)l[c]=l[c-1]+o[c-1]<<1;var h;if(n){h=new vn(1<<e);var f=15-e;for(c=0;c<a;++c)if(r[c])for(var g=c<<4|r[c],b=e-r[c],y=l[r[c]-1]++<<b,S=y|(1<<b)-1;y<=S;++y)h[bs[y]>>f]=g}else for(h=new vn(a),c=0;c<a;++c)r[c]&&(h[c]=bs[l[r[c]-1]++]>>15-r[c]);return h},Or=new tn(288);for(var we=0;we<144;++we)Or[we]=8;for(var we=144;we<256;++we)Or[we]=9;for(var we=256;we<280;++we)Or[we]=7;for(var we=280;we<288;++we)Or[we]=8;var va=new tn(32);for(var we=0;we<32;++we)va[we]=5;var ju=tr(Or,9,0),Ou=tr(Or,9,1),Bu=tr(va,5,0),Mu=tr(va,5,1),os=function(r){for(var e=r[0],n=1;n<r.length;++n)r[n]>e&&(e=r[n]);return e},qn=function(r,e,n){var a=e/8|0;return(r[a]|r[a+1]<<8)>>(e&7)&n},ss=function(r,e){var n=e/8|0;return(r[n]|r[n+1]<<8|r[n+2]<<16)>>(e&7)},ks=function(r){return(r+7)/8|0},Fc=function(r,e,n){return(e==null||e<0)&&(e=0),(n==null||n>r.length)&&(n=r.length),new tn(r.subarray(e,n))},Eu=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],Rn=function(r,e,n){var a=new Error(e||Eu[r]);if(a.code=r,Error.captureStackTrace&&Error.captureStackTrace(a,Rn),!n)throw a;return a},qu=function(r,e,n,a){var c=r.length,o=a?a.length:0;if(!c||e.f&&!e.l)return n||new tn(0);var l=!n,h=l||e.i!=2,f=e.i;l&&(n=new tn(c*3));var g=function(Nt){var Ft=n.length;if(Nt>Ft){var _t=new tn(Math.max(Ft*2,Nt));_t.set(n),n=_t}},b=e.f||0,y=e.p||0,S=e.b||0,p=e.l,O=e.d,F=e.m,q=e.n,_=c*8;do{if(!p){b=qn(r,y,1);var B=qn(r,y+1,3);if(y+=3,B)if(B==1)p=Ou,O=Mu,F=9,q=5;else if(B==2){var wt=qn(r,y,31)+257,tt=qn(r,y+10,15)+4,z=wt+qn(r,y+5,31)+1;y+=14;for(var rt=new tn(z),dt=new tn(19),P=0;P<tt;++P)dt[ms[P]]=qn(r,y+P*3,7);y+=tt*3;for(var k=os(dt),W=(1<<k)-1,D=tr(dt,k,1),P=0;P<z;){var st=D[qn(r,y,W)];y+=st&15;var Y=st>>4;if(Y<16)rt[P++]=Y;else{var it=0,lt=0;for(Y==16?(lt=3+qn(r,y,3),y+=2,it=rt[P-1]):Y==17?(lt=3+qn(r,y,7),y+=3):Y==18&&(lt=11+qn(r,y,127),y+=7);lt--;)rt[P++]=it}}var $=rt.subarray(0,wt),ht=rt.subarray(wt);F=os($),q=os(ht),p=tr($,F,1),O=tr(ht,q,1)}else Rn(1);else{var Y=ks(y)+4,ot=r[Y-4]|r[Y-3]<<8,ut=Y+ot;if(ut>c){f&&Rn(0);break}h&&g(S+ot),n.set(r.subarray(Y,ut),S),e.b=S+=ot,e.p=y=ut*8,e.f=b;continue}if(y>_){f&&Rn(0);break}}h&&g(S+131072);for(var pt=(1<<F)-1,It=(1<<q)-1,N=y;;N=y){var it=p[ss(r,y)&pt],C=it>>4;if(y+=it&15,y>_){f&&Rn(0);break}if(it||Rn(2),C<256)n[S++]=C;else if(C==256){N=y,p=null;break}else{var M=C-254;if(C>264){var P=C-257,T=go[P];M=qn(r,y,(1<<T)-1)+Pc[P],y+=T}var J=O[ss(r,y)&It],Q=J>>4;J||Rn(3),y+=J&15;var ht=Cu[Q];if(Q>3){var T=mo[Q];ht+=ss(r,y)&(1<<T)-1,y+=T}if(y>_){f&&Rn(0);break}h&&g(S+131072);var et=S+M;if(S<ht){var nt=o-ht,At=Math.min(ht,et);for(nt+S<0&&Rn(3);S<At;++S)n[S]=a[nt+S]}for(;S<et;++S)n[S]=n[S-ht]}}e.l=p,e.p=N,e.b=S,e.f=b,p&&(b=1,e.m=F,e.d=O,e.n=q)}while(!b);return S!=n.length&&l?Fc(n,0,S):n.subarray(0,S)},dr=function(r,e,n){n<<=e&7;var a=e/8|0;r[a]|=n,r[a+1]|=n>>8},la=function(r,e,n){n<<=e&7;var a=e/8|0;r[a]|=n,r[a+1]|=n>>8,r[a+2]|=n>>16},cs=function(r,e){for(var n=[],a=0;a<r.length;++a)r[a]&&n.push({s:a,f:r[a]});var c=n.length,o=n.slice();if(!c)return{t:Cc,l:0};if(c==1){var l=new tn(n[0].s+1);return l[n[0].s]=1,{t:l,l:1}}n.sort(function(ut,wt){return ut.f-wt.f}),n.push({s:-1,f:25001});var h=n[0],f=n[1],g=0,b=1,y=2;for(n[0]={s:-1,f:h.f+f.f,l:h,r:f};b!=c-1;)h=n[n[g].f<n[y].f?g++:y++],f=n[g!=b&&n[g].f<n[y].f?g++:y++],n[b++]={s:-1,f:h.f+f.f,l:h,r:f};for(var S=o[0].s,a=1;a<c;++a)o[a].s>S&&(S=o[a].s);var p=new vn(S+1),O=ys(n[b-1],p,0);if(O>e){var a=0,F=0,q=O-e,_=1<<q;for(o.sort(function(wt,tt){return p[tt.s]-p[wt.s]||wt.f-tt.f});a<c;++a){var B=o[a].s;if(p[B]>e)F+=_-(1<<O-p[B]),p[B]=e;else break}for(F>>=q;F>0;){var Y=o[a].s;p[Y]<e?F-=1<<e-p[Y]++-1:++a}for(;a>=0&&F;--a){var ot=o[a].s;p[ot]==e&&(--p[ot],++F)}O=e}return{t:new tn(p),l:O}},ys=function(r,e,n){return r.s==-1?Math.max(ys(r.l,e,n+1),ys(r.r,e,n+1)):e[r.s]=n},oc=function(r){for(var e=r.length;e&&!r[--e];);for(var n=new vn(++e),a=0,c=r[0],o=1,l=function(f){n[a++]=f},h=1;h<=e;++h)if(r[h]==c&&h!=e)++o;else{if(!c&&o>2){for(;o>138;o-=138)l(32754);o>2&&(l(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(l(c),--o;o>6;o-=6)l(8304);o>2&&(l(o-3<<5|8208),o=0)}for(;o--;)l(c);o=1,c=r[h]}return{c:n.subarray(0,a),n:e}},ha=function(r,e){for(var n=0,a=0;a<e.length;++a)n+=r[a]*e[a];return n},Ic=function(r,e,n){var a=n.length,c=ks(e+2);r[c]=a&255,r[c+1]=a>>8,r[c+2]=r[c]^255,r[c+3]=r[c+1]^255;for(var o=0;o<a;++o)r[c+o+4]=n[o];return(c+4+a)*8},sc=function(r,e,n,a,c,o,l,h,f,g,b){dr(e,b++,n),++c[256];for(var y=cs(c,15),S=y.t,p=y.l,O=cs(o,15),F=O.t,q=O.l,_=oc(S),B=_.c,Y=_.n,ot=oc(F),ut=ot.c,wt=ot.n,tt=new vn(19),z=0;z<B.length;++z)++tt[B[z]&31];for(var z=0;z<ut.length;++z)++tt[ut[z]&31];for(var rt=cs(tt,7),dt=rt.t,P=rt.l,k=19;k>4&&!dt[ms[k-1]];--k);var W=g+5<<3,D=ha(c,Or)+ha(o,va)+l,st=ha(c,S)+ha(o,F)+l+14+3*k+ha(tt,dt)+2*tt[16]+3*tt[17]+7*tt[18];if(f>=0&&W<=D&&W<=st)return Ic(e,b,r.subarray(f,f+g));var it,lt,$,ht;if(dr(e,b,1+(st<D)),b+=2,st<D){it=tr(S,p,0),lt=S,$=tr(F,q,0),ht=F;var pt=tr(dt,P,0);dr(e,b,Y-257),dr(e,b+5,wt-1),dr(e,b+10,k-4),b+=14;for(var z=0;z<k;++z)dr(e,b+3*z,dt[ms[z]]);b+=3*k;for(var It=[B,ut],N=0;N<2;++N)for(var C=It[N],z=0;z<C.length;++z){var M=C[z]&31;dr(e,b,pt[M]),b+=dt[M],M>15&&(dr(e,b,C[z]>>5&127),b+=C[z]>>12)}}else it=ju,lt=Or,$=Bu,ht=va;for(var z=0;z<h;++z){var T=a[z];if(T>255){var M=T>>18&31;la(e,b,it[M+257]),b+=lt[M+257],M>7&&(dr(e,b,T>>23&31),b+=go[M]);var J=T&31;la(e,b,$[J]),b+=ht[J],J>3&&(la(e,b,T>>5&8191),b+=mo[J])}else la(e,b,it[T]),b+=lt[T]}return la(e,b,it[256]),b+lt[256]},Du=new Ps([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Cc=new tn(0),Ru=function(r,e,n,a,c,o){var l=o.z||r.length,h=new tn(a+l+5*(1+Math.ceil(l/7e3))+c),f=h.subarray(a,h.length-c),g=o.l,b=(o.r||0)&7;if(e){b&&(f[0]=o.r>>3);for(var y=Du[e-1],S=y>>13,p=y&8191,O=(1<<n)-1,F=o.p||new vn(32768),q=o.h||new vn(O+1),_=Math.ceil(n/3),B=2*_,Y=function(Ut){return(r[Ut]^r[Ut+1]<<_^r[Ut+2]<<B)&O},ot=new Ps(25e3),ut=new vn(288),wt=new vn(32),tt=0,z=0,rt=o.i||0,dt=0,P=o.w||0,k=0;rt+2<l;++rt){var W=Y(rt),D=rt&32767,st=q[W];if(F[D]=st,q[W]=D,P<=rt){var it=l-rt;if((tt>7e3||dt>24576)&&(it>423||!g)){b=sc(r,f,0,ot,ut,wt,z,dt,k,rt-k,b),dt=tt=z=0,k=rt;for(var lt=0;lt<286;++lt)ut[lt]=0;for(var lt=0;lt<30;++lt)wt[lt]=0}var $=2,ht=0,pt=p,It=D-st&32767;if(it>2&&W==Y(rt-It))for(var N=Math.min(S,it)-1,C=Math.min(32767,rt),M=Math.min(258,it);It<=C&&--pt&&D!=st;){if(r[rt+$]==r[rt+$-It]){for(var T=0;T<M&&r[rt+T]==r[rt+T-It];++T);if(T>$){if($=T,ht=It,T>N)break;for(var J=Math.min(It,T-2),Q=0,lt=0;lt<J;++lt){var et=rt-It+lt&32767,nt=F[et],At=et-nt&32767;At>Q&&(Q=At,st=et)}}}D=st,st=F[D],It+=D-st&32767}if(ht){ot[dt++]=268435456|vs[$]<<18|ac[ht];var Nt=vs[$]&31,Ft=ac[ht]&31;z+=go[Nt]+mo[Ft],++ut[257+Nt],++wt[Ft],P=rt+$,++tt}else ot[dt++]=r[rt],++ut[r[rt]]}}for(rt=Math.max(rt,P);rt<l;++rt)ot[dt++]=r[rt],++ut[r[rt]];b=sc(r,f,g,ot,ut,wt,z,dt,k,rt-k,b),g||(o.r=b&7|f[b/8|0]<<3,b-=7,o.h=q,o.p=F,o.i=rt,o.w=P)}else{for(var rt=o.w||0;rt<l+g;rt+=65535){var _t=rt+65535;_t>=l&&(f[b/8|0]=g,_t=l),b=Ic(f,b+1,r.subarray(rt,_t))}o.i=l}return Fc(h,0,a+ks(b)+c)},jc=function(){var r=1,e=0;return{p:function(n){for(var a=r,c=e,o=n.length|0,l=0;l!=o;){for(var h=Math.min(l+2655,o);l<h;++l)c+=a+=n[l];a=(a&65535)+15*(a>>16),c=(c&65535)+15*(c>>16)}r=a,e=c},d:function(){return r%=65521,e%=65521,(r&255)<<24|(r&65280)<<8|(e&255)<<8|e>>8}}},Tu=function(r,e,n,a,c){if(!c&&(c={l:1},e.dictionary)){var o=e.dictionary.subarray(-32768),l=new tn(o.length+r.length);l.set(o),l.set(r,o.length),r=l,c.w=o.length}return Ru(r,e.level==null?6:e.level,e.mem==null?c.l?Math.ceil(Math.max(8,Math.min(13,Math.log(r.length)))*1.5):20:12+e.mem,n,a,c)},Oc=function(r,e,n){for(;n;++e)r[e]=n,n>>>=8},zu=function(r,e){var n=e.level,a=n==0?0:n<6?1:n==9?3:2;if(r[0]=120,r[1]=a<<6|(e.dictionary&&32),r[1]|=31-(r[0]<<8|r[1])%31,e.dictionary){var c=jc();c.p(e.dictionary),Oc(r,2,c.d())}},Uu=function(r,e){return((r[0]&15)!=8||r[0]>>4>7||(r[0]<<8|r[1])%31)&&Rn(6,"invalid zlib data"),(r[1]>>5&1)==+!e&&Rn(6,"invalid zlib data: "+(r[1]&32?"need":"unexpected")+" dictionary"),(r[1]>>3&4)+2};function ws(r,e){e||(e={});var n=jc();n.p(r);var a=Tu(r,e,e.dictionary?6:2,4);return zu(a,e),Oc(a,a.length-4,n.d()),a}function Hu(r,e){return qu(r.subarray(Uu(r,e&&e.dictionary),-4),{i:2},e&&e.out,e&&e.dictionary)}var Wu=typeof TextDecoder<"u"&&new TextDecoder,Vu=0;try{Wu.decode(Cc,{stream:!0}),Vu=1}catch{}var Ht=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function us(){Ht.console&&typeof Ht.console.log=="function"&&Ht.console.log.apply(Ht.console,arguments)}var me={log:us,warn:function(r){Ht.console&&(typeof Ht.console.warn=="function"?Ht.console.warn.apply(Ht.console,arguments):us.call(null,arguments))},error:function(r){Ht.console&&(typeof Ht.console.error=="function"?Ht.console.error.apply(Ht.console,arguments):us(r))}};function ls(r,e,n){var a=new XMLHttpRequest;a.open("GET",r),a.responseType="blob",a.onload=function(){Yr(a.response,e,n)},a.onerror=function(){me.error("could not download file")},a.send()}function cc(r){var e=new XMLHttpRequest;e.open("HEAD",r,!1);try{e.send()}catch{}return e.status>=200&&e.status<=299}function uo(r){try{r.dispatchEvent(new MouseEvent("click"))}catch{var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),r.dispatchEvent(e)}}var fa,Ls,Yr=Ht.saveAs||((typeof window>"u"?"undefined":ve(window))!=="object"||window!==Ht?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(r,e,n){var a=Ht.URL||Ht.webkitURL,c=document.createElement("a");e=e||r.name||"download",c.download=e,c.rel="noopener",typeof r=="string"?(c.href=r,c.origin!==location.origin?cc(c.href)?ls(r,e,n):uo(c,c.target="_blank"):uo(c)):(c.href=a.createObjectURL(r),setTimeout(function(){a.revokeObjectURL(c.href)},4e4),setTimeout(function(){uo(c)},0))}:"msSaveOrOpenBlob"in navigator?function(r,e,n){if(e=e||r.name||"download",typeof r=="string")if(cc(r))ls(r,e,n);else{var a=document.createElement("a");a.href=r,a.target="_blank",setTimeout(function(){uo(a)})}else navigator.msSaveOrOpenBlob(function(c,o){return o===void 0?o={autoBom:!1}:ve(o)!=="object"&&(me.warn("Deprecated: Expected third argument to be a object"),o={autoBom:!o}),o.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(c.type)?new Blob([String.fromCharCode(65279),c],{type:c.type}):c}(r,n),e)}:function(r,e,n,a){if((a=a||open("","_blank"))&&(a.document.title=a.document.body.innerText="downloading..."),typeof r=="string")return ls(r,e,n);var c=r.type==="application/octet-stream",o=/constructor/i.test(Ht.HTMLElement)||Ht.safari,l=/CriOS\/[\d]+/.test(navigator.userAgent);if((l||c&&o)&&(typeof FileReader>"u"?"undefined":ve(FileReader))==="object"){var h=new FileReader;h.onloadend=function(){var b=h.result;b=l?b:b.replace(/^data:[^;]*;/,"data:attachment/file;"),a?a.location.href=b:location=b,a=null},h.readAsDataURL(r)}else{var f=Ht.URL||Ht.webkitURL,g=f.createObjectURL(r);a?a.location=g:location.href=g,a=null,setTimeout(function(){f.revokeObjectURL(g)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function Bc(r){var e;r=r||"",this.ok=!1,r.charAt(0)=="#"&&(r=r.substr(1,6)),r={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[r=(r=r.replace(/ /g,"")).toLowerCase()]||r;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(h){return[parseInt(h[1]),parseInt(h[2]),parseInt(h[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(h){return[parseInt(h[1],16),parseInt(h[2],16),parseInt(h[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(h){return[parseInt(h[1]+h[1],16),parseInt(h[2]+h[2],16),parseInt(h[3]+h[3],16)]}}],a=0;a<n.length;a++){var c=n[a].re,o=n[a].process,l=c.exec(r);l&&(e=o(l),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var h=this.r.toString(16),f=this.g.toString(16),g=this.b.toString(16);return h.length==1&&(h="0"+h),f.length==1&&(f="0"+f),g.length==1&&(g="0"+g),"#"+h+f+g}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function hs(r,e){var n=r[0],a=r[1],c=r[2],o=r[3];n=Ke(n,a,c,o,e[0],7,-680876936),o=Ke(o,n,a,c,e[1],12,-389564586),c=Ke(c,o,n,a,e[2],17,606105819),a=Ke(a,c,o,n,e[3],22,-**********),n=Ke(n,a,c,o,e[4],7,-176418897),o=Ke(o,n,a,c,e[5],12,**********),c=Ke(c,o,n,a,e[6],17,-**********),a=Ke(a,c,o,n,e[7],22,-45705983),n=Ke(n,a,c,o,e[8],7,**********),o=Ke(o,n,a,c,e[9],12,-**********),c=Ke(c,o,n,a,e[10],17,-42063),a=Ke(a,c,o,n,e[11],22,-**********),n=Ke(n,a,c,o,e[12],7,**********),o=Ke(o,n,a,c,e[13],12,-40341101),c=Ke(c,o,n,a,e[14],17,-**********),n=Ze(n,a=Ke(a,c,o,n,e[15],22,**********),c,o,e[1],5,-165796510),o=Ze(o,n,a,c,e[6],9,-**********),c=Ze(c,o,n,a,e[11],14,643717713),a=Ze(a,c,o,n,e[0],20,-373897302),n=Ze(n,a,c,o,e[5],5,-701558691),o=Ze(o,n,a,c,e[10],9,38016083),c=Ze(c,o,n,a,e[15],14,-660478335),a=Ze(a,c,o,n,e[4],20,-405537848),n=Ze(n,a,c,o,e[9],5,568446438),o=Ze(o,n,a,c,e[14],9,-1019803690),c=Ze(c,o,n,a,e[3],14,-187363961),a=Ze(a,c,o,n,e[8],20,1163531501),n=Ze(n,a,c,o,e[13],5,-1444681467),o=Ze(o,n,a,c,e[2],9,-51403784),c=Ze(c,o,n,a,e[7],14,1735328473),n=$e(n,a=Ze(a,c,o,n,e[12],20,-1926607734),c,o,e[5],4,-378558),o=$e(o,n,a,c,e[8],11,-2022574463),c=$e(c,o,n,a,e[11],16,1839030562),a=$e(a,c,o,n,e[14],23,-35309556),n=$e(n,a,c,o,e[1],4,-1530992060),o=$e(o,n,a,c,e[4],11,1272893353),c=$e(c,o,n,a,e[7],16,-155497632),a=$e(a,c,o,n,e[10],23,-1094730640),n=$e(n,a,c,o,e[13],4,681279174),o=$e(o,n,a,c,e[0],11,-358537222),c=$e(c,o,n,a,e[3],16,-722521979),a=$e(a,c,o,n,e[6],23,76029189),n=$e(n,a,c,o,e[9],4,-640364487),o=$e(o,n,a,c,e[12],11,-421815835),c=$e(c,o,n,a,e[15],16,530742520),n=Qe(n,a=$e(a,c,o,n,e[2],23,-995338651),c,o,e[0],6,-198630844),o=Qe(o,n,a,c,e[7],10,1126891415),c=Qe(c,o,n,a,e[14],15,-1416354905),a=Qe(a,c,o,n,e[5],21,-57434055),n=Qe(n,a,c,o,e[12],6,1700485571),o=Qe(o,n,a,c,e[3],10,-1894986606),c=Qe(c,o,n,a,e[10],15,-1051523),a=Qe(a,c,o,n,e[1],21,-2054922799),n=Qe(n,a,c,o,e[8],6,1873313359),o=Qe(o,n,a,c,e[15],10,-30611744),c=Qe(c,o,n,a,e[6],15,-1560198380),a=Qe(a,c,o,n,e[13],21,1309151649),n=Qe(n,a,c,o,e[4],6,-145523070),o=Qe(o,n,a,c,e[11],10,-1120210379),c=Qe(c,o,n,a,e[2],15,718787259),a=Qe(a,c,o,n,e[9],21,-343485551),r[0]=Cr(n,r[0]),r[1]=Cr(a,r[1]),r[2]=Cr(c,r[2]),r[3]=Cr(o,r[3])}function vo(r,e,n,a,c,o){return e=Cr(Cr(e,r),Cr(a,o)),Cr(e<<c|e>>>32-c,n)}function Ke(r,e,n,a,c,o,l){return vo(e&n|~e&a,r,e,c,o,l)}function Ze(r,e,n,a,c,o,l){return vo(e&a|n&~a,r,e,c,o,l)}function $e(r,e,n,a,c,o,l){return vo(e^n^a,r,e,c,o,l)}function Qe(r,e,n,a,c,o,l){return vo(n^(e|~a),r,e,c,o,l)}function Mc(r){var e,n=r.length,a=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=r.length;e+=64)hs(a,Gu(r.substring(e-64,e)));r=r.substring(e-64);var c=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<r.length;e++)c[e>>2]|=r.charCodeAt(e)<<(e%4<<3);if(c[e>>2]|=128<<(e%4<<3),e>55)for(hs(a,c),e=0;e<16;e++)c[e]=0;return c[14]=8*n,hs(a,c),a}function Gu(r){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=r.charCodeAt(e)+(r.charCodeAt(e+1)<<8)+(r.charCodeAt(e+2)<<16)+(r.charCodeAt(e+3)<<24);return n}fa=Ht.atob.bind(Ht),Ls=Ht.btoa.bind(Ht);var uc="0123456789abcdef".split("");function Ju(r){for(var e="",n=0;n<4;n++)e+=uc[r>>8*n+4&15]+uc[r>>8*n&15];return e}function Yu(r){return String.fromCharCode((255&r)>>0,(65280&r)>>8,(16711680&r)>>16,(**********&r)>>24)}function Ns(r){return Mc(r).map(Yu).join("")}var Xu=function(r){for(var e=0;e<r.length;e++)r[e]=Ju(r[e]);return r.join("")}(Mc("hello"))!="5d41402abc4b2a76b9719d911017c592";function Cr(r,e){if(Xu){var n=(65535&r)+(65535&e);return(r>>16)+(e>>16)+(n>>16)<<16|65535&n}return r+e&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function As(r,e){var n,a,c,o;if(r!==n){for(var l=(c=r,o=1+(256/r.length>>0),new Array(o+1).join(c)),h=[],f=0;f<256;f++)h[f]=f;var g=0;for(f=0;f<256;f++){var b=h[f];g=(g+b+l.charCodeAt(f))%256,h[f]=h[g],h[g]=b}n=r,a=h}else h=a;var y=e.length,S=0,p=0,O="";for(f=0;f<y;f++)p=(p+(b=h[S=(S+1)%256]))%256,h[S]=h[p],h[p]=b,l=h[(h[S]+h[p])%256],O+=String.fromCharCode(e.charCodeAt(f)^l);return O}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var lc={print:4,modify:8,copy:16,"annot-forms":32};function ji(r,e,n,a){this.v=1,this.r=2;var c=192;r.forEach(function(h){if(lc.perm!==void 0)throw new Error("Invalid permission: "+h);c+=lc[h]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var o=(e+this.padding).substr(0,32),l=(n+this.padding).substr(0,32);this.O=this.processOwnerPassword(o,l),this.P=-(1+(255^c)),this.encryptionKey=Ns(o+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(a)).substr(0,5),this.U=As(this.encryptionKey,this.padding)}function Oi(r){if(/[^\u0000-\u00ff]/.test(r))throw new Error("Invalid PDF Name Object: "+r+", Only accept ASCII characters.");for(var e="",n=r.length,a=0;a<n;a++){var c=r.charCodeAt(a);c<33||c===35||c===37||c===40||c===41||c===47||c===60||c===62||c===91||c===93||c===123||c===125||c>126?e+="#"+("0"+c.toString(16)).slice(-2):e+=r[a]}return e}function hc(r){if(ve(r)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(n,a,c){if(c=c||!1,typeof n!="string"||typeof a!="function"||typeof c!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(n)||(e[n]={});var o=Math.random().toString(35);return e[n][o]=[a,!!c],o},this.unsubscribe=function(n){for(var a in e)if(e[a][n])return delete e[a][n],Object.keys(e[a]).length===0&&delete e[a],!0;return!1},this.publish=function(n){if(e.hasOwnProperty(n)){var a=Array.prototype.slice.call(arguments,1),c=[];for(var o in e[n]){var l=e[n][o];try{l[0].apply(r,a)}catch(h){Ht.console&&me.error("jsPDF PubSub Error",h.message,h)}l[1]&&c.push(o)}c.length&&c.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function ba(r){if(!(this instanceof ba))return new ba(r);var e="opacity,stroke-opacity".split(",");for(var n in r)r.hasOwnProperty(n)&&e.indexOf(n)>=0&&(this[n]=r[n]);this.id="",this.objectNumber=-1}function Ec(r,e){this.gState=r,this.matrix=e,this.id="",this.objectNumber=-1}function Ir(r,e,n,a,c){if(!(this instanceof Ir))return new Ir(r,e,n,a,c);this.type=r==="axial"?2:3,this.coords=e,this.colors=n,Ec.call(this,a,c)}function Xr(r,e,n,a,c){if(!(this instanceof Xr))return new Xr(r,e,n,a,c);this.boundingBox=r,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,Ec.call(this,a,c)}function Tt(r){var e,n=typeof arguments[0]=="string"?arguments[0]:"p",a=arguments[1],c=arguments[2],o=arguments[3],l=[],h=1,f=16,g="S",b=null;ve(r=r||{})==="object"&&(n=r.orientation,a=r.unit||a,c=r.format||c,o=r.compress||r.compressPdf||o,(b=r.encryption||null)!==null&&(b.userPassword=b.userPassword||"",b.ownerPassword=b.ownerPassword||"",b.userPermissions=b.userPermissions||[]),h=typeof r.userUnit=="number"?Math.abs(r.userUnit):1,r.precision!==void 0&&(e=r.precision),r.floatPrecision!==void 0&&(f=r.floatPrecision),g=r.defaultPathOperation||"S"),l=r.filters||(o===!0?["FlateEncode"]:l),a=a||"mm",n=(""+(n||"P")).toLowerCase();var y=r.putOnlyUsedFonts||!1,S={},p={internal:{},__private__:{}};p.__private__.PubSub=hc;var O="1.3",F=p.__private__.getPdfVersion=function(){return O};p.__private__.setPdfVersion=function(s){O=s};var q={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return q};var _=p.__private__.getPageFormat=function(s){return q[s]};c=c||"a4";var B={COMPAT:"compat",ADVANCED:"advanced"},Y=B.COMPAT;function ot(){this.saveGraphicsState(),E(new zt(jt,0,0,-jt,0,mr()*jt).toString()+" cm"),this.setFontSize(this.getFontSize()/jt),g="n",Y=B.ADVANCED}function ut(){this.restoreGraphicsState(),g="S",Y=B.COMPAT}var wt=p.__private__.combineFontStyleAndFontWeight=function(s,v){if(s=="bold"&&v=="normal"||s=="bold"&&v==400||s=="normal"&&v=="italic"||s=="bold"&&v=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return v&&(s=v==400||v==="normal"?s==="italic"?"italic":"normal":v!=700&&v!=="bold"||s!=="normal"?(v==700?"bold":v)+""+s:"bold"),s};p.advancedAPI=function(s){var v=Y===B.COMPAT;return v&&ot.call(this),typeof s!="function"||(s(this),v&&ut.call(this)),this},p.compatAPI=function(s){var v=Y===B.ADVANCED;return v&&ut.call(this),typeof s!="function"||(s(this),v&&ot.call(this)),this},p.isAdvancedAPI=function(){return Y===B.ADVANCED};var tt,z=function(s){if(Y!==B.ADVANCED)throw new Error(s+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},rt=p.roundToPrecision=p.__private__.roundToPrecision=function(s,v){var j=e||v;if(isNaN(s)||isNaN(j))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return s.toFixed(j).replace(/0+$/,"")};tt=p.hpf=p.__private__.hpf=typeof f=="number"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return rt(s,f)}:f==="smart"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return rt(s,s>-1&&s<1?16:5)}:function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return rt(s,16)};var dt=p.f2=p.__private__.f2=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f2");return rt(s,2)},P=p.__private__.f3=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f3");return rt(s,3)},k=p.scale=p.__private__.scale=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.scale");return Y===B.COMPAT?s*jt:Y===B.ADVANCED?s:void 0},W=function(s){return Y===B.COMPAT?mr()-s:Y===B.ADVANCED?s:void 0},D=function(s){return k(W(s))};p.__private__.setPrecision=p.setPrecision=function(s){typeof parseInt(s,10)=="number"&&(e=parseInt(s,10))};var st,it="00000000000000000000000000000000",lt=p.__private__.getFileId=function(){return it},$=p.__private__.setFileId=function(s){return it=s!==void 0&&/^[a-fA-F0-9]{32}$/.test(s)?s.toUpperCase():it.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),b!==null&&(Ye=new ji(b.userPermissions,b.userPassword,b.ownerPassword,it)),it};p.setFileId=function(s){return $(s),this},p.getFileId=function(){return lt()};var ht=p.__private__.convertDateToPDFDate=function(s){var v=s.getTimezoneOffset(),j=v<0?"+":"-",R=Math.floor(Math.abs(v/60)),X=Math.abs(v%60),ct=[j,M(R),"'",M(X),"'"].join("");return["D:",s.getFullYear(),M(s.getMonth()+1),M(s.getDate()),M(s.getHours()),M(s.getMinutes()),M(s.getSeconds()),ct].join("")},pt=p.__private__.convertPDFDateToDate=function(s){var v=parseInt(s.substr(2,4),10),j=parseInt(s.substr(6,2),10)-1,R=parseInt(s.substr(8,2),10),X=parseInt(s.substr(10,2),10),ct=parseInt(s.substr(12,2),10),yt=parseInt(s.substr(14,2),10);return new Date(v,j,R,X,ct,yt,0)},It=p.__private__.setCreationDate=function(s){var v;if(s===void 0&&(s=new Date),s instanceof Date)v=ht(s);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(s))throw new Error("Invalid argument passed to jsPDF.setCreationDate");v=s}return st=v},N=p.__private__.getCreationDate=function(s){var v=st;return s==="jsDate"&&(v=pt(st)),v};p.setCreationDate=function(s){return It(s),this},p.getCreationDate=function(s){return N(s)};var C,M=p.__private__.padd2=function(s){return("0"+parseInt(s)).slice(-2)},T=p.__private__.padd2Hex=function(s){return("00"+(s=s.toString())).substr(s.length)},J=0,Q=[],et=[],nt=0,At=[],Nt=[],Ft=!1,_t=et,Ut=function(){J=0,nt=0,et=[],Q=[],At=[],rr=Be(),Fn=Be()};p.__private__.setCustomOutputDestination=function(s){Ft=!0,_t=s};var ft=function(s){Ft||(_t=s)};p.__private__.resetCustomOutputDestination=function(){Ft=!1,_t=et};var E=p.__private__.out=function(s){return s=s.toString(),nt+=s.length+1,_t.push(s),_t},Kt=p.__private__.write=function(s){return E(arguments.length===1?s.toString():Array.prototype.join.call(arguments," "))},Et=p.__private__.getArrayBuffer=function(s){for(var v=s.length,j=new ArrayBuffer(v),R=new Uint8Array(j);v--;)R[v]=s.charCodeAt(v);return j},Lt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return Lt};var xt=r.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(s){return xt=Y===B.ADVANCED?s/jt:s,this};var Ct,kt=p.__private__.getFontSize=p.getFontSize=function(){return Y===B.COMPAT?xt:xt*jt},qt=r.R2L||!1;p.__private__.setR2L=p.setR2L=function(s){return qt=s,this},p.__private__.getR2L=p.getR2L=function(){return qt};var Gt,Qt=p.__private__.setZoomMode=function(s){var v=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(s))Ct=s;else if(isNaN(s)){if(v.indexOf(s)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+s+'" is not recognized.');Ct=s}else Ct=parseInt(s,10)};p.__private__.getZoomMode=function(){return Ct};var te,ie=p.__private__.setPageMode=function(s){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(s)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+s+'" is not recognized.');Gt=s};p.__private__.getPageMode=function(){return Gt};var fe=p.__private__.setLayoutMode=function(s){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(s)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+s+'" is not recognized.');te=s};p.__private__.getLayoutMode=function(){return te},p.__private__.setDisplayMode=p.setDisplayMode=function(s,v,j){return Qt(s),fe(v),ie(j),this};var Wt={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(s){if(Object.keys(Wt).indexOf(s)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Wt[s]},p.__private__.getDocumentProperties=function(){return Wt},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(s){for(var v in Wt)Wt.hasOwnProperty(v)&&s[v]&&(Wt[v]=s[v]);return this},p.__private__.setDocumentProperty=function(s,v){if(Object.keys(Wt).indexOf(s)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Wt[s]=v};var ee,jt,Je,oe,_n,pe={},Le={},Un=[],ce={},Mr={},Ae={},Pn={},nr=null,xe=0,Jt=[],ue=new hc(p),Er=r.hotfixes||[],We={},Hn={},Wn=[],zt=function s(v,j,R,X,ct,yt){if(!(this instanceof s))return new s(v,j,R,X,ct,yt);isNaN(v)&&(v=1),isNaN(j)&&(j=0),isNaN(R)&&(R=0),isNaN(X)&&(X=1),isNaN(ct)&&(ct=0),isNaN(yt)&&(yt=0),this._matrix=[v,j,R,X,ct,yt]};Object.defineProperty(zt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(zt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(zt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(zt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(zt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(zt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(zt.prototype,"a",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(zt.prototype,"b",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(zt.prototype,"c",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(zt.prototype,"d",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(zt.prototype,"e",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(zt.prototype,"f",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(zt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(zt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(zt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(zt.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),zt.prototype.join=function(s){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(tt).join(s)},zt.prototype.multiply=function(s){var v=s.sx*this.sx+s.shy*this.shx,j=s.sx*this.shy+s.shy*this.sy,R=s.shx*this.sx+s.sy*this.shx,X=s.shx*this.shy+s.sy*this.sy,ct=s.tx*this.sx+s.ty*this.shx+this.tx,yt=s.tx*this.shy+s.ty*this.sy+this.ty;return new zt(v,j,R,X,ct,yt)},zt.prototype.decompose=function(){var s=this.sx,v=this.shy,j=this.shx,R=this.sy,X=this.tx,ct=this.ty,yt=Math.sqrt(s*s+v*v),Ot=(s/=yt)*j+(v/=yt)*R;j-=s*Ot,R-=v*Ot;var Dt=Math.sqrt(j*j+R*R);return Ot/=Dt,s*(R/=Dt)<v*(j/=Dt)&&(s=-s,v=-v,Ot=-Ot,yt=-yt),{scale:new zt(yt,0,0,Dt,0,0),translate:new zt(1,0,0,1,X,ct),rotate:new zt(s,v,-v,s,0,0),skew:new zt(1,0,Ot,1,0,0)}},zt.prototype.toString=function(s){return this.join(" ")},zt.prototype.inversed=function(){var s=this.sx,v=this.shy,j=this.shx,R=this.sy,X=this.tx,ct=this.ty,yt=1/(s*R-v*j),Ot=R*yt,Dt=-v*yt,Zt=-j*yt,Yt=s*yt;return new zt(Ot,Dt,Zt,Yt,-Ot*X-Zt*ct,-Dt*X-Yt*ct)},zt.prototype.applyToPoint=function(s){var v=s.x*this.sx+s.y*this.shx+this.tx,j=s.x*this.shy+s.y*this.sy+this.ty;return new pi(v,j)},zt.prototype.applyToRectangle=function(s){var v=this.applyToPoint(s),j=this.applyToPoint(new pi(s.x+s.w,s.y+s.h));return new Wi(v.x,v.y,j.x-v.x,j.y-v.y)},zt.prototype.clone=function(){var s=this.sx,v=this.shy,j=this.shx,R=this.sy,X=this.tx,ct=this.ty;return new zt(s,v,j,R,X,ct)},p.Matrix=zt;var kn=p.matrixMult=function(s,v){return v.multiply(s)},Vn=new zt(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=Vn;var an=function(s,v){if(!Mr[s]){var j=(v instanceof Ir?"Sh":"P")+(Object.keys(ce).length+1).toString(10);v.id=j,Mr[s]=j,ce[j]=v,ue.publish("addPattern",v)}};p.ShadingPattern=Ir,p.TilingPattern=Xr,p.addShadingPattern=function(s,v){return z("addShadingPattern()"),an(s,v),this},p.beginTilingPattern=function(s){z("beginTilingPattern()"),qa(s.boundingBox[0],s.boundingBox[1],s.boundingBox[2]-s.boundingBox[0],s.boundingBox[3]-s.boundingBox[1],s.matrix)},p.endTilingPattern=function(s,v){z("endTilingPattern()"),v.stream=Nt[C].join(`
`),an(s,v),ue.publish("endTilingPattern",v),Wn.pop().restore()};var De=p.__private__.newObject=function(){var s=Be();return fn(s,!0),s},Be=p.__private__.newObjectDeferred=function(){return J++,Q[J]=function(){return nt},J},fn=function(s,v){return v=typeof v=="boolean"&&v,Q[s]=nt,v&&E(s+" 0 obj"),s},ei=p.__private__.newAdditionalObject=function(){var s={objId:Be(),content:""};return At.push(s),s},rr=Be(),Fn=Be(),In=p.__private__.decodeColorString=function(s){var v=s.split(" ");if(v.length!==2||v[1]!=="g"&&v[1]!=="G")v.length===5&&(v[4]==="k"||v[4]==="K")&&(v=[(1-v[0])*(1-v[3]),(1-v[1])*(1-v[3]),(1-v[2])*(1-v[3]),"r"]);else{var j=parseFloat(v[0]);v=[j,j,j,"r"]}for(var R="#",X=0;X<3;X++)R+=("0"+Math.floor(255*parseFloat(v[X])).toString(16)).slice(-2);return R},Cn=p.__private__.encodeColorString=function(s){var v;typeof s=="string"&&(s={ch1:s});var j=s.ch1,R=s.ch2,X=s.ch3,ct=s.ch4,yt=s.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof j=="string"&&j.charAt(0)!=="#"){var Ot=new Bc(j);if(Ot.ok)j=Ot.toHex();else if(!/^\d*\.?\d*$/.test(j))throw new Error('Invalid color "'+j+'" passed to jsPDF.encodeColorString.')}if(typeof j=="string"&&/^#[0-9A-Fa-f]{3}$/.test(j)&&(j="#"+j[1]+j[1]+j[2]+j[2]+j[3]+j[3]),typeof j=="string"&&/^#[0-9A-Fa-f]{6}$/.test(j)){var Dt=parseInt(j.substr(1),16);j=Dt>>16&255,R=Dt>>8&255,X=255&Dt}if(R===void 0||ct===void 0&&j===R&&R===X)if(typeof j=="string")v=j+" "+yt[0];else switch(s.precision){case 2:v=dt(j/255)+" "+yt[0];break;case 3:default:v=P(j/255)+" "+yt[0]}else if(ct===void 0||ve(ct)==="object"){if(ct&&!isNaN(ct.a)&&ct.a===0)return v=["1.","1.","1.",yt[1]].join(" ");if(typeof j=="string")v=[j,R,X,yt[1]].join(" ");else switch(s.precision){case 2:v=[dt(j/255),dt(R/255),dt(X/255),yt[1]].join(" ");break;default:case 3:v=[P(j/255),P(R/255),P(X/255),yt[1]].join(" ")}}else if(typeof j=="string")v=[j,R,X,ct,yt[2]].join(" ");else switch(s.precision){case 2:v=[dt(j),dt(R),dt(X),dt(ct),yt[2]].join(" ");break;case 3:default:v=[P(j),P(R),P(X),P(ct),yt[2]].join(" ")}return v},Gn=p.__private__.getFilters=function(){return l},bn=p.__private__.putStream=function(s){var v=(s=s||{}).data||"",j=s.filters||Gn(),R=s.alreadyAppliedFilters||[],X=s.addLength1||!1,ct=v.length,yt=s.objectId,Ot=function(Xe){return Xe};if(b!==null&&yt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");b!==null&&(Ot=Ye.encryptor(yt,0));var Dt={};j===!0&&(j=["FlateEncode"]);var Zt=s.additionalKeyValues||[],Yt=(Dt=Tt.API.processDataByFilters!==void 0?Tt.API.processDataByFilters(v,j):{data:v,reverseChain:[]}).reverseChain+(Array.isArray(R)?R.join(" "):R.toString());if(Dt.data.length!==0&&(Zt.push({key:"Length",value:Dt.data.length}),X===!0&&Zt.push({key:"Length1",value:ct})),Yt.length!=0)if(Yt.split("/").length-1==1)Zt.push({key:"Filter",value:Yt});else{Zt.push({key:"Filter",value:"["+Yt+"]"});for(var re=0;re<Zt.length;re+=1)if(Zt[re].key==="DecodeParms"){for(var Ne=[],Se=0;Se<Dt.reverseChain.split("/").length-1;Se+=1)Ne.push("null");Ne.push(Zt[re].value),Zt[re].value="["+Ne.join(" ")+"]"}}E("<<");for(var Me=0;Me<Zt.length;Me++)E("/"+Zt[Me].key+" "+Zt[Me].value);E(">>"),Dt.data.length!==0&&(E("stream"),E(Ot(Dt.data)),E("endstream"))},Jn=p.__private__.putPage=function(s){var v=s.number,j=s.data,R=s.objId,X=s.contentsObjId;fn(R,!0),E("<</Type /Page"),E("/Parent "+s.rootDictionaryObjId+" 0 R"),E("/Resources "+s.resourceDictionaryObjId+" 0 R"),E("/MediaBox ["+parseFloat(tt(s.mediaBox.bottomLeftX))+" "+parseFloat(tt(s.mediaBox.bottomLeftY))+" "+tt(s.mediaBox.topRightX)+" "+tt(s.mediaBox.topRightY)+"]"),s.cropBox!==null&&E("/CropBox ["+tt(s.cropBox.bottomLeftX)+" "+tt(s.cropBox.bottomLeftY)+" "+tt(s.cropBox.topRightX)+" "+tt(s.cropBox.topRightY)+"]"),s.bleedBox!==null&&E("/BleedBox ["+tt(s.bleedBox.bottomLeftX)+" "+tt(s.bleedBox.bottomLeftY)+" "+tt(s.bleedBox.topRightX)+" "+tt(s.bleedBox.topRightY)+"]"),s.trimBox!==null&&E("/TrimBox ["+tt(s.trimBox.bottomLeftX)+" "+tt(s.trimBox.bottomLeftY)+" "+tt(s.trimBox.topRightX)+" "+tt(s.trimBox.topRightY)+"]"),s.artBox!==null&&E("/ArtBox ["+tt(s.artBox.bottomLeftX)+" "+tt(s.artBox.bottomLeftY)+" "+tt(s.artBox.topRightX)+" "+tt(s.artBox.topRightY)+"]"),typeof s.userUnit=="number"&&s.userUnit!==1&&E("/UserUnit "+s.userUnit),ue.publish("putPage",{objId:R,pageContext:Jt[v],pageNumber:v,page:j}),E("/Contents "+X+" 0 R"),E(">>"),E("endobj");var ct=j.join(`
`);return Y===B.ADVANCED&&(ct+=`
Q`),fn(X,!0),bn({data:ct,filters:Gn(),objectId:X}),E("endobj"),R},qr=p.__private__.putPages=function(){var s,v,j=[];for(s=1;s<=xe;s++)Jt[s].objId=Be(),Jt[s].contentsObjId=Be();for(s=1;s<=xe;s++)j.push(Jn({number:s,data:Nt[s],objId:Jt[s].objId,contentsObjId:Jt[s].contentsObjId,mediaBox:Jt[s].mediaBox,cropBox:Jt[s].cropBox,bleedBox:Jt[s].bleedBox,trimBox:Jt[s].trimBox,artBox:Jt[s].artBox,userUnit:Jt[s].userUnit,rootDictionaryObjId:rr,resourceDictionaryObjId:Fn}));fn(rr,!0),E("<</Type /Pages");var R="/Kids [";for(v=0;v<xe;v++)R+=j[v]+" 0 R ";E(R+"]"),E("/Count "+xe),E(">>"),E("endobj"),ue.publish("postPutPages")},ni=function(s){ue.publish("putFont",{font:s,out:E,newObject:De,putStream:bn}),s.isAlreadyPutted!==!0&&(s.objectNumber=De(),E("<<"),E("/Type /Font"),E("/BaseFont /"+Oi(s.postScriptName)),E("/Subtype /Type1"),typeof s.encoding=="string"&&E("/Encoding /"+s.encoding),E("/FirstChar 32"),E("/LastChar 255"),E(">>"),E("endobj"))},ri=function(){for(var s in pe)pe.hasOwnProperty(s)&&(y===!1||y===!0&&S.hasOwnProperty(s))&&ni(pe[s])},ii=function(s){s.objectNumber=De();var v=[];v.push({key:"Type",value:"/XObject"}),v.push({key:"Subtype",value:"/Form"}),v.push({key:"BBox",value:"["+[tt(s.x),tt(s.y),tt(s.x+s.width),tt(s.y+s.height)].join(" ")+"]"}),v.push({key:"Matrix",value:"["+s.matrix.toString()+"]"});var j=s.pages[1].join(`
`);bn({data:j,additionalKeyValues:v,objectId:s.objectNumber}),E("endobj")},ai=function(){for(var s in We)We.hasOwnProperty(s)&&ii(We[s])},ya=function(s,v){var j,R=[],X=1/(v-1);for(j=0;j<1;j+=X)R.push(j);if(R.push(1),s[0].offset!=0){var ct={offset:0,color:s[0].color};s.unshift(ct)}if(s[s.length-1].offset!=1){var yt={offset:1,color:s[s.length-1].color};s.push(yt)}for(var Ot="",Dt=0,Zt=0;Zt<R.length;Zt++){for(j=R[Zt];j>s[Dt+1].offset;)Dt++;var Yt=s[Dt].offset,re=(j-Yt)/(s[Dt+1].offset-Yt),Ne=s[Dt].color,Se=s[Dt+1].color;Ot+=T(Math.round((1-re)*Ne[0]+re*Se[0]).toString(16))+T(Math.round((1-re)*Ne[1]+re*Se[1]).toString(16))+T(Math.round((1-re)*Ne[2]+re*Se[2]).toString(16))}return Ot.trim()},bo=function(s,v){v||(v=21);var j=De(),R=ya(s.colors,v),X=[];X.push({key:"FunctionType",value:"0"}),X.push({key:"Domain",value:"[0.0 1.0]"}),X.push({key:"Size",value:"["+v+"]"}),X.push({key:"BitsPerSample",value:"8"}),X.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),X.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),bn({data:R,additionalKeyValues:X,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:j}),E("endobj"),s.objectNumber=De(),E("<< /ShadingType "+s.type),E("/ColorSpace /DeviceRGB");var ct="/Coords ["+tt(parseFloat(s.coords[0]))+" "+tt(parseFloat(s.coords[1]))+" ";s.type===2?ct+=tt(parseFloat(s.coords[2]))+" "+tt(parseFloat(s.coords[3])):ct+=tt(parseFloat(s.coords[2]))+" "+tt(parseFloat(s.coords[3]))+" "+tt(parseFloat(s.coords[4]))+" "+tt(parseFloat(s.coords[5])),E(ct+="]"),s.matrix&&E("/Matrix ["+s.matrix.toString()+"]"),E("/Function "+j+" 0 R"),E("/Extend [true true]"),E(">>"),E("endobj")},yo=function(s,v){var j=Be(),R=De();v.push({resourcesOid:j,objectOid:R}),s.objectNumber=R;var X=[];X.push({key:"Type",value:"/Pattern"}),X.push({key:"PatternType",value:"1"}),X.push({key:"PaintType",value:"1"}),X.push({key:"TilingType",value:"1"}),X.push({key:"BBox",value:"["+s.boundingBox.map(tt).join(" ")+"]"}),X.push({key:"XStep",value:tt(s.xStep)}),X.push({key:"YStep",value:tt(s.yStep)}),X.push({key:"Resources",value:j+" 0 R"}),s.matrix&&X.push({key:"Matrix",value:"["+s.matrix.toString()+"]"}),bn({data:s.stream,additionalKeyValues:X,objectId:s.objectNumber}),E("endobj")},oi=function(s){var v;for(v in ce)ce.hasOwnProperty(v)&&(ce[v]instanceof Ir?bo(ce[v]):ce[v]instanceof Xr&&yo(ce[v],s))},wa=function(s){for(var v in s.objectNumber=De(),E("<<"),s)switch(v){case"opacity":E("/ca "+dt(s[v]));break;case"stroke-opacity":E("/CA "+dt(s[v]))}E(">>"),E("endobj")},wo=function(){var s;for(s in Ae)Ae.hasOwnProperty(s)&&wa(Ae[s])},Bi=function(){for(var s in E("/XObject <<"),We)We.hasOwnProperty(s)&&We[s].objectNumber>=0&&E("/"+s+" "+We[s].objectNumber+" 0 R");ue.publish("putXobjectDict"),E(">>")},Lo=function(){Ye.oid=De(),E("<<"),E("/Filter /Standard"),E("/V "+Ye.v),E("/R "+Ye.r),E("/U <"+Ye.toHexString(Ye.U)+">"),E("/O <"+Ye.toHexString(Ye.O)+">"),E("/P "+Ye.P),E(">>"),E("endobj")},La=function(){for(var s in E("/Font <<"),pe)pe.hasOwnProperty(s)&&(y===!1||y===!0&&S.hasOwnProperty(s))&&E("/"+s+" "+pe[s].objectNumber+" 0 R");E(">>")},No=function(){if(Object.keys(ce).length>0){for(var s in E("/Shading <<"),ce)ce.hasOwnProperty(s)&&ce[s]instanceof Ir&&ce[s].objectNumber>=0&&E("/"+s+" "+ce[s].objectNumber+" 0 R");ue.publish("putShadingPatternDict"),E(">>")}},si=function(s){if(Object.keys(ce).length>0){for(var v in E("/Pattern <<"),ce)ce.hasOwnProperty(v)&&ce[v]instanceof p.TilingPattern&&ce[v].objectNumber>=0&&ce[v].objectNumber<s&&E("/"+v+" "+ce[v].objectNumber+" 0 R");ue.publish("putTilingPatternDict"),E(">>")}},Ao=function(){if(Object.keys(Ae).length>0){var s;for(s in E("/ExtGState <<"),Ae)Ae.hasOwnProperty(s)&&Ae[s].objectNumber>=0&&E("/"+s+" "+Ae[s].objectNumber+" 0 R");ue.publish("putGStateDict"),E(">>")}},ke=function(s){fn(s.resourcesOid,!0),E("<<"),E("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),La(),No(),si(s.objectOid),Ao(),Bi(),E(">>"),E("endobj")},Na=function(){var s=[];ri(),wo(),ai(),oi(s),ue.publish("putResources"),s.forEach(ke),ke({resourcesOid:Fn,objectOid:Number.MAX_SAFE_INTEGER}),ue.publish("postPutResources")},Aa=function(){ue.publish("putAdditionalObjects");for(var s=0;s<At.length;s++){var v=At[s];fn(v.objId,!0),E(v.content),E("endobj")}ue.publish("postPutAdditionalObjects")},xa=function(s){Le[s.fontName]=Le[s.fontName]||{},Le[s.fontName][s.fontStyle]=s.id},Mi=function(s,v,j,R,X){var ct={id:"F"+(Object.keys(pe).length+1).toString(10),postScriptName:s,fontName:v,fontStyle:j,encoding:R,isStandardFont:X||!1,metadata:{}};return ue.publish("addFont",{font:ct,instance:this}),pe[ct.id]=ct,xa(ct),ct.id},xo=function(s){for(var v=0,j=Lt.length;v<j;v++){var R=Mi.call(this,s[v][0],s[v][1],s[v][2],Lt[v][3],!0);y===!1&&(S[R]=!0);var X=s[v][0].split("-");xa({id:R,fontName:X[0],fontStyle:X[1]||""})}ue.publish("addFonts",{fonts:pe,dictionary:Le})},jn=function(s){return s.foo=function(){try{return s.apply(this,arguments)}catch(R){var v=R.stack||"";~v.indexOf(" at ")&&(v=v.split(" at ")[1]);var j="Error in function "+v.split(`
`)[0].split("<")[0]+": "+R.message;if(!Ht.console)throw new Error(j);Ht.console.error(j,R),Ht.alert&&alert(j)}},s.foo.bar=s,s.foo},ci=function(s,v){var j,R,X,ct,yt,Ot,Dt,Zt,Yt;if(X=(v=v||{}).sourceEncoding||"Unicode",yt=v.outputEncoding,(v.autoencode||yt)&&pe[ee].metadata&&pe[ee].metadata[X]&&pe[ee].metadata[X].encoding&&(ct=pe[ee].metadata[X].encoding,!yt&&pe[ee].encoding&&(yt=pe[ee].encoding),!yt&&ct.codePages&&(yt=ct.codePages[0]),typeof yt=="string"&&(yt=ct[yt]),yt)){for(Dt=!1,Ot=[],j=0,R=s.length;j<R;j++)(Zt=yt[s.charCodeAt(j)])?Ot.push(String.fromCharCode(Zt)):Ot.push(s[j]),Ot[j].charCodeAt(0)>>8&&(Dt=!0);s=Ot.join("")}for(j=s.length;Dt===void 0&&j!==0;)s.charCodeAt(j-1)>>8&&(Dt=!0),j--;if(!Dt)return s;for(Ot=v.noBOM?[]:[254,255],j=0,R=s.length;j<R;j++){if((Yt=(Zt=s.charCodeAt(j))>>8)>>8)throw new Error("Character at position "+j+" of string '"+s+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Ot.push(Yt),Ot.push(Zt-(Yt<<8))}return String.fromCharCode.apply(void 0,Ot)},on=p.__private__.pdfEscape=p.pdfEscape=function(s,v){return ci(s,v).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ei=p.__private__.beginPage=function(s){Nt[++xe]=[],Jt[xe]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(s[0]),topRightY:Number(s[1])}},_a(xe),ft(Nt[C])},Sa=function(s,v){var j,R,X;switch(n=v||n,typeof s=="string"&&(j=_(s.toLowerCase()),Array.isArray(j)&&(R=j[0],X=j[1])),Array.isArray(s)&&(R=s[0]*jt,X=s[1]*jt),isNaN(R)&&(R=c[0],X=c[1]),(R>14400||X>14400)&&(me.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),R=Math.min(14400,R),X=Math.min(14400,X)),c=[R,X],n.substr(0,1)){case"l":X>R&&(c=[X,R]);break;case"p":R>X&&(c=[X,R])}Ei(c),ja(Ti),E(On),Ui!==0&&E(Ui+" J"),Hi!==0&&E(Hi+" j"),ue.publish("addPage",{pageNumber:xe})},So=function(s){s>0&&s<=xe&&(Nt.splice(s,1),Jt.splice(s,1),xe--,C>xe&&(C=xe),this.setPage(C))},_a=function(s){s>0&&s<=xe&&(C=s)},_o=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return Nt.length-1},Pa=function(s,v,j){var R,X=void 0;return j=j||{},s=s!==void 0?s:pe[ee].fontName,v=v!==void 0?v:pe[ee].fontStyle,R=s.toLowerCase(),Le[R]!==void 0&&Le[R][v]!==void 0?X=Le[R][v]:Le[s]!==void 0&&Le[s][v]!==void 0?X=Le[s][v]:j.disableWarning===!1&&me.warn("Unable to look up font label for font '"+s+"', '"+v+"'. Refer to getFontList() for available fonts."),X||j.noFallback||(X=Le.times[v])==null&&(X=Le.times.normal),X},Po=p.__private__.putInfo=function(){var s=De(),v=function(R){return R};for(var j in b!==null&&(v=Ye.encryptor(s,0)),E("<<"),E("/Producer ("+on(v("jsPDF "+Tt.version))+")"),Wt)Wt.hasOwnProperty(j)&&Wt[j]&&E("/"+j.substr(0,1).toUpperCase()+j.substr(1)+" ("+on(v(Wt[j]))+")");E("/CreationDate ("+on(v(st))+")"),E(">>"),E("endobj")},qi=p.__private__.putCatalog=function(s){var v=(s=s||{}).rootDictionaryObjId||rr;switch(De(),E("<<"),E("/Type /Catalog"),E("/Pages "+v+" 0 R"),Ct||(Ct="fullwidth"),Ct){case"fullwidth":E("/OpenAction [3 0 R /FitH null]");break;case"fullheight":E("/OpenAction [3 0 R /FitV null]");break;case"fullpage":E("/OpenAction [3 0 R /Fit]");break;case"original":E("/OpenAction [3 0 R /XYZ null null 1]");break;default:var j=""+Ct;j.substr(j.length-1)==="%"&&(Ct=parseInt(Ct)/100),typeof Ct=="number"&&E("/OpenAction [3 0 R /XYZ null null "+dt(Ct)+"]")}switch(te||(te="continuous"),te){case"continuous":E("/PageLayout /OneColumn");break;case"single":E("/PageLayout /SinglePage");break;case"two":case"twoleft":E("/PageLayout /TwoColumnLeft");break;case"tworight":E("/PageLayout /TwoColumnRight")}Gt&&E("/PageMode /"+Gt),ue.publish("putCatalog"),E(">>"),E("endobj")},ko=p.__private__.putTrailer=function(){E("trailer"),E("<<"),E("/Size "+(J+1)),E("/Root "+J+" 0 R"),E("/Info "+(J-1)+" 0 R"),b!==null&&E("/Encrypt "+Ye.oid+" 0 R"),E("/ID [ <"+it+"> <"+it+"> ]"),E(">>")},Fo=p.__private__.putHeader=function(){E("%PDF-"+O),E("%ºß¬à")},Io=p.__private__.putXRef=function(){var s="0000000000";E("xref"),E("0 "+(J+1)),E("0000000000 65535 f ");for(var v=1;v<=J;v++)typeof Q[v]=="function"?E((s+Q[v]()).slice(-10)+" 00000 n "):Q[v]!==void 0?E((s+Q[v]).slice(-10)+" 00000 n "):E("0000000000 00000 n ")},ir=p.__private__.buildDocument=function(){Ut(),ft(et),ue.publish("buildDocument"),Fo(),qr(),Aa(),Na(),b!==null&&Lo(),Po(),qi();var s=nt;return Io(),ko(),E("startxref"),E(""+s),E("%%EOF"),ft(Nt[C]),et.join(`
`)},ui=p.__private__.getBlob=function(s){return new Blob([Et(s)],{type:"application/pdf"})},li=p.output=p.__private__.output=jn(function(s,v){switch(typeof(v=v||{})=="string"?v={filename:v}:v.filename=v.filename||"generated.pdf",s){case void 0:return ir();case"save":p.save(v.filename);break;case"arraybuffer":return Et(ir());case"blob":return ui(ir());case"bloburi":case"bloburl":if(Ht.URL!==void 0&&typeof Ht.URL.createObjectURL=="function")return Ht.URL&&Ht.URL.createObjectURL(ui(ir()))||void 0;me.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var j="",R=ir();try{j=Ls(R)}catch{j=Ls(unescape(encodeURIComponent(R)))}return"data:application/pdf;filename="+v.filename+";base64,"+j;case"pdfobjectnewwindow":if(Object.prototype.toString.call(Ht)==="[object Window]"){var X="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",ct=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';v.pdfObjectUrl&&(X=v.pdfObjectUrl,ct="");var yt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+X+'"'+ct+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(v)+");<\/script></body></html>",Ot=Ht.open();return Ot!==null&&Ot.document.write(yt),Ot}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(Ht)==="[object Window]"){var Dt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(v.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+v.filename+'" width="500px" height="400px" /></body></html>',Zt=Ht.open();if(Zt!==null){Zt.document.write(Dt);var Yt=this;Zt.document.documentElement.querySelector("#pdfViewer").onload=function(){Zt.document.title=v.filename,Zt.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(Yt.output("bloburl"))}}return Zt}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(Ht)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var re='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",v)+'"></iframe></body></html>',Ne=Ht.open();if(Ne!==null&&(Ne.document.write(re),Ne.document.title=v.filename),Ne||typeof safari>"u")return Ne;break;case"datauri":case"dataurl":return Ht.document.location.href=this.output("datauristring",v);default:return null}}),ka=function(s){return Array.isArray(Er)===!0&&Er.indexOf(s)>-1};switch(a){case"pt":jt=1;break;case"mm":jt=72/25.4;break;case"cm":jt=72/2.54;break;case"in":jt=72;break;case"px":jt=ka("px_scaling")==1?.75:96/72;break;case"pc":case"em":jt=12;break;case"ex":jt=6;break;default:if(typeof a!="number")throw new Error("Invalid unit: "+a);jt=a}var Ye=null;It(),$();var Co=function(s){return b!==null?Ye.encryptor(s,0):function(v){return v}},Fa=p.__private__.getPageInfo=p.getPageInfo=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Jt[s].objId,pageNumber:s,pageContext:Jt[s]}},Vt=p.__private__.getPageInfoByObjId=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var v in Jt)if(Jt[v].objId===s)break;return Fa(v)},jo=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:Jt[C].objId,pageNumber:C,pageContext:Jt[C]}};p.addPage=function(){return Sa.apply(this,arguments),this},p.setPage=function(){return _a.apply(this,arguments),ft.call(this,Nt[C]),this},p.insertPage=function(s){return this.addPage(),this.movePage(C,s),this},p.movePage=function(s,v){var j,R;if(s>v){j=Nt[s],R=Jt[s];for(var X=s;X>v;X--)Nt[X]=Nt[X-1],Jt[X]=Jt[X-1];Nt[v]=j,Jt[v]=R,this.setPage(v)}else if(s<v){j=Nt[s],R=Jt[s];for(var ct=s;ct<v;ct++)Nt[ct]=Nt[ct+1],Jt[ct]=Jt[ct+1];Nt[v]=j,Jt[v]=R,this.setPage(v)}return this},p.deletePage=function(){return So.apply(this,arguments),this},p.__private__.text=p.text=function(s,v,j,R,X){var ct,yt,Ot,Dt,Zt,Yt,re,Ne,Se,Me=(R=R||{}).scope||this;if(typeof s=="number"&&typeof v=="number"&&(typeof j=="string"||Array.isArray(j))){var Xe=j;j=v,v=s,s=Xe}if(arguments[3]instanceof zt?(z("The transform parameter of text() with a Matrix value"),Se=X):(Ot=arguments[4],Dt=arguments[5],ve(re=arguments[3])==="object"&&re!==null||(typeof Ot=="string"&&(Dt=Ot,Ot=null),typeof re=="string"&&(Dt=re,re=null),typeof re=="number"&&(Ot=re,re=null),R={flags:re,angle:Ot,align:Dt})),isNaN(v)||isNaN(j)||s==null)throw new Error("Invalid arguments passed to jsPDF.text");if(s.length===0)return Me;var ze="",Bn=!1,dn=typeof R.lineHeightFactor=="number"?R.lineHeightFactor:Rr,Kn=Me.internal.scaleFactor;function Da(be){return be=be.split("	").join(Array(R.TabLen||9).join(" ")),on(be,re)}function Yi(be){for(var ye,Ie=be.concat(),Re=[],ur=Ie.length;ur--;)typeof(ye=Ie.shift())=="string"?Re.push(ye):Array.isArray(be)&&(ye.length===1||ye[1]===void 0&&ye[2]===void 0)?Re.push(ye[0]):Re.push([ye[0],ye[1],ye[2]]);return Re}function Xi(be,ye){var Ie;if(typeof be=="string")Ie=ye(be)[0];else if(Array.isArray(be)){for(var Re,ur,ra=be.concat(),Si=[],Ha=ra.length;Ha--;)typeof(Re=ra.shift())=="string"?Si.push(ye(Re)[0]):Array.isArray(Re)&&typeof Re[0]=="string"&&(ur=ye(Re[0],Re[1],Re[2]),Si.push([ur[0],ur[1],ur[2]]));Ie=Si}return Ie}var mi=!1,Ki=!0;if(typeof s=="string")mi=!0;else if(Array.isArray(s)){var Zi=s.concat();yt=[];for(var vi,Ve=Zi.length;Ve--;)(typeof(vi=Zi.shift())!="string"||Array.isArray(vi)&&typeof vi[0]!="string")&&(Ki=!1);mi=Ki}if(mi===!1)throw new Error('Type of text must be string or Array. "'+s+'" is not recognized.');typeof s=="string"&&(s=s.match(/[\r?\n]/)?s.split(/\r\n|\r|\n/g):[s]);var bi=xt/Me.internal.scaleFactor,yi=bi*(dn-1);switch(R.baseline){case"bottom":j-=yi;break;case"top":j+=bi-yi;break;case"hanging":j+=bi-2*yi;break;case"middle":j+=bi/2-yi}if((Yt=R.maxWidth||0)>0&&(typeof s=="string"?s=Me.splitTextToSize(s,Yt):Object.prototype.toString.call(s)==="[object Array]"&&(s=s.reduce(function(be,ye){return be.concat(Me.splitTextToSize(ye,Yt))},[]))),ct={text:s,x:v,y:j,options:R,mutex:{pdfEscape:on,activeFontKey:ee,fonts:pe,activeFontSize:xt}},ue.publish("preProcessText",ct),s=ct.text,Ot=(R=ct.options).angle,!(Se instanceof zt)&&Ot&&typeof Ot=="number"){Ot*=Math.PI/180,R.rotationDirection===0&&(Ot=-Ot),Y===B.ADVANCED&&(Ot=-Ot);var wi=Math.cos(Ot),$i=Math.sin(Ot);Se=new zt(wi,$i,-$i,wi,0,0)}else Ot&&Ot instanceof zt&&(Se=Ot);Y!==B.ADVANCED||Se||(Se=Vn),(Zt=R.charSpace||di)!==void 0&&(ze+=tt(k(Zt))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(Ne=R.horizontalScale)!==void 0&&(ze+=tt(100*Ne)+` Tz
`),R.lang;var sn=-1,Uo=R.renderingMode!==void 0?R.renderingMode:R.stroke,Qi=Me.internal.getCurrentPageInfo().pageContext;switch(Uo){case 0:case!1:case"fill":sn=0;break;case 1:case!0:case"stroke":sn=1;break;case 2:case"fillThenStroke":sn=2;break;case 3:case"invisible":sn=3;break;case 4:case"fillAndAddForClipping":sn=4;break;case 5:case"strokeAndAddPathForClipping":sn=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":sn=6;break;case 7:case"addToPathForClipping":sn=7}var Ra=Qi.usedRenderingMode!==void 0?Qi.usedRenderingMode:-1;sn!==-1?ze+=sn+` Tr
`:Ra!==-1&&(ze+=`0 Tr
`),sn!==-1&&(Qi.usedRenderingMode=sn),Dt=R.align||"left";var yn,Li=xt*dn,Ta=Me.internal.pageSize.getWidth(),za=pe[ee];Zt=R.charSpace||di,Yt=R.maxWidth||0,re=Object.assign({autoencode:!0,noBOM:!0},R.flags);var vr=[],Ur=function(be){return Me.getStringUnitWidth(be,{font:za,charSpace:Zt,fontSize:xt,doKerning:!1})*xt/Kn};if(Object.prototype.toString.call(s)==="[object Array]"){var cn;yt=Yi(s),Dt!=="left"&&(yn=yt.map(Ur));var en,br=0;if(Dt==="right"){v-=yn[0],s=[],Ve=yt.length;for(var or=0;or<Ve;or++)or===0?(en=Xn(v),cn=ar(j)):(en=k(br-yn[or]),cn=-Li),s.push([yt[or],en,cn]),br=yn[or]}else if(Dt==="center"){v-=yn[0]/2,s=[],Ve=yt.length;for(var sr=0;sr<Ve;sr++)sr===0?(en=Xn(v),cn=ar(j)):(en=k((br-yn[sr])/2),cn=-Li),s.push([yt[sr],en,cn]),br=yn[sr]}else if(Dt==="left"){s=[],Ve=yt.length;for(var Ni=0;Ni<Ve;Ni++)s.push(yt[Ni])}else if(Dt==="justify"&&za.encoding==="Identity-H"){s=[],Ve=yt.length,Yt=Yt!==0?Yt:Ta;for(var cr=0,Fe=0;Fe<Ve;Fe++)if(cn=Fe===0?ar(j):-Li,en=Fe===0?Xn(v):cr,Fe<Ve-1){var ta=k((Yt-yn[Fe])/(yt[Fe].split(" ").length-1)),nn=yt[Fe].split(" ");s.push([nn[0]+" ",en,cn]),cr=0;for(var wn=1;wn<nn.length;wn++){var Ai=(Ur(nn[wn-1]+" "+nn[wn])-Ur(nn[wn]))*Kn+ta;wn==nn.length-1?s.push([nn[wn],Ai,0]):s.push([nn[wn]+" ",Ai,0]),cr-=Ai}}else s.push([yt[Fe],en,cn]);s.push(["",cr,0])}else{if(Dt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(s=[],Ve=yt.length,Yt=Yt!==0?Yt:Ta,Fe=0;Fe<Ve;Fe++)cn=Fe===0?ar(j):-Li,en=Fe===0?Xn(v):0,Fe<Ve-1?vr.push(tt(k((Yt-yn[Fe])/(yt[Fe].split(" ").length-1)))):vr.push(0),s.push([yt[Fe],en,cn])}}var Ua=typeof R.R2L=="boolean"?R.R2L:qt;Ua===!0&&(s=Xi(s,function(be,ye,Ie){return[be.split("").reverse().join(""),ye,Ie]})),ct={text:s,x:v,y:j,options:R,mutex:{pdfEscape:on,activeFontKey:ee,fonts:pe,activeFontSize:xt}},ue.publish("postProcessText",ct),s=ct.text,Bn=ct.mutex.isHex||!1;var ea=pe[ee].encoding;ea!=="WinAnsiEncoding"&&ea!=="StandardEncoding"||(s=Xi(s,function(be,ye,Ie){return[Da(be),ye,Ie]})),yt=Yi(s),s=[];for(var Hr,Wr,yr,Vr=0,xi=1,Gr=Array.isArray(yt[0])?xi:Vr,wr="",na=function(be,ye,Ie){var Re="";return Ie instanceof zt?(Ie=typeof R.angle=="number"?kn(Ie,new zt(1,0,0,1,be,ye)):kn(new zt(1,0,0,1,be,ye),Ie),Y===B.ADVANCED&&(Ie=kn(new zt(1,0,0,-1,0,0),Ie)),Re=Ie.join(" ")+` Tm
`):Re=tt(be)+" "+tt(ye)+` Td
`,Re},Ln=0;Ln<yt.length;Ln++){switch(wr="",Gr){case xi:yr=(Bn?"<":"(")+yt[Ln][0]+(Bn?">":")"),Hr=parseFloat(yt[Ln][1]),Wr=parseFloat(yt[Ln][2]);break;case Vr:yr=(Bn?"<":"(")+yt[Ln]+(Bn?">":")"),Hr=Xn(v),Wr=ar(j)}vr!==void 0&&vr[Ln]!==void 0&&(wr=vr[Ln]+` Tw
`),Ln===0?s.push(wr+na(Hr,Wr,Se)+yr):Gr===Vr?s.push(wr+yr):Gr===xi&&s.push(wr+na(Hr,Wr,Se)+yr)}s=Gr===Vr?s.join(` Tj
T* `):s.join(` Tj
`),s+=` Tj
`;var Nn=`BT
/`;return Nn+=ee+" "+xt+` Tf
`,Nn+=tt(xt*dn)+` TL
`,Nn+=Tr+`
`,Nn+=ze,Nn+=s,E(Nn+="ET"),S[ee]=!0,Me};var Oo=p.__private__.clip=p.clip=function(s){return E(s==="evenodd"?"W*":"W"),this};p.clipEvenOdd=function(){return Oo("evenodd")},p.__private__.discardPath=p.discardPath=function(){return E("n"),this};var Yn=p.__private__.isValidStyle=function(s){var v=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(s)!==-1&&(v=!0),v};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(s){return Yn(s)&&(g=s),this};var Ia=p.__private__.getStyle=p.getStyle=function(s){var v=g;switch(s){case"D":case"S":v="S";break;case"F":v="f";break;case"FD":case"DF":v="B";break;case"f":case"f*":case"B":case"B*":v=s}return v},Ca=p.close=function(){return E("h"),this};p.stroke=function(){return E("S"),this},p.fill=function(s){return hi("f",s),this},p.fillEvenOdd=function(s){return hi("f*",s),this},p.fillStroke=function(s){return hi("B",s),this},p.fillStrokeEvenOdd=function(s){return hi("B*",s),this};var hi=function(s,v){ve(v)==="object"?Mo(v,s):E(s)},Di=function(s){s===null||Y===B.ADVANCED&&s===void 0||(s=Ia(s),E(s))};function Bo(s,v,j,R,X){var ct=new Xr(v||this.boundingBox,j||this.xStep,R||this.yStep,this.gState,X||this.matrix);ct.stream=this.stream;var yt=s+"$$"+this.cloneIndex+++"$$";return an(yt,ct),ct}var Mo=function(s,v){var j=Mr[s.key],R=ce[j];if(R instanceof Ir)E("q"),E(Eo(v)),R.gState&&p.setGState(R.gState),E(s.matrix.toString()+" cm"),E("/"+j+" sh"),E("Q");else if(R instanceof Xr){var X=new zt(1,0,0,-1,0,mr());s.matrix&&(X=X.multiply(s.matrix||Vn),j=Bo.call(R,s.key,s.boundingBox,s.xStep,s.yStep,X).id),E("q"),E("/Pattern cs"),E("/"+j+" scn"),R.gState&&p.setGState(R.gState),E(v),E("Q")}},Eo=function(s){switch(s){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Ri=p.moveTo=function(s,v){return E(tt(k(s))+" "+tt(D(v))+" m"),this},Dr=p.lineTo=function(s,v){return E(tt(k(s))+" "+tt(D(v))+" l"),this},pr=p.curveTo=function(s,v,j,R,X,ct){return E([tt(k(s)),tt(D(v)),tt(k(j)),tt(D(R)),tt(k(X)),tt(D(ct)),"c"].join(" ")),this};p.__private__.line=p.line=function(s,v,j,R,X){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||!Yn(X))throw new Error("Invalid arguments passed to jsPDF.line");return Y===B.COMPAT?this.lines([[j-s,R-v]],s,v,[1,1],X||"S"):this.lines([[j-s,R-v]],s,v,[1,1]).stroke()},p.__private__.lines=p.lines=function(s,v,j,R,X,ct){var yt,Ot,Dt,Zt,Yt,re,Ne,Se,Me,Xe,ze,Bn;if(typeof s=="number"&&(Bn=j,j=v,v=s,s=Bn),R=R||[1,1],ct=ct||!1,isNaN(v)||isNaN(j)||!Array.isArray(s)||!Array.isArray(R)||!Yn(X)||typeof ct!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Ri(v,j),yt=R[0],Ot=R[1],Zt=s.length,Xe=v,ze=j,Dt=0;Dt<Zt;Dt++)(Yt=s[Dt]).length===2?(Xe=Yt[0]*yt+Xe,ze=Yt[1]*Ot+ze,Dr(Xe,ze)):(re=Yt[0]*yt+Xe,Ne=Yt[1]*Ot+ze,Se=Yt[2]*yt+Xe,Me=Yt[3]*Ot+ze,Xe=Yt[4]*yt+Xe,ze=Yt[5]*Ot+ze,pr(re,Ne,Se,Me,Xe,ze));return ct&&Ca(),Di(X),this},p.path=function(s){for(var v=0;v<s.length;v++){var j=s[v],R=j.c;switch(j.op){case"m":Ri(R[0],R[1]);break;case"l":Dr(R[0],R[1]);break;case"c":pr.apply(this,R);break;case"h":Ca()}}return this},p.__private__.rect=p.rect=function(s,v,j,R,X){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||!Yn(X))throw new Error("Invalid arguments passed to jsPDF.rect");return Y===B.COMPAT&&(R=-R),E([tt(k(s)),tt(D(v)),tt(k(j)),tt(k(R)),"re"].join(" ")),Di(X),this},p.__private__.triangle=p.triangle=function(s,v,j,R,X,ct,yt){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||isNaN(X)||isNaN(ct)||!Yn(yt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[j-s,R-v],[X-j,ct-R],[s-X,v-ct]],s,v,[1,1],yt,!0),this},p.__private__.roundedRect=p.roundedRect=function(s,v,j,R,X,ct,yt){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||isNaN(X)||isNaN(ct)||!Yn(yt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Ot=4/3*(Math.SQRT2-1);return X=Math.min(X,.5*j),ct=Math.min(ct,.5*R),this.lines([[j-2*X,0],[X*Ot,0,X,ct-ct*Ot,X,ct],[0,R-2*ct],[0,ct*Ot,-X*Ot,ct,-X,ct],[2*X-j,0],[-X*Ot,0,-X,-ct*Ot,-X,-ct],[0,2*ct-R],[0,-ct*Ot,X*Ot,-ct,X,-ct]],s+X,v,[1,1],yt,!0),this},p.__private__.ellipse=p.ellipse=function(s,v,j,R,X){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||!Yn(X))throw new Error("Invalid arguments passed to jsPDF.ellipse");var ct=4/3*(Math.SQRT2-1)*j,yt=4/3*(Math.SQRT2-1)*R;return Ri(s+j,v),pr(s+j,v-yt,s+ct,v-R,s,v-R),pr(s-ct,v-R,s-j,v-yt,s-j,v),pr(s-j,v+yt,s-ct,v+R,s,v+R),pr(s+ct,v+R,s+j,v+yt,s+j,v),Di(X),this},p.__private__.circle=p.circle=function(s,v,j,R){if(isNaN(s)||isNaN(v)||isNaN(j)||!Yn(R))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(s,v,j,j,R)},p.setFont=function(s,v,j){return j&&(v=wt(v,j)),ee=Pa(s,v,{disableWarning:!1}),this};var qo=p.__private__.getFont=p.getFont=function(){return pe[Pa.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var s,v,j={};for(s in Le)if(Le.hasOwnProperty(s))for(v in j[s]=[],Le[s])Le[s].hasOwnProperty(v)&&j[s].push(v);return j},p.addFont=function(s,v,j,R,X){var ct=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&ct.indexOf(arguments[3])!==-1?X=arguments[3]:arguments[3]&&ct.indexOf(arguments[3])==-1&&(j=wt(j,R)),X=X||"Identity-H",Mi.call(this,s,v,j,X)};var Rr,Ti=r.lineWidth||.200025,fi=p.__private__.getLineWidth=p.getLineWidth=function(){return Ti},ja=p.__private__.setLineWidth=p.setLineWidth=function(s){return Ti=s,E(tt(k(s))+" w"),this};p.__private__.setLineDash=Tt.API.setLineDash=Tt.API.setLineDashPattern=function(s,v){if(s=s||[],v=v||0,isNaN(v)||!Array.isArray(s))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return s=s.map(function(j){return tt(k(j))}).join(" "),v=tt(k(v)),E("["+s+"] "+v+" d"),this};var Oa=p.__private__.getLineHeight=p.getLineHeight=function(){return xt*Rr};p.__private__.getLineHeight=p.getLineHeight=function(){return xt*Rr};var Ba=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(s){return typeof(s=s||1.15)=="number"&&(Rr=s),this},Ma=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return Rr};Ba(r.lineHeight);var Xn=p.__private__.getHorizontalCoordinate=function(s){return k(s)},ar=p.__private__.getVerticalCoordinate=function(s){return Y===B.ADVANCED?s:Jt[C].mediaBox.topRightY-Jt[C].mediaBox.bottomLeftY-k(s)},Do=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(s){return tt(Xn(s))},gr=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(s){return tt(ar(s))},On=r.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return In(On)},p.__private__.setStrokeColor=p.setDrawColor=function(s,v,j,R){return On=Cn({ch1:s,ch2:v,ch3:j,ch4:R,pdfColorType:"draw",precision:2}),E(On),this};var zi=r.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return In(zi)},p.__private__.setFillColor=p.setFillColor=function(s,v,j,R){return zi=Cn({ch1:s,ch2:v,ch3:j,ch4:R,pdfColorType:"fill",precision:2}),E(zi),this};var Tr=r.textColor||"0 g",Ro=p.__private__.getTextColor=p.getTextColor=function(){return In(Tr)};p.__private__.setTextColor=p.setTextColor=function(s,v,j,R){return Tr=Cn({ch1:s,ch2:v,ch3:j,ch4:R,pdfColorType:"text",precision:3}),this};var di=r.charSpace,To=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(di||0)};p.__private__.setCharSpace=p.setCharSpace=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return di=s,this};var Ui=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(s){var v=p.CapJoinStyles[s];if(v===void 0)throw new Error("Line cap style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Ui=v,E(v+" J"),this};var Hi=0;p.__private__.setLineJoin=p.setLineJoin=function(s){var v=p.CapJoinStyles[s];if(v===void 0)throw new Error("Line join style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Hi=v,E(v+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(s){if(s=s||0,isNaN(s))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return E(tt(k(s))+" M"),this},p.GState=ba,p.setGState=function(s){(s=typeof s=="string"?Ae[Pn[s]]:Ea(null,s)).equals(nr)||(E("/"+s.id+" gs"),nr=s)};var Ea=function(s,v){if(!s||!Pn[s]){var j=!1;for(var R in Ae)if(Ae.hasOwnProperty(R)&&Ae[R].equals(v)){j=!0;break}if(j)v=Ae[R];else{var X="GS"+(Object.keys(Ae).length+1).toString(10);Ae[X]=v,v.id=X}return s&&(Pn[s]=v.id),ue.publish("addGState",v),v}};p.addGState=function(s,v){return Ea(s,v),this},p.saveGraphicsState=function(){return E("q"),Un.push({key:ee,size:xt,color:Tr}),this},p.restoreGraphicsState=function(){E("Q");var s=Un.pop();return ee=s.key,xt=s.size,Tr=s.color,nr=null,this},p.setCurrentTransformationMatrix=function(s){return E(s.toString()+" cm"),this},p.comment=function(s){return E("#"+s),this};var pi=function(s,v){var j=s||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return j},set:function(ct){isNaN(ct)||(j=parseFloat(ct))}});var R=v||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return R},set:function(ct){isNaN(ct)||(R=parseFloat(ct))}});var X="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return X},set:function(ct){X=ct.toString()}}),this},Wi=function(s,v,j,R){pi.call(this,s,v),this.type="rect";var X=j||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return X},set:function(yt){isNaN(yt)||(X=parseFloat(yt))}});var ct=R||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return ct},set:function(yt){isNaN(yt)||(ct=parseFloat(yt))}}),this},Vi=function(){this.page=xe,this.currentPage=C,this.pages=Nt.slice(0),this.pagesContext=Jt.slice(0),this.x=Je,this.y=oe,this.matrix=_n,this.width=zr(C),this.height=mr(C),this.outputDestination=_t,this.id="",this.objectNumber=-1};Vi.prototype.restore=function(){xe=this.page,C=this.currentPage,Jt=this.pagesContext,Nt=this.pages,Je=this.x,oe=this.y,_n=this.matrix,Gi(C,this.width),Ji(C,this.height),_t=this.outputDestination};var qa=function(s,v,j,R,X){Wn.push(new Vi),xe=C=0,Nt=[],Je=s,oe=v,_n=X,Ei([j,R])},zo=function(s){if(Hn[s])Wn.pop().restore();else{var v=new Vi,j="Xo"+(Object.keys(We).length+1).toString(10);v.id=j,Hn[s]=j,We[j]=v,ue.publish("addFormObject",v),Wn.pop().restore()}};for(var gi in p.beginFormObject=function(s,v,j,R,X){return qa(s,v,j,R,X),this},p.endFormObject=function(s){return zo(s),this},p.doFormObject=function(s,v){var j=We[Hn[s]];return E("q"),E(v.toString()+" cm"),E("/"+j.id+" Do"),E("Q"),this},p.getFormObject=function(s){var v=We[Hn[s]];return{x:v.x,y:v.y,width:v.width,height:v.height,matrix:v.matrix}},p.save=function(s,v){return s=s||"generated.pdf",(v=v||{}).returnPromise=v.returnPromise||!1,v.returnPromise===!1?(Yr(ui(ir()),s),typeof Yr.unload=="function"&&Ht.setTimeout&&setTimeout(Yr.unload,911),this):new Promise(function(j,R){try{var X=Yr(ui(ir()),s);typeof Yr.unload=="function"&&Ht.setTimeout&&setTimeout(Yr.unload,911),j(X)}catch(ct){R(ct.message)}})},Tt.API)Tt.API.hasOwnProperty(gi)&&(gi==="events"&&Tt.API.events.length?function(s,v){var j,R,X;for(X=v.length-1;X!==-1;X--)j=v[X][0],R=v[X][1],s.subscribe.apply(s,[j].concat(typeof R=="function"?[R]:R))}(ue,Tt.API.events):p[gi]=Tt.API[gi]);var zr=p.getPageWidth=function(s){return(Jt[s=s||C].mediaBox.topRightX-Jt[s].mediaBox.bottomLeftX)/jt},Gi=p.setPageWidth=function(s,v){Jt[s].mediaBox.topRightX=v*jt+Jt[s].mediaBox.bottomLeftX},mr=p.getPageHeight=function(s){return(Jt[s=s||C].mediaBox.topRightY-Jt[s].mediaBox.bottomLeftY)/jt},Ji=p.setPageHeight=function(s,v){Jt[s].mediaBox.topRightY=v*jt+Jt[s].mediaBox.bottomLeftY};return p.internal={pdfEscape:on,getStyle:Ia,getFont:qo,getFontSize:kt,getCharSpace:To,getTextColor:Ro,getLineHeight:Oa,getLineHeightFactor:Ma,getLineWidth:fi,write:Kt,getHorizontalCoordinate:Xn,getVerticalCoordinate:ar,getCoordinateString:Do,getVerticalCoordinateString:gr,collections:{},newObject:De,newAdditionalObject:ei,newObjectDeferred:Be,newObjectDeferredBegin:fn,getFilters:Gn,putStream:bn,events:ue,scaleFactor:jt,pageSize:{getWidth:function(){return zr(C)},setWidth:function(s){Gi(C,s)},getHeight:function(){return mr(C)},setHeight:function(s){Ji(C,s)}},encryptionOptions:b,encryption:Ye,getEncryptor:Co,output:li,getNumberOfPages:_o,pages:Nt,out:E,f2:dt,f3:P,getPageInfo:Fa,getPageInfoByObjId:Vt,getCurrentPageInfo:jo,getPDFVersion:F,Point:pi,Rectangle:Wi,Matrix:zt,hasHotfix:ka},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return zr(C)},set:function(s){Gi(C,s)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return mr(C)},set:function(s){Ji(C,s)},enumerable:!0,configurable:!0}),xo.call(p,Lt),ee="F1",Sa(c,n),ue.publish("initialized"),p}ji.prototype.lsbFirstWord=function(r){return String.fromCharCode(r>>0&255,r>>8&255,r>>16&255,r>>24&255)},ji.prototype.toHexString=function(r){return r.split("").map(function(e){return("0"+(255&e.charCodeAt(0)).toString(16)).slice(-2)}).join("")},ji.prototype.hexToBytes=function(r){for(var e=[],n=0;n<r.length;n+=2)e.push(String.fromCharCode(parseInt(r.substr(n,2),16)));return e.join("")},ji.prototype.processOwnerPassword=function(r,e){return As(Ns(e).substr(0,5),r)},ji.prototype.encryptor=function(r,e){var n=Ns(this.encryptionKey+String.fromCharCode(255&r,r>>8&255,r>>16&255,255&e,e>>8&255)).substr(0,10);return function(a){return As(n,a)}},ba.prototype.equals=function(r){var e,n="id,objectNumber,equals";if(!r||ve(r)!==ve(this))return!1;var a=0;for(e in this)if(!(n.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!r.hasOwnProperty(e)||this[e]!==r[e])return!1;a++}for(e in r)r.hasOwnProperty(e)&&n.indexOf(e)<0&&a--;return a===0},Tt.API={events:[]},Tt.version="2.5.2";var Pe=Tt.API,Fs=1,ti=function(r){return r.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ii=function(r){return r.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Xt=function(r){return r.toFixed(2)},Fr=function(r){return r.toFixed(5)};Pe.__acroform__={};var hn=function(r,e){r.prototype=Object.create(e.prototype),r.prototype.constructor=r},fc=function(r){return r*Fs},$n=function(r){var e=new Dc,n=Mt.internal.getHeight(r)||0,a=Mt.internal.getWidth(r)||0;return e.BBox=[0,0,Number(Xt(a)),Number(Xt(n))],e},Ku=Pe.__acroform__.setBit=function(r,e){if(r=r||0,e=e||0,isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return r|=1<<e},Zu=Pe.__acroform__.clearBit=function(r,e){if(r=r||0,e=e||0,isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return r&=~(1<<e)},$u=Pe.__acroform__.getBit=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return r&1<<e?1:0},Ce=Pe.__acroform__.getBitForPdf=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return $u(r,e-1)},je=Pe.__acroform__.setBitForPdf=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return Ku(r,e-1)},Oe=Pe.__acroform__.clearBitForPdf=function(r,e){if(isNaN(r)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return Zu(r,e-1)},Qu=Pe.__acroform__.calculateCoordinates=function(r,e){var n=e.internal.getHorizontalCoordinate,a=e.internal.getVerticalCoordinate,c=r[0],o=r[1],l=r[2],h=r[3],f={};return f.lowerLeft_X=n(c)||0,f.lowerLeft_Y=a(o+h)||0,f.upperRight_X=n(c+l)||0,f.upperRight_Y=a(o)||0,[Number(Xt(f.lowerLeft_X)),Number(Xt(f.lowerLeft_Y)),Number(Xt(f.upperRight_X)),Number(Xt(f.upperRight_Y))]},tl=function(r){if(r.appearanceStreamContent)return r.appearanceStreamContent;if(r.V||r.DV){var e=[],n=r._V||r.DV,a=xs(r,n),c=r.scope.internal.getFont(r.fontName,r.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(r.scope.__private__.encodeColorString(r.color)),e.push("/"+c+" "+Xt(a.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC");var o=$n(r);return o.scope=r.scope,o.stream=e.join(`
`),o}},xs=function(r,e){var n=r.fontSize===0?r.maxFontSize:r.fontSize,a={text:"",fontSize:""},c=(e=(e=e.substr(0,1)=="("?e.substr(1):e).substr(e.length-1)==")"?e.substr(0,e.length-1):e).split(" ");c=r.multiline?c.map(function(P){return P.split(`
`)}):c.map(function(P){return[P]});var o=n,l=Mt.internal.getHeight(r)||0;l=l<0?-l:l;var h=Mt.internal.getWidth(r)||0;h=h<0?-h:h;var f=function(P,k,W){if(P+1<c.length){var D=k+" "+c[P+1][0];return lo(D,r,W).width<=h-4}return!1};o++;t:for(;o>0;){e="",o--;var g,b,y=lo("3",r,o).height,S=r.multiline?l-o:(l-y)/2,p=S+=2,O=0,F=0,q=0;if(o<=0){e=`(...) Tj
`,e+="% Width of Text: "+lo(e,r,o=12).width+", FieldWidth:"+h+`
`;break}for(var _="",B=0,Y=0;Y<c.length;Y++)if(c.hasOwnProperty(Y)){var ot=!1;if(c[Y].length!==1&&q!==c[Y].length-1){if((y+2)*(B+2)+2>l)continue t;_+=c[Y][q],ot=!0,F=Y,Y--}else{_=(_+=c[Y][q]+" ").substr(_.length-1)==" "?_.substr(0,_.length-1):_;var ut=parseInt(Y),wt=f(ut,_,o),tt=Y>=c.length-1;if(wt&&!tt){_+=" ",q=0;continue}if(wt||tt){if(tt)F=ut;else if(r.multiline&&(y+2)*(B+2)+2>l)continue t}else{if(!r.multiline||(y+2)*(B+2)+2>l)continue t;F=ut}}for(var z="",rt=O;rt<=F;rt++){var dt=c[rt];if(r.multiline){if(rt===F){z+=dt[q]+" ",q=(q+1)%dt.length;continue}if(rt===O){z+=dt[dt.length-1]+" ";continue}}z+=dt[0]+" "}switch(z=z.substr(z.length-1)==" "?z.substr(0,z.length-1):z,b=lo(z,r,o).width,r.textAlign){case"right":g=h-b-2;break;case"center":g=(h-b)/2;break;case"left":default:g=2}e+=Xt(g)+" "+Xt(p)+` Td
`,e+="("+ti(z)+`) Tj
`,e+=-Xt(g)+` 0 Td
`,p=-(o+2),b=0,O=ot?F:F+1,B++,_=""}break}return a.text=e,a.fontSize=o,a},lo=function(r,e,n){var a=e.scope.internal.getFont(e.fontName,e.fontStyle),c=e.scope.getStringUnitWidth(r,{font:a,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:e.scope.getStringUnitWidth("3",{font:a,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:c}},el={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},nl=function(r,e){var n={type:"reference",object:r};e.internal.getPageInfo(r.page).pageContext.annotations.find(function(a){return a.type===n.type&&a.object===n.object})===void 0&&e.internal.getPageInfo(r.page).pageContext.annotations.push(n)},rl=function(r,e){for(var n in r)if(r.hasOwnProperty(n)){var a=n,c=r[n];e.internal.newObjectDeferredBegin(c.objId,!0),ve(c)==="object"&&typeof c.putStream=="function"&&c.putStream(),delete r[a]}},il=function(r,e){if(e.scope=r,r.internal!==void 0&&(r.internal.acroformPlugin===void 0||r.internal.acroformPlugin.isInitialized===!1)){if(Tn.FieldNum=0,r.internal.acroformPlugin=JSON.parse(JSON.stringify(el)),r.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");Fs=r.internal.scaleFactor,r.internal.acroformPlugin.acroFormDictionaryRoot=new Rc,r.internal.acroformPlugin.acroFormDictionaryRoot.scope=r,r.internal.acroformPlugin.acroFormDictionaryRoot._eventID=r.internal.events.subscribe("postPutResources",function(){(function(n){n.internal.events.unsubscribe(n.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete n.internal.acroformPlugin.acroFormDictionaryRoot._eventID,n.internal.acroformPlugin.printedOut=!0})(r)}),r.internal.events.subscribe("buildDocument",function(){(function(n){n.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var a=n.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var c in a)if(a.hasOwnProperty(c)){var o=a[c];o.objId=void 0,o.hasAnnotation&&nl(o,n)}})(r)}),r.internal.events.subscribe("putCatalog",function(){(function(n){if(n.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");n.internal.write("/AcroForm "+n.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(r)}),r.internal.events.subscribe("postPutPages",function(n){(function(a,c){var o=!a;for(var l in a||(c.internal.newObjectDeferredBegin(c.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),c.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),a=a||c.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(a.hasOwnProperty(l)){var h=a[l],f=[],g=h.Rect;if(h.Rect&&(h.Rect=Qu(h.Rect,c)),c.internal.newObjectDeferredBegin(h.objId,!0),h.DA=Mt.createDefaultAppearanceStream(h),ve(h)==="object"&&typeof h.getKeyValueListForStream=="function"&&(f=h.getKeyValueListForStream()),h.Rect=g,h.hasAppearanceStream&&!h.appearanceStreamContent){var b=tl(h);f.push({key:"AP",value:"<</N "+b+">>"}),c.internal.acroformPlugin.xForms.push(b)}if(h.appearanceStreamContent){var y="";for(var S in h.appearanceStreamContent)if(h.appearanceStreamContent.hasOwnProperty(S)){var p=h.appearanceStreamContent[S];if(y+="/"+S+" ",y+="<<",Object.keys(p).length>=1||Array.isArray(p)){for(var l in p)if(p.hasOwnProperty(l)){var O=p[l];typeof O=="function"&&(O=O.call(c,h)),y+="/"+l+" "+O+" ",c.internal.acroformPlugin.xForms.indexOf(O)>=0||c.internal.acroformPlugin.xForms.push(O)}}else typeof(O=p)=="function"&&(O=O.call(c,h)),y+="/"+l+" "+O,c.internal.acroformPlugin.xForms.indexOf(O)>=0||c.internal.acroformPlugin.xForms.push(O);y+=">>"}f.push({key:"AP",value:`<<
`+y+">>"})}c.internal.putStream({additionalKeyValues:f,objectId:h.objId}),c.internal.out("endobj")}o&&rl(c.internal.acroformPlugin.xForms,c)})(n,r)}),r.internal.acroformPlugin.isInitialized=!0}},qc=Pe.__acroform__.arrayToPdfArray=function(r,e,n){var a=function(l){return l};if(Array.isArray(r)){for(var c="[",o=0;o<r.length;o++)switch(o!==0&&(c+=" "),ve(r[o])){case"boolean":case"number":case"object":c+=r[o].toString();break;case"string":r[o].substr(0,1)!=="/"?(e!==void 0&&n&&(a=n.internal.getEncryptor(e)),c+="("+ti(a(r[o].toString()))+")"):c+=r[o].toString()}return c+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},fs=function(r,e,n){var a=function(c){return c};return e!==void 0&&n&&(a=n.internal.getEncryptor(e)),(r=r||"").toString(),r="("+ti(a(r))+")"},Qn=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(r){this._objId=r}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};Qn.prototype.toString=function(){return this.objId+" 0 R"},Qn.prototype.putStream=function(){var r=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:r,objectId:this.objId}),this.scope.internal.out("endobj")},Qn.prototype.getKeyValueListForStream=function(){var r=[],e=Object.getOwnPropertyNames(this).filter(function(o){return o!="content"&&o!="appearanceStreamContent"&&o!="scope"&&o!="objId"&&o.substring(0,1)!="_"});for(var n in e)if(Object.getOwnPropertyDescriptor(this,e[n]).configurable===!1){var a=e[n],c=this[a];c&&(Array.isArray(c)?r.push({key:a,value:qc(c,this.objId,this.scope)}):c instanceof Qn?(c.scope=this.scope,r.push({key:a,value:c.objId+" 0 R"})):typeof c!="function"&&r.push({key:a,value:c}))}return r};var Dc=function(){Qn.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var r,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(n){e=n}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(n){r=n.trim()},get:function(){return r||null}})};hn(Dc,Qn);var Rc=function(){Qn.call(this);var r,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(r){var n=function(a){return a};return this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),"("+ti(n(r))+")"}},set:function(n){r=n}})};hn(Rc,Qn);var Tn=function r(){Qn.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute F supplied.');e=_}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!Ce(e,3)},set:function(_){_?this.F=je(e,3):this.F=Oe(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute Ff supplied.');n=_}});var a=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(a.length!==0)return a},set:function(_){a=_!==void 0?_:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[0])?0:a[0]},set:function(_){a[0]=_}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[1])?0:a[1]},set:function(_){a[1]=_}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[2])?0:a[2]},set:function(_){a[2]=_}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[3])?0:a[3]},set:function(_){a[3]=_}});var c="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return c},set:function(_){switch(_){case"/Btn":case"/Tx":case"/Ch":case"/Sig":c=_;break;default:throw new Error('Invalid value "'+_+'" for attribute FT supplied.')}}});var o=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!o||o.length<1){if(this instanceof po)return;o="FieldObject"+r.FieldNum++}var _=function(B){return B};return this.scope&&(_=this.scope.internal.getEncryptor(this.objId)),"("+ti(_(o))+")"},set:function(_){o=_.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return o},set:function(_){o=_}});var l="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return l},set:function(_){l=_}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(_){h=_}});var f=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return f},set:function(_){f=_}});var g=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return g===void 0?50/Fs:g},set:function(_){g=_}});var b="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return b},set:function(_){b=_}});var y="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!y||this instanceof po||this instanceof jr))return fs(y,this.objId,this.scope)},set:function(_){_=_.toString(),y=_}});var S=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(S)return this instanceof Te?S:fs(S,this.objId,this.scope)},set:function(_){_=_.toString(),S=this instanceof Te?_:_.substr(0,1)==="("?Ii(_.substr(1,_.length-2)):Ii(_)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Te?Ii(S.substr(1,S.length-1)):S},set:function(_){_=_.toString(),S=this instanceof Te?"/"+_:_}});var p=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(p)return p},set:function(_){this.V=_}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(p)return this instanceof Te?p:fs(p,this.objId,this.scope)},set:function(_){_=_.toString(),p=this instanceof Te?_:_.substr(0,1)==="("?Ii(_.substr(1,_.length-2)):Ii(_)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Te?Ii(p.substr(1,p.length-1)):p},set:function(_){_=_.toString(),p=this instanceof Te?"/"+_:_}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var O,F=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return F},set:function(_){_=!!_,F=_}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(O)return O},set:function(_){O=_}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,1)},set:function(_){_?this.Ff=je(this.Ff,1):this.Ff=Oe(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,2)},set:function(_){_?this.Ff=je(this.Ff,2):this.Ff=Oe(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,3)},set:function(_){_?this.Ff=je(this.Ff,3):this.Ff=Oe(this.Ff,3)}});var q=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(q!==null)return q},set:function(_){if([0,1,2].indexOf(_)===-1)throw new Error('Invalid value "'+_+'" for attribute Q supplied.');q=_}}),Object.defineProperty(this,"textAlign",{get:function(){var _;switch(q){case 0:default:_="left";break;case 1:_="center";break;case 2:_="right"}return _},configurable:!0,enumerable:!0,set:function(_){switch(_){case"right":case 2:q=2;break;case"center":case 1:q=1;break;case"left":case 0:default:q=0}}})};hn(Tn,Qn);var Kr=function(){Tn.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var r=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return r},set:function(n){r=n}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return r},set:function(n){r=n}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return qc(e,this.objId,this.scope)},set:function(n){var a,c;c=[],typeof(a=n)=="string"&&(c=function(o,l,h){h||(h=1);for(var f,g=[];f=l.exec(o);)g.push(f[h]);return g}(a,/\((.*?)\)/g)),e=c}}),this.getOptions=function(){return e},this.setOptions=function(n){e=n,this.sort&&e.sort()},this.addOption=function(n){n=(n=n||"").toString(),e.push(n),this.sort&&e.sort()},this.removeOption=function(n,a){for(a=a||!1,n=(n=n||"").toString();e.indexOf(n)!==-1&&(e.splice(e.indexOf(n),1),a!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,18)},set:function(n){n?this.Ff=je(this.Ff,18):this.Ff=Oe(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,19)},set:function(n){this.combo===!0&&(n?this.Ff=je(this.Ff,19):this.Ff=Oe(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,20)},set:function(n){n?(this.Ff=je(this.Ff,20),e.sort()):this.Ff=Oe(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,22)},set:function(n){n?this.Ff=je(this.Ff,22):this.Ff=Oe(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,23)},set:function(n){n?this.Ff=je(this.Ff,23):this.Ff=Oe(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,27)},set:function(n){n?this.Ff=je(this.Ff,27):this.Ff=Oe(this.Ff,27)}}),this.hasAppearanceStream=!1};hn(Kr,Tn);var Zr=function(){Kr.call(this),this.fontName="helvetica",this.combo=!1};hn(Zr,Kr);var $r=function(){Zr.call(this),this.combo=!0};hn($r,Zr);var da=function(){$r.call(this),this.edit=!0};hn(da,$r);var Te=function(){Tn.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,15)},set:function(n){n?this.Ff=je(this.Ff,15):this.Ff=Oe(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,16)},set:function(n){n?this.Ff=je(this.Ff,16):this.Ff=Oe(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,17)},set:function(n){n?this.Ff=je(this.Ff,17):this.Ff=Oe(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,26)},set:function(n){n?this.Ff=je(this.Ff,26):this.Ff=Oe(this.Ff,26)}});var r,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var n=function(o){return o};if(this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),Object.keys(e).length!==0){var a,c=[];for(a in c.push("<<"),e)c.push("/"+a+" ("+ti(n(e[a]))+")");return c.push(">>"),c.join(`
`)}},set:function(n){ve(n)==="object"&&(e=n)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(n){typeof n=="string"&&(e.CA=n)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(n){r=n}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(n){r="/"+n}})};hn(Te,Tn);var pa=function(){Te.call(this),this.pushButton=!0};hn(pa,Te);var Qr=function(){Te.call(this),this.radio=!0,this.pushButton=!1;var r=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return r},set:function(e){r=e!==void 0?e:[]}})};hn(Qr,Te);var po=function(){var r,e;Tn.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return r},set:function(c){r=c}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(c){e=c}});var n,a={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var c=function(h){return h};this.scope&&(c=this.scope.internal.getEncryptor(this.objId));var o,l=[];for(o in l.push("<<"),a)l.push("/"+o+" ("+ti(c(a[o]))+")");return l.push(">>"),l.join(`
`)},set:function(c){ve(c)==="object"&&(a=c)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return a.CA||""},set:function(c){typeof c=="string"&&(a.CA=c)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(c){n=c}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(c){n="/"+c}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Mt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};hn(po,Tn),Qr.prototype.setAppearance=function(r){if(!("createAppearanceStream"in r)||!("getCA"in r))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=r.createAppearanceStream(n.optionName),n.caption=r.getCA()}},Qr.prototype.createOption=function(r){var e=new po;return e.Parent=this,e.optionName=r,this.Kids.push(e),al.call(this.scope,e),e};var ga=function(){Te.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Mt.CheckBox.createAppearanceStream()};hn(ga,Te);var jr=function(){Tn.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,13)},set:function(e){e?this.Ff=je(this.Ff,13):this.Ff=Oe(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,21)},set:function(e){e?this.Ff=je(this.Ff,21):this.Ff=Oe(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,23)},set:function(e){e?this.Ff=je(this.Ff,23):this.Ff=Oe(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,24)},set:function(e){e?this.Ff=je(this.Ff,24):this.Ff=Oe(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,25)},set:function(e){e?this.Ff=je(this.Ff,25):this.Ff=Oe(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,26)},set:function(e){e?this.Ff=je(this.Ff,26):this.Ff=Oe(this.Ff,26)}});var r=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return r},set:function(e){r=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return r},set:function(e){Number.isInteger(e)&&(r=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};hn(jr,Tn);var ma=function(){jr.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,14)},set:function(r){r?this.Ff=je(this.Ff,14):this.Ff=Oe(this.Ff,14)}}),this.password=!0};hn(ma,jr);var Mt={CheckBox:{createAppearanceStream:function(){return{N:{On:Mt.CheckBox.YesNormal},D:{On:Mt.CheckBox.YesPushDown,Off:Mt.CheckBox.OffPushDown}}},YesPushDown:function(r){var e=$n(r);e.scope=r.scope;var n=[],a=r.scope.internal.getFont(r.fontName,r.fontStyle).id,c=r.scope.__private__.encodeColorString(r.color),o=xs(r,r.caption);return n.push("0.749023 g"),n.push("0 0 "+Xt(Mt.internal.getWidth(r))+" "+Xt(Mt.internal.getHeight(r))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+a+" "+Xt(o.fontSize)+" Tf "+c),n.push("BT"),n.push(o.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join(`
`),e},YesNormal:function(r){var e=$n(r);e.scope=r.scope;var n=r.scope.internal.getFont(r.fontName,r.fontStyle).id,a=r.scope.__private__.encodeColorString(r.color),c=[],o=Mt.internal.getHeight(r),l=Mt.internal.getWidth(r),h=xs(r,r.caption);return c.push("1 g"),c.push("0 0 "+Xt(l)+" "+Xt(o)+" re"),c.push("f"),c.push("q"),c.push("0 0 1 rg"),c.push("0 0 "+Xt(l-1)+" "+Xt(o-1)+" re"),c.push("W"),c.push("n"),c.push("0 g"),c.push("BT"),c.push("/"+n+" "+Xt(h.fontSize)+" Tf "+a),c.push(h.text),c.push("ET"),c.push("Q"),e.stream=c.join(`
`),e},OffPushDown:function(r){var e=$n(r);e.scope=r.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+Xt(Mt.internal.getWidth(r))+" "+Xt(Mt.internal.getHeight(r))+" re"),n.push("f"),e.stream=n.join(`
`),e}},RadioButton:{Circle:{createAppearanceStream:function(r){var e={D:{Off:Mt.RadioButton.Circle.OffPushDown},N:{}};return e.N[r]=Mt.RadioButton.Circle.YesNormal,e.D[r]=Mt.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(r){var e=$n(r);e.scope=r.scope;var n=[],a=Mt.internal.getWidth(r)<=Mt.internal.getHeight(r)?Mt.internal.getWidth(r)/4:Mt.internal.getHeight(r)/4;a=Number((.9*a).toFixed(5));var c=Mt.internal.Bezier_C,o=Number((a*c).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+Fr(Mt.internal.getWidth(r)/2)+" "+Fr(Mt.internal.getHeight(r)/2)+" cm"),n.push(a+" 0 m"),n.push(a+" "+o+" "+o+" "+a+" 0 "+a+" c"),n.push("-"+o+" "+a+" -"+a+" "+o+" -"+a+" 0 c"),n.push("-"+a+" -"+o+" -"+o+" -"+a+" 0 -"+a+" c"),n.push(o+" -"+a+" "+a+" -"+o+" "+a+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(r){var e=$n(r);e.scope=r.scope;var n=[],a=Mt.internal.getWidth(r)<=Mt.internal.getHeight(r)?Mt.internal.getWidth(r)/4:Mt.internal.getHeight(r)/4;a=Number((.9*a).toFixed(5));var c=Number((2*a).toFixed(5)),o=Number((c*Mt.internal.Bezier_C).toFixed(5)),l=Number((a*Mt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+Fr(Mt.internal.getWidth(r)/2)+" "+Fr(Mt.internal.getHeight(r)/2)+" cm"),n.push(c+" 0 m"),n.push(c+" "+o+" "+o+" "+c+" 0 "+c+" c"),n.push("-"+o+" "+c+" -"+c+" "+o+" -"+c+" 0 c"),n.push("-"+c+" -"+o+" -"+o+" -"+c+" 0 -"+c+" c"),n.push(o+" -"+c+" "+c+" -"+o+" "+c+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+Fr(Mt.internal.getWidth(r)/2)+" "+Fr(Mt.internal.getHeight(r)/2)+" cm"),n.push(a+" 0 m"),n.push(a+" "+l+" "+l+" "+a+" 0 "+a+" c"),n.push("-"+l+" "+a+" -"+a+" "+l+" -"+a+" 0 c"),n.push("-"+a+" -"+l+" -"+l+" -"+a+" 0 -"+a+" c"),n.push(l+" -"+a+" "+a+" -"+l+" "+a+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},OffPushDown:function(r){var e=$n(r);e.scope=r.scope;var n=[],a=Mt.internal.getWidth(r)<=Mt.internal.getHeight(r)?Mt.internal.getWidth(r)/4:Mt.internal.getHeight(r)/4;a=Number((.9*a).toFixed(5));var c=Number((2*a).toFixed(5)),o=Number((c*Mt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+Fr(Mt.internal.getWidth(r)/2)+" "+Fr(Mt.internal.getHeight(r)/2)+" cm"),n.push(c+" 0 m"),n.push(c+" "+o+" "+o+" "+c+" 0 "+c+" c"),n.push("-"+o+" "+c+" -"+c+" "+o+" -"+c+" 0 c"),n.push("-"+c+" -"+o+" -"+o+" -"+c+" 0 -"+c+" c"),n.push(o+" -"+c+" "+c+" -"+o+" "+c+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e}},Cross:{createAppearanceStream:function(r){var e={D:{Off:Mt.RadioButton.Cross.OffPushDown},N:{}};return e.N[r]=Mt.RadioButton.Cross.YesNormal,e.D[r]=Mt.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(r){var e=$n(r);e.scope=r.scope;var n=[],a=Mt.internal.calculateCross(r);return n.push("q"),n.push("1 1 "+Xt(Mt.internal.getWidth(r)-2)+" "+Xt(Mt.internal.getHeight(r)-2)+" re"),n.push("W"),n.push("n"),n.push(Xt(a.x1.x)+" "+Xt(a.x1.y)+" m"),n.push(Xt(a.x2.x)+" "+Xt(a.x2.y)+" l"),n.push(Xt(a.x4.x)+" "+Xt(a.x4.y)+" m"),n.push(Xt(a.x3.x)+" "+Xt(a.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(r){var e=$n(r);e.scope=r.scope;var n=Mt.internal.calculateCross(r),a=[];return a.push("0.749023 g"),a.push("0 0 "+Xt(Mt.internal.getWidth(r))+" "+Xt(Mt.internal.getHeight(r))+" re"),a.push("f"),a.push("q"),a.push("1 1 "+Xt(Mt.internal.getWidth(r)-2)+" "+Xt(Mt.internal.getHeight(r)-2)+" re"),a.push("W"),a.push("n"),a.push(Xt(n.x1.x)+" "+Xt(n.x1.y)+" m"),a.push(Xt(n.x2.x)+" "+Xt(n.x2.y)+" l"),a.push(Xt(n.x4.x)+" "+Xt(n.x4.y)+" m"),a.push(Xt(n.x3.x)+" "+Xt(n.x3.y)+" l"),a.push("s"),a.push("Q"),e.stream=a.join(`
`),e},OffPushDown:function(r){var e=$n(r);e.scope=r.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+Xt(Mt.internal.getWidth(r))+" "+Xt(Mt.internal.getHeight(r))+" re"),n.push("f"),e.stream=n.join(`
`),e}}},createDefaultAppearanceStream:function(r){var e=r.scope.internal.getFont(r.fontName,r.fontStyle).id,n=r.scope.__private__.encodeColorString(r.color);return"/"+e+" "+r.fontSize+" Tf "+n}};Mt.internal={Bezier_C:.551915024494,calculateCross:function(r){var e=Mt.internal.getWidth(r),n=Mt.internal.getHeight(r),a=Math.min(e,n);return{x1:{x:(e-a)/2,y:(n-a)/2+a},x2:{x:(e-a)/2+a,y:(n-a)/2},x3:{x:(e-a)/2,y:(n-a)/2},x4:{x:(e-a)/2+a,y:(n-a)/2+a}}}},Mt.internal.getWidth=function(r){var e=0;return ve(r)==="object"&&(e=fc(r.Rect[2])),e},Mt.internal.getHeight=function(r){var e=0;return ve(r)==="object"&&(e=fc(r.Rect[3])),e};var al=Pe.addField=function(r){if(il(this,r),!(r instanceof Tn))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=r).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),r.page=r.scope.internal.getCurrentPageInfo().pageNumber,this};Pe.AcroFormChoiceField=Kr,Pe.AcroFormListBox=Zr,Pe.AcroFormComboBox=$r,Pe.AcroFormEditBox=da,Pe.AcroFormButton=Te,Pe.AcroFormPushButton=pa,Pe.AcroFormRadioButton=Qr,Pe.AcroFormCheckBox=ga,Pe.AcroFormTextField=jr,Pe.AcroFormPasswordField=ma,Pe.AcroFormAppearance=Mt,Pe.AcroForm={ChoiceField:Kr,ListBox:Zr,ComboBox:$r,EditBox:da,Button:Te,PushButton:pa,RadioButton:Qr,CheckBox:ga,TextField:jr,PasswordField:ma,Appearance:Mt},Tt.AcroForm={ChoiceField:Kr,ListBox:Zr,ComboBox:$r,EditBox:da,Button:Te,PushButton:pa,RadioButton:Qr,CheckBox:ga,TextField:jr,PasswordField:ma,Appearance:Mt};var ol=Tt.AcroForm;function Tc(r){return r.reduce(function(e,n,a){return e[n]=a,e},{})}(function(r){r.__addimage__={};var e="UNKNOWN",n={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},a=r.__addimage__.getImageFileTypeByImageData=function(P,k){var W,D,st,it,lt,$=e;if((k=k||e)==="RGBA"||P.data!==void 0&&P.data instanceof Uint8ClampedArray&&"height"in P&&"width"in P)return"RGBA";if(wt(P))for(lt in n)for(st=n[lt],W=0;W<st.length;W+=1){for(it=!0,D=0;D<st[W].length;D+=1)if(st[W][D]!==void 0&&st[W][D]!==P[D]){it=!1;break}if(it===!0){$=lt;break}}else for(lt in n)for(st=n[lt],W=0;W<st.length;W+=1){for(it=!0,D=0;D<st[W].length;D+=1)if(st[W][D]!==void 0&&st[W][D]!==P.charCodeAt(D)){it=!1;break}if(it===!0){$=lt;break}}return $===e&&k!==e&&($=k),$},c=function P(k){for(var W=this.internal.write,D=this.internal.putStream,st=(0,this.internal.getFilters)();st.indexOf("FlateEncode")!==-1;)st.splice(st.indexOf("FlateEncode"),1);k.objectId=this.internal.newObject();var it=[];if(it.push({key:"Type",value:"/XObject"}),it.push({key:"Subtype",value:"/Image"}),it.push({key:"Width",value:k.width}),it.push({key:"Height",value:k.height}),k.colorSpace===q.INDEXED?it.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(k.palette.length/3-1)+" "+("sMask"in k&&k.sMask!==void 0?k.objectId+2:k.objectId+1)+" 0 R]"}):(it.push({key:"ColorSpace",value:"/"+k.colorSpace}),k.colorSpace===q.DEVICE_CMYK&&it.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),it.push({key:"BitsPerComponent",value:k.bitsPerComponent}),"decodeParameters"in k&&k.decodeParameters!==void 0&&it.push({key:"DecodeParms",value:"<<"+k.decodeParameters+">>"}),"transparency"in k&&Array.isArray(k.transparency)){for(var lt="",$=0,ht=k.transparency.length;$<ht;$++)lt+=k.transparency[$]+" "+k.transparency[$]+" ";it.push({key:"Mask",value:"["+lt+"]"})}k.sMask!==void 0&&it.push({key:"SMask",value:k.objectId+1+" 0 R"});var pt=k.filter!==void 0?["/"+k.filter]:void 0;if(D({data:k.data,additionalKeyValues:it,alreadyAppliedFilters:pt,objectId:k.objectId}),W("endobj"),"sMask"in k&&k.sMask!==void 0){var It="/Predictor "+k.predictor+" /Colors 1 /BitsPerComponent "+k.bitsPerComponent+" /Columns "+k.width,N={width:k.width,height:k.height,colorSpace:"DeviceGray",bitsPerComponent:k.bitsPerComponent,decodeParameters:It,data:k.sMask};"filter"in k&&(N.filter=k.filter),P.call(this,N)}if(k.colorSpace===q.INDEXED){var C=this.internal.newObject();D({data:z(new Uint8Array(k.palette)),objectId:C}),W("endobj")}},o=function(){var P=this.internal.collections.addImage_images;for(var k in P)c.call(this,P[k])},l=function(){var P,k=this.internal.collections.addImage_images,W=this.internal.write;for(var D in k)W("/I"+(P=k[D]).index,P.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",o),this.internal.events.subscribe("putXobjectDict",l))},f=function(){var P=this.internal.collections.addImage_images;return h.call(this),P},g=function(){return Object.keys(this.internal.collections.addImage_images).length},b=function(P){return typeof r["process"+P.toUpperCase()]=="function"},y=function(P){return ve(P)==="object"&&P.nodeType===1},S=function(P,k){if(P.nodeName==="IMG"&&P.hasAttribute("src")){var W=""+P.getAttribute("src");if(W.indexOf("data:image/")===0)return fa(unescape(W).split("base64,").pop());var D=r.loadFile(W,!0);if(D!==void 0)return D}if(P.nodeName==="CANVAS"){if(P.width===0||P.height===0)throw new Error("Given canvas must have data. Canvas width: "+P.width+", height: "+P.height);var st;switch(k){case"PNG":st="image/png";break;case"WEBP":st="image/webp";break;case"JPEG":case"JPG":default:st="image/jpeg"}return fa(P.toDataURL(st,1).split("base64,").pop())}},p=function(P){var k=this.internal.collections.addImage_images;if(k){for(var W in k)if(P===k[W].alias)return k[W]}},O=function(P,k,W){return P||k||(P=-96,k=-96),P<0&&(P=-1*W.width*72/P/this.internal.scaleFactor),k<0&&(k=-1*W.height*72/k/this.internal.scaleFactor),P===0&&(P=k*W.width/W.height),k===0&&(k=P*W.height/W.width),[P,k]},F=function(P,k,W,D,st,it){var lt=O.call(this,W,D,st),$=this.internal.getCoordinateString,ht=this.internal.getVerticalCoordinateString,pt=f.call(this);if(W=lt[0],D=lt[1],pt[st.index]=st,it){it*=Math.PI/180;var It=Math.cos(it),N=Math.sin(it),C=function(T){return T.toFixed(4)},M=[C(It),C(N),C(-1*N),C(It),0,0,"cm"]}this.internal.write("q"),it?(this.internal.write([1,"0","0",1,$(P),ht(k+D),"cm"].join(" ")),this.internal.write(M.join(" ")),this.internal.write([$(W),"0","0",$(D),"0","0","cm"].join(" "))):this.internal.write([$(W),"0","0",$(D),$(P),ht(k+D),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+st.index+" Do"),this.internal.write("Q")},q=r.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};r.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var _=r.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},B=r.__addimage__.sHashCode=function(P){var k,W,D=0;if(typeof P=="string")for(W=P.length,k=0;k<W;k++)D=(D<<5)-D+P.charCodeAt(k),D|=0;else if(wt(P))for(W=P.byteLength/2,k=0;k<W;k++)D=(D<<5)-D+P[k],D|=0;return D},Y=r.__addimage__.validateStringAsBase64=function(P){(P=P||"").toString().trim();var k=!0;return P.length===0&&(k=!1),P.length%4!=0&&(k=!1),/^[A-Za-z0-9+/]+$/.test(P.substr(0,P.length-2))===!1&&(k=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(P.substr(-2))===!1&&(k=!1),k},ot=r.__addimage__.extractImageFromDataUrl=function(P){var k=(P=P||"").split("base64,"),W=null;if(k.length===2){var D=/^data:(\w*\/\w*);*(charset=(?!charset=)[\w=-]*)*;*$/.exec(k[0]);Array.isArray(D)&&(W={mimeType:D[1],charset:D[2],data:k[1]})}return W},ut=r.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};r.__addimage__.isArrayBuffer=function(P){return ut()&&P instanceof ArrayBuffer};var wt=r.__addimage__.isArrayBufferView=function(P){return ut()&&typeof Uint32Array<"u"&&(P instanceof Int8Array||P instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&P instanceof Uint8ClampedArray||P instanceof Int16Array||P instanceof Uint16Array||P instanceof Int32Array||P instanceof Uint32Array||P instanceof Float32Array||P instanceof Float64Array)},tt=r.__addimage__.binaryStringToUint8Array=function(P){for(var k=P.length,W=new Uint8Array(k),D=0;D<k;D++)W[D]=P.charCodeAt(D);return W},z=r.__addimage__.arrayBufferToBinaryString=function(P){for(var k="",W=wt(P)?P:new Uint8Array(P),D=0;D<W.length;D+=8192)k+=String.fromCharCode.apply(null,W.subarray(D,D+8192));return k};r.addImage=function(){var P,k,W,D,st,it,lt,$,ht;if(typeof arguments[1]=="number"?(k=e,W=arguments[1],D=arguments[2],st=arguments[3],it=arguments[4],lt=arguments[5],$=arguments[6],ht=arguments[7]):(k=arguments[1],W=arguments[2],D=arguments[3],st=arguments[4],it=arguments[5],lt=arguments[6],$=arguments[7],ht=arguments[8]),ve(P=arguments[0])==="object"&&!y(P)&&"imageData"in P){var pt=P;P=pt.imageData,k=pt.format||k||e,W=pt.x||W||0,D=pt.y||D||0,st=pt.w||pt.width||st,it=pt.h||pt.height||it,lt=pt.alias||lt,$=pt.compression||$,ht=pt.rotation||pt.angle||ht}var It=this.internal.getFilters();if($===void 0&&It.indexOf("FlateEncode")!==-1&&($="SLOW"),isNaN(W)||isNaN(D))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var N=rt.call(this,P,k,lt,$);return F.call(this,W,D,st,it,N,ht),this};var rt=function(P,k,W,D){var st,it,lt;if(typeof P=="string"&&a(P)===e){P=unescape(P);var $=dt(P,!1);($!==""||($=r.loadFile(P,!0))!==void 0)&&(P=$)}if(y(P)&&(P=S(P,k)),k=a(P,k),!b(k))throw new Error("addImage does not support files of type '"+k+"', please ensure that a plugin for '"+k+"' support is added.");if(((lt=W)==null||lt.length===0)&&(W=function(ht){return typeof ht=="string"||wt(ht)?B(ht):wt(ht.data)?B(ht.data):null}(P)),(st=p.call(this,W))||(ut()&&(P instanceof Uint8Array||k==="RGBA"||(it=P,P=tt(P))),st=this["process"+k.toUpperCase()](P,g.call(this),W,function(ht){return ht&&typeof ht=="string"&&(ht=ht.toUpperCase()),ht in r.image_compression?ht:_.NONE}(D),it)),!st)throw new Error("An unknown error occurred whilst processing the image.");return st},dt=r.__addimage__.convertBase64ToBinaryString=function(P,k){var W;k=typeof k!="boolean"||k;var D,st="";if(typeof P=="string"){D=(W=ot(P))!==null?W.data:P;try{st=fa(D)}catch(it){if(k)throw Y(D)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+it.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return st};r.getImageProperties=function(P){var k,W,D="";if(y(P)&&(P=S(P)),typeof P=="string"&&a(P)===e&&((D=dt(P,!1))===""&&(D=r.loadFile(P)||""),P=D),W=a(P),!b(W))throw new Error("addImage does not support files of type '"+W+"', please ensure that a plugin for '"+W+"' support is added.");if(!ut()||P instanceof Uint8Array||(P=tt(P)),!(k=this["process"+W.toUpperCase()](P)))throw new Error("An unknown error occurred whilst processing the image");return k.fileType=W,k}})(Tt.API),function(r){var e=function(n){if(n!==void 0&&n!="")return!0};Tt.API.events.push(["addPage",function(n){this.internal.getPageInfo(n.pageNumber).pageContext.annotations=[]}]),r.events.push(["putPage",function(n){for(var a,c,o,l=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,f=this.internal.getPageInfoByObjId(n.objId),g=n.pageContext.annotations,b=!1,y=0;y<g.length&&!b;y++)switch((a=g[y]).type){case"link":(e(a.options.url)||e(a.options.pageNumber))&&(b=!0);break;case"reference":case"text":case"freetext":b=!0}if(b!=0){this.internal.write("/Annots [");for(var S=0;S<g.length;S++){a=g[S];var p=this.internal.pdfEscape,O=this.internal.getEncryptor(n.objId);switch(a.type){case"reference":this.internal.write(" "+a.object.objId+" 0 R ");break;case"text":var F=this.internal.newAdditionalObject(),q=this.internal.newAdditionalObject(),_=this.internal.getEncryptor(F.objId),B=a.title||"Note";o="<</Type /Annot /Subtype /Text "+(c="/Rect ["+l(a.bounds.x)+" "+h(a.bounds.y+a.bounds.h)+" "+l(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y)+"] ")+"/Contents ("+p(_(a.contents))+")",o+=" /Popup "+q.objId+" 0 R",o+=" /P "+f.objId+" 0 R",o+=" /T ("+p(_(B))+") >>",F.content=o;var Y=F.objId+" 0 R";o="<</Type /Annot /Subtype /Popup "+(c="/Rect ["+l(a.bounds.x+30)+" "+h(a.bounds.y+a.bounds.h)+" "+l(a.bounds.x+a.bounds.w+30)+" "+h(a.bounds.y)+"] ")+" /Parent "+Y,a.open&&(o+=" /Open true"),o+=" >>",q.content=o,this.internal.write(F.objId,"0 R",q.objId,"0 R");break;case"freetext":c="/Rect ["+l(a.bounds.x)+" "+h(a.bounds.y)+" "+l(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y+a.bounds.h)+"] ";var ot=a.color||"#000000";o="<</Type /Annot /Subtype /FreeText "+c+"/Contents ("+p(O(a.contents))+")",o+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+ot+")",o+=" /Border [0 0 0]",o+=" >>",this.internal.write(o);break;case"link":if(a.options.name){var ut=this.annotations._nameMap[a.options.name];a.options.pageNumber=ut.page,a.options.top=ut.y}else a.options.top||(a.options.top=0);if(c="/Rect ["+a.finalBounds.x+" "+a.finalBounds.y+" "+a.finalBounds.w+" "+a.finalBounds.h+"] ",o="",a.options.url)o="<</Type /Annot /Subtype /Link "+c+"/Border [0 0 0] /A <</S /URI /URI ("+p(O(a.options.url))+") >>";else if(a.options.pageNumber)switch(o="<</Type /Annot /Subtype /Link "+c+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(a.options.pageNumber).objId+" 0 R",a.options.magFactor=a.options.magFactor||"XYZ",a.options.magFactor){case"Fit":o+=" /Fit]";break;case"FitH":o+=" /FitH "+a.options.top+"]";break;case"FitV":a.options.left=a.options.left||0,o+=" /FitV "+a.options.left+"]";break;case"XYZ":default:var wt=h(a.options.top);a.options.left=a.options.left||0,a.options.zoom===void 0&&(a.options.zoom=0),o+=" /XYZ "+a.options.left+" "+wt+" "+a.options.zoom+"]"}o!=""&&(o+=" >>",this.internal.write(o))}}this.internal.write("]")}}]),r.createAnnotation=function(n){var a=this.internal.getCurrentPageInfo();switch(n.type){case"link":this.link(n.bounds.x,n.bounds.y,n.bounds.w,n.bounds.h,n);break;case"text":case"freetext":a.pageContext.annotations.push(n)}},r.link=function(n,a,c,o,l){var h=this.internal.getCurrentPageInfo(),f=this.internal.getCoordinateString,g=this.internal.getVerticalCoordinateString;h.pageContext.annotations.push({finalBounds:{x:f(n),y:g(a),w:f(n+c),h:g(a+o)},options:l,type:"link"})},r.textWithLink=function(n,a,c,o){var l,h,f=this.getTextWidth(n),g=this.internal.getLineHeight()/this.internal.scaleFactor;if(o.maxWidth!==void 0){h=o.maxWidth;var b=this.splitTextToSize(n,h).length;l=Math.ceil(g*b)}else h=f,l=g;return this.text(n,a,c,o),c+=.2*g,o.align==="center"&&(a-=f/2),o.align==="right"&&(a-=f),this.link(a,c-g,h,l,o),f},r.getTextWidth=function(n){var a=this.internal.getFontSize();return this.getStringUnitWidth(n)*a/this.internal.scaleFactor}}(Tt.API),function(r){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},n={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},a={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},c=[1570,1571,1573,1575];r.__arabicParser__={};var o=r.__arabicParser__.isInArabicSubstitutionA=function(F){return e[F.charCodeAt(0)]!==void 0},l=r.__arabicParser__.isArabicLetter=function(F){return typeof F=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(F)},h=r.__arabicParser__.isArabicEndLetter=function(F){return l(F)&&o(F)&&e[F.charCodeAt(0)].length<=2},f=r.__arabicParser__.isArabicAlfLetter=function(F){return l(F)&&c.indexOf(F.charCodeAt(0))>=0};r.__arabicParser__.arabicLetterHasIsolatedForm=function(F){return l(F)&&o(F)&&e[F.charCodeAt(0)].length>=1};var g=r.__arabicParser__.arabicLetterHasFinalForm=function(F){return l(F)&&o(F)&&e[F.charCodeAt(0)].length>=2};r.__arabicParser__.arabicLetterHasInitialForm=function(F){return l(F)&&o(F)&&e[F.charCodeAt(0)].length>=3};var b=r.__arabicParser__.arabicLetterHasMedialForm=function(F){return l(F)&&o(F)&&e[F.charCodeAt(0)].length==4},y=r.__arabicParser__.resolveLigatures=function(F){var q=0,_=n,B="",Y=0;for(q=0;q<F.length;q+=1)_[F.charCodeAt(q)]!==void 0?(Y++,typeof(_=_[F.charCodeAt(q)])=="number"&&(B+=String.fromCharCode(_),_=n,Y=0),q===F.length-1&&(_=n,B+=F.charAt(q-(Y-1)),q-=Y-1,Y=0)):(_=n,B+=F.charAt(q-Y),q-=Y,Y=0);return B};r.__arabicParser__.isArabicDiacritic=function(F){return F!==void 0&&a[F.charCodeAt(0)]!==void 0};var S=r.__arabicParser__.getCorrectForm=function(F,q,_){return l(F)?o(F)===!1?-1:!g(F)||!l(q)&&!l(_)||!l(_)&&h(q)||h(F)&&!l(q)||h(F)&&f(q)||h(F)&&h(q)?0:b(F)&&l(q)&&!h(q)&&l(_)&&g(_)?3:h(F)||!l(_)?1:2:-1},p=function(F){var q=0,_=0,B=0,Y="",ot="",ut="",wt=(F=F||"").split("\\s+"),tt=[];for(q=0;q<wt.length;q+=1){for(tt.push(""),_=0;_<wt[q].length;_+=1)Y=wt[q][_],ot=wt[q][_-1],ut=wt[q][_+1],l(Y)?(B=S(Y,ot,ut),tt[q]+=B!==-1?String.fromCharCode(e[Y.charCodeAt(0)][B]):Y):tt[q]+=Y;tt[q]=y(tt[q])}return tt.join(" ")},O=r.__arabicParser__.processArabic=r.processArabic=function(){var F,q=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,_=[];if(Array.isArray(q)){var B=0;for(_=[],B=0;B<q.length;B+=1)Array.isArray(q[B])?_.push([p(q[B][0]),q[B][1],q[B][2]]):_.push([p(q[B])]);F=_}else F=p(q);return typeof arguments[0]=="string"?F:(arguments[0].text=F,arguments[0])};r.events.push(["preProcessText",O])}(Tt.API),Tt.API.autoPrint=function(r){var e;switch((r=r||{}).variant=r.variant||"non-conform",r.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},function(r){var e=function(){var n=void 0;Object.defineProperty(this,"pdf",{get:function(){return n},set:function(h){n=h}});var a=150;Object.defineProperty(this,"width",{get:function(){return a},set:function(h){a=isNaN(h)||Number.isInteger(h)===!1||h<0?150:h,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=a+1)}});var c=300;Object.defineProperty(this,"height",{get:function(){return c},set:function(h){c=isNaN(h)||Number.isInteger(h)===!1||h<0?300:h,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=c+1)}});var o=[];Object.defineProperty(this,"childNodes",{get:function(){return o},set:function(h){o=h}});var l={};Object.defineProperty(this,"style",{get:function(){return l},set:function(h){l=h}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(n,a){var c;if((n=n||"2d")!=="2d")return null;for(c in a)this.pdf.context2d.hasOwnProperty(c)&&(this.pdf.context2d[c]=a[c]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},r.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Tt.API),function(r){var e={left:0,top:0,bottom:0,right:0},n=!1,a=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),c.call(this))},c=function(){this.internal.__cell__.lastCell=new o,this.internal.__cell__.pages=1},o=function(){var f=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return f},set:function(F){f=F}});var g=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return g},set:function(F){g=F}});var b=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return b},set:function(F){b=F}});var y=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return y},set:function(F){y=F}});var S=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return S},set:function(F){S=F}});var p=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return p},set:function(F){p=F}});var O=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return O},set:function(F){O=F}}),this};o.prototype.clone=function(){return new o(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},o.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},r.setHeaderFunction=function(f){return a.call(this),this.internal.__cell__.headerFunction=typeof f=="function"?f:void 0,this},r.getTextDimensions=function(f,g){a.call(this);var b=(g=g||{}).fontSize||this.getFontSize(),y=g.font||this.getFont(),S=g.scaleFactor||this.internal.scaleFactor,p=0,O=0,F=0,q=this;if(!Array.isArray(f)&&typeof f!="string"){if(typeof f!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");f=String(f)}var _=g.maxWidth;_>0?typeof f=="string"?f=this.splitTextToSize(f,_):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function(Y,ot){return Y.concat(q.splitTextToSize(ot,_))},[])):f=Array.isArray(f)?f:[f];for(var B=0;B<f.length;B++)p<(F=this.getStringUnitWidth(f[B],{font:y})*b)&&(p=F);return p!==0&&(O=f.length),{w:p/=S,h:Math.max((O*b*this.getLineHeightFactor()-b*(this.getLineHeightFactor()-1))/S,0)}},r.cellAddPage=function(){a.call(this),this.addPage();var f=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new o(f.left,f.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var l=r.cell=function(){var f;f=arguments[0]instanceof o?arguments[0]:new o(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),a.call(this);var g=this.internal.__cell__.lastCell,b=this.internal.__cell__.padding,y=this.internal.__cell__.margins||e,S=this.internal.__cell__.tableHeaderRow,p=this.internal.__cell__.printHeaders;return g.lineNumber!==void 0&&(g.lineNumber===f.lineNumber?(f.x=(g.x||0)+(g.width||0),f.y=g.y||0):g.y+g.height+f.height+y.bottom>this.getPageHeight()?(this.cellAddPage(),f.y=y.top,p&&S&&(this.printHeaderRow(f.lineNumber,!0),f.y+=S[0].height)):f.y=g.y+g.height||f.y),f.text[0]!==void 0&&(this.rect(f.x,f.y,f.width,f.height,n===!0?"FD":void 0),f.align==="right"?this.text(f.text,f.x+f.width-b,f.y+b,{align:"right",baseline:"top"}):f.align==="center"?this.text(f.text,f.x+f.width/2,f.y+b,{align:"center",baseline:"top",maxWidth:f.width-b-b}):this.text(f.text,f.x+b,f.y+b,{align:"left",baseline:"top",maxWidth:f.width-b-b})),this.internal.__cell__.lastCell=f,this};r.table=function(f,g,b,y,S){if(a.call(this),!b)throw new Error("No data for PDF table.");var p,O,F,q,_=[],B=[],Y=[],ot={},ut={},wt=[],tt=[],z=(S=S||{}).autoSize||!1,rt=S.printHeaders!==!1,dt=S.css&&S.css["font-size"]!==void 0?16*S.css["font-size"]:S.fontSize||12,P=S.margins||Object.assign({width:this.getPageWidth()},e),k=typeof S.padding=="number"?S.padding:3,W=S.headerBackgroundColor||"#c8c8c8",D=S.headerTextColor||"#000";if(c.call(this),this.internal.__cell__.printHeaders=rt,this.internal.__cell__.margins=P,this.internal.__cell__.table_font_size=dt,this.internal.__cell__.padding=k,this.internal.__cell__.headerBackgroundColor=W,this.internal.__cell__.headerTextColor=D,this.setFontSize(dt),y==null)B=_=Object.keys(b[0]),Y=_.map(function(){return"left"});else if(Array.isArray(y)&&ve(y[0])==="object")for(_=y.map(function(pt){return pt.name}),B=y.map(function(pt){return pt.prompt||pt.name||""}),Y=y.map(function(pt){return pt.align||"left"}),p=0;p<y.length;p+=1)ut[y[p].name]=y[p].width*(19.049976/25.4);else Array.isArray(y)&&typeof y[0]=="string"&&(B=_=y,Y=_.map(function(){return"left"}));if(z||Array.isArray(y)&&typeof y[0]=="string")for(p=0;p<_.length;p+=1){for(ot[q=_[p]]=b.map(function(pt){return pt[q]}),this.setFont(void 0,"bold"),wt.push(this.getTextDimensions(B[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),O=ot[q],this.setFont(void 0,"normal"),F=0;F<O.length;F+=1)wt.push(this.getTextDimensions(O[F],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);ut[q]=Math.max.apply(null,wt)+k+k,wt=[]}if(rt){var st={};for(p=0;p<_.length;p+=1)st[_[p]]={},st[_[p]].text=B[p],st[_[p]].align=Y[p];var it=h.call(this,st,ut);tt=_.map(function(pt){return new o(f,g,ut[pt],it,st[pt].text,void 0,st[pt].align)}),this.setTableHeaderRow(tt),this.printHeaderRow(1,!1)}var lt=y.reduce(function(pt,It){return pt[It.name]=It.align,pt},{});for(p=0;p<b.length;p+=1){"rowStart"in S&&S.rowStart instanceof Function&&S.rowStart({row:p,data:b[p]},this);var $=h.call(this,b[p],ut);for(F=0;F<_.length;F+=1){var ht=b[p][_[F]];"cellStart"in S&&S.cellStart instanceof Function&&S.cellStart({row:p,col:F,data:ht},this),l.call(this,new o(f,g,ut[_[F]],$,ht,p+2,lt[_[F]]))}}return this.internal.__cell__.table_x=f,this.internal.__cell__.table_y=g,this};var h=function(f,g){var b=this.internal.__cell__.padding,y=this.internal.__cell__.table_font_size,S=this.internal.scaleFactor;return Object.keys(f).map(function(p){var O=f[p];return this.splitTextToSize(O.hasOwnProperty("text")?O.text:O,g[p]-b-b)},this).map(function(p){return this.getLineHeightFactor()*p.length*y/S+b+b},this).reduce(function(p,O){return Math.max(p,O)},0)};r.setTableHeaderRow=function(f){a.call(this),this.internal.__cell__.tableHeaderRow=f},r.printHeaderRow=function(f,g){if(a.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var b;if(n=!0,typeof this.internal.__cell__.headerFunction=="function"){var y=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new o(y[0],y[1],y[2],y[3],void 0,-1)}this.setFont(void 0,"bold");for(var S=[],p=0;p<this.internal.__cell__.tableHeaderRow.length;p+=1){b=this.internal.__cell__.tableHeaderRow[p].clone(),g&&(b.y=this.internal.__cell__.margins.top||0,S.push(b)),b.lineNumber=f;var O=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),l.call(this,b),this.setTextColor(O)}S.length>0&&this.setTableHeaderRow(S),this.setFont(void 0,"normal"),n=!1}}(Tt.API);var zc={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},Uc=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Ss=Tc(Uc),Hc=[100,200,300,400,500,600,700,800,900],sl=Tc(Hc);function _s(r){var e=r.family.replace(/"|'/g,"").toLowerCase(),n=function(o){return zc[o=o||"normal"]?o:"normal"}(r.style),a=function(o){if(!o)return 400;if(typeof o=="number")return o>=100&&o<=900&&o%100==0?o:400;if(/^\d00$/.test(o))return parseInt(o);switch(o){case"bold":return 700;case"normal":default:return 400}}(r.weight),c=function(o){return typeof Ss[o=o||"normal"]=="number"?o:"normal"}(r.stretch);return{family:e,style:n,weight:a,stretch:c,src:r.src||[],ref:r.ref||{name:e,style:[c,n,a].join(" ")}}}function dc(r,e,n,a){var c;for(c=n;c>=0&&c<e.length;c+=a)if(r[e[c]])return r[e[c]];for(c=n;c>=0&&c<e.length;c-=a)if(r[e[c]])return r[e[c]]}var cl={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},pc={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function gc(r){return[r.stretch,r.style,r.weight,r.family].join(" ")}function ul(r,e,n){for(var a=(n=n||{}).defaultFontFamily||"times",c=Object.assign({},cl,n.genericFontFamilies||{}),o=null,l=null,h=0;h<e.length;++h)if(c[(o=_s(e[h])).family]&&(o.family=c[o.family]),r.hasOwnProperty(o.family)){l=r[o.family];break}if(!(l=l||r[a]))throw new Error("Could not find a font-family for the rule '"+gc(o)+"' and default family '"+a+"'.");if(l=function(f,g){if(g[f])return g[f];var b=Ss[f],y=b<=Ss.normal?-1:1,S=dc(g,Uc,b,y);if(!S)throw new Error("Could not find a matching font-stretch value for "+f);return S}(o.stretch,l),l=function(f,g){if(g[f])return g[f];for(var b=zc[f],y=0;y<b.length;++y)if(g[b[y]])return g[b[y]];throw new Error("Could not find a matching font-style for "+f)}(o.style,l),!(l=function(f,g){if(g[f])return g[f];if(f===400&&g[500])return g[500];if(f===500&&g[400])return g[400];var b=sl[f],y=dc(g,Hc,b,f<400?-1:1);if(!y)throw new Error("Could not find a matching font-weight for value "+f);return y}(o.weight,l)))throw new Error("Failed to resolve a font for the rule '"+gc(o)+"'.");return l}function mc(r){return r.trimLeft()}function ll(r,e){for(var n=0;n<r.length;){if(r.charAt(n)===e)return[r.substring(0,n),r.substring(n+1)];n+=1}return null}function hl(r){var e=r.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return e===null?null:[e[0],r.substring(e[0].length)]}var ho,vc,bc,ds=["times"];(function(r){var e,n,a,c,o,l,h,f,g,b=function(N){return N=N||{},this.isStrokeTransparent=N.isStrokeTransparent||!1,this.strokeOpacity=N.strokeOpacity||1,this.strokeStyle=N.strokeStyle||"#000000",this.fillStyle=N.fillStyle||"#000000",this.isFillTransparent=N.isFillTransparent||!1,this.fillOpacity=N.fillOpacity||1,this.font=N.font||"10px sans-serif",this.textBaseline=N.textBaseline||"alphabetic",this.textAlign=N.textAlign||"left",this.lineWidth=N.lineWidth||1,this.lineJoin=N.lineJoin||"miter",this.lineCap=N.lineCap||"butt",this.path=N.path||[],this.transform=N.transform!==void 0?N.transform.clone():new f,this.globalCompositeOperation=N.globalCompositeOperation||"normal",this.globalAlpha=N.globalAlpha||1,this.clip_path=N.clip_path||[],this.currentPoint=N.currentPoint||new l,this.miterLimit=N.miterLimit||10,this.lastPoint=N.lastPoint||new l,this.lineDashOffset=N.lineDashOffset||0,this.lineDash=N.lineDash||[],this.margin=N.margin||[0,0,0,0],this.prevPageLastElemOffset=N.prevPageLastElemOffset||0,this.ignoreClearRect=typeof N.ignoreClearRect!="boolean"||N.ignoreClearRect,this};r.events.push(["initialized",function(){this.context2d=new y(this),e=this.internal.f2,n=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,c=this.internal.getHorizontalCoordinate,o=this.internal.getVerticalCoordinate,l=this.internal.Point,h=this.internal.Rectangle,f=this.internal.Matrix,g=new b}]);var y=function(N){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var C=N;Object.defineProperty(this,"pdf",{get:function(){return C}});var M=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return M},set:function(ft){M=!!ft}});var T=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return T},set:function(ft){T=!!ft}});var J=0;Object.defineProperty(this,"posX",{get:function(){return J},set:function(ft){isNaN(ft)||(J=ft)}});var Q=0;Object.defineProperty(this,"posY",{get:function(){return Q},set:function(ft){isNaN(ft)||(Q=ft)}}),Object.defineProperty(this,"margin",{get:function(){return g.margin},set:function(ft){var E;typeof ft=="number"?E=[ft,ft,ft,ft]:((E=new Array(4))[0]=ft[0],E[1]=ft.length>=2?ft[1]:E[0],E[2]=ft.length>=3?ft[2]:E[0],E[3]=ft.length>=4?ft[3]:E[1]),g.margin=E}});var et=!1;Object.defineProperty(this,"autoPaging",{get:function(){return et},set:function(ft){et=ft}});var nt=0;Object.defineProperty(this,"lastBreak",{get:function(){return nt},set:function(ft){nt=ft}});var At=[];Object.defineProperty(this,"pageBreaks",{get:function(){return At},set:function(ft){At=ft}}),Object.defineProperty(this,"ctx",{get:function(){return g},set:function(ft){ft instanceof b&&(g=ft)}}),Object.defineProperty(this,"path",{get:function(){return g.path},set:function(ft){g.path=ft}});var Nt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Nt},set:function(ft){Nt=ft}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(ft){var E;E=S(ft),this.ctx.fillStyle=E.style,this.ctx.isFillTransparent=E.a===0,this.ctx.fillOpacity=E.a,this.pdf.setFillColor(E.r,E.g,E.b,{a:E.a}),this.pdf.setTextColor(E.r,E.g,E.b,{a:E.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(ft){var E=S(ft);this.ctx.strokeStyle=E.style,this.ctx.isStrokeTransparent=E.a===0,this.ctx.strokeOpacity=E.a,E.a===0?this.pdf.setDrawColor(255,255,255):(E.a,this.pdf.setDrawColor(E.r,E.g,E.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(ft){["butt","round","square"].indexOf(ft)!==-1&&(this.ctx.lineCap=ft,this.pdf.setLineCap(ft))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(ft){isNaN(ft)||(this.ctx.lineWidth=ft,this.pdf.setLineWidth(ft))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(ft){["bevel","round","miter"].indexOf(ft)!==-1&&(this.ctx.lineJoin=ft,this.pdf.setLineJoin(ft))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(ft){isNaN(ft)||(this.ctx.miterLimit=ft,this.pdf.setMiterLimit(ft))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(ft){this.ctx.textBaseline=ft}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(ft){["right","end","center","left","start"].indexOf(ft)!==-1&&(this.ctx.textAlign=ft)}});var Ft=null;function _t(ft,E){if(Ft===null){var Kt=function(Et){var Lt=[];return Object.keys(Et).forEach(function(xt){Et[xt].forEach(function(Ct){var kt=null;switch(Ct){case"bold":kt={family:xt,weight:"bold"};break;case"italic":kt={family:xt,style:"italic"};break;case"bolditalic":kt={family:xt,weight:"bold",style:"italic"};break;case"":case"normal":kt={family:xt}}kt!==null&&(kt.ref={name:xt,style:Ct},Lt.push(kt))})}),Lt}(ft.getFontList());Ft=function(Et){for(var Lt={},xt=0;xt<Et.length;++xt){var Ct=_s(Et[xt]),kt=Ct.family,qt=Ct.stretch,Gt=Ct.style,Qt=Ct.weight;Lt[kt]=Lt[kt]||{},Lt[kt][qt]=Lt[kt][qt]||{},Lt[kt][qt][Gt]=Lt[kt][qt][Gt]||{},Lt[kt][qt][Gt][Qt]=Ct}return Lt}(Kt.concat(E))}return Ft}var Ut=null;Object.defineProperty(this,"fontFaces",{get:function(){return Ut},set:function(ft){Ft=null,Ut=ft}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(ft){var E;if(this.ctx.font=ft,(E=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(ft))!==null){var Kt=E[1],Et=(E[2],E[3]),Lt=E[4],xt=(E[5],E[6]),Ct=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(Lt)[2];Lt=Math.floor(Ct==="px"?parseFloat(Lt)*this.pdf.internal.scaleFactor:Ct==="em"?parseFloat(Lt)*this.pdf.getFontSize():parseFloat(Lt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(Lt);var kt=function(Wt){var ee,jt,Je=[],oe=Wt.trim();if(oe==="")return ds;if(oe in pc)return[pc[oe]];for(;oe!=="";){switch(jt=null,ee=(oe=mc(oe)).charAt(0)){case'"':case"'":jt=ll(oe.substring(1),ee);break;default:jt=hl(oe)}if(jt===null||(Je.push(jt[0]),(oe=mc(jt[1]))!==""&&oe.charAt(0)!==","))return ds;oe=oe.replace(/^,/,"")}return Je}(xt);if(this.fontFaces){var qt=ul(_t(this.pdf,this.fontFaces),kt.map(function(Wt){return{family:Wt,stretch:"normal",weight:Et,style:Kt}}));this.pdf.setFont(qt.ref.name,qt.ref.style)}else{var Gt="";(Et==="bold"||parseInt(Et,10)>=700||Kt==="bold")&&(Gt="bold"),Kt==="italic"&&(Gt+="italic"),Gt.length===0&&(Gt="normal");for(var Qt="",te={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ie=0;ie<kt.length;ie++){if(this.pdf.internal.getFont(kt[ie],Gt,{noFallback:!0,disableWarning:!0})!==void 0){Qt=kt[ie];break}if(Gt==="bolditalic"&&this.pdf.internal.getFont(kt[ie],"bold",{noFallback:!0,disableWarning:!0})!==void 0)Qt=kt[ie],Gt="bold";else if(this.pdf.internal.getFont(kt[ie],"normal",{noFallback:!0,disableWarning:!0})!==void 0){Qt=kt[ie],Gt="normal";break}}if(Qt===""){for(var fe=0;fe<kt.length;fe++)if(te[kt[fe]]){Qt=te[kt[fe]];break}}Qt=Qt===""?"Times":Qt,this.pdf.setFont(Qt,Gt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(ft){this.ctx.globalCompositeOperation=ft}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(ft){this.ctx.globalAlpha=ft}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(ft){this.ctx.lineDashOffset=ft,It.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(ft){this.ctx.lineDash=ft,It.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(ft){this.ctx.ignoreClearRect=!!ft}})};y.prototype.setLineDash=function(N){this.lineDash=N},y.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},y.prototype.fill=function(){ot.call(this,"fill",!1)},y.prototype.stroke=function(){ot.call(this,"stroke",!1)},y.prototype.beginPath=function(){this.path=[{type:"begin"}]},y.prototype.moveTo=function(N,C){if(isNaN(N)||isNaN(C))throw me.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var M=this.ctx.transform.applyToPoint(new l(N,C));this.path.push({type:"mt",x:M.x,y:M.y}),this.ctx.lastPoint=new l(N,C)},y.prototype.closePath=function(){var N=new l(0,0),C=0;for(C=this.path.length-1;C!==-1;C--)if(this.path[C].type==="begin"&&ve(this.path[C+1])==="object"&&typeof this.path[C+1].x=="number"){N=new l(this.path[C+1].x,this.path[C+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new l(N.x,N.y)},y.prototype.lineTo=function(N,C){if(isNaN(N)||isNaN(C))throw me.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var M=this.ctx.transform.applyToPoint(new l(N,C));this.path.push({type:"lt",x:M.x,y:M.y}),this.ctx.lastPoint=new l(M.x,M.y)},y.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),ot.call(this,null,!0)},y.prototype.quadraticCurveTo=function(N,C,M,T){if(isNaN(M)||isNaN(T)||isNaN(N)||isNaN(C))throw me.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var J=this.ctx.transform.applyToPoint(new l(M,T)),Q=this.ctx.transform.applyToPoint(new l(N,C));this.path.push({type:"qct",x1:Q.x,y1:Q.y,x:J.x,y:J.y}),this.ctx.lastPoint=new l(J.x,J.y)},y.prototype.bezierCurveTo=function(N,C,M,T,J,Q){if(isNaN(J)||isNaN(Q)||isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw me.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var et=this.ctx.transform.applyToPoint(new l(J,Q)),nt=this.ctx.transform.applyToPoint(new l(N,C)),At=this.ctx.transform.applyToPoint(new l(M,T));this.path.push({type:"bct",x1:nt.x,y1:nt.y,x2:At.x,y2:At.y,x:et.x,y:et.y}),this.ctx.lastPoint=new l(et.x,et.y)},y.prototype.arc=function(N,C,M,T,J,Q){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T)||isNaN(J))throw me.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(Q=!!Q,!this.ctx.transform.isIdentity){var et=this.ctx.transform.applyToPoint(new l(N,C));N=et.x,C=et.y;var nt=this.ctx.transform.applyToPoint(new l(0,M)),At=this.ctx.transform.applyToPoint(new l(0,0));M=Math.sqrt(Math.pow(nt.x-At.x,2)+Math.pow(nt.y-At.y,2))}Math.abs(J-T)>=2*Math.PI&&(T=0,J=2*Math.PI),this.path.push({type:"arc",x:N,y:C,radius:M,startAngle:T,endAngle:J,counterclockwise:Q})},y.prototype.arcTo=function(N,C,M,T,J){throw new Error("arcTo not implemented.")},y.prototype.rect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw me.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(N,C),this.lineTo(N+M,C),this.lineTo(N+M,C+T),this.lineTo(N,C+T),this.lineTo(N,C),this.lineTo(N+M,C),this.lineTo(N,C)},y.prototype.fillRect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw me.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var J={};this.lineCap!=="butt"&&(J.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(J.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(N,C,M,T),this.fill(),J.hasOwnProperty("lineCap")&&(this.lineCap=J.lineCap),J.hasOwnProperty("lineJoin")&&(this.lineJoin=J.lineJoin)}},y.prototype.strokeRect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw me.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");O.call(this)||(this.beginPath(),this.rect(N,C,M,T),this.stroke())},y.prototype.clearRect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw me.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(N,C,M,T))},y.prototype.save=function(N){N=typeof N!="boolean"||N;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("q");if(this.pdf.setPage(C),N){this.ctx.fontSize=this.pdf.internal.getFontSize();var T=new b(this.ctx);this.ctxStack.push(this.ctx),this.ctx=T}},y.prototype.restore=function(N){N=typeof N!="boolean"||N;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("Q");this.pdf.setPage(C),N&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},y.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var S=function(N){var C,M,T,J;if(N.isCanvasGradient===!0&&(N=N.getColor()),!N)return{r:0,g:0,b:0,a:0,style:N};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(N))C=0,M=0,T=0,J=0;else{var Q=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(N);if(Q!==null)C=parseInt(Q[1]),M=parseInt(Q[2]),T=parseInt(Q[3]),J=1;else if((Q=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(N))!==null)C=parseInt(Q[1]),M=parseInt(Q[2]),T=parseInt(Q[3]),J=parseFloat(Q[4]);else{if(J=1,typeof N=="string"&&N.charAt(0)!=="#"){var et=new Bc(N);N=et.ok?et.toHex():"#000000"}N.length===4?(C=N.substring(1,2),C+=C,M=N.substring(2,3),M+=M,T=N.substring(3,4),T+=T):(C=N.substring(1,3),M=N.substring(3,5),T=N.substring(5,7)),C=parseInt(C,16),M=parseInt(M,16),T=parseInt(T,16)}}return{r:C,g:M,b:T,a:J,style:N}},p=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},O=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};y.prototype.fillText=function(N,C,M,T){if(isNaN(C)||isNaN(M)||typeof N!="string")throw me.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(T=isNaN(T)?void 0:T,!p.call(this)){var J=$(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;k.call(this,{text:N,x:C,y:M,scale:Q,angle:J,align:this.textAlign,maxWidth:T})}},y.prototype.strokeText=function(N,C,M,T){if(isNaN(C)||isNaN(M)||typeof N!="string")throw me.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!O.call(this)){T=isNaN(T)?void 0:T;var J=$(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;k.call(this,{text:N,x:C,y:M,scale:Q,renderingMode:"stroke",angle:J,align:this.textAlign,maxWidth:T})}},y.prototype.measureText=function(N){if(typeof N!="string")throw me.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var C=this.pdf,M=this.pdf.internal.scaleFactor,T=C.internal.getFontSize(),J=C.getStringUnitWidth(N)*T/C.internal.scaleFactor,Q=function(et){var nt=(et=et||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return nt}}),this};return new Q({width:J*=Math.round(96*M/72*1e4)/1e4})},y.prototype.scale=function(N,C){if(isNaN(N)||isNaN(C))throw me.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var M=new f(N,0,0,C,0,0);this.ctx.transform=this.ctx.transform.multiply(M)},y.prototype.rotate=function(N){if(isNaN(N))throw me.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var C=new f(Math.cos(N),Math.sin(N),-Math.sin(N),Math.cos(N),0,0);this.ctx.transform=this.ctx.transform.multiply(C)},y.prototype.translate=function(N,C){if(isNaN(N)||isNaN(C))throw me.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var M=new f(1,0,0,1,N,C);this.ctx.transform=this.ctx.transform.multiply(M)},y.prototype.transform=function(N,C,M,T,J,Q){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T)||isNaN(J)||isNaN(Q))throw me.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var et=new f(N,C,M,T,J,Q);this.ctx.transform=this.ctx.transform.multiply(et)},y.prototype.setTransform=function(N,C,M,T,J,Q){N=isNaN(N)?1:N,C=isNaN(C)?0:C,M=isNaN(M)?0:M,T=isNaN(T)?1:T,J=isNaN(J)?0:J,Q=isNaN(Q)?0:Q,this.ctx.transform=new f(N,C,M,T,J,Q)};var F=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};y.prototype.drawImage=function(N,C,M,T,J,Q,et,nt,At){var Nt=this.pdf.getImageProperties(N),Ft=1,_t=1,Ut=1,ft=1;T!==void 0&&nt!==void 0&&(Ut=nt/T,ft=At/J,Ft=Nt.width/T*nt/T,_t=Nt.height/J*At/J),Q===void 0&&(Q=C,et=M,C=0,M=0),T!==void 0&&nt===void 0&&(nt=T,At=J),T===void 0&&nt===void 0&&(nt=Nt.width,At=Nt.height);for(var E,Kt=this.ctx.transform.decompose(),Et=$(Kt.rotate.shx),Lt=new f,xt=(Lt=(Lt=(Lt=Lt.multiply(Kt.translate)).multiply(Kt.skew)).multiply(Kt.scale)).applyToRectangle(new h(Q-C*Ut,et-M*ft,T*Ft,J*_t)),Ct=q.call(this,xt),kt=[],qt=0;qt<Ct.length;qt+=1)kt.indexOf(Ct[qt])===-1&&kt.push(Ct[qt]);if(Y(kt),this.autoPaging)for(var Gt=kt[0],Qt=kt[kt.length-1],te=Gt;te<Qt+1;te++){this.pdf.setPage(te);var ie=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],fe=te===1?this.posY+this.margin[0]:this.margin[0],Wt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],ee=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],jt=te===1?0:Wt+(te-2)*ee;if(this.ctx.clip_path.length!==0){var Je=this.path;E=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=B(E,this.posX+this.margin[3],-jt+fe+this.ctx.prevPageLastElemOffset),ut.call(this,"fill",!0),this.path=Je}var oe=JSON.parse(JSON.stringify(xt));oe=B([oe],this.posX+this.margin[3],-jt+fe+this.ctx.prevPageLastElemOffset)[0];var _n=(te>Gt||te<Qt)&&F.call(this);_n&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ie,ee,null).clip().discardPath()),this.pdf.addImage(N,"JPEG",oe.x,oe.y,oe.w,oe.h,null,null,Et),_n&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(N,"JPEG",xt.x,xt.y,xt.w,xt.h,null,null,Et)};var q=function(N,C,M){var T=[];C=C||this.pdf.internal.pageSize.width,M=M||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var J=this.posY+this.ctx.prevPageLastElemOffset;switch(N.type){default:case"mt":case"lt":T.push(Math.floor((N.y+J)/M)+1);break;case"arc":T.push(Math.floor((N.y+J-N.radius)/M)+1),T.push(Math.floor((N.y+J+N.radius)/M)+1);break;case"qct":var Q=ht(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x,N.y);T.push(Math.floor((Q.y+J)/M)+1),T.push(Math.floor((Q.y+Q.h+J)/M)+1);break;case"bct":var et=pt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x2,N.y2,N.x,N.y);T.push(Math.floor((et.y+J)/M)+1),T.push(Math.floor((et.y+et.h+J)/M)+1);break;case"rect":T.push(Math.floor((N.y+J)/M)+1),T.push(Math.floor((N.y+N.h+J)/M)+1)}for(var nt=0;nt<T.length;nt+=1)for(;this.pdf.internal.getNumberOfPages()<T[nt];)_.call(this);return T},_=function(){var N=this.fillStyle,C=this.strokeStyle,M=this.font,T=this.lineCap,J=this.lineWidth,Q=this.lineJoin;this.pdf.addPage(),this.fillStyle=N,this.strokeStyle=C,this.font=M,this.lineCap=T,this.lineWidth=J,this.lineJoin=Q},B=function(N,C,M){for(var T=0;T<N.length;T++)switch(N[T].type){case"bct":N[T].x2+=C,N[T].y2+=M;case"qct":N[T].x1+=C,N[T].y1+=M;case"mt":case"lt":case"arc":default:N[T].x+=C,N[T].y+=M}return N},Y=function(N){return N.sort(function(C,M){return C-M})},ot=function(N,C){for(var M,T,J=this.fillStyle,Q=this.strokeStyle,et=this.lineCap,nt=this.lineWidth,At=Math.abs(nt*this.ctx.transform.scaleX),Nt=this.lineJoin,Ft=JSON.parse(JSON.stringify(this.path)),_t=JSON.parse(JSON.stringify(this.path)),Ut=[],ft=0;ft<_t.length;ft++)if(_t[ft].x!==void 0)for(var E=q.call(this,_t[ft]),Kt=0;Kt<E.length;Kt+=1)Ut.indexOf(E[Kt])===-1&&Ut.push(E[Kt]);for(var Et=0;Et<Ut.length;Et++)for(;this.pdf.internal.getNumberOfPages()<Ut[Et];)_.call(this);if(Y(Ut),this.autoPaging)for(var Lt=Ut[0],xt=Ut[Ut.length-1],Ct=Lt;Ct<xt+1;Ct++){this.pdf.setPage(Ct),this.fillStyle=J,this.strokeStyle=Q,this.lineCap=et,this.lineWidth=At,this.lineJoin=Nt;var kt=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],qt=Ct===1?this.posY+this.margin[0]:this.margin[0],Gt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Qt=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],te=Ct===1?0:Gt+(Ct-2)*Qt;if(this.ctx.clip_path.length!==0){var ie=this.path;M=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=B(M,this.posX+this.margin[3],-te+qt+this.ctx.prevPageLastElemOffset),ut.call(this,N,!0),this.path=ie}if(T=JSON.parse(JSON.stringify(Ft)),this.path=B(T,this.posX+this.margin[3],-te+qt+this.ctx.prevPageLastElemOffset),C===!1||Ct===0){var fe=(Ct>Lt||Ct<xt)&&F.call(this);fe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],kt,Qt,null).clip().discardPath()),ut.call(this,N,C),fe&&this.pdf.restoreGraphicsState()}this.lineWidth=nt}else this.lineWidth=At,ut.call(this,N,C),this.lineWidth=nt;this.path=Ft},ut=function(N,C){if((N!=="stroke"||C||!O.call(this))&&(N==="stroke"||C||!p.call(this))){for(var M,T,J=[],Q=this.path,et=0;et<Q.length;et++){var nt=Q[et];switch(nt.type){case"begin":J.push({begin:!0});break;case"close":J.push({close:!0});break;case"mt":J.push({start:nt,deltas:[],abs:[]});break;case"lt":var At=J.length;if(Q[et-1]&&!isNaN(Q[et-1].x)&&(M=[nt.x-Q[et-1].x,nt.y-Q[et-1].y],At>0)){for(;At>=0;At--)if(J[At-1].close!==!0&&J[At-1].begin!==!0){J[At-1].deltas.push(M),J[At-1].abs.push(nt);break}}break;case"bct":M=[nt.x1-Q[et-1].x,nt.y1-Q[et-1].y,nt.x2-Q[et-1].x,nt.y2-Q[et-1].y,nt.x-Q[et-1].x,nt.y-Q[et-1].y],J[J.length-1].deltas.push(M);break;case"qct":var Nt=Q[et-1].x+2/3*(nt.x1-Q[et-1].x),Ft=Q[et-1].y+2/3*(nt.y1-Q[et-1].y),_t=nt.x+2/3*(nt.x1-nt.x),Ut=nt.y+2/3*(nt.y1-nt.y),ft=nt.x,E=nt.y;M=[Nt-Q[et-1].x,Ft-Q[et-1].y,_t-Q[et-1].x,Ut-Q[et-1].y,ft-Q[et-1].x,E-Q[et-1].y],J[J.length-1].deltas.push(M);break;case"arc":J.push({deltas:[],abs:[],arc:!0}),Array.isArray(J[J.length-1].abs)&&J[J.length-1].abs.push(nt)}}T=C?null:N==="stroke"?"stroke":"fill";for(var Kt=!1,Et=0;Et<J.length;Et++)if(J[Et].arc)for(var Lt=J[Et].abs,xt=0;xt<Lt.length;xt++){var Ct=Lt[xt];Ct.type==="arc"?z.call(this,Ct.x,Ct.y,Ct.radius,Ct.startAngle,Ct.endAngle,Ct.counterclockwise,void 0,C,!Kt):W.call(this,Ct.x,Ct.y),Kt=!0}else if(J[Et].close===!0)this.pdf.internal.out("h"),Kt=!1;else if(J[Et].begin!==!0){var kt=J[Et].start.x,qt=J[Et].start.y;D.call(this,J[Et].deltas,kt,qt),Kt=!0}T&&rt.call(this,T),C&&dt.call(this)}},wt=function(N){var C=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,M=C*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return N-M;case"top":return N+C-M;case"hanging":return N+C-2*M;case"middle":return N+C/2-M;case"ideographic":return N;case"alphabetic":default:return N}},tt=function(N){return N+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};y.prototype.createLinearGradient=function(){var N=function(){};return N.colorStops=[],N.addColorStop=function(C,M){this.colorStops.push([C,M])},N.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},N.isCanvasGradient=!0,N},y.prototype.createPattern=function(){return this.createLinearGradient()},y.prototype.createRadialGradient=function(){return this.createLinearGradient()};var z=function(N,C,M,T,J,Q,et,nt,At){for(var Nt=it.call(this,M,T,J,Q),Ft=0;Ft<Nt.length;Ft++){var _t=Nt[Ft];Ft===0&&(At?P.call(this,_t.x1+N,_t.y1+C):W.call(this,_t.x1+N,_t.y1+C)),st.call(this,N,C,_t.x2,_t.y2,_t.x3,_t.y3,_t.x4,_t.y4)}nt?dt.call(this):rt.call(this,et)},rt=function(N){switch(N){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},dt=function(){this.pdf.clip(),this.pdf.discardPath()},P=function(N,C){this.pdf.internal.out(n(N)+" "+a(C)+" m")},k=function(N){var C;switch(N.align){case"right":case"end":C="right";break;case"center":C="center";break;case"left":case"start":default:C="left"}var M=this.pdf.getTextDimensions(N.text),T=wt.call(this,N.y),J=tt.call(this,T)-M.h,Q=this.ctx.transform.applyToPoint(new l(N.x,T)),et=this.ctx.transform.decompose(),nt=new f;nt=(nt=(nt=nt.multiply(et.translate)).multiply(et.skew)).multiply(et.scale);for(var At,Nt,Ft,_t=this.ctx.transform.applyToRectangle(new h(N.x,T,M.w,M.h)),Ut=nt.applyToRectangle(new h(N.x,J,M.w,M.h)),ft=q.call(this,Ut),E=[],Kt=0;Kt<ft.length;Kt+=1)E.indexOf(ft[Kt])===-1&&E.push(ft[Kt]);if(Y(E),this.autoPaging)for(var Et=E[0],Lt=E[E.length-1],xt=Et;xt<Lt+1;xt++){this.pdf.setPage(xt);var Ct=xt===1?this.posY+this.margin[0]:this.margin[0],kt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],qt=this.pdf.internal.pageSize.height-this.margin[2],Gt=qt-this.margin[0],Qt=this.pdf.internal.pageSize.width-this.margin[1],te=Qt-this.margin[3],ie=xt===1?0:kt+(xt-2)*Gt;if(this.ctx.clip_path.length!==0){var fe=this.path;At=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=B(At,this.posX+this.margin[3],-1*ie+Ct),ut.call(this,"fill",!0),this.path=fe}var Wt=B([JSON.parse(JSON.stringify(Ut))],this.posX+this.margin[3],-ie+Ct+this.ctx.prevPageLastElemOffset)[0];N.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*N.scale),Ft=this.lineWidth,this.lineWidth=Ft*N.scale);var ee=this.autoPaging!=="text";if(ee||Wt.y+Wt.h<=qt){if(ee||Wt.y>=Ct&&Wt.x<=Qt){var jt=ee?N.text:this.pdf.splitTextToSize(N.text,N.maxWidth||Qt-Wt.x)[0],Je=B([JSON.parse(JSON.stringify(_t))],this.posX+this.margin[3],-ie+Ct+this.ctx.prevPageLastElemOffset)[0],oe=ee&&(xt>Et||xt<Lt)&&F.call(this);oe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],te,Gt,null).clip().discardPath()),this.pdf.text(jt,Je.x,Je.y,{angle:N.angle,align:C,renderingMode:N.renderingMode}),oe&&this.pdf.restoreGraphicsState()}}else Wt.y<qt&&(this.ctx.prevPageLastElemOffset+=qt-Wt.y);N.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Ft)}else N.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*N.scale),Ft=this.lineWidth,this.lineWidth=Ft*N.scale),this.pdf.text(N.text,Q.x+this.posX,Q.y+this.posY,{angle:N.angle,align:C,renderingMode:N.renderingMode,maxWidth:N.maxWidth}),N.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Ft)},W=function(N,C,M,T){M=M||0,T=T||0,this.pdf.internal.out(n(N+M)+" "+a(C+T)+" l")},D=function(N,C,M){return this.pdf.lines(N,C,M,null,null)},st=function(N,C,M,T,J,Q,et,nt){this.pdf.internal.out([e(c(M+N)),e(o(T+C)),e(c(J+N)),e(o(Q+C)),e(c(et+N)),e(o(nt+C)),"c"].join(" "))},it=function(N,C,M,T){for(var J=2*Math.PI,Q=Math.PI/2;C>M;)C-=J;var et=Math.abs(M-C);et<J&&T&&(et=J-et);for(var nt=[],At=T?-1:1,Nt=C;et>1e-5;){var Ft=Nt+At*Math.min(et,Q);nt.push(lt.call(this,N,Nt,Ft)),et-=Math.abs(Ft-Nt),Nt=Ft}return nt},lt=function(N,C,M){var T=(M-C)/2,J=N*Math.cos(T),Q=N*Math.sin(T),et=J,nt=-Q,At=et*et+nt*nt,Nt=At+et*J+nt*Q,Ft=4/3*(Math.sqrt(2*At*Nt)-Nt)/(et*Q-nt*J),_t=et-Ft*nt,Ut=nt+Ft*et,ft=_t,E=-Ut,Kt=T+C,Et=Math.cos(Kt),Lt=Math.sin(Kt);return{x1:N*Math.cos(C),y1:N*Math.sin(C),x2:_t*Et-Ut*Lt,y2:_t*Lt+Ut*Et,x3:ft*Et-E*Lt,y3:ft*Lt+E*Et,x4:N*Math.cos(M),y4:N*Math.sin(M)}},$=function(N){return 180*N/Math.PI},ht=function(N,C,M,T,J,Q){var et=N+.5*(M-N),nt=C+.5*(T-C),At=J+.5*(M-J),Nt=Q+.5*(T-Q),Ft=Math.min(N,J,et,At),_t=Math.max(N,J,et,At),Ut=Math.min(C,Q,nt,Nt),ft=Math.max(C,Q,nt,Nt);return new h(Ft,Ut,_t-Ft,ft-Ut)},pt=function(N,C,M,T,J,Q,et,nt){var At,Nt,Ft,_t,Ut,ft,E,Kt,Et,Lt,xt,Ct,kt,qt,Gt=M-N,Qt=T-C,te=J-M,ie=Q-T,fe=et-J,Wt=nt-Q;for(Nt=0;Nt<41;Nt++)Et=(E=(Ft=N+(At=Nt/40)*Gt)+At*((Ut=M+At*te)-Ft))+At*(Ut+At*(J+At*fe-Ut)-E),Lt=(Kt=(_t=C+At*Qt)+At*((ft=T+At*ie)-_t))+At*(ft+At*(Q+At*Wt-ft)-Kt),Nt==0?(xt=Et,Ct=Lt,kt=Et,qt=Lt):(xt=Math.min(xt,Et),Ct=Math.min(Ct,Lt),kt=Math.max(kt,Et),qt=Math.max(qt,Lt));return new h(Math.round(xt),Math.round(Ct),Math.round(kt-xt),Math.round(qt-Ct))},It=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var N,C,M=(N=this.ctx.lineDash,C=this.ctx.lineDashOffset,JSON.stringify({lineDash:N,lineDashOffset:C}));this.prevLineDash!==M&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=M)}}})(Tt.API),function(r){var e=function(o){var l,h,f,g,b,y,S,p,O,F;for(h=[],f=0,g=(o+=l="\0\0\0\0".slice(o.length%4||4)).length;g>f;f+=4)(b=(o.charCodeAt(f)<<24)+(o.charCodeAt(f+1)<<16)+(o.charCodeAt(f+2)<<8)+o.charCodeAt(f+3))!==0?(y=(b=((b=((b=((b=(b-(F=b%85))/85)-(O=b%85))/85)-(p=b%85))/85)-(S=b%85))/85)%85,h.push(y+33,S+33,p+33,O+33,F+33)):h.push(122);return function(q,_){for(var B=_;B>0;B--)q.pop()}(h,l.length),String.fromCharCode.apply(String,h)+"~>"},n=function(o){var l,h,f,g,b,y=String,S="length",p=255,O="charCodeAt",F="slice",q="replace";for(o[F](-2),o=o[F](0,-2)[q](/\s/g,"")[q]("z","!!!!!"),f=[],g=0,b=(o+=l="uuuuu"[F](o[S]%5||5))[S];b>g;g+=5)h=52200625*(o[O](g)-33)+614125*(o[O](g+1)-33)+7225*(o[O](g+2)-33)+85*(o[O](g+3)-33)+(o[O](g+4)-33),f.push(p&h>>24,p&h>>16,p&h>>8,p&h);return function(_,B){for(var Y=B;Y>0;Y--)_.pop()}(f,l[S]),y.fromCharCode.apply(y,f)},a=function(o){var l=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((o=o.replace(/\s/g,"")).indexOf(">")!==-1&&(o=o.substr(0,o.indexOf(">"))),o.length%2&&(o+="0"),l.test(o)===!1)return"";for(var h="",f=0;f<o.length;f+=2)h+=String.fromCharCode("0x"+(o[f]+o[f+1]));return h},c=function(o){for(var l=new Uint8Array(o.length),h=o.length;h--;)l[h]=o.charCodeAt(h);return o=(l=ws(l)).reduce(function(f,g){return f+String.fromCharCode(g)},"")};r.processDataByFilters=function(o,l){var h=0,f=o||"",g=[];for(typeof(l=l||[])=="string"&&(l=[l]),h=0;h<l.length;h+=1)switch(l[h]){case"ASCII85Decode":case"/ASCII85Decode":f=n(f),g.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=e(f),g.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=a(f),g.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=f.split("").map(function(b){return("0"+b.charCodeAt().toString(16)).slice(-2)}).join("")+">",g.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=c(f),g.push("/FlateDecode");break;default:throw new Error('The filter: "'+l[h]+'" is not implemented')}return{data:f,reverseChain:g.reverse().join(" ")}}}(Tt.API),function(r){r.loadFile=function(e,n,a){return function(c,o,l){o=o!==!1,l=typeof l=="function"?l:function(){};var h=void 0;try{h=function(f,g,b){var y=new XMLHttpRequest,S=0,p=function(O){var F=O.length,q=[],_=String.fromCharCode;for(S=0;S<F;S+=1)q.push(_(255&O.charCodeAt(S)));return q.join("")};if(y.open("GET",f,!g),y.overrideMimeType("text/plain; charset=x-user-defined"),g===!1&&(y.onload=function(){y.status===200?b(p(this.responseText)):b(void 0)}),y.send(null),g&&y.status===200)return p(y.responseText)}(c,o,l)}catch{}return h}(e,n,a)},r.loadImageFile=r.loadFile}(Tt.API),function(r){function e(){return(Ht.html2canvas?Promise.resolve(Ht.html2canvas):gs(()=>import("./html2canvas-e0a7d97b.js"),[])).catch(function(l){return Promise.reject(new Error("Could not load html2canvas: "+l))}).then(function(l){return l.default?l.default:l})}function n(){return(Ht.DOMPurify?Promise.resolve(Ht.DOMPurify):gs(()=>import("./purify.es-dceb4668.js"),[])).catch(function(l){return Promise.reject(new Error("Could not load dompurify: "+l))}).then(function(l){return l.default?l.default:l})}var a=function(l){var h=ve(l);return h==="undefined"?"undefined":h==="string"||l instanceof String?"string":h==="number"||l instanceof Number?"number":h==="function"||l instanceof Function?"function":l&&l.constructor===Array?"array":l&&l.nodeType===1?"element":h==="object"?"object":"unknown"},c=function(l,h){var f=document.createElement(l);for(var g in h.className&&(f.className=h.className),h.innerHTML&&h.dompurify&&(f.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)f.style[g]=h.style[g];return f},o=function l(h){var f=Object.assign(l.convert(Promise.resolve()),JSON.parse(JSON.stringify(l.template))),g=l.convert(Promise.resolve(),f);return g=(g=g.setProgress(1,l,1,[l])).set(h)};(o.prototype=Object.create(Promise.prototype)).constructor=o,o.convert=function(l,h){return l.__proto__=h||o.prototype,l},o.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},o.prototype.from=function(l,h){return this.then(function(){switch(h=h||function(f){switch(a(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(l)){case"string":return this.then(n).then(function(f){return this.set({src:c("div",{innerHTML:l,dompurify:f})})});case"element":return this.set({src:l});case"canvas":return this.set({canvas:l});case"img":return this.set({img:l});default:return this.error("Unknown source type.")}})},o.prototype.to=function(l){switch(l){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},o.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var l={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=function f(g,b){for(var y=g.nodeType===3?document.createTextNode(g.nodeValue):g.cloneNode(!1),S=g.firstChild;S;S=S.nextSibling)b!==!0&&S.nodeType===1&&S.nodeName==="SCRIPT"||y.appendChild(f(S,b));return g.nodeType===1&&(g.nodeName==="CANVAS"?(y.width=g.width,y.height=g.height,y.getContext("2d").drawImage(g,0,0)):g.nodeName!=="TEXTAREA"&&g.nodeName!=="SELECT"||(y.value=g.value),y.addEventListener("load",function(){y.scrollTop=g.scrollTop,y.scrollLeft=g.scrollLeft},!0)),y}(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(l.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=c("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=c("div",{className:"html2pdf__container",style:l}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(c("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},o.prototype.toCanvas=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(e).then(function(h){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,h(this.prop.container,f)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},o.prototype.toContext2d=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(e).then(function(h){var f=this.opt.jsPDF,g=this.opt.fontFaces,b=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,y=Object.assign({async:!0,allowTaint:!0,scale:b,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete y.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=g,g)for(var S=0;S<g.length;++S){var p=g[S],O=p.src.find(function(F){return F.format==="truetype"});O&&f.addFont(O.url,p.ref.name,p.ref.style)}return y.windowHeight=y.windowHeight||0,y.windowHeight=y.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):y.windowHeight,f.context2d.save(!0),h(this.prop.container,y)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},o.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var l=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=l})},o.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},o.prototype.output=function(l,h,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(l,h):this.outputPdf(l,h)},o.prototype.outputPdf=function(l,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(l,h)})},o.prototype.outputImg=function(l){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(l){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+l+'" is not supported.'}})},o.prototype.save=function(l){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(l?{filename:l}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},o.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},o.prototype.set=function(l){if(a(l)!=="object")return this;var h=Object.keys(l||{}).map(function(f){if(f in o.template.prop)return function(){this.prop[f]=l[f]};switch(f){case"margin":return this.setMargin.bind(this,l.margin);case"jsPDF":return function(){return this.opt.jsPDF=l.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,l.pageSize);default:return function(){this.opt[f]=l[f]}}},this);return this.then(function(){return this.thenList(h)})},o.prototype.get=function(l,h){return this.then(function(){var f=l in o.template.prop?this.prop[l]:this.opt[l];return h?h(f):f})},o.prototype.setMargin=function(l){return this.then(function(){switch(a(l)){case"number":l=[l,l,l,l];case"array":if(l.length===2&&(l=[l[0],l[1],l[0],l[1]]),l.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=l}).then(this.setPageSize)},o.prototype.setPageSize=function(l){function h(f,g){return Math.floor(f*g/72*96)}return this.then(function(){(l=l||Tt.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(l.inner={width:l.width-this.opt.margin[1]-this.opt.margin[3],height:l.height-this.opt.margin[0]-this.opt.margin[2]},l.inner.px={width:h(l.inner.width,l.k),height:h(l.inner.height,l.k)},l.inner.ratio=l.inner.height/l.inner.width),this.prop.pageSize=l})},o.prototype.setProgress=function(l,h,f,g){return l!=null&&(this.progress.val=l),h!=null&&(this.progress.state=h),f!=null&&(this.progress.n=f),g!=null&&(this.progress.stack=g),this.progress.ratio=this.progress.val/this.progress.state,this},o.prototype.updateProgress=function(l,h,f,g){return this.setProgress(l?this.progress.val+l:null,h||null,f?this.progress.n+f:null,g?this.progress.stack.concat(g):null)},o.prototype.then=function(l,h){var f=this;return this.thenCore(l,h,function(g,b){return f.updateProgress(null,null,1,[g]),Promise.prototype.then.call(this,function(y){return f.updateProgress(null,g),y}).then(g,b).then(function(y){return f.updateProgress(1),y})})},o.prototype.thenCore=function(l,h,f){f=f||Promise.prototype.then,l&&(l=l.bind(this)),h&&(h=h.bind(this));var g=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:o.convert(Object.assign({},this),Promise.prototype),b=f.call(g,l,h);return o.convert(b,this.__proto__)},o.prototype.thenExternal=function(l,h){return Promise.prototype.then.call(this,l,h)},o.prototype.thenList=function(l){var h=this;return l.forEach(function(f){h=h.thenCore(f)}),h},o.prototype.catch=function(l){l&&(l=l.bind(this));var h=Promise.prototype.catch.call(this,l);return o.convert(h,this)},o.prototype.catchExternal=function(l){return Promise.prototype.catch.call(this,l)},o.prototype.error=function(l){return this.then(function(){throw new Error(l)})},o.prototype.using=o.prototype.set,o.prototype.saveAs=o.prototype.save,o.prototype.export=o.prototype.output,o.prototype.run=o.prototype.then,Tt.getPageSize=function(l,h,f){if(ve(l)==="object"){var g=l;l=g.orientation,h=g.unit||h,f=g.format||f}h=h||"mm",f=f||"a4",l=(""+(l||"P")).toLowerCase();var b,y=(""+f).toLowerCase(),S={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":b=1;break;case"mm":b=72/25.4;break;case"cm":b=72/2.54;break;case"in":b=72;break;case"px":b=.75;break;case"pc":case"em":b=12;break;case"ex":b=6;break;default:throw"Invalid unit: "+h}var p,O=0,F=0;if(S.hasOwnProperty(y))O=S[y][1]/b,F=S[y][0]/b;else try{O=f[1],F=f[0]}catch{throw new Error("Invalid format: "+f)}if(l==="p"||l==="portrait")l="p",F>O&&(p=F,F=O,O=p);else{if(l!=="l"&&l!=="landscape")throw"Invalid orientation: "+l;l="l",O>F&&(p=F,F=O,O=p)}return{width:F,height:O,unit:h,k:b,orientation:l}},r.html=function(l,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(_s):null;var f=new o(h);return h.worker?f:f.from(l).doCallback()}}(Tt.API),Tt.API.addJS=function(r){return bc=r,this.internal.events.subscribe("postPutResources",function(){ho=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(ho+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),vc=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+bc+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){ho!==void 0&&vc!==void 0&&this.internal.out("/Names <</JavaScript "+ho+" 0 R>>")}),this},function(r){var e;r.events.push(["postPutResources",function(){var n=this,a=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var c=n.outline.render().split(/\r\n/),o=0;o<c.length;o++){var l=c[o],h=a.exec(l);if(h!=null){var f=h[1];n.internal.newObjectDeferredBegin(f,!1)}n.internal.write(l)}if(this.outline.createNamedDestinations){var g=this.internal.pages.length,b=[];for(o=0;o<g;o++){var y=n.internal.newObject();b.push(y);var S=n.internal.getPageInfo(o+1);n.internal.write("<< /D["+S.objId+" 0 R /XYZ null null null]>> endobj")}var p=n.internal.newObject();for(n.internal.write("<< /Names [ "),o=0;o<b.length;o++)n.internal.write("(page_"+(o+1)+")"+b[o]+" 0 R");n.internal.write(" ] >>","endobj"),e=n.internal.newObject(),n.internal.write("<< /Dests "+p+" 0 R"),n.internal.write(">>","endobj")}}]),r.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),r.events.push(["initialized",function(){var n=this;n.outline={createNamedDestinations:!1,root:{children:[]}},n.outline.add=function(a,c,o){var l={title:c,options:o,children:[]};return a==null&&(a=this.root),a.children.push(l),l},n.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=n,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},n.outline.genIds_r=function(a){a.id=n.internal.newObjectDeferred();for(var c=0;c<a.children.length;c++)this.genIds_r(a.children[c])},n.outline.renderRoot=function(a){this.objStart(a),this.line("/Type /Outlines"),a.children.length>0&&(this.line("/First "+this.makeRef(a.children[0])),this.line("/Last "+this.makeRef(a.children[a.children.length-1]))),this.line("/Count "+this.count_r({count:0},a)),this.objEnd()},n.outline.renderItems=function(a){for(var c=this.ctx.pdf.internal.getVerticalCoordinateString,o=0;o<a.children.length;o++){var l=a.children[o];this.objStart(l),this.line("/Title "+this.makeString(l.title)),this.line("/Parent "+this.makeRef(a)),o>0&&this.line("/Prev "+this.makeRef(a.children[o-1])),o<a.children.length-1&&this.line("/Next "+this.makeRef(a.children[o+1])),l.children.length>0&&(this.line("/First "+this.makeRef(l.children[0])),this.line("/Last "+this.makeRef(l.children[l.children.length-1])));var h=this.count=this.count_r({count:0},l);if(h>0&&this.line("/Count "+h),l.options&&l.options.pageNumber){var f=n.internal.getPageInfo(l.options.pageNumber);this.line("/Dest ["+f.objId+" 0 R /XYZ 0 "+c(0)+" 0]")}this.objEnd()}for(var g=0;g<a.children.length;g++)this.renderItems(a.children[g])},n.outline.line=function(a){this.ctx.val+=a+`\r
`},n.outline.makeRef=function(a){return a.id+" 0 R"},n.outline.makeString=function(a){return"("+n.internal.pdfEscape(a)+")"},n.outline.objStart=function(a){this.ctx.val+=`\r
`+a.id+` 0 obj\r
<<\r
`},n.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},n.outline.count_r=function(a,c){for(var o=0;o<c.children.length;o++)a.count++,this.count_r(a,c.children[o]);return a.count}}])}(Tt.API),function(r){var e=[192,193,194,195,196,197,198,199];r.processJPEG=function(n,a,c,o,l,h){var f,g=this.decode.DCT_DECODE,b=null;if(typeof n=="string"||this.__addimage__.isArrayBuffer(n)||this.__addimage__.isArrayBufferView(n)){switch(n=l||n,n=this.__addimage__.isArrayBuffer(n)?new Uint8Array(n):n,(f=function(y){for(var S,p=256*y.charCodeAt(4)+y.charCodeAt(5),O=y.length,F={width:0,height:0,numcomponents:1},q=4;q<O;q+=2){if(q+=p,e.indexOf(y.charCodeAt(q+1))!==-1){S=256*y.charCodeAt(q+5)+y.charCodeAt(q+6),F={width:256*y.charCodeAt(q+7)+y.charCodeAt(q+8),height:S,numcomponents:y.charCodeAt(q+9)};break}p=256*y.charCodeAt(q+2)+y.charCodeAt(q+3)}return F}(n=this.__addimage__.isArrayBufferView(n)?this.__addimage__.arrayBufferToBinaryString(n):n)).numcomponents){case 1:h=this.color_spaces.DEVICE_GRAY;break;case 4:h=this.color_spaces.DEVICE_CMYK;break;case 3:h=this.color_spaces.DEVICE_RGB}b={data:n,width:f.width,height:f.height,colorSpace:h,bitsPerComponent:8,filter:g,index:a,alias:c}}return b}}(Tt.API);var Ci,fo,yc,wc,Lc,fl=function(){var r,e,n;function a(o){var l,h,f,g,b,y,S,p,O,F,q,_,B,Y;for(this.data=o,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},y=null;;){switch(l=this.readUInt32(),O=(function(){var ot,ut;for(ut=[],ot=0;ot<4;++ot)ut.push(String.fromCharCode(this.data[this.pos++]));return ut}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(l);break;case"fcTL":y&&this.animation.frames.push(y),this.pos+=4,y={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},b=this.readUInt16(),g=this.readUInt16()||100,y.delay=1e3*b/g,y.disposeOp=this.data[this.pos++],y.blendOp=this.data[this.pos++],y.data=[];break;case"IDAT":case"fdAT":for(O==="fdAT"&&(this.pos+=4,l-=4),o=(y!=null?y.data:void 0)||this.imgData,_=0;0<=l?_<l:_>l;0<=l?++_:--_)o.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(f=this.palette.length/3,this.transparency.indexed=this.read(l),this.transparency.indexed.length>f)throw new Error("More transparent colors than palette size");if((F=f-this.transparency.indexed.length)>0)for(B=0;0<=F?B<F:B>F;0<=F?++B:--B)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(l)[0];break;case 2:this.transparency.rgb=this.read(l)}break;case"tEXt":S=(q=this.read(l)).indexOf(0),p=String.fromCharCode.apply(String,q.slice(0,S)),this.text[p]=String.fromCharCode.apply(String,q.slice(S+1));break;case"IEND":return y&&this.animation.frames.push(y),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(Y=this.colorType)===4||Y===6,h=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*h,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=l}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(o){var l,h;for(h=[],l=0;0<=o?l<o:l>o;0<=o?++l:--l)h.push(this.data[this.pos++]);return h},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(o){var l=this.pixelBitlength/8,h=new Uint8Array(this.width*this.height*l),f=0,g=this;if(o==null&&(o=this.imgData),o.length===0)return new Uint8Array(0);function b(y,S,p,O){var F,q,_,B,Y,ot,ut,wt,tt,z,rt,dt,P,k,W,D,st,it,lt,$,ht,pt=Math.ceil((g.width-y)/p),It=Math.ceil((g.height-S)/O),N=g.width==pt&&g.height==It;for(k=l*pt,dt=N?h:new Uint8Array(k*It),ot=o.length,P=0,q=0;P<It&&f<ot;){switch(o[f++]){case 0:for(B=st=0;st<k;B=st+=1)dt[q++]=o[f++];break;case 1:for(B=it=0;it<k;B=it+=1)F=o[f++],Y=B<l?0:dt[q-l],dt[q++]=(F+Y)%256;break;case 2:for(B=lt=0;lt<k;B=lt+=1)F=o[f++],_=(B-B%l)/l,W=P&&dt[(P-1)*k+_*l+B%l],dt[q++]=(W+F)%256;break;case 3:for(B=$=0;$<k;B=$+=1)F=o[f++],_=(B-B%l)/l,Y=B<l?0:dt[q-l],W=P&&dt[(P-1)*k+_*l+B%l],dt[q++]=(F+Math.floor((Y+W)/2))%256;break;case 4:for(B=ht=0;ht<k;B=ht+=1)F=o[f++],_=(B-B%l)/l,Y=B<l?0:dt[q-l],P===0?W=D=0:(W=dt[(P-1)*k+_*l+B%l],D=_&&dt[(P-1)*k+(_-1)*l+B%l]),ut=Y+W-D,wt=Math.abs(ut-Y),z=Math.abs(ut-W),rt=Math.abs(ut-D),tt=wt<=z&&wt<=rt?Y:z<=rt?W:D,dt[q++]=(F+tt)%256;break;default:throw new Error("Invalid filter algorithm: "+o[f-1])}if(!N){var C=((S+P*O)*g.width+y)*l,M=P*k;for(B=0;B<pt;B+=1){for(var T=0;T<l;T+=1)h[C++]=dt[M++];C+=(p-1)*l}}P++}}return o=Hu(o),g.interlaceMethod==1?(b(0,0,8,8),b(4,0,8,8),b(0,4,4,8),b(2,0,4,4),b(0,2,2,4),b(1,0,2,2),b(0,1,1,2)):b(0,0,1,1),h},a.prototype.decodePalette=function(){var o,l,h,f,g,b,y,S,p;for(h=this.palette,b=this.transparency.indexed||[],g=new Uint8Array((b.length||0)+h.length),f=0,o=0,l=y=0,S=h.length;y<S;l=y+=3)g[f++]=h[l],g[f++]=h[l+1],g[f++]=h[l+2],g[f++]=(p=b[o++])!=null?p:255;return g},a.prototype.copyToImageData=function(o,l){var h,f,g,b,y,S,p,O,F,q,_;if(f=this.colors,F=null,h=this.hasAlphaChannel,this.palette.length&&(F=(_=this._decodedPalette)!=null?_:this._decodedPalette=this.decodePalette(),f=4,h=!0),O=(g=o.data||o).length,y=F||l,b=S=0,f===1)for(;b<O;)p=F?4*l[b/4]:S,q=y[p++],g[b++]=q,g[b++]=q,g[b++]=q,g[b++]=h?y[p++]:255,S=p;else for(;b<O;)p=F?4*l[b/4]:S,g[b++]=y[p++],g[b++]=y[p++],g[b++]=y[p++],g[b++]=h?y[p++]:255,S=p},a.prototype.decode=function(){var o;return o=new Uint8Array(this.width*this.height*4),this.copyToImageData(o,this.decodePixels()),o};var c=function(){if(Object.prototype.toString.call(Ht)==="[object Window]"){try{e=Ht.document.createElement("canvas"),n=e.getContext("2d")}catch{return!1}return!0}return!1};return c(),r=function(o){var l;if(c()===!0)return n.width=o.width,n.height=o.height,n.clearRect(0,0,o.width,o.height),n.putImageData(o,0,0),(l=new Image).src=e.toDataURL(),l;throw new Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(o){var l,h,f,g,b,y,S,p;if(this.animation){for(p=[],h=b=0,y=(S=this.animation.frames).length;b<y;h=++b)l=S[h],f=o.createImageData(l.width,l.height),g=this.decodePixels(new Uint8Array(l.data)),this.copyToImageData(f,g),l.imageData=f,p.push(l.image=r(f));return p}},a.prototype.renderFrame=function(o,l){var h,f,g;return h=(f=this.animation.frames)[l],g=f[l-1],l===0&&o.clearRect(0,0,this.width,this.height),(g!=null?g.disposeOp:void 0)===1?o.clearRect(g.xOffset,g.yOffset,g.width,g.height):(g!=null?g.disposeOp:void 0)===2&&o.putImageData(g.imageData,g.xOffset,g.yOffset),h.blendOp===0&&o.clearRect(h.xOffset,h.yOffset,h.width,h.height),o.drawImage(h.image,h.xOffset,h.yOffset)},a.prototype.animate=function(o){var l,h,f,g,b,y,S=this;return h=0,y=this.animation,g=y.numFrames,f=y.frames,b=y.numPlays,(l=function(){var p,O;if(p=h++%g,O=f[p],S.renderFrame(o,p),g>1&&h/g<b)return S.animation._timeout=setTimeout(l,O.delay)})()},a.prototype.stopAnimation=function(){var o;return clearTimeout((o=this.animation)!=null?o._timeout:void 0)},a.prototype.render=function(o){var l,h;return o._png&&o._png.stopAnimation(),o._png=this,o.width=this.width,o.height=this.height,l=o.getContext("2d"),this.animation?(this.decodeFrames(l),this.animate(l)):(h=l.createImageData(this.width,this.height),this.copyToImageData(h,this.decodePixels()),l.putImageData(h,0,0))},a}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function dl(r){var e=0;if(r[e++]!==71||r[e++]!==73||r[e++]!==70||r[e++]!==56||(r[e++]+1&253)!=56||r[e++]!==97)throw new Error("Invalid GIF 87a/89a header.");var n=r[e++]|r[e++]<<8,a=r[e++]|r[e++]<<8,c=r[e++],o=c>>7,l=1<<(7&c)+1;r[e++],r[e++];var h=null,f=null;o&&(h=e,f=l,e+=3*l);var g=!0,b=[],y=0,S=null,p=0,O=null;for(this.width=n,this.height=a;g&&e<r.length;)switch(r[e++]){case 33:switch(r[e++]){case 255:if(r[e]!==11||r[e+1]==78&&r[e+2]==69&&r[e+3]==84&&r[e+4]==83&&r[e+5]==67&&r[e+6]==65&&r[e+7]==80&&r[e+8]==69&&r[e+9]==50&&r[e+10]==46&&r[e+11]==48&&r[e+12]==3&&r[e+13]==1&&r[e+16]==0)e+=14,O=r[e++]|r[e++]<<8,e++;else for(e+=12;;){if(!((P=r[e++])>=0))throw Error("Invalid block size");if(P===0)break;e+=P}break;case 249:if(r[e++]!==4||r[e+4]!==0)throw new Error("Invalid graphics extension block.");var F=r[e++];y=r[e++]|r[e++]<<8,S=r[e++],!(1&F)&&(S=null),p=F>>2&7,e++;break;case 254:for(;;){if(!((P=r[e++])>=0))throw Error("Invalid block size");if(P===0)break;e+=P}break;default:throw new Error("Unknown graphic control label: 0x"+r[e-1].toString(16))}break;case 44:var q=r[e++]|r[e++]<<8,_=r[e++]|r[e++]<<8,B=r[e++]|r[e++]<<8,Y=r[e++]|r[e++]<<8,ot=r[e++],ut=ot>>6&1,wt=1<<(7&ot)+1,tt=h,z=f,rt=!1;ot>>7&&(rt=!0,tt=e,z=wt,e+=3*wt);var dt=e;for(e++;;){var P;if(!((P=r[e++])>=0))throw Error("Invalid block size");if(P===0)break;e+=P}b.push({x:q,y:_,width:B,height:Y,has_local_palette:rt,palette_offset:tt,palette_size:z,data_offset:dt,data_length:e-dt,transparent_index:S,interlaced:!!ut,delay:y,disposal:p});break;case 59:g=!1;break;default:throw new Error("Unknown gif block: 0x"+r[e-1].toString(16))}this.numFrames=function(){return b.length},this.loopCount=function(){return O},this.frameInfo=function(k){if(k<0||k>=b.length)throw new Error("Frame index out of range.");return b[k]},this.decodeAndBlitFrameBGRA=function(k,W){var D=this.frameInfo(k),st=D.width*D.height,it=new Uint8Array(st);Nc(r,D.data_offset,it,st);var lt=D.palette_offset,$=D.transparent_index;$===null&&($=256);var ht=D.width,pt=n-ht,It=ht,N=4*(D.y*n+D.x),C=4*((D.y+D.height)*n+D.x),M=N,T=4*pt;D.interlaced===!0&&(T+=4*n*7);for(var J=8,Q=0,et=it.length;Q<et;++Q){var nt=it[Q];if(It===0&&(It=ht,(M+=T)>=C&&(T=4*pt+4*n*(J-1),M=N+(ht+pt)*(J<<1),J>>=1)),nt===$)M+=4;else{var At=r[lt+3*nt],Nt=r[lt+3*nt+1],Ft=r[lt+3*nt+2];W[M++]=Ft,W[M++]=Nt,W[M++]=At,W[M++]=255}--It}},this.decodeAndBlitFrameRGBA=function(k,W){var D=this.frameInfo(k),st=D.width*D.height,it=new Uint8Array(st);Nc(r,D.data_offset,it,st);var lt=D.palette_offset,$=D.transparent_index;$===null&&($=256);var ht=D.width,pt=n-ht,It=ht,N=4*(D.y*n+D.x),C=4*((D.y+D.height)*n+D.x),M=N,T=4*pt;D.interlaced===!0&&(T+=4*n*7);for(var J=8,Q=0,et=it.length;Q<et;++Q){var nt=it[Q];if(It===0&&(It=ht,(M+=T)>=C&&(T=4*pt+4*n*(J-1),M=N+(ht+pt)*(J<<1),J>>=1)),nt===$)M+=4;else{var At=r[lt+3*nt],Nt=r[lt+3*nt+1],Ft=r[lt+3*nt+2];W[M++]=At,W[M++]=Nt,W[M++]=Ft,W[M++]=255}--It}}}function Nc(r,e,n,a){for(var c=r[e++],o=1<<c,l=o+1,h=l+1,f=c+1,g=(1<<f)-1,b=0,y=0,S=0,p=r[e++],O=new Int32Array(4096),F=null;;){for(;b<16&&p!==0;)y|=r[e++]<<b,b+=8,p===1?p=r[e++]:--p;if(b<f)break;var q=y&g;if(y>>=f,b-=f,q!==o){if(q===l)break;for(var _=q<h?q:F,B=0,Y=_;Y>o;)Y=O[Y]>>8,++B;var ot=Y;if(S+B+(_!==q?1:0)>a)return void me.log("Warning, gif stream longer than expected.");n[S++]=ot;var ut=S+=B;for(_!==q&&(n[S++]=ot),Y=_;B--;)Y=O[Y],n[--ut]=255&Y,Y>>=8;F!==null&&h<4096&&(O[h++]=F<<8|ot,h>=g+1&&f<12&&(++f,g=g<<1|1)),F=q}else h=l+1,g=(1<<(f=c+1))-1,F=null}return S!==a&&me.log("Warning, gif stream shorter than expected."),n}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function ps(r){var e,n,a,c,o,l=Math.floor,h=new Array(64),f=new Array(64),g=new Array(64),b=new Array(64),y=new Array(65535),S=new Array(65535),p=new Array(64),O=new Array(64),F=[],q=0,_=7,B=new Array(64),Y=new Array(64),ot=new Array(64),ut=new Array(256),wt=new Array(2048),tt=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],z=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],rt=[0,1,2,3,4,5,6,7,8,9,10,11],dt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],P=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],k=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],W=[0,1,2,3,4,5,6,7,8,9,10,11],D=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],st=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function it(N,C){for(var M=0,T=0,J=new Array,Q=1;Q<=16;Q++){for(var et=1;et<=N[Q];et++)J[C[T]]=[],J[C[T]][0]=M,J[C[T]][1]=Q,T++,M++;M*=2}return J}function lt(N){for(var C=N[0],M=N[1]-1;M>=0;)C&1<<M&&(q|=1<<_),M--,--_<0&&(q==255?($(255),$(0)):$(q),_=7,q=0)}function $(N){F.push(N)}function ht(N){$(N>>8&255),$(255&N)}function pt(N,C,M,T,J){for(var Q,et=J[0],nt=J[240],At=function(Lt,xt){var Ct,kt,qt,Gt,Qt,te,ie,fe,Wt,ee,jt=0;for(Wt=0;Wt<8;++Wt){Ct=Lt[jt],kt=Lt[jt+1],qt=Lt[jt+2],Gt=Lt[jt+3],Qt=Lt[jt+4],te=Lt[jt+5],ie=Lt[jt+6];var Je=Ct+(fe=Lt[jt+7]),oe=Ct-fe,_n=kt+ie,pe=kt-ie,Le=qt+te,Un=qt-te,ce=Gt+Qt,Mr=Gt-Qt,Ae=Je+ce,Pn=Je-ce,nr=_n+Le,xe=_n-Le;Lt[jt]=Ae+nr,Lt[jt+4]=Ae-nr;var Jt=.707106781*(xe+Pn);Lt[jt+2]=Pn+Jt,Lt[jt+6]=Pn-Jt;var ue=.382683433*((Ae=Mr+Un)-(xe=pe+oe)),Er=.5411961*Ae+ue,We=1.306562965*xe+ue,Hn=.707106781*(nr=Un+pe),Wn=oe+Hn,zt=oe-Hn;Lt[jt+5]=zt+Er,Lt[jt+3]=zt-Er,Lt[jt+1]=Wn+We,Lt[jt+7]=Wn-We,jt+=8}for(jt=0,Wt=0;Wt<8;++Wt){Ct=Lt[jt],kt=Lt[jt+8],qt=Lt[jt+16],Gt=Lt[jt+24],Qt=Lt[jt+32],te=Lt[jt+40],ie=Lt[jt+48];var kn=Ct+(fe=Lt[jt+56]),Vn=Ct-fe,an=kt+ie,De=kt-ie,Be=qt+te,fn=qt-te,ei=Gt+Qt,rr=Gt-Qt,Fn=kn+ei,In=kn-ei,Cn=an+Be,Gn=an-Be;Lt[jt]=Fn+Cn,Lt[jt+32]=Fn-Cn;var bn=.707106781*(Gn+In);Lt[jt+16]=In+bn,Lt[jt+48]=In-bn;var Jn=.382683433*((Fn=rr+fn)-(Gn=De+Vn)),qr=.5411961*Fn+Jn,ni=1.306562965*Gn+Jn,ri=.707106781*(Cn=fn+De),ii=Vn+ri,ai=Vn-ri;Lt[jt+40]=ai+qr,Lt[jt+24]=ai-qr,Lt[jt+8]=ii+ni,Lt[jt+56]=ii-ni,jt++}for(Wt=0;Wt<64;++Wt)ee=Lt[Wt]*xt[Wt],p[Wt]=ee>0?ee+.5|0:ee-.5|0;return p}(N,C),Nt=0;Nt<64;++Nt)O[tt[Nt]]=At[Nt];var Ft=O[0]-M;M=O[0],Ft==0?lt(T[0]):(lt(T[S[Q=32767+Ft]]),lt(y[Q]));for(var _t=63;_t>0&&O[_t]==0;)_t--;if(_t==0)return lt(et),M;for(var Ut,ft=1;ft<=_t;){for(var E=ft;O[ft]==0&&ft<=_t;)++ft;var Kt=ft-E;if(Kt>=16){Ut=Kt>>4;for(var Et=1;Et<=Ut;++Et)lt(nt);Kt&=15}Q=32767+O[ft],lt(J[(Kt<<4)+S[Q]]),lt(y[Q]),ft++}return _t!=63&&lt(et),M}function It(N){N=Math.min(Math.max(N,1),100),o!=N&&(function(C){for(var M=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],T=0;T<64;T++){var J=l((M[T]*C+50)/100);J=Math.min(Math.max(J,1),255),h[tt[T]]=J}for(var Q=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],et=0;et<64;et++){var nt=l((Q[et]*C+50)/100);nt=Math.min(Math.max(nt,1),255),f[tt[et]]=nt}for(var At=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Nt=0,Ft=0;Ft<8;Ft++)for(var _t=0;_t<8;_t++)g[Nt]=1/(h[tt[Nt]]*At[Ft]*At[_t]*8),b[Nt]=1/(f[tt[Nt]]*At[Ft]*At[_t]*8),Nt++}(N<50?Math.floor(5e3/N):Math.floor(200-2*N)),o=N)}this.encode=function(N,C){C&&It(C),F=new Array,q=0,_=7,ht(65496),ht(65504),ht(16),$(74),$(70),$(73),$(70),$(0),$(1),$(1),$(0),ht(1),ht(1),$(0),$(0),function(){ht(65499),ht(132),$(0);for(var kt=0;kt<64;kt++)$(h[kt]);$(1);for(var qt=0;qt<64;qt++)$(f[qt])}(),function(kt,qt){ht(65472),ht(17),$(8),ht(qt),ht(kt),$(3),$(1),$(17),$(0),$(2),$(17),$(1),$(3),$(17),$(1)}(N.width,N.height),function(){ht(65476),ht(418),$(0);for(var kt=0;kt<16;kt++)$(z[kt+1]);for(var qt=0;qt<=11;qt++)$(rt[qt]);$(16);for(var Gt=0;Gt<16;Gt++)$(dt[Gt+1]);for(var Qt=0;Qt<=161;Qt++)$(P[Qt]);$(1);for(var te=0;te<16;te++)$(k[te+1]);for(var ie=0;ie<=11;ie++)$(W[ie]);$(17);for(var fe=0;fe<16;fe++)$(D[fe+1]);for(var Wt=0;Wt<=161;Wt++)$(st[Wt])}(),ht(65498),ht(12),$(3),$(1),$(0),$(2),$(17),$(3),$(17),$(0),$(63),$(0);var M=0,T=0,J=0;q=0,_=7,this.encode.displayName="_encode_";for(var Q,et,nt,At,Nt,Ft,_t,Ut,ft,E=N.data,Kt=N.width,Et=N.height,Lt=4*Kt,xt=0;xt<Et;){for(Q=0;Q<Lt;){for(Nt=Lt*xt+Q,_t=-1,Ut=0,ft=0;ft<64;ft++)Ft=Nt+(Ut=ft>>3)*Lt+(_t=4*(7&ft)),xt+Ut>=Et&&(Ft-=Lt*(xt+1+Ut-Et)),Q+_t>=Lt&&(Ft-=Q+_t-Lt+4),et=E[Ft++],nt=E[Ft++],At=E[Ft++],B[ft]=(wt[et]+wt[nt+256>>0]+wt[At+512>>0]>>16)-128,Y[ft]=(wt[et+768>>0]+wt[nt+1024>>0]+wt[At+1280>>0]>>16)-128,ot[ft]=(wt[et+1280>>0]+wt[nt+1536>>0]+wt[At+1792>>0]>>16)-128;M=pt(B,g,M,e,a),T=pt(Y,b,T,n,c),J=pt(ot,b,J,n,c),Q+=32}xt+=8}if(_>=0){var Ct=[];Ct[1]=_+1,Ct[0]=(1<<_+1)-1,lt(Ct)}return ht(65497),new Uint8Array(F)},r=r||50,function(){for(var N=String.fromCharCode,C=0;C<256;C++)ut[C]=N(C)}(),e=it(z,rt),n=it(k,W),a=it(dt,P),c=it(D,st),function(){for(var N=1,C=2,M=1;M<=15;M++){for(var T=N;T<C;T++)S[32767+T]=M,y[32767+T]=[],y[32767+T][1]=M,y[32767+T][0]=T;for(var J=-(C-1);J<=-N;J++)S[32767+J]=M,y[32767+J]=[],y[32767+J][1]=M,y[32767+J][0]=C-1+J;N<<=1,C<<=1}}(),function(){for(var N=0;N<256;N++)wt[N]=19595*N,wt[N+256>>0]=38470*N,wt[N+512>>0]=7471*N+32768,wt[N+768>>0]=-11059*N,wt[N+1024>>0]=-21709*N,wt[N+1280>>0]=32768*N+8421375,wt[N+1536>>0]=-27439*N,wt[N+1792>>0]=-5329*N}(),It(r)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function Dn(r,e){if(this.pos=0,this.buffer=r,this.datav=new DataView(r.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Ac(r){function e(z){if(!z)throw Error("assert :P")}function n(z,rt,dt){for(var P=0;4>P;P++)if(z[rt+P]!=dt.charCodeAt(P))return!0;return!1}function a(z,rt,dt,P,k){for(var W=0;W<k;W++)z[rt+W]=dt[P+W]}function c(z,rt,dt,P){for(var k=0;k<P;k++)z[rt+k]=dt}function o(z){return new Int32Array(z)}function l(z,rt){for(var dt=[],P=0;P<z;P++)dt.push(new rt);return dt}function h(z,rt){var dt=[];return function P(k,W,D){for(var st=D[W],it=0;it<st&&(k.push(D.length>W+1?[]:new rt),!(D.length<W+1));it++)P(k[it],W+1,D)}(dt,0,z),dt}var f=function(){var z=this;function rt(t,i){for(var u=1<<i-1>>>0;t&u;)u>>>=1;return u?(t&u-1)+u:t}function dt(t,i,u,d,m){e(!(d%u));do t[i+(d-=u)]=m;while(0<d)}function P(t,i,u,d,m){if(e(2328>=m),512>=m)var w=o(512);else if((w=o(m))==null)return 0;return function(L,A,x,I,U,K){var Z,G,vt=A,at=1<<x,H=o(16),V=o(16);for(e(U!=0),e(I!=null),e(L!=null),e(0<x),G=0;G<U;++G){if(15<I[G])return 0;++H[I[G]]}if(H[0]==U)return 0;for(V[1]=0,Z=1;15>Z;++Z){if(H[Z]>1<<Z)return 0;V[Z+1]=V[Z]+H[Z]}for(G=0;G<U;++G)Z=I[G],0<I[G]&&(K[V[Z]++]=G);if(V[15]==1)return(I=new k).g=0,I.value=K[0],dt(L,vt,1,at,I),at;var gt,bt=-1,mt=at-1,Bt=0,St=1,Rt=1,Pt=1<<x;for(G=0,Z=1,U=2;Z<=x;++Z,U<<=1){if(St+=Rt<<=1,0>(Rt-=H[Z]))return 0;for(;0<H[Z];--H[Z])(I=new k).g=Z,I.value=K[G++],dt(L,vt+Bt,U,Pt,I),Bt=rt(Bt,Z)}for(Z=x+1,U=2;15>=Z;++Z,U<<=1){if(St+=Rt<<=1,0>(Rt-=H[Z]))return 0;for(;0<H[Z];--H[Z]){if(I=new k,(Bt&mt)!=bt){for(vt+=Pt,gt=1<<(bt=Z)-x;15>bt&&!(0>=(gt-=H[bt]));)++bt,gt<<=1;at+=Pt=1<<(gt=bt-x),L[A+(bt=Bt&mt)].g=gt+x,L[A+bt].value=vt-A-bt}I.g=Z-x,I.value=K[G++],dt(L,vt+(Bt>>x),U,Pt,I),Bt=rt(Bt,Z)}}return St!=2*V[15]-1?0:at}(t,i,u,d,m,w)}function k(){this.value=this.g=0}function W(){this.value=this.g=0}function D(){this.G=l(5,k),this.H=o(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=l(Ve,W)}function st(t,i,u,d){e(t!=null),e(i!=null),e(2147483648>d),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=i,t.pa=u,t.Jd=i,t.Yc=u+d,t.Zc=4<=d?u+d-4+1:u,Q(t)}function it(t,i){for(var u=0;0<i--;)u|=nt(t,128)<<i;return u}function lt(t,i){var u=it(t,i);return et(t)?-u:u}function $(t,i,u,d){var m,w=0;for(e(t!=null),e(i!=null),e(4294967288>d),t.Sb=d,t.Ra=0,t.u=0,t.h=0,4<d&&(d=4),m=0;m<d;++m)w+=i[u+m]<<8*m;t.Ra=w,t.bb=d,t.oa=i,t.pa=u}function ht(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<wi-8>>>0,++t.bb,t.u-=8;M(t)&&(t.h=1,t.u=0)}function pt(t,i){if(e(0<=i),!t.h&&i<=yi){var u=C(t)&bi[i];return t.u+=i,ht(t),u}return t.h=1,t.u=0}function It(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function N(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function C(t){return t.Ra>>>(t.u&wi-1)>>>0}function M(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>wi}function T(t,i){t.u=i,t.h=M(t)}function J(t){t.u>=$i&&(e(t.u>=$i),ht(t))}function Q(t){e(t!=null&&t.oa!=null),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(t!=null&&t.oa!=null),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function et(t){return it(t,1)}function nt(t,i){var u=t.Ca;0>t.b&&Q(t);var d=t.b,m=u*i>>>8,w=(t.I>>>d>m)+0;for(w?(u-=m,t.I-=m+1<<d>>>0):u=m+1,d=u,m=0;256<=d;)m+=8,d>>=8;return d=7^m+sn[d],t.b-=d,t.Ca=(u<<d)-1,w}function At(t,i,u){t[i+0]=u>>24&255,t[i+1]=u>>16&255,t[i+2]=u>>8&255,t[i+3]=u>>0&255}function Nt(t,i){return t[i+0]<<0|t[i+1]<<8}function Ft(t,i){return Nt(t,i)|t[i+2]<<16}function _t(t,i){return Nt(t,i)|Nt(t,i+2)<<16}function Ut(t,i){var u=1<<i;return e(t!=null),e(0<i),t.X=o(u),t.X==null?0:(t.Mb=32-i,t.Xa=i,1)}function ft(t,i){e(t!=null),e(i!=null),e(t.Xa==i.Xa),a(i.X,0,t.X,0,1<<i.Xa)}function E(){this.X=[],this.Xa=this.Mb=0}function Kt(t,i,u,d){e(u!=null),e(d!=null);var m=u[0],w=d[0];return m==0&&(m=(t*w+i/2)/i),w==0&&(w=(i*m+t/2)/t),0>=m||0>=w?0:(u[0]=m,d[0]=w,1)}function Et(t,i){return t+(1<<i)-1>>>i}function Lt(t,i){return((4278255360&t)+(4278255360&i)>>>0&4278255360)+((16711935&t)+(16711935&i)>>>0&16711935)>>>0}function xt(t,i){z[i]=function(u,d,m,w,L,A,x){var I;for(I=0;I<L;++I){var U=z[t](A[x+I-1],m,w+I);A[x+I]=Lt(u[d+I],U)}}}function Ct(){this.ud=this.hd=this.jd=0}function kt(t,i){return((4278124286&(t^i))>>>1)+(t&i)>>>0}function qt(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function Gt(t,i){return qt(t+(t-i+.5>>1))}function Qt(t,i,u){return Math.abs(i-u)-Math.abs(t-u)}function te(t,i,u,d,m,w,L){for(d=w[L-1],u=0;u<m;++u)w[L+u]=d=Lt(t[i+u],d)}function ie(t,i,u,d,m){var w;for(w=0;w<u;++w){var L=t[i+w],A=L>>8&255,x=16711935&(x=(x=16711935&L)+((A<<16)+A));d[m+w]=(4278255360&L)+x>>>0}}function fe(t,i){i.jd=t>>0&255,i.hd=t>>8&255,i.ud=t>>16&255}function Wt(t,i,u,d,m,w){var L;for(L=0;L<d;++L){var A=i[u+L],x=A>>>8,I=A,U=255&(U=(U=A>>>16)+((t.jd<<24>>24)*(x<<24>>24)>>>5));I=255&(I=(I=I+((t.hd<<24>>24)*(x<<24>>24)>>>5))+((t.ud<<24>>24)*(U<<24>>24)>>>5)),m[w+L]=(4278255360&A)+(U<<16)+I}}function ee(t,i,u,d,m){z[i]=function(w,L,A,x,I,U,K,Z,G){for(x=K;x<Z;++x)for(K=0;K<G;++K)I[U++]=m(A[d(w[L++])])},z[t]=function(w,L,A,x,I,U,K){var Z=8>>w.b,G=w.Ea,vt=w.K[0],at=w.w;if(8>Z)for(w=(1<<w.b)-1,at=(1<<Z)-1;L<A;++L){var H,V=0;for(H=0;H<G;++H)H&w||(V=d(x[I++])),U[K++]=m(vt[V&at]),V>>=Z}else z["VP8LMapColor"+u](x,I,vt,at,U,K,L,A,G)}}function jt(t,i,u,d,m){for(u=i+u;i<u;){var w=t[i++];d[m++]=w>>16&255,d[m++]=w>>8&255,d[m++]=w>>0&255}}function Je(t,i,u,d,m){for(u=i+u;i<u;){var w=t[i++];d[m++]=w>>16&255,d[m++]=w>>8&255,d[m++]=w>>0&255,d[m++]=w>>24&255}}function oe(t,i,u,d,m){for(u=i+u;i<u;){var w=(L=t[i++])>>16&240|L>>12&15,L=L>>0&240|L>>28&15;d[m++]=w,d[m++]=L}}function _n(t,i,u,d,m){for(u=i+u;i<u;){var w=(L=t[i++])>>16&248|L>>13&7,L=L>>5&224|L>>3&31;d[m++]=w,d[m++]=L}}function pe(t,i,u,d,m){for(u=i+u;i<u;){var w=t[i++];d[m++]=w>>0&255,d[m++]=w>>8&255,d[m++]=w>>16&255}}function Le(t,i,u,d,m,w){if(w==0)for(u=i+u;i<u;)At(d,((w=t[i++])[0]>>24|w[1]>>8&65280|w[2]<<8&16711680|w[3]<<24)>>>0),m+=32;else a(d,m,t,i,u)}function Un(t,i){z[i][0]=z[t+"0"],z[i][1]=z[t+"1"],z[i][2]=z[t+"2"],z[i][3]=z[t+"3"],z[i][4]=z[t+"4"],z[i][5]=z[t+"5"],z[i][6]=z[t+"6"],z[i][7]=z[t+"7"],z[i][8]=z[t+"8"],z[i][9]=z[t+"9"],z[i][10]=z[t+"10"],z[i][11]=z[t+"11"],z[i][12]=z[t+"12"],z[i][13]=z[t+"13"],z[i][14]=z[t+"0"],z[i][15]=z[t+"0"]}function ce(t){return t==Vo||t==Go||t==Xa||t==Jo}function Mr(){this.eb=[],this.size=this.A=this.fb=0}function Ae(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function Pn(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new Mr,this.f.kb=new Ae,this.sd=null}function nr(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function xe(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Jt(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function ue(t,i){var u=t.T,d=i.ba.f.RGBA,m=d.eb,w=d.fb+t.ka*d.A,L=xn[i.ba.S],A=t.y,x=t.O,I=t.f,U=t.N,K=t.ea,Z=t.W,G=i.cc,vt=i.dc,at=i.Mc,H=i.Nc,V=t.ka,gt=t.ka+t.T,bt=t.U,mt=bt+1>>1;for(V==0?L(A,x,null,null,I,U,K,Z,I,U,K,Z,m,w,null,null,bt):(L(i.ec,i.fc,A,x,G,vt,at,H,I,U,K,Z,m,w-d.A,m,w,bt),++u);V+2<gt;V+=2)G=I,vt=U,at=K,H=Z,U+=t.Rc,Z+=t.Rc,w+=2*d.A,L(A,(x+=2*t.fa)-t.fa,A,x,G,vt,at,H,I,U,K,Z,m,w-d.A,m,w,bt);return x+=t.fa,t.j+gt<t.o?(a(i.ec,i.fc,A,x,bt),a(i.cc,i.dc,I,U,mt),a(i.Mc,i.Nc,K,Z,mt),u--):1&gt||L(A,x,null,null,I,U,K,Z,I,U,K,Z,m,w+d.A,null,null,bt),u}function Er(t,i,u){var d=t.F,m=[t.J];if(d!=null){var w=t.U,L=i.ba.S,A=L==Ya||L==Xa;i=i.ba.f.RGBA;var x=[0],I=t.ka;x[0]=t.T,t.Kb&&(I==0?--x[0]:(--I,m[0]-=t.width),t.j+t.ka+t.T==t.o&&(x[0]=t.o-t.j-I));var U=i.eb;I=i.fb+I*i.A,t=ye(d,m[0],t.width,w,x,U,I+(A?0:3),i.A),e(u==x),t&&ce(L)&&Nn(U,I,A,w,x,i.A)}return 0}function We(t){var i=t.ma,u=i.ba.S,d=11>u,m=u==Ga||u==Ja||u==Ya||u==Wo||u==12||ce(u);if(i.memory=null,i.Ib=null,i.Jb=null,i.Nd=null,!Ki(i.Oa,t,m?11:12))return 0;if(m&&ce(u)&&yt(),t.da)alert("todo:use_scaling");else{if(d){if(i.Ib=Jt,t.Kb){if(u=t.U+1>>1,i.memory=o(t.U+2*u),i.memory==null)return 0;i.ec=i.memory,i.fc=0,i.cc=i.ec,i.dc=i.fc+t.U,i.Mc=i.cc,i.Nc=i.dc+u,i.Ib=ue,yt()}}else alert("todo:EmitYUV");m&&(i.Jb=Er,d&&X())}if(d&&!Ts){for(t=0;256>t;++t)lu[t]=89858*(t-128)+Za>>Ka,du[t]=-22014*(t-128)+Za,fu[t]=-45773*(t-128),hu[t]=113618*(t-128)+Za>>Ka;for(t=aa;t<Ko;++t)i=76283*(t-16)+Za>>Ka,pu[t-aa]=dn(i,255),gu[t-aa]=dn(i+8>>4,15);Ts=1}return 1}function Hn(t){var i=t.ma,u=t.U,d=t.T;return e(!(1&t.ka)),0>=u||0>=d?0:(u=i.Ib(t,i),i.Jb!=null&&i.Jb(t,i,u),i.Dc+=u,1)}function Wn(t){t.ma.memory=null}function zt(t,i,u,d){return pt(t,8)!=47?0:(i[0]=pt(t,14)+1,u[0]=pt(t,14)+1,d[0]=pt(t,1),pt(t,3)!=0?0:!t.h)}function kn(t,i){if(4>t)return t+1;var u=t-2>>1;return(2+(1&t)<<u)+pt(i,u)+1}function Vn(t,i){return 120<i?i-120:1<=(u=((u=Kc[i-1])>>4)*t+(8-(15&u)))?u:1;var u}function an(t,i,u){var d=C(u),m=t[i+=255&d].g-8;return 0<m&&(T(u,u.u+8),d=C(u),i+=t[i].value,i+=d&(1<<m)-1),T(u,u.u+t[i].g),t[i].value}function De(t,i,u){return u.g+=t.g,u.value+=t.value<<i>>>0,e(8>=u.g),t.g}function Be(t,i,u){var d=t.xc;return e((i=d==0?0:t.vc[t.md*(u>>d)+(i>>d)])<t.Wb),t.Ya[i]}function fn(t,i,u,d){var m=t.ab,w=t.c*i,L=t.C;i=L+i;var A=u,x=d;for(d=t.Ta,u=t.Ua;0<m--;){var I=t.gc[m],U=L,K=i,Z=A,G=x,vt=(x=d,A=u,I.Ea);switch(e(U<K),e(K<=I.nc),I.hc){case 2:Ra(Z,G,(K-U)*vt,x,A);break;case 0:var at=U,H=K,V=x,gt=A,bt=(Pt=I).Ea;at==0&&(Uo(Z,G,null,null,1,V,gt),te(Z,G+1,0,0,bt-1,V,gt+1),G+=bt,gt+=bt,++at);for(var mt=1<<Pt.b,Bt=mt-1,St=Et(bt,Pt.b),Rt=Pt.K,Pt=Pt.w+(at>>Pt.b)*St;at<H;){var se=Rt,le=Pt,ae=1;for(Qi(Z,G,V,gt-bt,1,V,gt);ae<bt;){var ne=(ae&~Bt)+mt;ne>bt&&(ne=bt),(0,vr[se[le++]>>8&15])(Z,G+ +ae,V,gt+ae-bt,ne-ae,V,gt+ae),ae=ne}G+=bt,gt+=bt,++at&Bt||(Pt+=St)}K!=I.nc&&a(x,A-vt,x,A+(K-U-1)*vt,vt);break;case 1:for(vt=Z,H=G,bt=(Z=I.Ea)-(gt=Z&~(V=(G=1<<I.b)-1)),at=Et(Z,I.b),mt=I.K,I=I.w+(U>>I.b)*at;U<K;){for(Bt=mt,St=I,Rt=new Ct,Pt=H+gt,se=H+Z;H<Pt;)fe(Bt[St++],Rt),Ur(Rt,vt,H,G,x,A),H+=G,A+=G;H<se&&(fe(Bt[St++],Rt),Ur(Rt,vt,H,bt,x,A),H+=bt,A+=bt),++U&V||(I+=at)}break;case 3:if(Z==x&&G==A&&0<I.b){for(H=x,Z=vt=A+(K-U)*vt-(gt=(K-U)*Et(I.Ea,I.b)),G=x,V=A,at=[],gt=(bt=gt)-1;0<=gt;--gt)at[gt]=G[V+gt];for(gt=bt-1;0<=gt;--gt)H[Z+gt]=at[gt];yn(I,U,K,x,vt,x,A)}else yn(I,U,K,Z,G,x,A)}A=d,x=u}x!=u&&a(d,u,A,x,w)}function ei(t,i){var u=t.V,d=t.Ba+t.c*t.C,m=i-t.C;if(e(i<=t.l.o),e(16>=m),0<m){var w=t.l,L=t.Ta,A=t.Ua,x=w.width;if(fn(t,m,u,d),m=A=[A],e((u=t.C)<(d=i)),e(w.v<w.va),d>w.o&&(d=w.o),u<w.j){var I=w.j-u;u=w.j,m[0]+=I*x}if(u>=d?u=0:(m[0]+=4*w.v,w.ka=u-w.j,w.U=w.va-w.v,w.T=d-u,u=1),u){if(A=A[0],11>(u=t.ca).S){var U=u.f.RGBA,K=(d=u.S,m=w.U,w=w.T,I=U.eb,U.A),Z=w;for(U=U.fb+t.Ma*U.A;0<Z--;){var G=L,vt=A,at=m,H=I,V=U;switch(d){case Va:cn(G,vt,at,H,V);break;case Ga:en(G,vt,at,H,V);break;case Vo:en(G,vt,at,H,V),Nn(H,V,0,at,1,0);break;case Is:sr(G,vt,at,H,V);break;case Ja:Le(G,vt,at,H,V,1);break;case Go:Le(G,vt,at,H,V,1),Nn(H,V,0,at,1,0);break;case Ya:Le(G,vt,at,H,V,0);break;case Xa:Le(G,vt,at,H,V,0),Nn(H,V,1,at,1,0);break;case Wo:br(G,vt,at,H,V);break;case Jo:br(G,vt,at,H,V),be(H,V,at,1,0);break;case Cs:or(G,vt,at,H,V);break;default:e(0)}A+=x,U+=K}t.Ma+=w}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=u.height)}}t.C=i,e(t.C<=t.i)}function rr(t){var i;if(0<t.ua)return 0;for(i=0;i<t.Wb;++i){var u=t.Ya[i].G,d=t.Ya[i].H;if(0<u[1][d[1]+0].g||0<u[2][d[2]+0].g||0<u[3][d[3]+0].g)return 0}return 1}function Fn(t,i,u,d,m,w){if(t.Z!=0){var L=t.qd,A=t.rd;for(e(Nr[t.Z]!=null);i<u;++i)Nr[t.Z](L,A,d,m,d,m,w),L=d,A=m,m+=w;t.qd=L,t.rd=A}}function In(t,i){var u=t.l.ma,d=u.Z==0||u.Z==1?t.l.j:t.C;if(d=t.C<d?d:t.C,e(i<=t.l.o),i>d){var m=t.l.width,w=u.ca,L=u.tb+m*d,A=t.V,x=t.Ba+t.c*d,I=t.gc;e(t.ab==1),e(I[0].hc==3),Ta(I[0],d,i,A,x,w,L),Fn(u,d,i,w,L,m)}t.C=t.Ma=i}function Cn(t,i,u,d,m,w,L){var A=t.$/d,x=t.$%d,I=t.m,U=t.s,K=u+t.$,Z=K;m=u+d*m;var G=u+d*w,vt=280+U.ua,at=t.Pb?A:16777216,H=0<U.ua?U.Wa:null,V=U.wc,gt=K<G?Be(U,x,A):null;e(t.C<w),e(G<=m);var bt=!1;t:for(;;){for(;bt||K<G;){var mt=0;if(A>=at){var Bt=K-u;e((at=t).Pb),at.wd=at.m,at.xd=Bt,0<at.s.ua&&ft(at.s.Wa,at.s.vb),at=A+$c}if(x&V||(gt=Be(U,x,A)),e(gt!=null),gt.Qb&&(i[K]=gt.qb,bt=!0),!bt)if(J(I),gt.jc){mt=I,Bt=i;var St=K,Rt=gt.pd[C(mt)&Ve-1];e(gt.jc),256>Rt.g?(T(mt,mt.u+Rt.g),Bt[St]=Rt.value,mt=0):(T(mt,mt.u+Rt.g-256),e(256<=Rt.value),mt=Rt.value),mt==0&&(bt=!0)}else mt=an(gt.G[0],gt.H[0],I);if(I.h)break;if(bt||256>mt){if(!bt)if(gt.nd)i[K]=(gt.qb|mt<<8)>>>0;else{if(J(I),bt=an(gt.G[1],gt.H[1],I),J(I),Bt=an(gt.G[2],gt.H[2],I),St=an(gt.G[3],gt.H[3],I),I.h)break;i[K]=(St<<24|bt<<16|mt<<8|Bt)>>>0}if(bt=!1,++K,++x>=d&&(x=0,++A,L!=null&&A<=w&&!(A%16)&&L(t,A),H!=null))for(;Z<K;)mt=i[Z++],H.X[(506832829*mt&**********)>>>H.Mb]=mt}else if(280>mt){if(mt=kn(mt-256,I),Bt=an(gt.G[4],gt.H[4],I),J(I),Bt=Vn(d,Bt=kn(Bt,I)),I.h)break;if(K-u<Bt||m-K<mt)break t;for(St=0;St<mt;++St)i[K+St]=i[K+St-Bt];for(K+=mt,x+=mt;x>=d;)x-=d,++A,L!=null&&A<=w&&!(A%16)&&L(t,A);if(e(K<=m),x&V&&(gt=Be(U,x,A)),H!=null)for(;Z<K;)mt=i[Z++],H.X[(506832829*mt&**********)>>>H.Mb]=mt}else{if(!(mt<vt))break t;for(bt=mt-280,e(H!=null);Z<K;)mt=i[Z++],H.X[(506832829*mt&**********)>>>H.Mb]=mt;mt=K,e(!(bt>>>(Bt=H).Xa)),i[mt]=Bt.X[bt],bt=!0}bt||e(I.h==M(I))}if(t.Pb&&I.h&&K<m)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&ft(t.s.vb,t.s.Wa);else{if(I.h)break t;L!=null&&L(t,A>w?w:A),t.a=0,t.$=K-u}return 1}return t.a=3,0}function Gn(t){e(t!=null),t.vc=null,t.yc=null,t.Ya=null;var i=t.Wa;i!=null&&(i.X=null),t.vb=null,e(t!=null)}function bn(){var t=new zo;return t==null?null:(t.a=0,t.xb=Bs,Un("Predictor","VP8LPredictors"),Un("Predictor","VP8LPredictors_C"),Un("PredictorAdd","VP8LPredictorsAdd"),Un("PredictorAdd","VP8LPredictorsAdd_C"),Ra=ie,Ur=Wt,cn=jt,en=Je,br=oe,or=_n,sr=pe,z.VP8LMapColor32b=Li,z.VP8LMapColor8b=za,t)}function Jn(t,i,u,d,m){var w=1,L=[t],A=[i],x=d.m,I=d.s,U=null,K=0;t:for(;;){if(u)for(;w&&pt(x,1);){var Z=L,G=A,vt=d,at=1,H=vt.m,V=vt.gc[vt.ab],gt=pt(H,2);if(vt.Oc&1<<gt)w=0;else{switch(vt.Oc|=1<<gt,V.hc=gt,V.Ea=Z[0],V.nc=G[0],V.K=[null],++vt.ab,e(4>=vt.ab),gt){case 0:case 1:V.b=pt(H,3)+2,at=Jn(Et(V.Ea,V.b),Et(V.nc,V.b),0,vt,V.K),V.K=V.K[0];break;case 3:var bt,mt=pt(H,8)+1,Bt=16<mt?0:4<mt?1:2<mt?2:3;if(Z[0]=Et(V.Ea,Bt),V.b=Bt,bt=at=Jn(mt,1,0,vt,V.K)){var St,Rt=mt,Pt=V,se=1<<(8>>Pt.b),le=o(se);if(le==null)bt=0;else{var ae=Pt.K[0],ne=Pt.w;for(le[0]=Pt.K[0][0],St=1;St<1*Rt;++St)le[St]=Lt(ae[ne+St],le[St-1]);for(;St<4*se;++St)le[St]=0;Pt.K[0]=null,Pt.K[0]=le,bt=1}}at=bt;break;case 2:break;default:e(0)}w=at}}if(L=L[0],A=A[0],w&&pt(x,1)&&!(w=1<=(K=pt(x,4))&&11>=K)){d.a=3;break t}var ge;if(ge=w)e:{var de,$t,Ee,un=d,qe=L,ln=A,he=K,gn=u,mn=un.m,Ue=un.s,Ge=[null],rn=1,Sn=0,Zn=Zc[he];n:for(;;){if(gn&&pt(mn,1)){var He=pt(mn,3)+2,hr=Et(qe,He),Jr=Et(ln,He),_i=hr*Jr;if(!Jn(hr,Jr,0,un,Ge))break n;for(Ge=Ge[0],Ue.xc=He,de=0;de<_i;++de){var Ar=Ge[de]>>8&65535;Ge[de]=Ar,Ar>=rn&&(rn=Ar+1)}}if(mn.h)break n;for($t=0;5>$t;++$t){var _e=js[$t];!$t&&0<he&&(_e+=1<<he),Sn<_e&&(Sn=_e)}var Zo=l(rn*Zn,k),Hs=rn,Ws=l(Hs,D);if(Ws==null)var Qa=null;else e(65536>=Hs),Qa=Ws;var oa=o(Sn);if(Qa==null||oa==null||Zo==null){un.a=1;break n}var to=Zo;for(de=Ee=0;de<rn;++de){var En=Qa[de],Pi=En.G,ki=En.H,Vs=0,eo=1,Gs=0;for($t=0;5>$t;++$t){_e=js[$t],Pi[$t]=to,ki[$t]=Ee,!$t&&0<he&&(_e+=1<<he);i:{var no,$o=_e,ro=un,sa=oa,bu=to,yu=Ee,Qo=0,xr=ro.m,wu=pt(xr,1);if(c(sa,0,0,$o),wu){var Lu=pt(xr,1)+1,Nu=pt(xr,1),Js=pt(xr,Nu==0?1:8);sa[Js]=1,Lu==2&&(sa[Js=pt(xr,8)]=1);var io=1}else{var Ys=o(19),Xs=pt(xr,4)+4;if(19<Xs){ro.a=3;var ao=0;break i}for(no=0;no<Xs;++no)Ys[Xc[no]]=pt(xr,3);var ts=void 0,ca=void 0,Ks=ro,Au=Ys,oo=$o,Zs=sa,es=0,Sr=Ks.m,$s=8,Qs=l(128,k);r:for(;P(Qs,0,7,Au,19);){if(pt(Sr,1)){var xu=2+2*pt(Sr,3);if((ts=2+pt(Sr,xu))>oo)break r}else ts=oo;for(ca=0;ca<oo&&ts--;){J(Sr);var tc=Qs[0+(127&C(Sr))];T(Sr,Sr.u+tc.g);var Fi=tc.value;if(16>Fi)Zs[ca++]=Fi,Fi!=0&&($s=Fi);else{var Su=Fi==16,ec=Fi-16,_u=Jc[ec],nc=pt(Sr,Gc[ec])+_u;if(ca+nc>oo)break r;for(var Pu=Su?$s:0;0<nc--;)Zs[ca++]=Pu}}es=1;break r}es||(Ks.a=3),io=es}(io=io&&!xr.h)&&(Qo=P(bu,yu,8,sa,$o)),io&&Qo!=0?ao=Qo:(ro.a=3,ao=0)}if(ao==0)break n;if(eo&&Yc[$t]==1&&(eo=to[Ee].g==0),Vs+=to[Ee].g,Ee+=ao,3>=$t){var ua,ns=oa[0];for(ua=1;ua<_e;++ua)oa[ua]>ns&&(ns=oa[ua]);Gs+=ns}}if(En.nd=eo,En.Qb=0,eo&&(En.qb=(Pi[3][ki[3]+0].value<<24|Pi[1][ki[1]+0].value<<16|Pi[2][ki[2]+0].value)>>>0,Vs==0&&256>Pi[0][ki[0]+0].value&&(En.Qb=1,En.qb+=Pi[0][ki[0]+0].value<<8)),En.jc=!En.Qb&&6>Gs,En.jc){var so,fr=En;for(so=0;so<Ve;++so){var _r=so,Pr=fr.pd[_r],co=fr.G[0][fr.H[0]+_r];256<=co.value?(Pr.g=co.g+256,Pr.value=co.value):(Pr.g=0,Pr.value=0,_r>>=De(co,8,Pr),_r>>=De(fr.G[1][fr.H[1]+_r],16,Pr),_r>>=De(fr.G[2][fr.H[2]+_r],0,Pr),De(fr.G[3][fr.H[3]+_r],24,Pr))}}}Ue.vc=Ge,Ue.Wb=rn,Ue.Ya=Qa,Ue.yc=Zo,ge=1;break e}ge=0}if(!(w=ge)){d.a=3;break t}if(0<K){if(I.ua=1<<K,!Ut(I.Wa,K)){d.a=1,w=0;break t}}else I.ua=0;var rs=d,rc=L,ku=A,is=rs.s,as=is.xc;if(rs.c=rc,rs.i=ku,is.md=Et(rc,as),is.wc=as==0?-1:(1<<as)-1,u){d.xb=au;break t}if((U=o(L*A))==null){d.a=1,w=0;break t}w=(w=Cn(d,U,0,L,A,A,null))&&!x.h;break t}return w?(m!=null?m[0]=U:(e(U==null),e(u)),d.$=0,u||Gn(I)):Gn(I),w}function qr(t,i){var u=t.c*t.i,d=u+i+16*i;return e(t.c<=i),t.V=o(d),t.V==null?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+u+i,1)}function ni(t,i){var u=t.C,d=i-u,m=t.V,w=t.Ba+t.c*u;for(e(i<=t.l.o);0<d;){var L=16<d?16:d,A=t.l.ma,x=t.l.width,I=x*L,U=A.ca,K=A.tb+x*u,Z=t.Ta,G=t.Ua;fn(t,L,m,w),Ie(Z,G,U,K,I),Fn(A,u,u+L,U,K,x),d-=L,m+=L*t.c,u+=L}e(u==i),t.C=t.Ma=i}function ri(){this.ub=this.yd=this.td=this.Rb=0}function ii(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function ai(){this.Fb=this.Bb=this.Cb=0,this.Zb=o(4),this.Lb=o(4)}function ya(){this.Yb=function(){var t=[];return function i(u,d,m){for(var w=m[d],L=0;L<w&&(u.push(m.length>d+1?[]:0),!(m.length<d+1));L++)i(u[L],d+1,m)}(t,0,[3,11]),t}()}function bo(){this.jb=o(3),this.Wc=h([4,8],ya),this.Xc=h([4,17],ya)}function yo(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new o(4),this.od=new o(4)}function oi(){this.ld=this.La=this.dd=this.tc=0}function wa(){this.Na=this.la=0}function wo(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Bi(){this.ad=o(384),this.Za=0,this.Ob=o(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function Lo(){this.uc=this.M=this.Nb=0,this.wa=Array(new oi),this.Y=0,this.ya=Array(new Bi),this.aa=0,this.l=new si}function La(){this.y=o(16),this.f=o(8),this.ea=o(8)}function No(){this.cb=this.a=0,this.sc="",this.m=new It,this.Od=new ri,this.Kc=new ii,this.ed=new yo,this.Qa=new ai,this.Ic=this.$c=this.Aa=0,this.D=new Lo,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=l(8,It),this.ia=0,this.pb=l(4,wo),this.Pa=new bo,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new La),this.Hd=0,this.rb=Array(new wa),this.sb=0,this.wa=Array(new oi),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Bi),this.L=this.aa=0,this.gd=h([4,2],oi),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function si(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Ao(){var t=new No;return t!=null&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,ia||(ia=xa)),t}function ke(t,i,u){return t.a==0&&(t.a=i,t.sc=u,t.cb=0),0}function Na(t,i,u){return 3<=u&&t[i+0]==157&&t[i+1]==1&&t[i+2]==42}function Aa(t,i){if(t==null)return 0;if(t.a=0,t.sc="OK",i==null)return ke(t,2,"null VP8Io passed to VP8GetHeaders()");var u=i.data,d=i.w,m=i.ha;if(4>m)return ke(t,7,"Truncated header.");var w=u[d+0]|u[d+1]<<8|u[d+2]<<16,L=t.Od;if(L.Rb=!(1&w),L.td=w>>1&7,L.yd=w>>4&1,L.ub=w>>5,3<L.td)return ke(t,3,"Incorrect keyframe parameters.");if(!L.yd)return ke(t,4,"Frame not displayable.");d+=3,m-=3;var A=t.Kc;if(L.Rb){if(7>m)return ke(t,7,"cannot parse picture header");if(!Na(u,d,m))return ke(t,3,"Bad code word");A.c=16383&(u[d+4]<<8|u[d+3]),A.Td=u[d+4]>>6,A.i=16383&(u[d+6]<<8|u[d+5]),A.Ud=u[d+6]>>6,d+=7,m-=7,t.za=A.c+15>>4,t.Ub=A.i+15>>4,i.width=A.c,i.height=A.i,i.Da=0,i.j=0,i.v=0,i.va=i.width,i.o=i.height,i.da=0,i.ib=i.width,i.hb=i.height,i.U=i.width,i.T=i.height,c((w=t.Pa).jb,0,255,w.jb.length),e((w=t.Qa)!=null),w.Cb=0,w.Bb=0,w.Fb=1,c(w.Zb,0,0,w.Zb.length),c(w.Lb,0,0,w.Lb)}if(L.ub>m)return ke(t,7,"bad partition length");st(w=t.m,u,d,L.ub),d+=L.ub,m-=L.ub,L.Rb&&(A.Ld=et(w),A.Kd=et(w)),A=t.Qa;var x,I=t.Pa;if(e(w!=null),e(A!=null),A.Cb=et(w),A.Cb){if(A.Bb=et(w),et(w)){for(A.Fb=et(w),x=0;4>x;++x)A.Zb[x]=et(w)?lt(w,7):0;for(x=0;4>x;++x)A.Lb[x]=et(w)?lt(w,6):0}if(A.Bb)for(x=0;3>x;++x)I.jb[x]=et(w)?it(w,8):255}else A.Bb=0;if(w.Ka)return ke(t,3,"cannot parse segment header");if((A=t.ed).zd=et(w),A.Tb=it(w,6),A.wb=it(w,3),A.Pc=et(w),A.Pc&&et(w)){for(I=0;4>I;++I)et(w)&&(A.vd[I]=lt(w,6));for(I=0;4>I;++I)et(w)&&(A.od[I]=lt(w,6))}if(t.L=A.Tb==0?0:A.zd?1:2,w.Ka)return ke(t,3,"cannot parse filter header");var U=m;if(m=x=d,d=x+U,A=U,t.Xb=(1<<it(t.m,2))-1,U<3*(I=t.Xb))u=7;else{for(x+=3*I,A-=3*I,U=0;U<I;++U){var K=u[m+0]|u[m+1]<<8|u[m+2]<<16;K>A&&(K=A),st(t.Jc[+U],u,x,K),x+=K,A-=K,m+=3}st(t.Jc[+I],u,x,A),u=x<d?0:5}if(u!=0)return ke(t,u,"cannot parse partitions");for(u=it(x=t.m,7),m=et(x)?lt(x,4):0,d=et(x)?lt(x,4):0,A=et(x)?lt(x,4):0,I=et(x)?lt(x,4):0,x=et(x)?lt(x,4):0,U=t.Qa,K=0;4>K;++K){if(U.Cb){var Z=U.Zb[K];U.Fb||(Z+=u)}else{if(0<K){t.pb[K]=t.pb[0];continue}Z=u}var G=t.pb[K];G.Sc[0]=Yo[dn(Z+m,127)],G.Sc[1]=Xo[dn(Z+0,127)],G.Eb[0]=2*Yo[dn(Z+d,127)],G.Eb[1]=101581*Xo[dn(Z+A,127)]>>16,8>G.Eb[1]&&(G.Eb[1]=8),G.Qc[0]=Yo[dn(Z+I,117)],G.Qc[1]=Xo[dn(Z+x,127)],G.lc=Z+x}if(!L.Rb)return ke(t,4,"Not a key frame.");for(et(w),L=t.Pa,u=0;4>u;++u){for(m=0;8>m;++m)for(d=0;3>d;++d)for(A=0;11>A;++A)I=nt(w,ru[u][m][d][A])?it(w,8):eu[u][m][d][A],L.Wc[u][m].Yb[d][A]=I;for(m=0;17>m;++m)L.Xc[u][m]=L.Wc[u][iu[m]]}return t.kc=et(w),t.kc&&(t.Bd=it(w,8)),t.cb=1}function xa(t,i,u,d,m,w,L){var A=i[m].Yb[u];for(u=0;16>m;++m){if(!nt(t,A[u+0]))return m;for(;!nt(t,A[u+1]);)if(A=i[++m].Yb[0],u=0,m==16)return 16;var x=i[m+1].Yb;if(nt(t,A[u+2])){var I=t,U=0;if(nt(I,(Z=A)[(K=u)+3]))if(nt(I,Z[K+6])){for(A=0,K=2*(U=nt(I,Z[K+8]))+(Z=nt(I,Z[K+9+U])),U=0,Z=Qc[K];Z[A];++A)U+=U+nt(I,Z[A]);U+=3+(8<<K)}else nt(I,Z[K+7])?(U=7+2*nt(I,165),U+=nt(I,145)):U=5+nt(I,159);else U=nt(I,Z[K+4])?3+nt(I,Z[K+5]):2;A=x[2]}else U=1,A=x[1];x=L+tu[m],0>(I=t).b&&Q(I);var K,Z=I.b,G=(K=I.Ca>>1)-(I.I>>Z)>>31;--I.b,I.Ca+=G,I.Ca|=1,I.I-=(K+1&G)<<Z,w[x]=((U^G)-G)*d[(0<m)+0]}return 16}function Mi(t){var i=t.rb[t.sb-1];i.la=0,i.Na=0,c(t.zc,0,0,t.zc.length),t.ja=0}function xo(t,i){if(t==null)return 0;if(i==null)return ke(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!Aa(t,i))return 0;if(e(t.cb),i.ac==null||i.ac(i)){i.ob&&(t.L=0);var u=$a[t.L];if(t.L==2?(t.yb=0,t.zb=0):(t.yb=i.v-u>>4,t.zb=i.j-u>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=i.o+15+u>>4,t.Hb=i.va+15+u>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var d=t.ed;for(u=0;4>u;++u){var m;if(t.Qa.Cb){var w=t.Qa.Lb[u];t.Qa.Fb||(w+=d.Tb)}else w=d.Tb;for(m=0;1>=m;++m){var L=t.gd[u][m],A=w;if(d.Pc&&(A+=d.vd[0],m&&(A+=d.od[0])),0<(A=0>A?0:63<A?63:A)){var x=A;0<d.wb&&(x=4<d.wb?x>>2:x>>1)>9-d.wb&&(x=9-d.wb),1>x&&(x=1),L.dd=x,L.tc=2*A+x,L.ld=40<=A?2:15<=A?1:0}else L.tc=0;L.La=m}}}u=0}else ke(t,6,"Frame setup failed"),u=t.a;if(u=u==0){if(u){t.$c=0,0<t.Aa||(t.Ic=vu);t:{u=t.Ic,d=4*(x=t.za);var I=32*x,U=x+1,K=0<t.L?x*(0<t.Aa?2:1):0,Z=(t.Aa==2?2:1)*x;if((L=d+832+(m=3*(16*u+$a[t.L])/2*I)+(w=t.Fa!=null&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=L)u=0;else{if(L>t.Vb){if(t.Vb=0,t.Ec=o(L),t.Fc=0,t.Ec==null){u=ke(t,1,"no memory during frame initialization.");break t}t.Vb=L}L=t.Ec,A=t.Fc,t.Ac=L,t.Bc=A,A+=d,t.Gd=l(I,La),t.Hd=0,t.rb=l(U+1,wa),t.sb=1,t.wa=K?l(K,oi):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=x),e(!0),t.oc=L,t.pc=A,A+=832,t.ya=l(Z,Bi),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,t.Aa==2&&(t.D.aa+=x),t.R=16*x,t.B=8*x,x=(I=$a[t.L])*t.R,I=I/2*t.B,t.sa=L,t.ta=A+x,t.qa=t.sa,t.ra=t.ta+16*u*t.R+I,t.Ha=t.qa,t.Ia=t.ra+8*u*t.B+I,t.$c=0,A+=m,t.mb=w?L:null,t.nb=w?A:null,e(A+w<=t.Fc+t.Vb),Mi(t),c(t.Ac,t.Bc,0,d),u=1}}if(u){if(i.ka=0,i.y=t.sa,i.O=t.ta,i.f=t.qa,i.N=t.ra,i.ea=t.Ha,i.Vd=t.Ia,i.fa=t.R,i.Rc=t.B,i.F=null,i.J=0,!Ha){for(u=-255;255>=u;++u)Re[255+u]=0>u?-u:u;for(u=-1020;1020>=u;++u)ur[1020+u]=-128>u?-128:127<u?127:u;for(u=-112;112>=u;++u)ra[112+u]=-16>u?-16:15<u?15:u;for(u=-255;510>=u;++u)Si[255+u]=0>u?0:255<u?255:u;Ha=1}Ni=Po,cr=So,ta=_a,nn=_o,wn=Pa,Fe=Sa,Ai=zi,Ua=Tr,ea=To,Hr=Ui,Wr=Ro,yr=di,Vr=Hi,xi=Ea,Gr=Ma,wr=Xn,na=ar,Ln=Do,Mn[0]=Yn,Mn[1]=ko,Mn[2]=jo,Mn[3]=Oo,Mn[4]=Ia,Mn[5]=hi,Mn[6]=Ca,Mn[7]=Di,Mn[8]=Mo,Mn[9]=Bo,Lr[0]=ka,Lr[1]=Io,Lr[2]=ir,Lr[3]=ui,Lr[4]=Ye,Lr[5]=Co,Lr[6]=Fa,lr[0]=pr,lr[1]=Fo,lr[2]=Eo,lr[3]=Ri,lr[4]=Rr,lr[5]=qo,lr[6]=Ti,u=1}else u=0}u&&(u=function(G,vt){for(G.M=0;G.M<G.Va;++G.M){var at,H=G.Jc[G.M&G.Xb],V=G.m,gt=G;for(at=0;at<gt.za;++at){var bt=V,mt=gt,Bt=mt.Ac,St=mt.Bc+4*at,Rt=mt.zc,Pt=mt.ya[mt.aa+at];if(mt.Qa.Bb?Pt.$b=nt(bt,mt.Pa.jb[0])?2+nt(bt,mt.Pa.jb[2]):nt(bt,mt.Pa.jb[1]):Pt.$b=0,mt.kc&&(Pt.Ad=nt(bt,mt.Bd)),Pt.Za=!nt(bt,145)+0,Pt.Za){var se=Pt.Ob,le=0;for(mt=0;4>mt;++mt){var ae,ne=Rt[0+mt];for(ae=0;4>ae;++ae){ne=nu[Bt[St+ae]][ne];for(var ge=Os[nt(bt,ne[0])];0<ge;)ge=Os[2*ge+nt(bt,ne[ge])];ne=-ge,Bt[St+ae]=ne}a(se,le,Bt,St,4),le+=4,Rt[0+mt]=ne}}else ne=nt(bt,156)?nt(bt,128)?1:3:nt(bt,163)?2:0,Pt.Ob[0]=ne,c(Bt,St,ne,4),c(Rt,0,ne,4);Pt.Dd=nt(bt,142)?nt(bt,114)?nt(bt,183)?1:3:2:0}if(gt.m.Ka)return ke(G,7,"Premature end-of-partition0 encountered.");for(;G.ja<G.za;++G.ja){if(gt=H,bt=(V=G).rb[V.sb-1],Bt=V.rb[V.sb+V.ja],at=V.ya[V.aa+V.ja],St=V.kc?at.Ad:0)bt.la=Bt.la=0,at.Za||(bt.Na=Bt.Na=0),at.Hc=0,at.Gc=0,at.ia=0;else{var de,$t;if(bt=Bt,Bt=gt,St=V.Pa.Xc,Rt=V.ya[V.aa+V.ja],Pt=V.pb[Rt.$b],mt=Rt.ad,se=0,le=V.rb[V.sb-1],ne=ae=0,c(mt,se,0,384),Rt.Za)var Ee=0,un=St[3];else{ge=o(16);var qe=bt.Na+le.Na;if(qe=ia(Bt,St[1],qe,Pt.Eb,0,ge,0),bt.Na=le.Na=(0<qe)+0,1<qe)Ni(ge,0,mt,se);else{var ln=ge[0]+3>>3;for(ge=0;256>ge;ge+=16)mt[se+ge]=ln}Ee=1,un=St[0]}var he=15&bt.la,gn=15&le.la;for(ge=0;4>ge;++ge){var mn=1&gn;for(ln=$t=0;4>ln;++ln)he=he>>1|(mn=(qe=ia(Bt,un,qe=mn+(1&he),Pt.Sc,Ee,mt,se))>Ee)<<7,$t=$t<<2|(3<qe?3:1<qe?2:mt[se+0]!=0),se+=16;he>>=4,gn=gn>>1|mn<<7,ae=(ae<<8|$t)>>>0}for(un=he,Ee=gn>>4,de=0;4>de;de+=2){for($t=0,he=bt.la>>4+de,gn=le.la>>4+de,ge=0;2>ge;++ge){for(mn=1&gn,ln=0;2>ln;++ln)qe=mn+(1&he),he=he>>1|(mn=0<(qe=ia(Bt,St[2],qe,Pt.Qc,0,mt,se)))<<3,$t=$t<<2|(3<qe?3:1<qe?2:mt[se+0]!=0),se+=16;he>>=2,gn=gn>>1|mn<<5}ne|=$t<<4*de,un|=he<<4<<de,Ee|=(240&gn)<<de}bt.la=un,le.la=Ee,Rt.Hc=ae,Rt.Gc=ne,Rt.ia=43690&ne?0:Pt.ia,St=!(ae|ne)}if(0<V.L&&(V.wa[V.Y+V.ja]=V.gd[at.$b][at.Za],V.wa[V.Y+V.ja].La|=!St),gt.Ka)return ke(G,7,"Premature end-of-file encountered.")}if(Mi(G),V=vt,gt=1,at=(H=G).D,bt=0<H.L&&H.M>=H.zb&&H.M<=H.Va,H.Aa==0)t:{if(at.M=H.M,at.uc=bt,Xi(H,at),gt=1,at=($t=H.D).Nb,bt=(ne=$a[H.L])*H.R,Bt=ne/2*H.B,ge=16*at*H.R,ln=8*at*H.B,St=H.sa,Rt=H.ta-bt+ge,Pt=H.qa,mt=H.ra-Bt+ln,se=H.Ha,le=H.Ia-Bt+ln,gn=(he=$t.M)==0,ae=he>=H.Va-1,H.Aa==2&&Xi(H,$t),$t.uc)for(mn=(qe=H).D.M,e(qe.D.uc),$t=qe.yb;$t<qe.Hb;++$t){Ee=$t,un=mn;var Ue=(Ge=(_e=qe).D).Nb;de=_e.R;var Ge=Ge.wa[Ge.Y+Ee],rn=_e.sa,Sn=_e.ta+16*Ue*de+16*Ee,Zn=Ge.dd,He=Ge.tc;if(He!=0)if(e(3<=He),_e.L==1)0<Ee&&wr(rn,Sn,de,He+4),Ge.La&&Ln(rn,Sn,de,He),0<un&&Gr(rn,Sn,de,He+4),Ge.La&&na(rn,Sn,de,He);else{var hr=_e.B,Jr=_e.qa,_i=_e.ra+8*Ue*hr+8*Ee,Ar=_e.Ha,_e=_e.Ia+8*Ue*hr+8*Ee;Ue=Ge.ld,0<Ee&&(Ua(rn,Sn,de,He+4,Zn,Ue),Hr(Jr,_i,Ar,_e,hr,He+4,Zn,Ue)),Ge.La&&(yr(rn,Sn,de,He,Zn,Ue),xi(Jr,_i,Ar,_e,hr,He,Zn,Ue)),0<un&&(Ai(rn,Sn,de,He+4,Zn,Ue),ea(Jr,_i,Ar,_e,hr,He+4,Zn,Ue)),Ge.La&&(Wr(rn,Sn,de,He,Zn,Ue),Vr(Jr,_i,Ar,_e,hr,He,Zn,Ue))}}if(H.ia&&alert("todo:DitherRow"),V.put!=null){if($t=16*he,he=16*(he+1),gn?(V.y=H.sa,V.O=H.ta+ge,V.f=H.qa,V.N=H.ra+ln,V.ea=H.Ha,V.W=H.Ia+ln):($t-=ne,V.y=St,V.O=Rt,V.f=Pt,V.N=mt,V.ea=se,V.W=le),ae||(he-=ne),he>V.o&&(he=V.o),V.F=null,V.J=null,H.Fa!=null&&0<H.Fa.length&&$t<he&&(V.J=Ji(H,V,$t,he-$t),V.F=H.mb,V.F==null&&V.F.length==0)){gt=ke(H,3,"Could not decode alpha data.");break t}$t<V.j&&(ne=V.j-$t,$t=V.j,e(!(1&ne)),V.O+=H.R*ne,V.N+=H.B*(ne>>1),V.W+=H.B*(ne>>1),V.F!=null&&(V.J+=V.width*ne)),$t<he&&(V.O+=V.v,V.N+=V.v>>1,V.W+=V.v>>1,V.F!=null&&(V.J+=V.v),V.ka=$t-V.j,V.U=V.va-V.v,V.T=he-$t,gt=V.put(V))}at+1!=H.Ic||ae||(a(H.sa,H.ta-bt,St,Rt+16*H.R,bt),a(H.qa,H.ra-Bt,Pt,mt+8*H.B,Bt),a(H.Ha,H.Ia-Bt,se,le+8*H.B,Bt))}if(!gt)return ke(G,6,"Output aborted.")}return 1}(t,i)),i.bc!=null&&i.bc(i),u&=1}return u?(t.cb=0,u):0}function jn(t,i,u,d,m){m=t[i+u+32*d]+(m>>3),t[i+u+32*d]=-256&m?0>m?0:255:m}function ci(t,i,u,d,m,w){jn(t,i,0,u,d+m),jn(t,i,1,u,d+w),jn(t,i,2,u,d-w),jn(t,i,3,u,d-m)}function on(t){return(20091*t>>16)+t}function Ei(t,i,u,d){var m,w=0,L=o(16);for(m=0;4>m;++m){var A=t[i+0]+t[i+8],x=t[i+0]-t[i+8],I=(35468*t[i+4]>>16)-on(t[i+12]),U=on(t[i+4])+(35468*t[i+12]>>16);L[w+0]=A+U,L[w+1]=x+I,L[w+2]=x-I,L[w+3]=A-U,w+=4,i++}for(m=w=0;4>m;++m)A=(t=L[w+0]+4)+L[w+8],x=t-L[w+8],I=(35468*L[w+4]>>16)-on(L[w+12]),jn(u,d,0,0,A+(U=on(L[w+4])+(35468*L[w+12]>>16))),jn(u,d,1,0,x+I),jn(u,d,2,0,x-I),jn(u,d,3,0,A-U),w++,d+=32}function Sa(t,i,u,d){var m=t[i+0]+4,w=35468*t[i+4]>>16,L=on(t[i+4]),A=35468*t[i+1]>>16;ci(u,d,0,m+L,t=on(t[i+1]),A),ci(u,d,1,m+w,t,A),ci(u,d,2,m-w,t,A),ci(u,d,3,m-L,t,A)}function So(t,i,u,d,m){Ei(t,i,u,d),m&&Ei(t,i+16,u,d+4)}function _a(t,i,u,d){cr(t,i+0,u,d,1),cr(t,i+32,u,d+128,1)}function _o(t,i,u,d){var m;for(t=t[i+0]+4,m=0;4>m;++m)for(i=0;4>i;++i)jn(u,d,i,m,t)}function Pa(t,i,u,d){t[i+0]&&nn(t,i+0,u,d),t[i+16]&&nn(t,i+16,u,d+4),t[i+32]&&nn(t,i+32,u,d+128),t[i+48]&&nn(t,i+48,u,d+128+4)}function Po(t,i,u,d){var m,w=o(16);for(m=0;4>m;++m){var L=t[i+0+m]+t[i+12+m],A=t[i+4+m]+t[i+8+m],x=t[i+4+m]-t[i+8+m],I=t[i+0+m]-t[i+12+m];w[0+m]=L+A,w[8+m]=L-A,w[4+m]=I+x,w[12+m]=I-x}for(m=0;4>m;++m)L=(t=w[0+4*m]+3)+w[3+4*m],A=w[1+4*m]+w[2+4*m],x=w[1+4*m]-w[2+4*m],I=t-w[3+4*m],u[d+0]=L+A>>3,u[d+16]=I+x>>3,u[d+32]=L-A>>3,u[d+48]=I-x>>3,d+=64}function qi(t,i,u){var d,m=i-32,w=pn,L=255-t[m-1];for(d=0;d<u;++d){var A,x=w,I=L+t[i-1];for(A=0;A<u;++A)t[i+A]=x[I+t[m+A]];i+=32}}function ko(t,i){qi(t,i,4)}function Fo(t,i){qi(t,i,8)}function Io(t,i){qi(t,i,16)}function ir(t,i){var u;for(u=0;16>u;++u)a(t,i+32*u,t,i-32,16)}function ui(t,i){var u;for(u=16;0<u;--u)c(t,i,t[i-1],16),i+=32}function li(t,i,u){var d;for(d=0;16>d;++d)c(i,u+32*d,t,16)}function ka(t,i){var u,d=16;for(u=0;16>u;++u)d+=t[i-1+32*u]+t[i+u-32];li(d>>5,t,i)}function Ye(t,i){var u,d=8;for(u=0;16>u;++u)d+=t[i-1+32*u];li(d>>4,t,i)}function Co(t,i){var u,d=8;for(u=0;16>u;++u)d+=t[i+u-32];li(d>>4,t,i)}function Fa(t,i){li(128,t,i)}function Vt(t,i,u){return t+2*i+u+2>>2}function jo(t,i){var u,d=i-32;for(d=new Uint8Array([Vt(t[d-1],t[d+0],t[d+1]),Vt(t[d+0],t[d+1],t[d+2]),Vt(t[d+1],t[d+2],t[d+3]),Vt(t[d+2],t[d+3],t[d+4])]),u=0;4>u;++u)a(t,i+32*u,d,0,d.length)}function Oo(t,i){var u=t[i-1],d=t[i-1+32],m=t[i-1+64],w=t[i-1+96];At(t,i+0,16843009*Vt(t[i-1-32],u,d)),At(t,i+32,16843009*Vt(u,d,m)),At(t,i+64,16843009*Vt(d,m,w)),At(t,i+96,16843009*Vt(m,w,w))}function Yn(t,i){var u,d=4;for(u=0;4>u;++u)d+=t[i+u-32]+t[i-1+32*u];for(d>>=3,u=0;4>u;++u)c(t,i+32*u,d,4)}function Ia(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],w=t[i-1-32],L=t[i+0-32],A=t[i+1-32],x=t[i+2-32],I=t[i+3-32];t[i+0+96]=Vt(d,m,t[i-1+96]),t[i+1+96]=t[i+0+64]=Vt(u,d,m),t[i+2+96]=t[i+1+64]=t[i+0+32]=Vt(w,u,d),t[i+3+96]=t[i+2+64]=t[i+1+32]=t[i+0+0]=Vt(L,w,u),t[i+3+64]=t[i+2+32]=t[i+1+0]=Vt(A,L,w),t[i+3+32]=t[i+2+0]=Vt(x,A,L),t[i+3+0]=Vt(I,x,A)}function Ca(t,i){var u=t[i+1-32],d=t[i+2-32],m=t[i+3-32],w=t[i+4-32],L=t[i+5-32],A=t[i+6-32],x=t[i+7-32];t[i+0+0]=Vt(t[i+0-32],u,d),t[i+1+0]=t[i+0+32]=Vt(u,d,m),t[i+2+0]=t[i+1+32]=t[i+0+64]=Vt(d,m,w),t[i+3+0]=t[i+2+32]=t[i+1+64]=t[i+0+96]=Vt(m,w,L),t[i+3+32]=t[i+2+64]=t[i+1+96]=Vt(w,L,A),t[i+3+64]=t[i+2+96]=Vt(L,A,x),t[i+3+96]=Vt(A,x,x)}function hi(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],w=t[i-1-32],L=t[i+0-32],A=t[i+1-32],x=t[i+2-32],I=t[i+3-32];t[i+0+0]=t[i+1+64]=w+L+1>>1,t[i+1+0]=t[i+2+64]=L+A+1>>1,t[i+2+0]=t[i+3+64]=A+x+1>>1,t[i+3+0]=x+I+1>>1,t[i+0+96]=Vt(m,d,u),t[i+0+64]=Vt(d,u,w),t[i+0+32]=t[i+1+96]=Vt(u,w,L),t[i+1+32]=t[i+2+96]=Vt(w,L,A),t[i+2+32]=t[i+3+96]=Vt(L,A,x),t[i+3+32]=Vt(A,x,I)}function Di(t,i){var u=t[i+0-32],d=t[i+1-32],m=t[i+2-32],w=t[i+3-32],L=t[i+4-32],A=t[i+5-32],x=t[i+6-32],I=t[i+7-32];t[i+0+0]=u+d+1>>1,t[i+1+0]=t[i+0+64]=d+m+1>>1,t[i+2+0]=t[i+1+64]=m+w+1>>1,t[i+3+0]=t[i+2+64]=w+L+1>>1,t[i+0+32]=Vt(u,d,m),t[i+1+32]=t[i+0+96]=Vt(d,m,w),t[i+2+32]=t[i+1+96]=Vt(m,w,L),t[i+3+32]=t[i+2+96]=Vt(w,L,A),t[i+3+64]=Vt(L,A,x),t[i+3+96]=Vt(A,x,I)}function Bo(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],w=t[i-1+96];t[i+0+0]=u+d+1>>1,t[i+2+0]=t[i+0+32]=d+m+1>>1,t[i+2+32]=t[i+0+64]=m+w+1>>1,t[i+1+0]=Vt(u,d,m),t[i+3+0]=t[i+1+32]=Vt(d,m,w),t[i+3+32]=t[i+1+64]=Vt(m,w,w),t[i+3+64]=t[i+2+64]=t[i+0+96]=t[i+1+96]=t[i+2+96]=t[i+3+96]=w}function Mo(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],w=t[i-1+96],L=t[i-1-32],A=t[i+0-32],x=t[i+1-32],I=t[i+2-32];t[i+0+0]=t[i+2+32]=u+L+1>>1,t[i+0+32]=t[i+2+64]=d+u+1>>1,t[i+0+64]=t[i+2+96]=m+d+1>>1,t[i+0+96]=w+m+1>>1,t[i+3+0]=Vt(A,x,I),t[i+2+0]=Vt(L,A,x),t[i+1+0]=t[i+3+32]=Vt(u,L,A),t[i+1+32]=t[i+3+64]=Vt(d,u,L),t[i+1+64]=t[i+3+96]=Vt(m,d,u),t[i+1+96]=Vt(w,m,d)}function Eo(t,i){var u;for(u=0;8>u;++u)a(t,i+32*u,t,i-32,8)}function Ri(t,i){var u;for(u=0;8>u;++u)c(t,i,t[i-1],8),i+=32}function Dr(t,i,u){var d;for(d=0;8>d;++d)c(i,u+32*d,t,8)}function pr(t,i){var u,d=8;for(u=0;8>u;++u)d+=t[i+u-32]+t[i-1+32*u];Dr(d>>4,t,i)}function qo(t,i){var u,d=4;for(u=0;8>u;++u)d+=t[i+u-32];Dr(d>>3,t,i)}function Rr(t,i){var u,d=4;for(u=0;8>u;++u)d+=t[i-1+32*u];Dr(d>>3,t,i)}function Ti(t,i){Dr(128,t,i)}function fi(t,i,u){var d=t[i-u],m=t[i+0],w=3*(m-d)+Ho[1020+t[i-2*u]-t[i+u]],L=Wa[112+(w+4>>3)];t[i-u]=pn[255+d+Wa[112+(w+3>>3)]],t[i+0]=pn[255+m-L]}function ja(t,i,u,d){var m=t[i+0],w=t[i+u];return An[255+t[i-2*u]-t[i-u]]>d||An[255+w-m]>d}function Oa(t,i,u,d){return 4*An[255+t[i-u]-t[i+0]]+An[255+t[i-2*u]-t[i+u]]<=d}function Ba(t,i,u,d,m){var w=t[i-3*u],L=t[i-2*u],A=t[i-u],x=t[i+0],I=t[i+u],U=t[i+2*u],K=t[i+3*u];return 4*An[255+A-x]+An[255+L-I]>d?0:An[255+t[i-4*u]-w]<=m&&An[255+w-L]<=m&&An[255+L-A]<=m&&An[255+K-U]<=m&&An[255+U-I]<=m&&An[255+I-x]<=m}function Ma(t,i,u,d){var m=2*d+1;for(d=0;16>d;++d)Oa(t,i+d,u,m)&&fi(t,i+d,u)}function Xn(t,i,u,d){var m=2*d+1;for(d=0;16>d;++d)Oa(t,i+d*u,1,m)&&fi(t,i+d*u,1)}function ar(t,i,u,d){var m;for(m=3;0<m;--m)Ma(t,i+=4*u,u,d)}function Do(t,i,u,d){var m;for(m=3;0<m;--m)Xn(t,i+=4,u,d)}function gr(t,i,u,d,m,w,L,A){for(w=2*w+1;0<m--;){if(Ba(t,i,u,w,L))if(ja(t,i,u,A))fi(t,i,u);else{var x=t,I=i,U=u,K=x[I-2*U],Z=x[I-U],G=x[I+0],vt=x[I+U],at=x[I+2*U],H=27*(gt=Ho[1020+3*(G-Z)+Ho[1020+K-vt]])+63>>7,V=18*gt+63>>7,gt=9*gt+63>>7;x[I-3*U]=pn[255+x[I-3*U]+gt],x[I-2*U]=pn[255+K+V],x[I-U]=pn[255+Z+H],x[I+0]=pn[255+G-H],x[I+U]=pn[255+vt-V],x[I+2*U]=pn[255+at-gt]}i+=d}}function On(t,i,u,d,m,w,L,A){for(w=2*w+1;0<m--;){if(Ba(t,i,u,w,L))if(ja(t,i,u,A))fi(t,i,u);else{var x=t,I=i,U=u,K=x[I-U],Z=x[I+0],G=x[I+U],vt=Wa[112+((at=3*(Z-K))+4>>3)],at=Wa[112+(at+3>>3)],H=vt+1>>1;x[I-2*U]=pn[255+x[I-2*U]+H],x[I-U]=pn[255+K+at],x[I+0]=pn[255+Z-vt],x[I+U]=pn[255+G-H]}i+=d}}function zi(t,i,u,d,m,w){gr(t,i,u,1,16,d,m,w)}function Tr(t,i,u,d,m,w){gr(t,i,1,u,16,d,m,w)}function Ro(t,i,u,d,m,w){var L;for(L=3;0<L;--L)On(t,i+=4*u,u,1,16,d,m,w)}function di(t,i,u,d,m,w){var L;for(L=3;0<L;--L)On(t,i+=4,1,u,16,d,m,w)}function To(t,i,u,d,m,w,L,A){gr(t,i,m,1,8,w,L,A),gr(u,d,m,1,8,w,L,A)}function Ui(t,i,u,d,m,w,L,A){gr(t,i,1,m,8,w,L,A),gr(u,d,1,m,8,w,L,A)}function Hi(t,i,u,d,m,w,L,A){On(t,i+4*m,m,1,8,w,L,A),On(u,d+4*m,m,1,8,w,L,A)}function Ea(t,i,u,d,m,w,L,A){On(t,i+4,1,m,8,w,L,A),On(u,d+4,1,m,8,w,L,A)}function pi(){this.ba=new Pn,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new xe,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Wi(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Vi(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function qa(){this.ua=0,this.Wa=new E,this.vb=new E,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new D,this.yc=new k}function zo(){this.xb=this.a=0,this.l=new si,this.ca=new Pn,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new N,this.Pb=0,this.wd=new N,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new qa,this.ab=0,this.gc=l(4,Vi),this.Oc=0}function gi(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new si,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function zr(t,i,u,d,m,w,L){for(t=t==null?0:t[i+0],i=0;i<L;++i)m[w+i]=t+u[d+i]&255,t=m[w+i]}function Gi(t,i,u,d,m,w,L){var A;if(t==null)zr(null,null,u,d,m,w,L);else for(A=0;A<L;++A)m[w+A]=t[i+A]+u[d+A]&255}function mr(t,i,u,d,m,w,L){if(t==null)zr(null,null,u,d,m,w,L);else{var A,x=t[i+0],I=x,U=x;for(A=0;A<L;++A)I=U+(x=t[i+A])-I,U=u[d+A]+(-256&I?0>I?0:255:I)&255,I=x,m[w+A]=U}}function Ji(t,i,u,d){var m=i.width,w=i.o;if(e(t!=null&&i!=null),0>u||0>=d||u+d>w)return null;if(!t.Cc){if(t.ga==null){var L;if(t.ga=new gi,(L=t.ga==null)||(L=i.width*i.o,e(t.Gb.length==0),t.Gb=o(L),t.Uc=0,t.Gb==null?L=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,L=1),L=!L),!L){L=t.ga;var A=t.Fa,x=t.P,I=t.qc,U=t.mb,K=t.nb,Z=x+1,G=I-1,vt=L.l;if(e(A!=null&&U!=null&&i!=null),Nr[0]=null,Nr[1]=zr,Nr[2]=Gi,Nr[3]=mr,L.ca=U,L.tb=K,L.c=i.width,L.i=i.height,e(0<L.c&&0<L.i),1>=I)i=0;else if(L.$a=A[x+0]>>0&3,L.Z=A[x+0]>>2&3,L.Lc=A[x+0]>>4&3,x=A[x+0]>>6&3,0>L.$a||1<L.$a||4<=L.Z||1<L.Lc||x)i=0;else if(vt.put=Hn,vt.ac=We,vt.bc=Wn,vt.ma=L,vt.width=i.width,vt.height=i.height,vt.Da=i.Da,vt.v=i.v,vt.va=i.va,vt.j=i.j,vt.o=i.o,L.$a)t:{e(L.$a==1),i=bn();e:for(;;){if(i==null){i=0;break t}if(e(L!=null),L.mc=i,i.c=L.c,i.i=L.i,i.l=L.l,i.l.ma=L,i.l.width=L.c,i.l.height=L.i,i.a=0,$(i.m,A,Z,G),!Jn(L.c,L.i,1,i,null)||(i.ab==1&&i.gc[0].hc==3&&rr(i.s)?(L.ic=1,A=i.c*i.i,i.Ta=null,i.Ua=0,i.V=o(A),i.Ba=0,i.V==null?(i.a=1,i=0):i=1):(L.ic=0,i=qr(i,L.c)),!i))break e;i=1;break t}L.mc=null,i=0}else i=G>=L.c*L.i;L=!i}if(L)return null;t.ga.Lc!=1?t.Ga=0:d=w-u}e(t.ga!=null),e(u+d<=w);t:{if(i=(A=t.ga).c,w=A.l.o,A.$a==0){if(Z=t.rc,G=t.Vc,vt=t.Fa,x=t.P+1+u*i,I=t.mb,U=t.nb+u*i,e(x<=t.P+t.qc),A.Z!=0)for(e(Nr[A.Z]!=null),L=0;L<d;++L)Nr[A.Z](Z,G,vt,x,I,U,i),Z=I,G=U,U+=i,x+=i;else for(L=0;L<d;++L)a(I,U,vt,x,i),Z=I,G=U,U+=i,x+=i;t.rc=Z,t.Vc=G}else{if(e(A.mc!=null),i=u+d,e((L=A.mc)!=null),e(i<=L.i),L.C>=i)i=1;else if(A.ic||X(),A.ic){A=L.V,Z=L.Ba,G=L.c;var at=L.i,H=(vt=1,x=L.$/G,I=L.$%G,U=L.m,K=L.s,L.$),V=G*at,gt=G*i,bt=K.wc,mt=H<gt?Be(K,I,x):null;e(H<=V),e(i<=at),e(rr(K));e:for(;;){for(;!U.h&&H<gt;){if(I&bt||(mt=Be(K,I,x)),e(mt!=null),J(U),256>(at=an(mt.G[0],mt.H[0],U)))A[Z+H]=at,++H,++I>=G&&(I=0,++x<=i&&!(x%16)&&In(L,x));else{if(!(280>at)){vt=0;break e}at=kn(at-256,U);var Bt,St=an(mt.G[4],mt.H[4],U);if(J(U),!(H>=(St=Vn(G,St=kn(St,U)))&&V-H>=at)){vt=0;break e}for(Bt=0;Bt<at;++Bt)A[Z+H+Bt]=A[Z+H+Bt-St];for(H+=at,I+=at;I>=G;)I-=G,++x<=i&&!(x%16)&&In(L,x);H<gt&&I&bt&&(mt=Be(K,I,x))}e(U.h==M(U))}In(L,x>i?i:x);break e}!vt||U.h&&H<V?(vt=0,L.a=U.h?5:3):L.$=H,i=vt}else i=Cn(L,L.V,L.Ba,L.c,L.i,i,ni);if(!i){d=0;break t}}u+d>=w&&(t.Cc=1),d=1}if(!d)return null;if(t.Cc&&((d=t.ga)!=null&&(d.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+u*m}function s(t,i,u,d,m,w){for(;0<m--;){var L,A=t,x=i+(u?1:0),I=t,U=i+(u?0:3);for(L=0;L<d;++L){var K=I[U+4*L];K!=255&&(K*=32897,A[x+4*L+0]=A[x+4*L+0]*K>>23,A[x+4*L+1]=A[x+4*L+1]*K>>23,A[x+4*L+2]=A[x+4*L+2]*K>>23)}i+=w}}function v(t,i,u,d,m){for(;0<d--;){var w;for(w=0;w<u;++w){var L=t[i+2*w+0],A=15&(I=t[i+2*w+1]),x=4369*A,I=(240&I|I>>4)*x>>16;t[i+2*w+0]=(240&L|L>>4)*x>>16&240|(15&L|L<<4)*x>>16>>4&15,t[i+2*w+1]=240&I|A}i+=m}}function j(t,i,u,d,m,w,L,A){var x,I,U=255;for(I=0;I<m;++I){for(x=0;x<d;++x){var K=t[i+x];w[L+4*x]=K,U&=K}i+=u,L+=A}return U!=255}function R(t,i,u,d,m){var w;for(w=0;w<m;++w)u[d+w]=t[i+w]>>8}function X(){Nn=s,be=v,ye=j,Ie=R}function ct(t,i,u){z[t]=function(d,m,w,L,A,x,I,U,K,Z,G,vt,at,H,V,gt,bt){var mt,Bt=bt-1>>1,St=A[x+0]|I[U+0]<<16,Rt=K[Z+0]|G[vt+0]<<16;e(d!=null);var Pt=3*St+Rt+131074>>2;for(i(d[m+0],255&Pt,Pt>>16,at,H),w!=null&&(Pt=3*Rt+St+131074>>2,i(w[L+0],255&Pt,Pt>>16,V,gt)),mt=1;mt<=Bt;++mt){var se=A[x+mt]|I[U+mt]<<16,le=K[Z+mt]|G[vt+mt]<<16,ae=St+se+Rt+le+524296,ne=ae+2*(se+Rt)>>3;Pt=ne+St>>1,St=(ae=ae+2*(St+le)>>3)+se>>1,i(d[m+2*mt-1],255&Pt,Pt>>16,at,H+(2*mt-1)*u),i(d[m+2*mt-0],255&St,St>>16,at,H+(2*mt-0)*u),w!=null&&(Pt=ae+Rt>>1,St=ne+le>>1,i(w[L+2*mt-1],255&Pt,Pt>>16,V,gt+(2*mt-1)*u),i(w[L+2*mt+0],255&St,St>>16,V,gt+(2*mt+0)*u)),St=se,Rt=le}1&bt||(Pt=3*St+Rt+131074>>2,i(d[m+bt-1],255&Pt,Pt>>16,at,H+(bt-1)*u),w!=null&&(Pt=3*Rt+St+131074>>2,i(w[L+bt-1],255&Pt,Pt>>16,V,gt+(bt-1)*u)))}}function yt(){xn[Va]=ou,xn[Ga]=Ms,xn[Is]=su,xn[Ja]=Es,xn[Ya]=qs,xn[Wo]=Ds,xn[Cs]=cu,xn[Vo]=Ms,xn[Go]=Es,xn[Xa]=qs,xn[Jo]=Ds}function Ot(t){return t&~uu?0>t?0:255:t>>Rs}function Dt(t,i){return Ot((19077*t>>8)+(26149*i>>8)-14234)}function Zt(t,i,u){return Ot((19077*t>>8)-(6419*i>>8)-(13320*u>>8)+8708)}function Yt(t,i){return Ot((19077*t>>8)+(33050*i>>8)-17685)}function re(t,i,u,d,m){d[m+0]=Dt(t,u),d[m+1]=Zt(t,i,u),d[m+2]=Yt(t,i)}function Ne(t,i,u,d,m){d[m+0]=Yt(t,i),d[m+1]=Zt(t,i,u),d[m+2]=Dt(t,u)}function Se(t,i,u,d,m){var w=Zt(t,i,u);i=w<<3&224|Yt(t,i)>>3,d[m+0]=248&Dt(t,u)|w>>5,d[m+1]=i}function Me(t,i,u,d,m){var w=240&Yt(t,i)|15;d[m+0]=240&Dt(t,u)|Zt(t,i,u)>>4,d[m+1]=w}function Xe(t,i,u,d,m){d[m+0]=255,re(t,i,u,d,m+1)}function ze(t,i,u,d,m){Ne(t,i,u,d,m),d[m+3]=255}function Bn(t,i,u,d,m){re(t,i,u,d,m),d[m+3]=255}function dn(t,i){return 0>t?0:t>i?i:t}function Kn(t,i,u){z[t]=function(d,m,w,L,A,x,I,U,K){for(var Z=U+(-2&K)*u;U!=Z;)i(d[m+0],w[L+0],A[x+0],I,U),i(d[m+1],w[L+0],A[x+0],I,U+u),m+=2,++L,++x,U+=2*u;1&K&&i(d[m+0],w[L+0],A[x+0],I,U)}}function Da(t,i,u){return u==0?t==0?i==0?6:5:i==0?4:0:u}function Yi(t,i,u,d,m){switch(t>>>30){case 3:cr(i,u,d,m,0);break;case 2:Fe(i,u,d,m);break;case 1:nn(i,u,d,m)}}function Xi(t,i){var u,d,m=i.M,w=i.Nb,L=t.oc,A=t.pc+40,x=t.oc,I=t.pc+584,U=t.oc,K=t.pc+600;for(u=0;16>u;++u)L[A+32*u-1]=129;for(u=0;8>u;++u)x[I+32*u-1]=129,U[K+32*u-1]=129;for(0<m?L[A-1-32]=x[I-1-32]=U[K-1-32]=129:(c(L,A-32-1,127,21),c(x,I-32-1,127,9),c(U,K-32-1,127,9)),d=0;d<t.za;++d){var Z=i.ya[i.aa+d];if(0<d){for(u=-1;16>u;++u)a(L,A+32*u-4,L,A+32*u+12,4);for(u=-1;8>u;++u)a(x,I+32*u-4,x,I+32*u+4,4),a(U,K+32*u-4,U,K+32*u+4,4)}var G=t.Gd,vt=t.Hd+d,at=Z.ad,H=Z.Hc;if(0<m&&(a(L,A-32,G[vt].y,0,16),a(x,I-32,G[vt].f,0,8),a(U,K-32,G[vt].ea,0,8)),Z.Za){var V=L,gt=A-32+16;for(0<m&&(d>=t.za-1?c(V,gt,G[vt].y[15],4):a(V,gt,G[vt+1].y,0,4)),u=0;4>u;u++)V[gt+128+u]=V[gt+256+u]=V[gt+384+u]=V[gt+0+u];for(u=0;16>u;++u,H<<=2)V=L,gt=A+zs[u],Mn[Z.Ob[u]](V,gt),Yi(H,at,16*+u,V,gt)}else if(V=Da(d,m,Z.Ob[0]),Lr[V](L,A),H!=0)for(u=0;16>u;++u,H<<=2)Yi(H,at,16*+u,L,A+zs[u]);for(u=Z.Gc,V=Da(d,m,Z.Dd),lr[V](x,I),lr[V](U,K),H=at,V=x,gt=I,255&(Z=u>>0)&&(170&Z?ta(H,256,V,gt):wn(H,256,V,gt)),Z=U,H=K,255&(u>>=8)&&(170&u?ta(at,320,Z,H):wn(at,320,Z,H)),m<t.Ub-1&&(a(G[vt].y,0,L,A+480,16),a(G[vt].f,0,x,I+224,8),a(G[vt].ea,0,U,K+224,8)),u=8*w*t.B,G=t.sa,vt=t.ta+16*d+16*w*t.R,at=t.qa,Z=t.ra+8*d+u,H=t.Ha,V=t.Ia+8*d+u,u=0;16>u;++u)a(G,vt+u*t.R,L,A+32*u,16);for(u=0;8>u;++u)a(at,Z+u*t.B,x,I+32*u,8),a(H,V+u*t.B,U,K+32*u,8)}}function mi(t,i,u,d,m,w,L,A,x){var I=[0],U=[0],K=0,Z=x!=null?x.kd:0,G=x??new Wi;if(t==null||12>u)return 7;G.data=t,G.w=i,G.ha=u,i=[i],u=[u],G.gb=[G.gb];t:{var vt=i,at=u,H=G.gb;if(e(t!=null),e(at!=null),e(H!=null),H[0]=0,12<=at[0]&&!n(t,vt[0],"RIFF")){if(n(t,vt[0]+8,"WEBP")){H=3;break t}var V=_t(t,vt[0]+4);if(12>V||4294967286<V){H=3;break t}if(Z&&V>at[0]-8){H=7;break t}H[0]=V,vt[0]+=12,at[0]-=12}H=0}if(H!=0)return H;for(V=0<G.gb[0],u=u[0];;){t:{var gt=t;at=i,H=u;var bt=I,mt=U,Bt=vt=[0];if((Pt=K=[K])[0]=0,8>H[0])H=7;else{if(!n(gt,at[0],"VP8X")){if(_t(gt,at[0]+4)!=10){H=3;break t}if(18>H[0]){H=7;break t}var St=_t(gt,at[0]+8),Rt=1+Ft(gt,at[0]+12);if(2147483648<=Rt*(gt=1+Ft(gt,at[0]+15))){H=3;break t}Bt!=null&&(Bt[0]=St),bt!=null&&(bt[0]=Rt),mt!=null&&(mt[0]=gt),at[0]+=18,H[0]-=18,Pt[0]=1}H=0}}if(K=K[0],vt=vt[0],H!=0)return H;if(at=!!(2&vt),!V&&K)return 3;if(w!=null&&(w[0]=!!(16&vt)),L!=null&&(L[0]=at),A!=null&&(A[0]=0),L=I[0],vt=U[0],K&&at&&x==null){H=0;break}if(4>u){H=7;break}if(V&&K||!V&&!K&&!n(t,i[0],"ALPH")){u=[u],G.na=[G.na],G.P=[G.P],G.Sa=[G.Sa];t:{St=t,H=i,V=u;var Pt=G.gb;bt=G.na,mt=G.P,Bt=G.Sa,Rt=22,e(St!=null),e(V!=null),gt=H[0];var se=V[0];for(e(bt!=null),e(Bt!=null),bt[0]=null,mt[0]=null,Bt[0]=0;;){if(H[0]=gt,V[0]=se,8>se){H=7;break t}var le=_t(St,gt+4);if(4294967286<le){H=3;break t}var ae=8+le+1&-2;if(Rt+=ae,0<Pt&&Rt>Pt){H=3;break t}if(!n(St,gt,"VP8 ")||!n(St,gt,"VP8L")){H=0;break t}if(se[0]<ae){H=7;break t}n(St,gt,"ALPH")||(bt[0]=St,mt[0]=gt+8,Bt[0]=le),gt+=ae,se-=ae}}if(u=u[0],G.na=G.na[0],G.P=G.P[0],G.Sa=G.Sa[0],H!=0)break}u=[u],G.Ja=[G.Ja],G.xa=[G.xa];t:if(Pt=t,H=i,V=u,bt=G.gb[0],mt=G.Ja,Bt=G.xa,St=H[0],gt=!n(Pt,St,"VP8 "),Rt=!n(Pt,St,"VP8L"),e(Pt!=null),e(V!=null),e(mt!=null),e(Bt!=null),8>V[0])H=7;else{if(gt||Rt){if(Pt=_t(Pt,St+4),12<=bt&&Pt>bt-12){H=3;break t}if(Z&&Pt>V[0]-8){H=7;break t}mt[0]=Pt,H[0]+=8,V[0]-=8,Bt[0]=Rt}else Bt[0]=5<=V[0]&&Pt[St+0]==47&&!(Pt[St+4]>>5),mt[0]=V[0];H=0}if(u=u[0],G.Ja=G.Ja[0],G.xa=G.xa[0],i=i[0],H!=0)break;if(4294967286<G.Ja)return 3;if(A==null||at||(A[0]=G.xa?2:1),L=[L],vt=[vt],G.xa){if(5>u){H=7;break}A=L,Z=vt,at=w,t==null||5>u?t=0:5<=u&&t[i+0]==47&&!(t[i+4]>>5)?(V=[0],Pt=[0],bt=[0],$(mt=new N,t,i,u),zt(mt,V,Pt,bt)?(A!=null&&(A[0]=V[0]),Z!=null&&(Z[0]=Pt[0]),at!=null&&(at[0]=bt[0]),t=1):t=0):t=0}else{if(10>u){H=7;break}A=vt,t==null||10>u||!Na(t,i+3,u-3)?t=0:(Z=t[i+0]|t[i+1]<<8|t[i+2]<<16,at=16383&(t[i+7]<<8|t[i+6]),t=16383&(t[i+9]<<8|t[i+8]),1&Z||3<(Z>>1&7)||!(Z>>4&1)||Z>>5>=G.Ja||!at||!t?t=0:(L&&(L[0]=at),A&&(A[0]=t),t=1))}if(!t||(L=L[0],vt=vt[0],K&&(I[0]!=L||U[0]!=vt)))return 3;x!=null&&(x[0]=G,x.offset=i-x.w,e(4294967286>i-x.w),e(x.offset==x.ha-u));break}return H==0||H==7&&K&&x==null?(w!=null&&(w[0]|=G.na!=null&&0<G.na.length),d!=null&&(d[0]=L),m!=null&&(m[0]=vt),0):H}function Ki(t,i,u){var d=i.width,m=i.height,w=0,L=0,A=d,x=m;if(i.Da=t!=null&&0<t.Da,i.Da&&(A=t.cd,x=t.bd,w=t.v,L=t.j,11>u||(w&=-2,L&=-2),0>w||0>L||0>=A||0>=x||w+A>d||L+x>m))return 0;if(i.v=w,i.j=L,i.va=w+A,i.o=L+x,i.U=A,i.T=x,i.da=t!=null&&0<t.da,i.da){if(!Kt(A,x,u=[t.ib],w=[t.hb]))return 0;i.ib=u[0],i.hb=w[0]}return i.ob=t!=null&&t.ob,i.Kb=t==null||!t.Sd,i.da&&(i.ob=i.ib<3*d/4&&i.hb<3*m/4,i.Kb=0),1}function Zi(t){if(t==null)return 2;if(11>t.S){var i=t.f.RGBA;i.fb+=(t.height-1)*i.A,i.A=-i.A}else i=t.f.kb,t=t.height,i.O+=(t-1)*i.fa,i.fa=-i.fa,i.N+=(t-1>>1)*i.Ab,i.Ab=-i.Ab,i.W+=(t-1>>1)*i.Db,i.Db=-i.Db,i.F!=null&&(i.J+=(t-1)*i.lb,i.lb=-i.lb);return 0}function vi(t,i,u,d){if(d==null||0>=t||0>=i)return 2;if(u!=null){if(u.Da){var m=u.cd,w=u.bd,L=-2&u.v,A=-2&u.j;if(0>L||0>A||0>=m||0>=w||L+m>t||A+w>i)return 2;t=m,i=w}if(u.da){if(!Kt(t,i,m=[u.ib],w=[u.hb]))return 2;t=m[0],i=w[0]}}d.width=t,d.height=i;t:{var x=d.width,I=d.height;if(t=d.S,0>=x||0>=I||!(t>=Va&&13>t))t=2;else{if(0>=d.Rd&&d.sd==null){L=w=m=i=0;var U=(A=x*Us[t])*I;if(11>t||(w=(I+1)/2*(i=(x+1)/2),t==12&&(L=(m=x)*I)),(I=o(U+2*w+L))==null){t=1;break t}d.sd=I,11>t?((x=d.f.RGBA).eb=I,x.fb=0,x.A=A,x.size=U):((x=d.f.kb).y=I,x.O=0,x.fa=A,x.Fd=U,x.f=I,x.N=0+U,x.Ab=i,x.Cd=w,x.ea=I,x.W=0+U+w,x.Db=i,x.Ed=w,t==12&&(x.F=I,x.J=0+U+2*w),x.Tc=L,x.lb=m)}if(i=1,m=d.S,w=d.width,L=d.height,m>=Va&&13>m)if(11>m)t=d.f.RGBA,i&=(A=Math.abs(t.A))*(L-1)+w<=t.size,i&=A>=w*Us[m],i&=t.eb!=null;else{t=d.f.kb,A=(w+1)/2,U=(L+1)/2,x=Math.abs(t.fa),I=Math.abs(t.Ab);var K=Math.abs(t.Db),Z=Math.abs(t.lb),G=Z*(L-1)+w;i&=x*(L-1)+w<=t.Fd,i&=I*(U-1)+A<=t.Cd,i=(i&=K*(U-1)+A<=t.Ed)&x>=w&I>=A&K>=A,i&=t.y!=null,i&=t.f!=null,i&=t.ea!=null,m==12&&(i&=Z>=w,i&=G<=t.Tc,i&=t.F!=null)}else i=0;t=i?0:2}}return t!=0||u!=null&&u.fd&&(t=Zi(d)),t}var Ve=64,bi=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],yi=24,wi=32,$i=8,sn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];xt("Predictor0","PredictorAdd0"),z.Predictor0=function(){return **********},z.Predictor1=function(t){return t},z.Predictor2=function(t,i,u){return i[u+0]},z.Predictor3=function(t,i,u){return i[u+1]},z.Predictor4=function(t,i,u){return i[u-1]},z.Predictor5=function(t,i,u){return kt(kt(t,i[u+1]),i[u+0])},z.Predictor6=function(t,i,u){return kt(t,i[u-1])},z.Predictor7=function(t,i,u){return kt(t,i[u+0])},z.Predictor8=function(t,i,u){return kt(i[u-1],i[u+0])},z.Predictor9=function(t,i,u){return kt(i[u+0],i[u+1])},z.Predictor10=function(t,i,u){return kt(kt(t,i[u-1]),kt(i[u+0],i[u+1]))},z.Predictor11=function(t,i,u){var d=i[u+0];return 0>=Qt(d>>24&255,t>>24&255,(i=i[u-1])>>24&255)+Qt(d>>16&255,t>>16&255,i>>16&255)+Qt(d>>8&255,t>>8&255,i>>8&255)+Qt(255&d,255&t,255&i)?d:t},z.Predictor12=function(t,i,u){var d=i[u+0];return(qt((t>>24&255)+(d>>24&255)-((i=i[u-1])>>24&255))<<24|qt((t>>16&255)+(d>>16&255)-(i>>16&255))<<16|qt((t>>8&255)+(d>>8&255)-(i>>8&255))<<8|qt((255&t)+(255&d)-(255&i)))>>>0},z.Predictor13=function(t,i,u){var d=i[u-1];return(Gt((t=kt(t,i[u+0]))>>24&255,d>>24&255)<<24|Gt(t>>16&255,d>>16&255)<<16|Gt(t>>8&255,d>>8&255)<<8|Gt(t>>0&255,d>>0&255))>>>0};var Uo=z.PredictorAdd0;z.PredictorAdd1=te,xt("Predictor2","PredictorAdd2"),xt("Predictor3","PredictorAdd3"),xt("Predictor4","PredictorAdd4"),xt("Predictor5","PredictorAdd5"),xt("Predictor6","PredictorAdd6"),xt("Predictor7","PredictorAdd7"),xt("Predictor8","PredictorAdd8"),xt("Predictor9","PredictorAdd9"),xt("Predictor10","PredictorAdd10"),xt("Predictor11","PredictorAdd11"),xt("Predictor12","PredictorAdd12"),xt("Predictor13","PredictorAdd13");var Qi=z.PredictorAdd2;ee("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),ee("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Ra,yn=z.ColorIndexInverseTransform,Li=z.MapARGB,Ta=z.VP8LColorIndexInverseTransformAlpha,za=z.MapAlpha,vr=z.VP8LPredictorsAdd=[];vr.length=16,(z.VP8LPredictors=[]).length=16,(z.VP8LPredictorsAdd_C=[]).length=16,(z.VP8LPredictors_C=[]).length=16;var Ur,cn,en,br,or,sr,Ni,cr,Fe,ta,nn,wn,Ai,Ua,ea,Hr,Wr,yr,Vr,xi,Gr,wr,na,Ln,Nn,be,ye,Ie,Re=o(511),ur=o(2041),ra=o(225),Si=o(767),Ha=0,Ho=ur,Wa=ra,pn=Si,An=Re,Va=0,Ga=1,Is=2,Ja=3,Ya=4,Wo=5,Cs=6,Vo=7,Go=8,Xa=9,Jo=10,Gc=[2,3,7],Jc=[3,3,11],js=[280,256,256,256,40],Yc=[0,1,1,1,0],Xc=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Kc=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Zc=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],$c=8,Yo=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],Xo=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ia=null,Qc=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],tu=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],Os=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],eu=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],nu=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],ru=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],iu=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],Lr=[],Mn=[],lr=[],au=1,Bs=2,Nr=[],xn=[];ct("UpsampleRgbLinePair",re,3),ct("UpsampleBgrLinePair",Ne,3),ct("UpsampleRgbaLinePair",Bn,4),ct("UpsampleBgraLinePair",ze,4),ct("UpsampleArgbLinePair",Xe,4),ct("UpsampleRgba4444LinePair",Me,2),ct("UpsampleRgb565LinePair",Se,2);var ou=z.UpsampleRgbLinePair,su=z.UpsampleBgrLinePair,Ms=z.UpsampleRgbaLinePair,Es=z.UpsampleBgraLinePair,qs=z.UpsampleArgbLinePair,Ds=z.UpsampleRgba4444LinePair,cu=z.UpsampleRgb565LinePair,Ka=16,Za=1<<Ka-1,aa=-227,Ko=482,Rs=6,uu=(256<<Rs)-1,Ts=0,lu=o(256),hu=o(256),fu=o(256),du=o(256),pu=o(Ko-aa),gu=o(Ko-aa);Kn("YuvToRgbRow",re,3),Kn("YuvToBgrRow",Ne,3),Kn("YuvToRgbaRow",Bn,4),Kn("YuvToBgraRow",ze,4),Kn("YuvToArgbRow",Xe,4),Kn("YuvToRgba4444Row",Me,2),Kn("YuvToRgb565Row",Se,2);var zs=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],$a=[0,2,8],mu=[8,7,6,4,4,2,2,2,1,1,1,1],vu=1;this.WebPDecodeRGBA=function(t,i,u,d,m){var w=Ga,L=new pi,A=new Pn;L.ba=A,A.S=w,A.width=[A.width],A.height=[A.height];var x=A.width,I=A.height,U=new nr;if(U==null||t==null)var K=2;else e(U!=null),K=mi(t,i,u,U.width,U.height,U.Pd,U.Qd,U.format,null);if(K!=0?x=0:(x!=null&&(x[0]=U.width[0]),I!=null&&(I[0]=U.height[0]),x=1),x){A.width=A.width[0],A.height=A.height[0],d!=null&&(d[0]=A.width),m!=null&&(m[0]=A.height);t:{if(d=new si,(m=new Wi).data=t,m.w=i,m.ha=u,m.kd=1,i=[0],e(m!=null),((t=mi(m.data,m.w,m.ha,null,null,null,i,null,m))==0||t==7)&&i[0]&&(t=4),(i=t)==0){if(e(L!=null),d.data=m.data,d.w=m.w+m.offset,d.ha=m.ha-m.offset,d.put=Hn,d.ac=We,d.bc=Wn,d.ma=L,m.xa){if((t=bn())==null){L=1;break t}if(function(Z,G){var vt=[0],at=[0],H=[0];e:for(;;){if(Z==null)return 0;if(G==null)return Z.a=2,0;if(Z.l=G,Z.a=0,$(Z.m,G.data,G.w,G.ha),!zt(Z.m,vt,at,H)){Z.a=3;break e}if(Z.xb=Bs,G.width=vt[0],G.height=at[0],!Jn(vt[0],at[0],1,Z,null))break e;return 1}return e(Z.a!=0),0}(t,d)){if(d=(i=vi(d.width,d.height,L.Oa,L.ba))==0){e:{d=t;n:for(;;){if(d==null){d=0;break e}if(e(d.s.yc!=null),e(d.s.Ya!=null),e(0<d.s.Wb),e((u=d.l)!=null),e((m=u.ma)!=null),d.xb!=0){if(d.ca=m.ba,d.tb=m.tb,e(d.ca!=null),!Ki(m.Oa,u,Ja)){d.a=2;break n}if(!qr(d,u.width)||u.da)break n;if((u.da||ce(d.ca.S))&&X(),11>d.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),d.ca.f.kb.F!=null&&X()),d.Pb&&0<d.s.ua&&d.s.vb.X==null&&!Ut(d.s.vb,d.s.Wa.Xa)){d.a=1;break n}d.xb=0}if(!Cn(d,d.V,d.Ba,d.c,d.i,u.o,ei))break n;m.Dc=d.Ma,d=1;break e}e(d.a!=0),d=0}d=!d}d&&(i=t.a)}else i=t.a}else{if((t=new Ao)==null){L=1;break t}if(t.Fa=m.na,t.P=m.P,t.qc=m.Sa,Aa(t,d)){if((i=vi(d.width,d.height,L.Oa,L.ba))==0){if(t.Aa=0,u=L.Oa,e((m=t)!=null),u!=null){if(0<(x=0>(x=u.Md)?0:100<x?255:255*x/100)){for(I=U=0;4>I;++I)12>(K=m.pb[I]).lc&&(K.ia=x*mu[0>K.lc?0:K.lc]>>3),U|=K.ia;U&&(alert("todo:VP8InitRandom"),m.ia=1)}m.Ga=u.Id,100<m.Ga?m.Ga=100:0>m.Ga&&(m.Ga=0)}xo(t,d)||(i=t.a)}}else i=t.a}i==0&&L.Oa!=null&&L.Oa.fd&&(i=Zi(L.ba))}L=i}w=L!=0?null:11>w?A.f.RGBA.eb:A.f.kb.y}else w=null;return w};var Us=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function g(z,rt){for(var dt="",P=0;P<4;P++)dt+=String.fromCharCode(z[rt++]);return dt}function b(z,rt){return(z[rt+0]<<0|z[rt+1]<<8|z[rt+2]<<16)>>>0}function y(z,rt){return(z[rt+0]<<0|z[rt+1]<<8|z[rt+2]<<16|z[rt+3]<<24)>>>0}new f;var S=[0],p=[0],O=[],F=new f,q=r,_=function(z,rt){var dt={},P=0,k=!1,W=0,D=0;if(dt.frames=[],!function(C,M,T,J){for(var Q=0;Q<J;Q++)if(C[M+Q]!=T.charCodeAt(Q))return!0;return!1}(z,rt,"RIFF",4)){var st,it;for(y(z,rt+=4),rt+=8;rt<z.length;){var lt=g(z,rt),$=y(z,rt+=4);rt+=4;var ht=$+(1&$);switch(lt){case"VP8 ":case"VP8L":dt.frames[P]===void 0&&(dt.frames[P]={}),(N=dt.frames[P]).src_off=k?D:rt-8,N.src_size=W+$+8,P++,k&&(k=!1,W=0,D=0);break;case"VP8X":(N=dt.header={}).feature_flags=z[rt];var pt=rt+4;N.canvas_width=1+b(z,pt),pt+=3,N.canvas_height=1+b(z,pt),pt+=3;break;case"ALPH":k=!0,W=ht+8,D=rt-8;break;case"ANIM":(N=dt.header).bgcolor=y(z,rt),pt=rt+4,N.loop_count=(st=z)[(it=pt)+0]<<0|st[it+1]<<8,pt+=2;break;case"ANMF":var It,N;(N=dt.frames[P]={}).offset_x=2*b(z,rt),rt+=3,N.offset_y=2*b(z,rt),rt+=3,N.width=1+b(z,rt),rt+=3,N.height=1+b(z,rt),rt+=3,N.duration=b(z,rt),rt+=3,It=z[rt++],N.dispose=1&It,N.blend=It>>1&1}lt!="ANMF"&&(rt+=ht)}return dt}}(q,0);_.response=q,_.rgbaoutput=!0,_.dataurl=!1;var B=_.header?_.header:null,Y=_.frames?_.frames:null;if(B){B.loop_counter=B.loop_count,S=[B.canvas_height],p=[B.canvas_width];for(var ot=0;ot<Y.length&&Y[ot].blend!=0;ot++);}var ut=Y[0],wt=F.WebPDecodeRGBA(q,ut.src_off,ut.src_size,p,S);ut.rgba=wt,ut.imgwidth=p[0],ut.imgheight=S[0];for(var tt=0;tt<p[0]*S[0]*4;tt++)O[tt]=wt[tt];return this.width=p,this.height=S,this.data=O,this}(function(r){var e=function(){return typeof ws=="function"},n=function(S,p,O,F){var q=4,_=l;switch(F){case r.image_compression.FAST:q=1,_=o;break;case r.image_compression.MEDIUM:q=6,_=h;break;case r.image_compression.SLOW:q=9,_=f}S=a(S,p,O,_);var B=ws(S,{level:q});return r.__addimage__.arrayBufferToBinaryString(B)},a=function(S,p,O,F){for(var q,_,B,Y=S.length/p,ot=new Uint8Array(S.length+Y),ut=b(),wt=0;wt<Y;wt+=1){if(B=wt*p,q=S.subarray(B,B+p),F)ot.set(F(q,O,_),B+wt);else{for(var tt,z=ut.length,rt=[];tt<z;tt+=1)rt[tt]=ut[tt](q,O,_);var dt=y(rt.concat());ot.set(rt[dt],B+wt)}_=q}return ot},c=function(S){var p=Array.apply([],S);return p.unshift(0),p},o=function(S,p){var O,F=[],q=S.length;F[0]=1;for(var _=0;_<q;_+=1)O=S[_-p]||0,F[_+1]=S[_]-O+256&255;return F},l=function(S,p,O){var F,q=[],_=S.length;q[0]=2;for(var B=0;B<_;B+=1)F=O&&O[B]||0,q[B+1]=S[B]-F+256&255;return q},h=function(S,p,O){var F,q,_=[],B=S.length;_[0]=3;for(var Y=0;Y<B;Y+=1)F=S[Y-p]||0,q=O&&O[Y]||0,_[Y+1]=S[Y]+256-(F+q>>>1)&255;return _},f=function(S,p,O){var F,q,_,B,Y=[],ot=S.length;Y[0]=4;for(var ut=0;ut<ot;ut+=1)F=S[ut-p]||0,q=O&&O[ut]||0,_=O&&O[ut-p]||0,B=g(F,q,_),Y[ut+1]=S[ut]-B+256&255;return Y},g=function(S,p,O){if(S===p&&p===O)return S;var F=Math.abs(p-O),q=Math.abs(S-O),_=Math.abs(S+p-O-O);return F<=q&&F<=_?S:q<=_?p:O},b=function(){return[c,o,l,h,f]},y=function(S){var p=S.map(function(O){return O.reduce(function(F,q){return F+Math.abs(q)},0)});return p.indexOf(Math.min.apply(null,p))};r.processPNG=function(S,p,O,F){var q,_,B,Y,ot,ut,wt,tt,z,rt,dt,P,k,W,D,st=this.decode.FLATE_DECODE,it="";if(this.__addimage__.isArrayBuffer(S)&&(S=new Uint8Array(S)),this.__addimage__.isArrayBufferView(S)){if(S=(B=new fl(S)).imgData,_=B.bits,q=B.colorSpace,ot=B.colors,[4,6].indexOf(B.colorType)!==-1){if(B.bits===8){z=(tt=B.pixelBitlength==32?new Uint32Array(B.decodePixels().buffer):B.pixelBitlength==16?new Uint16Array(B.decodePixels().buffer):new Uint8Array(B.decodePixels().buffer)).length,dt=new Uint8Array(z*B.colors),rt=new Uint8Array(z);var lt,$=B.pixelBitlength-B.bits;for(W=0,D=0;W<z;W++){for(k=tt[W],lt=0;lt<$;)dt[D++]=k>>>lt&255,lt+=B.bits;rt[W]=k>>>lt&255}}if(B.bits===16){z=(tt=new Uint32Array(B.decodePixels().buffer)).length,dt=new Uint8Array(z*(32/B.pixelBitlength)*B.colors),rt=new Uint8Array(z*(32/B.pixelBitlength)),P=B.colors>1,W=0,D=0;for(var ht=0;W<z;)k=tt[W++],dt[D++]=k>>>0&255,P&&(dt[D++]=k>>>16&255,k=tt[W++],dt[D++]=k>>>0&255),rt[ht++]=k>>>16&255;_=8}F!==r.image_compression.NONE&&e()?(S=n(dt,B.width*B.colors,B.colors,F),wt=n(rt,B.width,1,F)):(S=dt,wt=rt,st=void 0)}if(B.colorType===3&&(q=this.color_spaces.INDEXED,ut=B.palette,B.transparency.indexed)){var pt=B.transparency.indexed,It=0;for(W=0,z=pt.length;W<z;++W)It+=pt[W];if((It/=255)===z-1&&pt.indexOf(0)!==-1)Y=[pt.indexOf(0)];else if(It!==z){for(tt=B.decodePixels(),rt=new Uint8Array(tt.length),W=0,z=tt.length;W<z;W++)rt[W]=pt[tt[W]];wt=n(rt,B.width,1)}}var N=function(C){var M;switch(C){case r.image_compression.FAST:M=11;break;case r.image_compression.MEDIUM:M=13;break;case r.image_compression.SLOW:M=14;break;default:M=12}return M}(F);return st===this.decode.FLATE_DECODE&&(it="/Predictor "+N+" "),it+="/Colors "+ot+" /BitsPerComponent "+_+" /Columns "+B.width,(this.__addimage__.isArrayBuffer(S)||this.__addimage__.isArrayBufferView(S))&&(S=this.__addimage__.arrayBufferToBinaryString(S)),(wt&&this.__addimage__.isArrayBuffer(wt)||this.__addimage__.isArrayBufferView(wt))&&(wt=this.__addimage__.arrayBufferToBinaryString(wt)),{alias:O,data:S,index:p,filter:st,decodeParameters:it,transparency:Y,palette:ut,sMask:wt,predictor:N,width:B.width,height:B.height,bitsPerComponent:_,colorSpace:q}}}})(Tt.API),function(r){r.processGIF89A=function(e,n,a,c){var o=new dl(e),l=o.width,h=o.height,f=[];o.decodeAndBlitFrameRGBA(0,f);var g={data:f,width:l,height:h},b=new ps(100).encode(g,100);return r.processJPEG.call(this,b,n,a,c)},r.processGIF87A=r.processGIF89A}(Tt.API),Dn.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var r=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(r);for(var e=0;e<r;e++){var n=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:c,green:a,blue:n,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},Dn.prototype.parseBGR=function(){this.pos=this.offset;try{var r="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[r]()}catch(n){me.log("bit decode error:"+n)}},Dn.prototype.bit1=function(){var r,e=Math.ceil(this.width/8),n=e%4;for(r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,c=0;c<e;c++)for(var o=this.datav.getUint8(this.pos++,!0),l=a*this.width*4+8*c*4,h=0;h<8&&8*c+h<this.width;h++){var f=this.palette[o>>7-h&1];this.data[l+4*h]=f.blue,this.data[l+4*h+1]=f.green,this.data[l+4*h+2]=f.red,this.data[l+4*h+3]=255}n!==0&&(this.pos+=4-n)}},Dn.prototype.bit4=function(){for(var r=Math.ceil(this.width/2),e=r%4,n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,c=0;c<r;c++){var o=this.datav.getUint8(this.pos++,!0),l=a*this.width*4+2*c*4,h=o>>4,f=15&o,g=this.palette[h];if(this.data[l]=g.blue,this.data[l+1]=g.green,this.data[l+2]=g.red,this.data[l+3]=255,2*c+1>=this.width)break;g=this.palette[f],this.data[l+4]=g.blue,this.data[l+4+1]=g.green,this.data[l+4+2]=g.red,this.data[l+4+3]=255}e!==0&&(this.pos+=4-e)}},Dn.prototype.bit8=function(){for(var r=this.width%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,a=0;a<this.width;a++){var c=this.datav.getUint8(this.pos++,!0),o=n*this.width*4+4*a;if(c<this.palette.length){var l=this.palette[c];this.data[o]=l.red,this.data[o+1]=l.green,this.data[o+2]=l.blue,this.data[o+3]=255}else this.data[o]=255,this.data[o+1]=255,this.data[o+2]=255,this.data[o+3]=255}r!==0&&(this.pos+=4-r)}},Dn.prototype.bit15=function(){for(var r=this.width%3,e=parseInt("11111",2),n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,c=0;c<this.width;c++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var l=(o&e)/e*255|0,h=(o>>5&e)/e*255|0,f=(o>>10&e)/e*255|0,g=o>>15?255:0,b=a*this.width*4+4*c;this.data[b]=f,this.data[b+1]=h,this.data[b+2]=l,this.data[b+3]=g}this.pos+=r}},Dn.prototype.bit16=function(){for(var r=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),a=this.height-1;a>=0;a--){for(var c=this.bottom_up?a:this.height-1-a,o=0;o<this.width;o++){var l=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(l&e)/e*255|0,f=(l>>5&n)/n*255|0,g=(l>>11)/e*255|0,b=c*this.width*4+4*o;this.data[b]=g,this.data[b+1]=f,this.data[b+2]=h,this.data[b+3]=255}this.pos+=r}},Dn.prototype.bit24=function(){for(var r=this.height-1;r>=0;r--){for(var e=this.bottom_up?r:this.height-1-r,n=0;n<this.width;n++){var a=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),l=e*this.width*4+4*n;this.data[l]=o,this.data[l+1]=c,this.data[l+2]=a,this.data[l+3]=255}this.pos+=this.width%4}},Dn.prototype.bit32=function(){for(var r=this.height-1;r>=0;r--)for(var e=this.bottom_up?r:this.height-1-r,n=0;n<this.width;n++){var a=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),l=this.datav.getUint8(this.pos++,!0),h=e*this.width*4+4*n;this.data[h]=o,this.data[h+1]=c,this.data[h+2]=a,this.data[h+3]=l}},Dn.prototype.getData=function(){return this.data},function(r){r.processBMP=function(e,n,a,c){var o=new Dn(e,!1),l=o.width,h=o.height,f={data:o.getData(),width:l,height:h},g=new ps(100).encode(f,100);return r.processJPEG.call(this,g,n,a,c)}}(Tt.API),Ac.prototype.getData=function(){return this.data},function(r){r.processWEBP=function(e,n,a,c){var o=new Ac(e),l=o.width,h=o.height,f={data:o.getData(),width:l,height:h},g=new ps(100).encode(f,100);return r.processJPEG.call(this,g,n,a,c)}}(Tt.API),Tt.API.processRGBA=function(r,e,n){for(var a=r.data,c=a.length,o=new Uint8Array(c/4*3),l=new Uint8Array(c/4),h=0,f=0,g=0;g<c;g+=4){var b=a[g],y=a[g+1],S=a[g+2],p=a[g+3];o[h++]=b,o[h++]=y,o[h++]=S,l[f++]=p}var O=this.__addimage__.arrayBufferToBinaryString(o);return{alpha:this.__addimage__.arrayBufferToBinaryString(l),data:O,index:e,alias:n,colorSpace:"DeviceRGB",bitsPerComponent:8,width:r.width,height:r.height}},Tt.API.setLanguage=function(r){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[r]!==void 0&&(this.internal.languageSettings.languageCode=r,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},Ci=Tt.API,fo=Ci.getCharWidthsArray=function(r,e){var n,a,c=(e=e||{}).font||this.internal.getFont(),o=e.fontSize||this.internal.getFontSize(),l=e.charSpace||this.internal.getCharSpace(),h=e.widths?e.widths:c.metadata.Unicode.widths,f=h.fof?h.fof:1,g=e.kerning?e.kerning:c.metadata.Unicode.kerning,b=g.fof?g.fof:1,y=e.doKerning!==!1,S=0,p=r.length,O=0,F=h[0]||f,q=[];for(n=0;n<p;n++)a=r.charCodeAt(n),typeof c.metadata.widthOfString=="function"?q.push((c.metadata.widthOfGlyph(c.metadata.characterToGlyph(a))+l*(1e3/o)||0)/1e3):(S=y&&ve(g[a])==="object"&&!isNaN(parseInt(g[a][O],10))?g[a][O]/b:0,q.push((h[a]||F)/f+S)),O=a;return q},yc=Ci.getStringUnitWidth=function(r,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),a=e.font||this.internal.getFont(),c=e.charSpace||this.internal.getCharSpace();return Ci.processArabic&&(r=Ci.processArabic(r)),typeof a.metadata.widthOfString=="function"?a.metadata.widthOfString(r,n,c)/n:fo.apply(this,arguments).reduce(function(o,l){return o+l},0)},wc=function(r,e,n,a){for(var c=[],o=0,l=r.length,h=0;o!==l&&h+e[o]<n;)h+=e[o],o++;c.push(r.slice(0,o));var f=o;for(h=0;o!==l;)h+e[o]>a&&(c.push(r.slice(f,o)),h=0,f=o),h+=e[o],o++;return f!==o&&c.push(r.slice(f,o)),c},Lc=function(r,e,n){n||(n={});var a,c,o,l,h,f,g,b=[],y=[b],S=n.textIndent||0,p=0,O=0,F=r.split(" "),q=fo.apply(this,[" ",n])[0];if(f=n.lineIndent===-1?F[0].length+2:n.lineIndent||0){var _=Array(f).join(" "),B=[];F.map(function(ot){(ot=ot.split(/\s*\n/)).length>1?B=B.concat(ot.map(function(ut,wt){return(wt&&ut.length?`
`:"")+ut})):B.push(ot[0])}),F=B,f=yc.apply(this,[_,n])}for(o=0,l=F.length;o<l;o++){var Y=0;if(a=F[o],f&&a[0]==`
`&&(a=a.substr(1),Y=1),S+p+(O=(c=fo.apply(this,[a,n])).reduce(function(ot,ut){return ot+ut},0))>e||Y){if(O>e){for(h=wc.apply(this,[a,c,e-(S+p),e]),b.push(h.shift()),b=[h.pop()];h.length;)y.push([h.shift()]);O=c.slice(a.length-(b[0]?b[0].length:0)).reduce(function(ot,ut){return ot+ut},0)}else b=[a];y.push(b),S=O+f,p=q}else b.push(a),S+=p+O,p=q}return g=f?function(ot,ut){return(ut?_:"")+ot.join(" ")}:function(ot){return ot.join(" ")},y.map(g)},Ci.splitTextToSize=function(r,e,n){var a,c=(n=n||{}).fontSize||this.internal.getFontSize(),o=(function(b){if(b.widths&&b.kerning)return{widths:b.widths,kerning:b.kerning};var y=this.internal.getFont(b.fontName,b.fontStyle);return y.metadata.Unicode?{widths:y.metadata.Unicode.widths||{0:1},kerning:y.metadata.Unicode.kerning||{}}:{font:y.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,n);a=Array.isArray(r)?r:String(r).split(/\r?\n/);var l=1*this.internal.scaleFactor*e/c;o.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/c:0,o.lineIndent=n.lineIndent;var h,f,g=[];for(h=0,f=a.length;h<f;h++)g=g.concat(Lc.apply(this,[a[h],l,o]));return g},function(r){r.__fontmetrics__=r.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",n={},a={},c=0;c<e.length;c++)n[e[c]]="0123456789abcdef"[c],a["0123456789abcdef"[c]]=e[c];var o=function(y){return"0x"+parseInt(y,10).toString(16)},l=r.__fontmetrics__.compress=function(y){var S,p,O,F,q=["{"];for(var _ in y){if(S=y[_],isNaN(parseInt(_,10))?p="'"+_+"'":(_=parseInt(_,10),p=(p=o(_).slice(2)).slice(0,-1)+a[p.slice(-1)]),typeof S=="number")S<0?(O=o(S).slice(3),F="-"):(O=o(S).slice(2),F=""),O=F+O.slice(0,-1)+a[O.slice(-1)];else{if(ve(S)!=="object")throw new Error("Don't know what to do with value type "+ve(S)+".");O=l(S)}q.push(p+O)}return q.push("}"),q.join("")},h=r.__fontmetrics__.uncompress=function(y){if(typeof y!="string")throw new Error("Invalid argument passed to uncompress.");for(var S,p,O,F,q={},_=1,B=q,Y=[],ot="",ut="",wt=y.length-1,tt=1;tt<wt;tt+=1)(F=y[tt])=="'"?S?(O=S.join(""),S=void 0):S=[]:S?S.push(F):F=="{"?(Y.push([B,O]),B={},O=void 0):F=="}"?((p=Y.pop())[0][p[1]]=B,O=void 0,B=p[0]):F=="-"?_=-1:O===void 0?n.hasOwnProperty(F)?(ot+=n[F],O=parseInt(ot,16)*_,_=1,ot=""):ot+=F:n.hasOwnProperty(F)?(ut+=n[F],B[O]=parseInt(ut,16)*_,_=1,O=void 0,ut=""):ut+=F;return q},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},g={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},b={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};r.events.push(["addFont",function(y){var S=y.font,p=b.Unicode[S.postScriptName];p&&(S.metadata.Unicode={},S.metadata.Unicode.widths=p.widths,S.metadata.Unicode.kerning=p.kerning);var O=g.Unicode[S.postScriptName];O&&(S.metadata.Unicode.encoding=O,S.encoding=O.codePages[0])}])}(Tt.API),function(r){var e=function(n){for(var a=n.length,c=new Uint8Array(a),o=0;o<a;o++)c[o]=n.charCodeAt(o);return c};r.API.events.push(["addFont",function(n){var a=void 0,c=n.font,o=n.instance;if(!c.isStandardFont){if(o===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+c.postScriptName+"').");if(typeof(a=o.existsFileInVFS(c.postScriptName)===!1?o.loadFile(c.postScriptName):o.getFileFromVFS(c.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+c.postScriptName+"').");(function(l,h){h=/^\x00\x01\x00\x00/.test(h)?e(h):e(fa(h)),l.metadata=r.API.TTFFont.open(h),l.metadata.Unicode=l.metadata.Unicode||{encoding:{},kerning:{},widths:[]},l.metadata.glyIdsUsed=[0]})(c,a)}}])}(Tt),function(r){function e(){return(Ht.canvg?Promise.resolve(Ht.canvg):gs(()=>import("./index.es-9e25d560.js"),["assets/index.es-9e25d560.js","assets/vendor-3aca5368.js","assets/@react-pdf/renderer-15eed3d8.js","assets/@floating-ui/react-2bb8505c.js","assets/react-dates-9940ba2a.js","assets/@fortawesome/react-fontawesome-8ac6acae.js","assets/@fortawesome/fontawesome-svg-core-0d830203.js","assets/moment-timezone-c556e14f.js","assets/moment-55cb88ed.js","assets/@uppy/aws-s3-5653d8cf.js"])).catch(function(n){return Promise.reject(new Error("Could not load canvg: "+n))}).then(function(n){return n.default?n.default:n})}Tt.API.addSvgAsImage=function(n,a,c,o,l,h,f,g){if(isNaN(a)||isNaN(c))throw me.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(o)||isNaN(l))throw me.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var b=document.createElement("canvas");b.width=o,b.height=l;var y=b.getContext("2d");y.fillStyle="#fff",y.fillRect(0,0,b.width,b.height);var S={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return e().then(function(O){return O.fromString(y,n,S)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(O){return O.render(S)}).then(function(){p.addImage(b.toDataURL("image/jpeg",1),a,c,o,l,f,g)})}}(),Tt.API.putTotalPages=function(r){var e,n=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(r,"g"),n=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(r,this.internal.getFont()),"g"),n=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var a=1;a<=this.internal.getNumberOfPages();a++)for(var c=0;c<this.internal.pages[a].length;c++)this.internal.pages[a][c]=this.internal.pages[a][c].replace(e,n);return this},Tt.API.viewerPreferences=function(r,e){var n;r=r||{},e=e||!1;var a,c,o,l={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(l),f=[],g=0,b=0,y=0;function S(O,F){var q,_=!1;for(q=0;q<O.length;q+=1)O[q]===F&&(_=!0);return _}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(l)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,r==="reset"||e===!0){var p=h.length;for(y=0;y<p;y+=1)n[h[y]].value=n[h[y]].defaultValue,n[h[y]].explicitSet=!1}if(ve(r)==="object"){for(c in r)if(o=r[c],S(h,c)&&o!==void 0){if(n[c].type==="boolean"&&typeof o=="boolean")n[c].value=o;else if(n[c].type==="name"&&S(n[c].valueSet,o))n[c].value=o;else if(n[c].type==="integer"&&Number.isInteger(o))n[c].value=o;else if(n[c].type==="array"){for(g=0;g<o.length;g+=1)if(a=!0,o[g].length===1&&typeof o[g][0]=="number")f.push(String(o[g]-1));else if(o[g].length>1){for(b=0;b<o[g].length;b+=1)typeof o[g][b]!="number"&&(a=!1);a===!0&&f.push([o[g][0]-1,o[g][1]-1].join(" "))}n[c].value="["+f.join(" ")+"]"}else n[c].value=n[c].defaultValue;n[c].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var O,F=[];for(O in n)n[O].explicitSet===!0&&(n[O].type==="name"?F.push("/"+O+" /"+n[O].value):F.push("/"+O+" "+n[O].value));F.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+F.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this},function(r){var e=function(){var a='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',c=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),o=unescape(encodeURIComponent(a)),l=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),h=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),g=o.length+l.length+h.length+c.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+g+" >>"),this.internal.write("stream"),this.internal.write(c+o+l+h+f),this.internal.write("endstream"),this.internal.write("endobj")},n=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};r.addMetadata=function(a,c){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:a,namespaceuri:c||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",n),this.internal.events.subscribe("postPutResources",e)),this}}(Tt.API),function(r){var e=r.API,n=e.pdfEscape16=function(o,l){for(var h,f=l.metadata.Unicode.widths,g=["","0","00","000","0000"],b=[""],y=0,S=o.length;y<S;++y){if(h=l.metadata.characterToGlyph(o.charCodeAt(y)),l.metadata.glyIdsUsed.push(h),l.metadata.toUnicode[h]=o.charCodeAt(y),f.indexOf(h)==-1&&(f.push(h),f.push([parseInt(l.metadata.widthOfGlyph(h),10)])),h=="0")return b.join("");h=h.toString(16),b.push(g[4-h.length],h)}return b.join("")},a=function(o){var l,h,f,g,b,y,S;for(b=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,f=[],y=0,S=(h=Object.keys(o).sort(function(p,O){return p-O})).length;y<S;y++)l=h[y],f.length>=100&&(b+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar`,f=[]),o[l]!==void 0&&o[l]!==null&&typeof o[l].toString=="function"&&(g=("0000"+o[l].toString(16)).slice(-4),l=("0000"+(+l).toString(16)).slice(-4),f.push("<"+l+"><"+g+">"));return f.length&&(b+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar
`),b+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};e.events.push(["putFont",function(o){(function(l){var h=l.font,f=l.out,g=l.newObject,b=l.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="Identity-H"){for(var y=h.metadata.Unicode.widths,S=h.metadata.subset.encode(h.metadata.glyIdsUsed,1),p="",O=0;O<S.length;O++)p+=String.fromCharCode(S[O]);var F=g();b({data:p,addLength1:!0,objectId:F}),f("endobj");var q=g();b({data:a(h.metadata.toUnicode),addLength1:!0,objectId:q}),f("endobj");var _=g();f("<<"),f("/Type /FontDescriptor"),f("/FontName /"+Oi(h.fontName)),f("/FontFile2 "+F+" 0 R"),f("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),f("/Flags "+h.metadata.flags),f("/StemV "+h.metadata.stemV),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f(">>"),f("endobj");var B=g();f("<<"),f("/Type /Font"),f("/BaseFont /"+Oi(h.fontName)),f("/FontDescriptor "+_+" 0 R"),f("/W "+r.API.PDFObject.convert(y)),f("/CIDToGIDMap /Identity"),f("/DW 1000"),f("/Subtype /CIDFontType2"),f("/CIDSystemInfo"),f("<<"),f("/Supplement 0"),f("/Registry (Adobe)"),f("/Ordering ("+h.encoding+")"),f(">>"),f(">>"),f("endobj"),h.objectNumber=g(),f("<<"),f("/Type /Font"),f("/Subtype /Type0"),f("/ToUnicode "+q+" 0 R"),f("/BaseFont /"+Oi(h.fontName)),f("/Encoding /"+h.encoding),f("/DescendantFonts ["+B+" 0 R]"),f(">>"),f("endobj"),h.isAlreadyPutted=!0}})(o)}]),e.events.push(["putFont",function(o){(function(l){var h=l.font,f=l.out,g=l.newObject,b=l.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="WinAnsiEncoding"){for(var y=h.metadata.rawData,S="",p=0;p<y.length;p++)S+=String.fromCharCode(y[p]);var O=g();b({data:S,addLength1:!0,objectId:O}),f("endobj");var F=g();b({data:a(h.metadata.toUnicode),addLength1:!0,objectId:F}),f("endobj");var q=g();f("<<"),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f("/StemV "+h.metadata.stemV),f("/Type /FontDescriptor"),f("/FontFile2 "+O+" 0 R"),f("/Flags 96"),f("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),f("/FontName /"+Oi(h.fontName)),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f(">>"),f("endobj"),h.objectNumber=g();for(var _=0;_<h.metadata.hmtx.widths.length;_++)h.metadata.hmtx.widths[_]=parseInt(h.metadata.hmtx.widths[_]*(1e3/h.metadata.head.unitsPerEm));f("<</Subtype/TrueType/Type/Font/ToUnicode "+F+" 0 R/BaseFont/"+Oi(h.fontName)+"/FontDescriptor "+q+" 0 R/Encoding/"+h.encoding+" /FirstChar 29 /LastChar 255 /Widths "+r.API.PDFObject.convert(h.metadata.hmtx.widths)+">>"),f("endobj"),h.isAlreadyPutted=!0}})(o)}]);var c=function(o){var l,h=o.text||"",f=o.x,g=o.y,b=o.options||{},y=o.mutex||{},S=y.pdfEscape,p=y.activeFontKey,O=y.fonts,F=p,q="",_=0,B="",Y=O[F].encoding;if(O[F].encoding!=="Identity-H")return{text:h,x:f,y:g,options:b,mutex:y};for(B=h,F=p,Array.isArray(h)&&(B=h[0]),_=0;_<B.length;_+=1)O[F].metadata.hasOwnProperty("cmap")&&(l=O[F].metadata.cmap.unicode.codeMap[B[_].charCodeAt(0)]),l||B[_].charCodeAt(0)<256&&O[F].metadata.hasOwnProperty("Unicode")?q+=B[_]:q+="";var ot="";return parseInt(F.slice(1))<14||Y==="WinAnsiEncoding"?ot=S(q,F).split("").map(function(ut){return ut.charCodeAt(0).toString(16)}).join(""):Y==="Identity-H"&&(ot=n(q,O[F])),y.isHex=!0,{text:ot,x:f,y:g,options:b,mutex:y}};e.events.push(["postProcessText",function(o){var l=o.text||"",h=[],f={text:l,x:o.x,y:o.y,options:o.options,mutex:o.mutex};if(Array.isArray(l)){var g=0;for(g=0;g<l.length;g+=1)Array.isArray(l[g])&&l[g].length===3?h.push([c(Object.assign({},f,{text:l[g][0]})).text,l[g][1],l[g][2]]):h.push(c(Object.assign({},f,{text:l[g]})).text);o.text=h}else o.text=c(Object.assign({},f,{text:l})).text}])}(Tt),function(r){var e=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};r.existsFileInVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0},r.addFileToVFS=function(n,a){return e.call(this),this.internal.vFS[n]=a,this},r.getFileFromVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0?this.internal.vFS[n]:null}}(Tt.API),function(r){r.__bidiEngine__=r.prototype.__bidiEngine__=function(a){var c,o,l,h,f,g,b,y=e,S=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],p=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],O={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},F={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},q=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],_=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),B=!1,Y=0;this.__bidiEngine__={};var ot=function(P){var k=P.charCodeAt(),W=k>>8,D=F[W];return D!==void 0?y[256*D+(255&k)]:W===252||W===253?"AL":_.test(W)?"L":W===8?"R":"N"},ut=function(P){for(var k,W=0;W<P.length;W++){if((k=ot(P.charAt(W)))==="L")return!1;if(k==="R")return!0}return!1},wt=function(P,k,W,D){var st,it,lt,$,ht=k[D];switch(ht){case"L":case"R":B=!1;break;case"N":case"AN":break;case"EN":B&&(ht="AN");break;case"AL":B=!0,ht="R";break;case"WS":ht="N";break;case"CS":D<1||D+1>=k.length||(st=W[D-1])!=="EN"&&st!=="AN"||(it=k[D+1])!=="EN"&&it!=="AN"?ht="N":B&&(it="AN"),ht=it===st?it:"N";break;case"ES":ht=(st=D>0?W[D-1]:"B")==="EN"&&D+1<k.length&&k[D+1]==="EN"?"EN":"N";break;case"ET":if(D>0&&W[D-1]==="EN"){ht="EN";break}if(B){ht="N";break}for(lt=D+1,$=k.length;lt<$&&k[lt]==="ET";)lt++;ht=lt<$&&k[lt]==="EN"?"EN":"N";break;case"NSM":if(l&&!h){for($=k.length,lt=D+1;lt<$&&k[lt]==="NSM";)lt++;if(lt<$){var pt=P[D],It=pt>=1425&&pt<=2303||pt===64286;if(st=k[lt],It&&(st==="R"||st==="AL")){ht="R";break}}}ht=D<1||(st=k[D-1])==="B"?"N":W[D-1];break;case"B":B=!1,c=!0,ht=Y;break;case"S":o=!0,ht="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":B=!1;break;case"BN":ht="N"}return ht},tt=function(P,k,W){var D=P.split("");return W&&z(D,W,{hiLevel:Y}),D.reverse(),k&&k.reverse(),D.join("")},z=function(P,k,W){var D,st,it,lt,$,ht=-1,pt=P.length,It=0,N=[],C=Y?p:S,M=[];for(B=!1,c=!1,o=!1,st=0;st<pt;st++)M[st]=ot(P[st]);for(it=0;it<pt;it++){if($=It,N[it]=wt(P,M,N,it),D=240&(It=C[$][O[N[it]]]),It&=15,k[it]=lt=C[It][5],D>0)if(D===16){for(st=ht;st<it;st++)k[st]=1;ht=-1}else ht=-1;if(C[It][6])ht===-1&&(ht=it);else if(ht>-1){for(st=ht;st<it;st++)k[st]=lt;ht=-1}M[it]==="B"&&(k[it]=0),W.hiLevel|=lt}o&&function(T,J,Q){for(var et=0;et<Q;et++)if(T[et]==="S"){J[et]=Y;for(var nt=et-1;nt>=0&&T[nt]==="WS";nt--)J[nt]=Y}}(M,k,pt)},rt=function(P,k,W,D,st){if(!(st.hiLevel<P)){if(P===1&&Y===1&&!c)return k.reverse(),void(W&&W.reverse());for(var it,lt,$,ht,pt=k.length,It=0;It<pt;){if(D[It]>=P){for($=It+1;$<pt&&D[$]>=P;)$++;for(ht=It,lt=$-1;ht<lt;ht++,lt--)it=k[ht],k[ht]=k[lt],k[lt]=it,W&&(it=W[ht],W[ht]=W[lt],W[lt]=it);It=$}It++}}},dt=function(P,k,W){var D=P.split(""),st={hiLevel:Y};return W||(W=[]),z(D,W,st),function(it,lt,$){if($.hiLevel!==0&&b)for(var ht,pt=0;pt<it.length;pt++)lt[pt]===1&&(ht=q.indexOf(it[pt]))>=0&&(it[pt]=q[ht+1])}(D,W,st),rt(2,D,k,W,st),rt(1,D,k,W,st),D.join("")};return this.__bidiEngine__.doBidiReorder=function(P,k,W){if(function(st,it){if(it)for(var lt=0;lt<st.length;lt++)it[lt]=lt;h===void 0&&(h=ut(st)),g===void 0&&(g=ut(st))}(P,k),l||!f||g)if(l&&f&&h^g)Y=h?1:0,P=tt(P,k,W);else if(!l&&f&&g)Y=h?1:0,P=dt(P,k,W),P=tt(P,k);else if(!l||h||f||g){if(l&&!f&&h^g)P=tt(P,k),h?(Y=0,P=dt(P,k,W)):(Y=1,P=dt(P,k,W),P=tt(P,k));else if(l&&h&&!f&&g)Y=1,P=dt(P,k,W),P=tt(P,k);else if(!l&&!f&&h^g){var D=b;h?(Y=1,P=dt(P,k,W),Y=0,b=!1,P=dt(P,k,W),b=D):(Y=0,P=dt(P,k,W),P=tt(P,k),Y=1,b=!1,P=dt(P,k,W),b=D,P=tt(P,k))}}else Y=0,P=dt(P,k,W);else Y=h?1:0,P=dt(P,k,W);return P},this.__bidiEngine__.setOptions=function(P){P&&(l=P.isInputVisual,f=P.isOutputVisual,h=P.isInputRtl,g=P.isOutputRtl,b=P.isSymmetricSwapping)},this.__bidiEngine__.setOptions(a),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],n=new r.__bidiEngine__({isInputVisual:!0});r.API.events.push(["postProcessText",function(a){var c=a.text,o=(a.x,a.y,a.options||{}),l=(a.mutex,o.lang,[]);if(o.isInputVisual=typeof o.isInputVisual!="boolean"||o.isInputVisual,n.setOptions(o),Object.prototype.toString.call(c)==="[object Array]"){var h=0;for(l=[],h=0;h<c.length;h+=1)Object.prototype.toString.call(c[h])==="[object Array]"?l.push([n.doBidiReorder(c[h][0]),c[h][1],c[h][2]]):l.push([n.doBidiReorder(c[h])]);a.text=l}else a.text=n.doBidiReorder(c);n.setOptions({isInputVisual:!0})}])}(Tt),Tt.API.TTFFont=function(){function r(e){var n;if(this.rawData=e,n=this.contents=new Br(e),this.contents.pos=4,n.readString(4)==="ttcf")throw new Error("TTCF not supported.");n.pos=0,this.parse(),this.subset=new kl(this),this.registerTTF()}return r.open=function(e){return new r(e)},r.prototype.parse=function(){return this.directory=new pl(this.contents),this.head=new ml(this),this.name=new Ll(this),this.cmap=new Wc(this),this.toUnicode={},this.hhea=new vl(this),this.maxp=new Nl(this),this.hmtx=new Al(this),this.post=new yl(this),this.os2=new bl(this),this.loca=new Pl(this),this.glyf=new xl(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},r.prototype.registerTTF=function(){var e,n,a,c,o;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var l,h,f,g;for(g=[],l=0,h=(f=this.bbox).length;l<h;l++)e=f[l],g.push(Math.round(e*this.scaleFactor));return g}).call(this),this.stemV=0,this.post.exists?(a=255&(c=this.post.italic_angle),32768&(n=c>>16)&&(n=-(1+(65535^n))),this.italicAngle=+(n+"."+a)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(o=this.familyClass)===1||o===2||o===3||o===4||o===5||o===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},r.prototype.characterToGlyph=function(e){var n;return((n=this.cmap.unicode)!=null?n.codeMap[e]:void 0)||0},r.prototype.widthOfGlyph=function(e){var n;return n=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(e).advance*n},r.prototype.widthOfString=function(e,n,a){var c,o,l,h;for(l=0,o=0,h=(e=""+e).length;0<=h?o<h:o>h;o=0<=h?++o:--o)c=e.charCodeAt(o),l+=this.widthOfGlyph(this.characterToGlyph(c))+a*(1e3/n)||0;return l*(n/1e3)},r.prototype.lineHeight=function(e,n){var a;return n==null&&(n=!1),a=n?this.lineGap:0,(this.ascender+a-this.decender)/1e3*e},r}();var zn,Br=function(){function r(e){this.data=e??[],this.pos=0,this.length=this.data.length}return r.prototype.readByte=function(){return this.data[this.pos++]},r.prototype.writeByte=function(e){return this.data[this.pos++]=e},r.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},r.prototype.writeUInt32=function(e){return this.writeByte(e>>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e)},r.prototype.readInt32=function(){var e;return(e=this.readUInt32())>=2147483648?e-4294967296:e},r.prototype.writeInt32=function(e){return e<0&&(e+=4294967296),this.writeUInt32(e)},r.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},r.prototype.writeUInt16=function(e){return this.writeByte(e>>8&255),this.writeByte(255&e)},r.prototype.readInt16=function(){var e;return(e=this.readUInt16())>=32768?e-65536:e},r.prototype.writeInt16=function(e){return e<0&&(e+=65536),this.writeUInt16(e)},r.prototype.readString=function(e){var n,a;for(a=[],n=0;0<=e?n<e:n>e;n=0<=e?++n:--n)a[n]=String.fromCharCode(this.readByte());return a.join("")},r.prototype.writeString=function(e){var n,a,c;for(c=[],n=0,a=e.length;0<=a?n<a:n>a;n=0<=a?++n:--n)c.push(this.writeByte(e.charCodeAt(n)));return c},r.prototype.readShort=function(){return this.readInt16()},r.prototype.writeShort=function(e){return this.writeInt16(e)},r.prototype.readLongLong=function(){var e,n,a,c,o,l,h,f;return e=this.readByte(),n=this.readByte(),a=this.readByte(),c=this.readByte(),o=this.readByte(),l=this.readByte(),h=this.readByte(),f=this.readByte(),128&e?-1*(72057594037927940*(255^e)+281474976710656*(255^n)+1099511627776*(255^a)+4294967296*(255^c)+16777216*(255^o)+65536*(255^l)+256*(255^h)+(255^f)+1):72057594037927940*e+281474976710656*n+1099511627776*a+4294967296*c+16777216*o+65536*l+256*h+f},r.prototype.writeLongLong=function(e){var n,a;return n=Math.floor(e/4294967296),a=**********&e,this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n),this.writeByte(a>>24&255),this.writeByte(a>>16&255),this.writeByte(a>>8&255),this.writeByte(255&a)},r.prototype.readInt=function(){return this.readInt32()},r.prototype.writeInt=function(e){return this.writeInt32(e)},r.prototype.read=function(e){var n,a;for(n=[],a=0;0<=e?a<e:a>e;a=0<=e?++a:--a)n.push(this.readByte());return n},r.prototype.write=function(e){var n,a,c,o;for(o=[],a=0,c=e.length;a<c;a++)n=e[a],o.push(this.writeByte(n));return o},r}(),pl=function(){var r;function e(n){var a,c,o;for(this.scalarType=n.readInt(),this.tableCount=n.readShort(),this.searchRange=n.readShort(),this.entrySelector=n.readShort(),this.rangeShift=n.readShort(),this.tables={},c=0,o=this.tableCount;0<=o?c<o:c>o;c=0<=o?++c:--c)a={tag:n.readString(4),checksum:n.readInt(),offset:n.readInt(),length:n.readInt()},this.tables[a.tag]=a}return e.prototype.encode=function(n){var a,c,o,l,h,f,g,b,y,S,p,O,F;for(F in p=Object.keys(n).length,f=Math.log(2),y=16*Math.floor(Math.log(p)/f),l=Math.floor(y/f),b=16*p-y,(c=new Br).writeInt(this.scalarType),c.writeShort(p),c.writeShort(y),c.writeShort(l),c.writeShort(b),o=16*p,g=c.pos+o,h=null,O=[],n)for(S=n[F],c.writeString(F),c.writeInt(r(S)),c.writeInt(g),c.writeInt(S.length),O=O.concat(S),F==="head"&&(h=g),g+=S.length;g%4;)O.push(0),g++;return c.write(O),a=2981146554-r(c.data),c.pos=h+8,c.writeUInt32(a),c.data},r=function(n){var a,c,o,l;for(n=Vc.call(n);n.length%4;)n.push(0);for(o=new Br(n),c=0,a=0,l=n.length;a<l;a=a+=4)c+=o.readUInt32();return **********&c},e}(),gl={}.hasOwnProperty,er=function(r,e){for(var n in e)gl.call(e,n)&&(r[n]=e[n]);function a(){this.constructor=r}return a.prototype=e.prototype,r.prototype=new a,r.__super__=e.prototype,r};zn=function(){function r(e){var n;this.file=e,n=this.file.directory.tables[this.tag],this.exists=!!n,n&&(this.offset=n.offset,this.length=n.length,this.parse(this.file.contents))}return r.prototype.parse=function(){},r.prototype.encode=function(){},r.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},r}();var ml=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="head",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.revision=n.readInt(),this.checkSumAdjustment=n.readInt(),this.magicNumber=n.readInt(),this.flags=n.readShort(),this.unitsPerEm=n.readShort(),this.created=n.readLongLong(),this.modified=n.readLongLong(),this.xMin=n.readShort(),this.yMin=n.readShort(),this.xMax=n.readShort(),this.yMax=n.readShort(),this.macStyle=n.readShort(),this.lowestRecPPEM=n.readShort(),this.fontDirectionHint=n.readShort(),this.indexToLocFormat=n.readShort(),this.glyphDataFormat=n.readShort()},e.prototype.encode=function(n){var a;return(a=new Br).writeInt(this.version),a.writeInt(this.revision),a.writeInt(this.checkSumAdjustment),a.writeInt(this.magicNumber),a.writeShort(this.flags),a.writeShort(this.unitsPerEm),a.writeLongLong(this.created),a.writeLongLong(this.modified),a.writeShort(this.xMin),a.writeShort(this.yMin),a.writeShort(this.xMax),a.writeShort(this.yMax),a.writeShort(this.macStyle),a.writeShort(this.lowestRecPPEM),a.writeShort(this.fontDirectionHint),a.writeShort(n),a.writeShort(this.glyphDataFormat),a.data},e}(),xc=function(){function r(e,n){var a,c,o,l,h,f,g,b,y,S,p,O,F,q,_,B,Y;switch(this.platformID=e.readUInt16(),this.encodingID=e.readShort(),this.offset=n+e.readInt(),y=e.pos,e.pos=this.offset,this.format=e.readUInt16(),this.length=e.readUInt16(),this.language=e.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(f=0;f<256;++f)this.codeMap[f]=e.readByte();break;case 4:for(p=e.readUInt16(),S=p/2,e.pos+=6,o=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),e.pos+=2,F=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),g=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),b=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),c=(this.length-e.pos+this.offset)/2,h=function(){var ot,ut;for(ut=[],f=ot=0;0<=c?ot<c:ot>c;f=0<=c?++ot:--ot)ut.push(e.readUInt16());return ut}(),f=_=0,Y=o.length;_<Y;f=++_)for(q=o[f],a=B=O=F[f];O<=q?B<=q:B>=q;a=O<=q?++B:--B)b[f]===0?l=a+g[f]:(l=h[b[f]/2+(a-O)-(S-f)]||0)!==0&&(l+=g[f]),this.codeMap[a]=65535&l}e.pos=y}return r.encode=function(e,n){var a,c,o,l,h,f,g,b,y,S,p,O,F,q,_,B,Y,ot,ut,wt,tt,z,rt,dt,P,k,W,D,st,it,lt,$,ht,pt,It,N,C,M,T,J,Q,et,nt,At,Nt,Ft;switch(D=new Br,l=Object.keys(e).sort(function(_t,Ut){return _t-Ut}),n){case"macroman":for(F=0,q=function(){var _t=[];for(O=0;O<256;++O)_t.push(0);return _t}(),B={0:0},o={},st=0,ht=l.length;st<ht;st++)B[nt=e[c=l[st]]]==null&&(B[nt]=++F),o[c]={old:e[c],new:B[e[c]]},q[c]=B[e[c]];return D.writeUInt16(1),D.writeUInt16(0),D.writeUInt32(12),D.writeUInt16(0),D.writeUInt16(262),D.writeUInt16(0),D.write(q),{charMap:o,subtable:D.data,maxGlyphID:F+1};case"unicode":for(k=[],y=[],Y=0,B={},a={},_=g=null,it=0,pt=l.length;it<pt;it++)B[ut=e[c=l[it]]]==null&&(B[ut]=++Y),a[c]={old:ut,new:B[ut]},h=B[ut]-c,_!=null&&h===g||(_&&y.push(_),k.push(c),g=h),_=c;for(_&&y.push(_),y.push(65535),k.push(65535),dt=2*(rt=k.length),z=2*Math.pow(Math.log(rt)/Math.LN2,2),S=Math.log(z/2)/Math.LN2,tt=2*rt-z,f=[],wt=[],p=[],O=lt=0,It=k.length;lt<It;O=++lt){if(P=k[O],b=y[O],P===65535){f.push(0),wt.push(0);break}if(P-(W=a[P].new)>=32768)for(f.push(0),wt.push(2*(p.length+rt-O)),c=$=P;P<=b?$<=b:$>=b;c=P<=b?++$:--$)p.push(a[c].new);else f.push(W-P),wt.push(0)}for(D.writeUInt16(3),D.writeUInt16(1),D.writeUInt32(12),D.writeUInt16(4),D.writeUInt16(16+8*rt+2*p.length),D.writeUInt16(0),D.writeUInt16(dt),D.writeUInt16(z),D.writeUInt16(S),D.writeUInt16(tt),Q=0,N=y.length;Q<N;Q++)c=y[Q],D.writeUInt16(c);for(D.writeUInt16(0),et=0,C=k.length;et<C;et++)c=k[et],D.writeUInt16(c);for(At=0,M=f.length;At<M;At++)h=f[At],D.writeUInt16(h);for(Nt=0,T=wt.length;Nt<T;Nt++)ot=wt[Nt],D.writeUInt16(ot);for(Ft=0,J=p.length;Ft<J;Ft++)F=p[Ft],D.writeUInt16(F);return{charMap:a,subtable:D.data,maxGlyphID:Y+1}}},r}(),Wc=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="cmap",e.prototype.parse=function(n){var a,c,o;for(n.pos=this.offset,this.version=n.readUInt16(),o=n.readUInt16(),this.tables=[],this.unicode=null,c=0;0<=o?c<o:c>o;c=0<=o?++c:--c)a=new xc(n,this.offset),this.tables.push(a),a.isUnicode&&this.unicode==null&&(this.unicode=a);return!0},e.encode=function(n,a){var c,o;return a==null&&(a="macroman"),c=xc.encode(n,a),(o=new Br).writeUInt16(0),o.writeUInt16(1),c.table=o.data.concat(c.subtable),c},e}(),vl=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="hhea",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.ascender=n.readShort(),this.decender=n.readShort(),this.lineGap=n.readShort(),this.advanceWidthMax=n.readShort(),this.minLeftSideBearing=n.readShort(),this.minRightSideBearing=n.readShort(),this.xMaxExtent=n.readShort(),this.caretSlopeRise=n.readShort(),this.caretSlopeRun=n.readShort(),this.caretOffset=n.readShort(),n.pos+=8,this.metricDataFormat=n.readShort(),this.numberOfMetrics=n.readUInt16()},e}(),bl=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="OS/2",e.prototype.parse=function(n){if(n.pos=this.offset,this.version=n.readUInt16(),this.averageCharWidth=n.readShort(),this.weightClass=n.readUInt16(),this.widthClass=n.readUInt16(),this.type=n.readShort(),this.ySubscriptXSize=n.readShort(),this.ySubscriptYSize=n.readShort(),this.ySubscriptXOffset=n.readShort(),this.ySubscriptYOffset=n.readShort(),this.ySuperscriptXSize=n.readShort(),this.ySuperscriptYSize=n.readShort(),this.ySuperscriptXOffset=n.readShort(),this.ySuperscriptYOffset=n.readShort(),this.yStrikeoutSize=n.readShort(),this.yStrikeoutPosition=n.readShort(),this.familyClass=n.readShort(),this.panose=function(){var a,c;for(c=[],a=0;a<10;++a)c.push(n.readByte());return c}(),this.charRange=function(){var a,c;for(c=[],a=0;a<4;++a)c.push(n.readInt());return c}(),this.vendorID=n.readString(4),this.selection=n.readShort(),this.firstCharIndex=n.readShort(),this.lastCharIndex=n.readShort(),this.version>0&&(this.ascent=n.readShort(),this.descent=n.readShort(),this.lineGap=n.readShort(),this.winAscent=n.readShort(),this.winDescent=n.readShort(),this.codePageRange=function(){var a,c;for(c=[],a=0;a<2;a=++a)c.push(n.readInt());return c}(),this.version>1))return this.xHeight=n.readShort(),this.capHeight=n.readShort(),this.defaultChar=n.readShort(),this.breakChar=n.readShort(),this.maxContext=n.readShort()},e}(),yl=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="post",e.prototype.parse=function(n){var a,c,o;switch(n.pos=this.offset,this.format=n.readInt(),this.italicAngle=n.readInt(),this.underlinePosition=n.readShort(),this.underlineThickness=n.readShort(),this.isFixedPitch=n.readInt(),this.minMemType42=n.readInt(),this.maxMemType42=n.readInt(),this.minMemType1=n.readInt(),this.maxMemType1=n.readInt(),this.format){case 65536:break;case 131072:var l;for(c=n.readUInt16(),this.glyphNameIndex=[],l=0;0<=c?l<c:l>c;l=0<=c?++l:--l)this.glyphNameIndex.push(n.readUInt16());for(this.names=[],o=[];n.pos<this.offset+this.length;)a=n.readByte(),o.push(this.names.push(n.readString(a)));return o;case 151552:return c=n.readUInt16(),this.offsets=n.read(c);case 196608:break;case 262144:return this.map=(function(){var h,f,g;for(g=[],l=h=0,f=this.file.maxp.numGlyphs;0<=f?h<f:h>f;l=0<=f?++h:--h)g.push(n.readUInt32());return g}).call(this)}},e}(),wl=function(r,e){this.raw=r,this.length=r.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},Ll=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="name",e.prototype.parse=function(n){var a,c,o,l,h,f,g,b,y,S,p;for(n.pos=this.offset,n.readShort(),a=n.readShort(),f=n.readShort(),c=[],l=0;0<=a?l<a:l>a;l=0<=a?++l:--l)c.push({platformID:n.readShort(),encodingID:n.readShort(),languageID:n.readShort(),nameID:n.readShort(),length:n.readShort(),offset:this.offset+f+n.readShort()});for(g={},l=y=0,S=c.length;y<S;l=++y)o=c[l],n.pos=o.offset,b=n.readString(o.length),h=new wl(b,o),g[p=o.nameID]==null&&(g[p]=[]),g[o.nameID].push(h);this.strings=g,this.copyright=g[0],this.fontFamily=g[1],this.fontSubfamily=g[2],this.uniqueSubfamily=g[3],this.fontName=g[4],this.version=g[5];try{this.postscriptName=g[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=g[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=g[7],this.manufacturer=g[8],this.designer=g[9],this.description=g[10],this.vendorUrl=g[11],this.designerUrl=g[12],this.license=g[13],this.licenseUrl=g[14],this.preferredFamily=g[15],this.preferredSubfamily=g[17],this.compatibleFull=g[18],this.sampleText=g[19]},e}(),Nl=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="maxp",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.numGlyphs=n.readUInt16(),this.maxPoints=n.readUInt16(),this.maxContours=n.readUInt16(),this.maxCompositePoints=n.readUInt16(),this.maxComponentContours=n.readUInt16(),this.maxZones=n.readUInt16(),this.maxTwilightPoints=n.readUInt16(),this.maxStorage=n.readUInt16(),this.maxFunctionDefs=n.readUInt16(),this.maxInstructionDefs=n.readUInt16(),this.maxStackElements=n.readUInt16(),this.maxSizeOfInstructions=n.readUInt16(),this.maxComponentElements=n.readUInt16(),this.maxComponentDepth=n.readUInt16()},e}(),Al=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="hmtx",e.prototype.parse=function(n){var a,c,o,l,h,f,g;for(n.pos=this.offset,this.metrics=[],a=0,f=this.file.hhea.numberOfMetrics;0<=f?a<f:a>f;a=0<=f?++a:--a)this.metrics.push({advance:n.readUInt16(),lsb:n.readInt16()});for(o=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var b,y;for(y=[],a=b=0;0<=o?b<o:b>o;a=0<=o?++b:--b)y.push(n.readInt16());return y}(),this.widths=(function(){var b,y,S,p;for(p=[],b=0,y=(S=this.metrics).length;b<y;b++)l=S[b],p.push(l.advance);return p}).call(this),c=this.widths[this.widths.length-1],g=[],a=h=0;0<=o?h<o:h>o;a=0<=o?++h:--h)g.push(this.widths.push(c));return g},e.prototype.forGlyph=function(n){return n in this.metrics?this.metrics[n]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[n-this.metrics.length]}},e}(),Vc=[].slice,xl=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(n){var a,c,o,l,h,f,g,b,y,S;return n in this.cache?this.cache[n]:(l=this.file.loca,a=this.file.contents,c=l.indexOf(n),(o=l.lengthOf(n))===0?this.cache[n]=null:(a.pos=this.offset+c,h=(f=new Br(a.read(o))).readShort(),b=f.readShort(),S=f.readShort(),g=f.readShort(),y=f.readShort(),this.cache[n]=h===-1?new _l(f,b,S,g,y):new Sl(f,h,b,S,g,y),this.cache[n]))},e.prototype.encode=function(n,a,c){var o,l,h,f,g;for(h=[],l=[],f=0,g=a.length;f<g;f++)o=n[a[f]],l.push(h.length),o&&(h=h.concat(o.encode(c)));return l.push(h.length),{table:h,offsets:l}},e}(),Sl=function(){function r(e,n,a,c,o,l){this.raw=e,this.numberOfContours=n,this.xMin=a,this.yMin=c,this.xMax=o,this.yMax=l,this.compound=!1}return r.prototype.encode=function(){return this.raw.data},r}(),_l=function(){function r(e,n,a,c,o){var l,h;for(this.raw=e,this.xMin=n,this.yMin=a,this.xMax=c,this.yMax=o,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],l=this.raw;h=l.readShort(),this.glyphOffsets.push(l.pos),this.glyphIDs.push(l.readUInt16()),32&h;)l.pos+=1&h?4:2,128&h?l.pos+=8:64&h?l.pos+=4:8&h&&(l.pos+=2)}return r.prototype.encode=function(){var e,n,a;for(n=new Br(Vc.call(this.raw.data)),e=0,a=this.glyphIDs.length;e<a;++e)n.pos=this.glyphOffsets[e];return n.data},r}(),Pl=function(r){function e(){return e.__super__.constructor.apply(this,arguments)}return er(e,zn),e.prototype.tag="loca",e.prototype.parse=function(n){var a,c;return n.pos=this.offset,a=this.file.head.indexToLocFormat,this.offsets=a===0?(function(){var o,l;for(l=[],c=0,o=this.length;c<o;c+=2)l.push(2*n.readUInt16());return l}).call(this):(function(){var o,l;for(l=[],c=0,o=this.length;c<o;c+=4)l.push(n.readUInt32());return l}).call(this)},e.prototype.indexOf=function(n){return this.offsets[n]},e.prototype.lengthOf=function(n){return this.offsets[n+1]-this.offsets[n]},e.prototype.encode=function(n,a){for(var c=new Uint32Array(this.offsets.length),o=0,l=0,h=0;h<c.length;++h)if(c[h]=o,l<a.length&&a[l]==h){++l,c[h]=o;var f=this.offsets[h],g=this.offsets[h+1]-f;g>0&&(o+=g)}for(var b=new Array(4*c.length),y=0;y<c.length;++y)b[4*y+3]=255&c[y],b[4*y+2]=(65280&c[y])>>8,b[4*y+1]=(16711680&c[y])>>16,b[4*y]=(**********&c[y])>>24;return b},e}(),kl=function(){function r(e){this.font=e,this.subset={},this.unicodes={},this.next=33}return r.prototype.generateCmap=function(){var e,n,a,c,o;for(n in c=this.font.cmap.tables[0].codeMap,e={},o=this.subset)a=o[n],e[n]=c[a];return e},r.prototype.glyphsFor=function(e){var n,a,c,o,l,h,f;for(c={},l=0,h=e.length;l<h;l++)c[o=e[l]]=this.font.glyf.glyphFor(o);for(o in n=[],c)(a=c[o])!=null&&a.compound&&n.push.apply(n,a.glyphIDs);if(n.length>0)for(o in f=this.glyphsFor(n))a=f[o],c[o]=a;return c},r.prototype.encode=function(e,n){var a,c,o,l,h,f,g,b,y,S,p,O,F,q,_;for(c in a=Wc.encode(this.generateCmap(),"unicode"),l=this.glyphsFor(e),p={0:0},_=a.charMap)p[(f=_[c]).old]=f.new;for(O in S=a.maxGlyphID,l)O in p||(p[O]=S++);return b=function(B){var Y,ot;for(Y in ot={},B)ot[B[Y]]=Y;return ot}(p),y=Object.keys(b).sort(function(B,Y){return B-Y}),F=function(){var B,Y,ot;for(ot=[],B=0,Y=y.length;B<Y;B++)h=y[B],ot.push(b[h]);return ot}(),o=this.font.glyf.encode(l,F,p),g=this.font.loca.encode(o.offsets,F),q={cmap:this.font.cmap.raw(),glyf:o.table,loca:g,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(n)},this.font.os2.exists&&(q["OS/2"]=this.font.os2.raw()),this.font.directory.encode(q)},r}();Tt.API.PDFObject=function(){var r;function e(){}return r=function(n,a){return(Array(a+1).join("0")+n).slice(-a)},e.convert=function(n){var a,c,o,l;if(Array.isArray(n))return"["+function(){var h,f,g;for(g=[],h=0,f=n.length;h<f;h++)a=n[h],g.push(e.convert(a));return g}().join(" ")+"]";if(typeof n=="string")return"/"+n;if(n!=null&&n.isString)return"("+n+")";if(n instanceof Date)return"(D:"+r(n.getUTCFullYear(),4)+r(n.getUTCMonth(),2)+r(n.getUTCDate(),2)+r(n.getUTCHours(),2)+r(n.getUTCMinutes(),2)+r(n.getUTCSeconds(),2)+"Z)";if({}.toString.call(n)==="[object Object]"){for(c in o=["<<"],n)l=n[c],o.push("/"+c+" "+e.convert(l));return o.push(">>"),o.join(`
`)}return""+n},e}();const Il=Object.freeze(Object.defineProperty({__proto__:null,AcroForm:ol,AcroFormAppearance:Mt,AcroFormButton:Te,AcroFormCheckBox:ga,AcroFormChoiceField:Kr,AcroFormComboBox:$r,AcroFormEditBox:da,AcroFormListBox:Zr,AcroFormPasswordField:ma,AcroFormPushButton:pa,AcroFormRadioButton:Qr,AcroFormTextField:jr,GState:ba,ShadingPattern:Ir,TilingPattern:Xr,default:Tt,jsPDF:Tt},Symbol.toStringTag,{value:"Module"}));export{Tt as E,gs as _,Il as j};
