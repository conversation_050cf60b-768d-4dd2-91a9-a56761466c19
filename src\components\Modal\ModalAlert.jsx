// import { Close, danger, warning } from 'Assets/svgs'
import React from 'react';

const ModalAlert = ({
  closeModalFunction,
  message,
  title,
  messageClasses,
  titleClasses,
  buttonText = 'OK',
}) => {
  return (
    <aside
      className='fixed bottom-0 left-0 right-0 top-0 flex items-center justify-center '
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: '1000',
      }}
    >
      <section className='flex w-[25rem] min-w-[25rem]  flex-col gap-6 rounded-[.5rem] bg-white px-6 py-6'>
        <div className='flex justify-between '>
          <div>
            {/* <img crossOrigin='anonymous' src={danger} width={30} height={30} alt='danger' /> */}
          </div>
          <button onClick={closeModalFunction}>
            {/* <img crossOrigin='anonymous' src={Close} width={30} height={30} alt='close' /> */}
          </button>
        </div>
        {title ? (
          <div className={` ${titleClasses}`}>
            {title}
            {/* <div className='py-3 text-[1rem] leading-[1.75rem] text-[#333333]  '>{message}</div> */}
          </div>
        ) : null}
        {message ? (
          <div
            className={`text-[1.5rem] font-normal leading-[1.5rem] text-[#667085] ${messageClasses}`}
          >
            {message}
            {/* <div className='py-3 text-[1rem] leading-[1.75rem] text-[#333333]  '>{message}</div> */}
          </div>
        ) : null}

        <div className='flex w-full items-center justify-center font-medium leading-[1.5rem] text-[base]'>
          <button
            className='flex  h-[2.75rem] w-[10.375rem] items-center justify-center rounded-[.5rem] border border-[#DC5A5D] bg-[#DC5A5D] text-white'
            style={{
              width: '10.375rem',
              height: '2.75rem',
            }}
            onClick={closeModalFunction}
          >
            {buttonText}
          </button>
        </div>
      </section>
    </aside>
  );
};

export default ModalAlert;
