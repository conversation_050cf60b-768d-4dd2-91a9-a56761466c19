import React from "react";
import { <PERSON><PERSON>ill<PERSON>ye, AiOutlineEdit } from "react-icons/ai";
import { LazyLoad } from "Components/LazyLoad";
import { MkdPopover } from "Components/MkdPopover";
import { DropdownOption } from "Components/DropdownOptions";
import {
  KebabIcon,
  TrashIcon,
  // NotesIcon,
  // EditIcon,
  EditIcon2,
  UpdateIcon,
  // ChevronRightIcon,
} from "Assets/svgs";
import { operations, optionTypes } from "Utils/config";
import RenderDropdownActions from "./RenderDropdownActions";
import RenderActions from "./RenderActions";
import { processBind } from "./MkdListTableBindOperations";
const checkBinding = (action, row) => {
  if (action?.bind && ["hide"].includes(action?.bind?.action)) {
    return !processBind(action, row);
  } else {
    return true;
  }
};
const statusNames = {
  status: "status",
  verify: "verify",
  receipt_status: "receipt_status",
};

const getStatusIcon = (name) => {
  switch (name) {
    case statusNames.status:
      return <></>;
    case statusNames.verify:
      return <></>;
    case statusNames.receipt_status:
      return <></>;
  }
};

const getStatusMap = (name, config, row) => {
  if (config.mappingExist) {
    return config.mappingAction[row[name]];
  } else {
    return name;
  }
};

const MkdListTableRowDropdown = ({
  row,
  columns,
  actions,
  actionId = "id",
  setDeleteId,
  onPopoverStateChange = null,
}) => {
  const statusCol = columns?.find((column) =>
    ["status", "verify", "receipt_status"]?.includes(column?.accessor)
  );
  const statusRow = Object.keys(row).find((key) =>
    ["status", "verify", "receipt_status"]?.includes(key)
  );

  // console.log("statusRow >>", statusRow);

  return (
    <>
      {Object.keys(actions).filter(
        (key) =>
          actions[key]?.show &&
          actions[key]?.locations &&
          actions[key]?.locations?.length &&
          actions[key]?.locations?.includes("dropdown") &&
          checkBinding(actions[key], row)
      ).length ? (
        <div className="items center z-3 relative flex h-fit w-fit">
          <LazyLoad>
            <MkdPopover
              display={
                <KebabIcon
                  className="h-[1.5rem] w-[1.5rem] rotate-90"
                  stroke="#1F1D1A"
                />
              }
              tooltipClasses="!rounded-[.125rem] !min-w-fit !w-fit !max-w-fit !px-0 !right-[3.25rem] !border !border-black bg-white"
              place={"left-end"}
              onPopoverStateChange={onPopoverStateChange}
              classNameArrow={"!border-b !border-r !border-primary-black"}
            >
              {actions?.edit?.show && (
                <LazyLoad>
                  <DropdownOption
                    className="!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg"
                    icon={<EditIcon2 />}
                    name={"Edit"}
                    onClick={() => {
                      if (actions?.edit?.action) {
                        actions?.edit?.action([row[actionId]]);
                      }
                    }}
                  />
                </LazyLoad>
              )}

              {actions?.view?.show && (
                <LazyLoad>
                  <DropdownOption
                    className="!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg"
                    icon={<AiFillEye className="text-gray-400" />}
                    name={"View"}
                    onClick={() => {
                      if (actions?.view?.action) {
                        actions?.view?.action([row[actionId]]);
                      }
                    }}
                  />
                </LazyLoad>
              )}

              {actions?.status?.show && (
                <LazyLoad>
                  <DropdownOption
                    className="!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg"
                    icon={<UpdateIcon />}
                    name={getStatusMap(statusRow, statusCol, row)}
                    onClick={() => {
                      if (actions?.status?.action) {
                        actions?.status?.action([row[actionId]]);
                      }
                    }}
                  />
                </LazyLoad>
              )}

              {actions?.delete?.show && (
                <LazyLoad>
                  <DropdownOption
                    className="!w-[11rem] !min-w-[11rem] !max-w-[11rem] !bg-brown-main-bg"
                    icon={<TrashIcon />}
                    name={"Delete"}
                    onClick={() => {
                      if (!actions[key]?.action) {
                        if (setDeleteId) {
                          setDeleteId(row[actionId]);
                        }
                      } else if (actions[key]?.action) {
                        actions[key]?.action([row[actionId]]);
                      }
                      // setDeleteId(row[actionId]);
                    }}
                  />
                </LazyLoad>
              )}

              {Object.keys(actions)
                .filter(
                  (key) =>
                    actions[key]?.show &&
                    actions[key]?.locations &&
                    actions[key]?.locations?.includes("dropdown")
                )
                .map((key, keyIndex) => {
                  if (
                    actions[key]?.type &&
                    [optionTypes.DROPDOWN].includes(actions[key]?.type)
                  ) {
                    return (
                      <RenderDropdownActions
                        row={row}
                        key={keyIndex}
                        actionKey={key}
                        actionId={actionId}
                        action={actions[key]}
                      />
                    );
                  } else if (!actions[key]?.type) {
                    return (
                      <RenderActions
                        row={row}
                        key={keyIndex}
                        actionId={actionId}
                        action={actions[key]}
                      />
                    );
                  }
                })}
            </MkdPopover>
          </LazyLoad>
        </div>
      ) : null}
    </>
  );
};

export default MkdListTableRowDropdown;
