import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";

const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

const UploadedSessions = ({
  canUpload = true,
  uploadedFiles,
  setDeleteFileId,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);

  return (
    <>
      <div className="flex h-full w-full flex-col rounded-sm bg-boxdark">
        {/* Header */}
        <div className="mb-4 flex items-center justify-between border-b border-strokedark pb-4">
          <h3 className="text-xl font-semibold text-white">Sessions</h3>
          {canUpload && (
            <span className="text-sm text-bodydark2">
              {uploadedFiles?.length || 0} files uploaded
            </span>
          )}
        </div>

        {/* Files List */}
        <div className="custom-overflow flex-1 overflow-y-auto">
          <div className="space-y-4">
            {uploadedFiles?.map((file, index) => {
              const fileName = file.url.split("/").pop();
              const fileExt = fileName.split(".").pop().toLowerCase();
              const isAudio = audioFileTypes.includes(fileExt);

              return (
                <div
                  key={index}
                  className="rounded border border-strokedark bg-boxdark-2 p-4"
                >
                  <div className="flex items-center justify-between gap-4">
                    <div className="min-w-0 flex-1">
                      <a
                        href={file.url}
                        target="_blank"
                        className="mb-1 truncate text-sm font-medium text-white underline"
                      >
                        {fileName}
                      </a>
                      {isAudio && (
                        <div className="mt-2">
                          <AudioPlayer fileSource={file.url} />
                        </div>
                      )}
                    </div>
                    {canUpload && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowDeleteFileConfirmModal(true);
                          setLocalDeleteFileId(file.id);
                        }}
                        className="group rounded-full p-2 hover:bg-danger/40"
                      >
                        <FontAwesomeIcon
                          icon="fa-solid fa-trash"
                          className="h-4 w-4 text-danger group-hover:text-danger"
                        />
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Upload More Section */}
        {canUpload && (
          <div className="mt-4 border-t border-strokedark pt-4">
            <div className="flex flex-col items-center">
              <p className="mb-2 text-sm font-medium text-bodydark2">
                Upload more files
              </p>
              <FileUpload
                uploadedFilesProgressData={uploadedFilesProgressData}
                maxFileSize={2048}
                setFormData={setFormData}
              />
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteFileConfirmModal && (
        <ConfirmModal
          confirmText="Are you sure you want to delete this file?"
          setModalClose={() => setShowDeleteFileConfirmModal(false)}
          setFormYes={() => {
            setDeleteFileId(localDeleteFileId);
            setShowDeleteFileConfirmModal(false);
          }}
        />
      )}
    </>
  );
};

export default UploadedSessions;
