import{t as s,g as o,P as n}from"./aws-s3-5653d8cf.js";import{P as r}from"./dashboard-9ed0e038.js";import{y as t}from"../@fullcalendar/core-ff88745d.js";import{U as a}from"./core-5d4a6f29.js";const l={strings:{pluginNameOneDrive:"OneDrive"}},h={version:"3.4.0"};class p extends a{constructor(i,e){super(i,e),this.type="acquirer",this.files=[],this.storage=this.opts.storage||s,this.id=this.opts.id||"OneDrive",this.icon=()=>t("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},t("g",{fill:"none",fillRule:"nonzero"},t("path",{d:"M13.39 12.888l4.618 2.747 2.752-1.15a4.478 4.478 0 012.073-.352 6.858 6.858 0 00-5.527-5.04 6.895 6.895 0 00-6.876 2.982l.07-.002a5.5 5.5 0 012.89.815z",fill:"#0364B8"}),t("path",{d:"M13.39 12.887v.001a5.5 5.5 0 00-2.89-.815l-.07.002a5.502 5.502 0 00-4.822 2.964 5.43 5.43 0 00.38 5.62l4.073-1.702 1.81-.757 4.032-1.685 2.105-.88-4.619-2.748z",fill:"#0078D4"}),t("path",{d:"M22.833 14.133a4.479 4.479 0 00-2.073.352l-2.752 1.15.798.475 2.616 1.556 1.141.68 3.902 2.321a4.413 4.413 0 00-.022-4.25 4.471 4.471 0 00-3.61-2.284z",fill:"#1490DF"}),t("path",{d:"M22.563 18.346l-1.141-.68-2.616-1.556-.798-.475-2.105.88L11.87 18.2l-1.81.757-4.073 1.702A5.503 5.503 0 0010.5 23h12.031a4.472 4.472 0 003.934-2.333l-3.902-2.321z",fill:"#28A8EA"}))),this.opts.companionAllowedHosts=o(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new n(i,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionKeysParams:this.opts.companionKeysParams,companionCookiesRule:this.opts.companionCookiesRule,provider:"onedrive",pluginId:this.id,supportsRefreshToken:!1}),this.defaultLocale=l,this.i18nInit(),this.title=this.i18n("pluginNameOneDrive"),this.onFirstRender=this.onFirstRender.bind(this),this.render=this.render.bind(this)}install(){this.view=new r(this,{provider:this.provider,loadAllFiles:!0,virtualList:!0});const{target:i}=this.opts;i&&this.mount(i,this)}uninstall(){this.view.tearDown(),this.unmount()}async onFirstRender(){await Promise.all([this.provider.fetchPreAuthToken(),this.view.getFolder()])}render(i){return this.view.render(i)}}p.VERSION=h.version;export{p as O};
