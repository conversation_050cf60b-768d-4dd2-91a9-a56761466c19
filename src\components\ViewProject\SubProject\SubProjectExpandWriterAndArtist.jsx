import React from "react";
import SimpleFileUpload from "Components/FileUpload/SimpleFileUpload";
import UploadedAdminInstrumentals from "Components/PublicWorkOrder/WriterWorkOrder/UploadedAdminInstrumentals2";
import UploadedAdminInstrumentals2 from "Components/PublicWorkOrder/WriterWorkOrder/UploadedAdminInstrumentals2";

const SubProjectExpandWriterAndArtist = ({
  adminWriterInstrumentals,
  setDeleteFileId,
  handleInstrumentalUploads,
  uploadedFilesProgressData = {},
}) => {
  const [activeTab, setActiveTab] = React.useState("instrumentals");

  return (
    <div className="shadow-default w-full rounded border border-strokedark bg-boxdark p-4">
      {/* Tabs Navigation */}
      <div className="mb-4 flex gap-2 border-b border-stroke">
        <button
          className={`px-4 py-2 text-sm font-semibold ${
            activeTab === "instrumentals"
              ? "border-b-2 border-white text-primary"
              : "text-bodydark hover:text-white"
          }`}
          onClick={() => setActiveTab("instrumentals")}
        >
          Instrumental Files
        </button>
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {activeTab === "instrumentals" && (
          <div className="custom-overflow h-[220px] overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
            <div className="space-y-4">
              {/* Upload Section */}
              <div className="border-b border-stroke pb-4">
                <SimpleFileUpload
                  maxFileSize={15}
                  label={"Instrumental"}
                  setFormData={handleInstrumentalUploads}
                  uploadedFilesProgressData={uploadedFilesProgressData}
                />
              </div>

              {/* Uploaded Files Section */}
              {adminWriterInstrumentals &&
              adminWriterInstrumentals.length > 0 ? (
                <div className="pt-2">
                  <UploadedAdminInstrumentals2
                    uploadedFiles={adminWriterInstrumentals}
                    setDeleteFileId={setDeleteFileId}
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center py-4">
                  <span className="text-sm text-bodydark2">
                    No instrumental files found.
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubProjectExpandWriterAndArtist;
