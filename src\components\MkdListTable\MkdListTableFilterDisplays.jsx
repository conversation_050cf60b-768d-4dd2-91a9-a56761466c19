import React from "react";
import { Bi<PERSON><PERSON>erAlt, BiSearch } from "react-icons/bi";
import { ColumnIcon, FilterIcon, SortIcon } from "Assets/svgs";
export const displayTypes = {
  ROWS: "rows",
  COLUMNS: "columns",
  FILTER: "filter",
  SORT: "sort",
};
const MkdListTableFilterDisplays = ({
  columns = [],
  columnData = null,
  selectedOptions = [],
  setOpenFilter = null,
  setOpenColumns = null,
  display = [displayTypes.FILTER],
}) => {
  return (
    <div className="flex w-fit items-center justify-start gap-3">
      {display.includes(displayTypes.ROWS) ? (
        <button type="button" className="flex items-center gap-2">
          <ColumnIcon />
          <span>2000/2005</span>
          <span>Rows</span>
        </button>
      ) : null}
      {display.includes(displayTypes.COLUMNS) ? (
        <button
          type="button"
          onClick={() => setOpenColumns && setOpenColumns()}
          className="flex items-center gap-2"
        >
          <ColumnIcon />
          <span>
            {
              columns.filter(
                (item) =>
                  !["Row", "Action"].includes(item?.header) &&
                  item?.selected_column
              ).length
            }
            /
            {
              columns.filter(
                (item) => !["Row", "Action"].includes(item?.header)
              ).length
            }
          </span>
          <span>Columns</span>
        </button>
      ) : null}
      {display.includes(displayTypes.FILTER) ? (
        <button
          type="button"
          className="flex cursor-pointer items-center justify-between gap-2 rounded-md px-3 py-1"
          onClick={() => setOpenFilter && setOpenFilter()}
        >
          <FilterIcon />

          <span className="grow">Filters</span>
          {selectedOptions?.length > 0 && (
            <span className="flex !h-6 !w-6 items-center justify-center rounded-full bg-gray-800 text-start  text-white">
              {selectedOptions?.length > 0 ? selectedOptions?.length : null}
            </span>
          )}
        </button>
      ) : null}
      {display.includes(displayTypes.SORT) ? (
        <button type="button" className="flex items-center gap-2">
          <SortIcon />
          <span>Sort</span>
        </button>
      ) : null}
    </div>
  );
};

export default MkdListTableFilterDisplays;
