// src/components/SuccessModal.jsx
import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const SuccessModal = ({ isOpen, onClose, message }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      <div className="shadow-default w-full max-w-md transform rounded border border-strokedark bg-boxdark transition-all">
        {/* Modal Header */}
        <div className="flex items-center justify-between border-b border-stroke px-6 py-4">
          <div className="flex items-center gap-3">
            <FontAwesomeIcon
              icon="fa-solid fa-check-circle"
              className="text-xl text-primary"
            />
            <h3 className="text-xl font-medium text-white">Success</h3>
          </div>
          <button onClick={onClose} className="hover:text-primary">
            <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
          </button>
        </div>

        {/* Content Section */}
        <div className="px-6 py-4">
          <p className="text-base text-white">{message}</p>
        </div>

        {/* Footer */}
        <div className="border-t border-stroke px-6 py-4">
          <button
            onClick={onClose}
            className="flex w-full items-center justify-center rounded-sm bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
          >
            SUCCESS
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
