import { MkdDebounceInput } from "Components/MkdDebounceInput";
import React, { useState } from "react";
import { AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import MkdListTableFilterOptions from "./MkdListTableFilterOptions";
import { LazyLoad } from "Components/LazyLoad";
import { IoSearchCircleOutline, IoSearchCircleSharp } from "react-icons/io5";

import FilterJoinDropdown from "./FilterJoinDropdown";

const MkdListTableFilterDropdown = ({
  onSubmit,
  columns = [],
  selectedOptions = [],
  onColumnClick = null,
  setOptionValue = null,
  setSelectedOptions = null,
  onOptionValueChange = null,
}) => {
  const [showFilterOptions, setShowFilterOptions] = useState(false);

  return (
    <div className="filter-form-holder absolute left-0 top-[80%] z-[9999999] mt-4 w-[34.1875rem] min-w-[34.1875rem] max-w-[34.1875rem] rounded-md border border-gray-200 bg-white p-5 shadow-xl">
      <div
      // onSubmit={(e) => {
      //   e.preventDefault();
      //   console.log("e >>", e);
      //   if (onSubmit) {
      //     onSubmit();
      //   }
      // }}
      >
        {selectedOptions?.map((option, index) => (
          <div
            key={index}
            className="mb-2 grid w-full grid-cols-[30%_30%_30%_5%] items-center justify-between text-gray-600"
          >
            <div className="h-[2.5rem] rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none">
              {option?.accessor}
            </div>
            <select
              value={option?.operator}
              className="rounded-md border-none appearance-none outline-0"
              onChange={(e) => {
                setOptionValue &&
                  setOptionValue("operator", e.target.value, option?.uid);
              }}
            >
              <option value=""></option>
              <option value="eq">equals</option>
              <option value="cs">contains</option>
              <option value="sw">start with</option>
              <option value="ew">ends with</option>
              <option value="lt">lower than</option>
              <option value="le">lower or equal</option>
              <option value="ge">greater or equal</option>
              <option value="gt">greater than</option>
              <option value="bt">between</option>
              <option value="in">in</option>
              <option value="is">is null</option>
            </select>

            {columns?.length ? (
              <>
                {columns.map((columnData, columnDataIndex) => {
                  if (
                    columnData?.accessor === option?.accessor ||
                    columnData?.header === option?.accessor
                  ) {
                    if (columnData?.mappingExist) {
                      return (
                        <>
                          <select
                            className="rounded-md border-none appearance-none outline-0"
                            onChange={(e) => {
                              setOptionValue &&
                                setOptionValue(
                                  "value",
                                  e.target.value,
                                  option?.uid
                                );
                            }}
                            value={option?.value}
                          >
                            <option
                              value={""}
                              selected={!option?.value}
                            ></option>
                            {Object.keys(columnData?.mappings).map(
                              (columnDataKey, index) => (
                                <option
                                  key={index}
                                  value={columnDataKey}
                                  selected={columnDataKey === option?.value}
                                >
                                  {columnData?.mappings[columnDataKey]}
                                </option>
                              )
                            )}
                          </select>
                        </>
                      );
                    }

                    if (columnData?.join) {
                      return (
                        <FilterJoinDropdown
                          columnData={columnData}
                          option={option}
                          setOptionValue={setOptionValue}
                          key={columnDataIndex}
                        />
                      );
                    }
                    return (
                      <LazyLoad>
                        <MkdDebounceInput
                          type="text"
                          placeholder="Enter value"
                          setValue={(value) => {
                            setOptionValue &&
                              setOptionValue("value", value, option?.uid);
                          }}
                          value={option?.value}
                          showIcon={false}
                          className="!rounded-md !border !border-gray-700 !px-3 !py-2 !leading-tight !text-gray-700 !outline-none"
                          onReady={(value) => {
                            // setOptionValue && setOptionValue("value", value, option?.uid);
                          }}
                        />
                      </LazyLoad>
                    );
                  } else {
                    return null;
                  }
                })}
              </>
            ) : null}
            {/* <p className="text-xs italic text-red-500">
               {errors.id?.message}
             </p> */}

            <RiDeleteBin5Line
              className="text-2xl cursor-pointer"
              onClick={() => {
                setSelectedOptions((prev) =>
                  prev.filter((op) => op.uid !== option?.uid)
                );
              }}
            />
          </div>
        ))}
      </div>
      <div className="flex relative justify-between items-center font-semibold search-buttons">
        <div
          // type="submit"
          className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out "
          onClick={() => {
            setShowFilterOptions((prev) => !prev);
          }}
        >
          <AiOutlinePlus />
          Add filter
        </div>

        {showFilterOptions && (
          <LazyLoad>
            <MkdListTableFilterOptions
              onColumnClick={(data) => onColumnClick && onColumnClick(data)}
              setShowFilterOptions={setShowFilterOptions}
              columns={columns}
              selectedOptions={selectedOptions}
            />
          </LazyLoad>
        )}

        {/* {selectedOptions.length > 0 && (
        <div
          // type="reset"
          onClick={() => {
            setSelectedOptions([]);
            setFilterConditions([]);
          }}
          className="inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out"
        >
          Clear all filter
        </div>
      )} */}

        <button
          type="button"
          onClick={() => {
            if (onSubmit) {
              onSubmit();
            }
          }}
          className="mr-2 flex w-fit cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out "
        >
          <svg
            className="w-4 h-4 text-gray-500 dark:text-gray-400"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 20 20"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
            />
          </svg>
          Search
        </button>
      </div>
    </div>
  );
};

export default MkdListTableFilterDropdown;
