import React from "react";
import { useParams } from "react-router-dom";
import { tokenExpireError } from "../authContext";
import { GlobalContext } from "../globalContext";
import MkdSDK from "../utils/MkdSDK";

let sdk = new MkdSDK();

const ViewWorkOrderPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const { dispatch } = React.useContext(GlobalContext);
  const [viewModel, setViewModel] = React.useState({});

  const params = useParams();

  React.useEffect(function () {
    (async function () {
      try {
        sdk.setTable("work_order");
        const result = await sdk.callRestAPI({ id: Number(params?.id) }, "GET");
        if (!result.error) {
          setViewModel(result.model);
        }
      } catch (error) {
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  return (
    <div className="mx-auto rounded p-5 shadow-md">
      <h4 className="text-2xl font-medium">View Work Order</h4>

      <div className="mb-4 mt-4">
        <div className="mb-4 flex">
          <div className="flex-1">Writer Id</div>
          <div className="flex-1">{viewModel?.writer_id}</div>
        </div>
      </div>

      <div className="mb-4 mt-4">
        <div className="mb-4 flex">
          <div className="flex-1">Artist Id</div>
          <div className="flex-1">{viewModel?.artist_id}</div>
        </div>
      </div>

      <div className="mb-4 mt-4">
        <div className="mb-4 flex">
          <div className="flex-1">Engineer Id</div>
          <div className="flex-1">{viewModel?.engineer_id}</div>
        </div>
      </div>
    </div>
  );
};

export default ViewWorkOrderPage;
