import axios from "axios";
import MkdSD<PERSON> from "../utils/MkdSDK";

let sdk = new MkdSDK();

export const addWorkOrderAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/add`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllWorkOrderAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/work_order/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getWorkOrderDetailsAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/view/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateWorkOrderAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteWorkOrderAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const addNoteAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/note`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateStatusAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/status`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const getWorkOrderPublicDetailsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/details`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateLyricsPublicAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/lyrics`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const uploadFilesAPI = async (formData) => {
  try {
    const uri = `https://app.equalityrecords.com/v3/api/custom/equality_record/work_order/public/upload_files`;
    const res = await axios.post(uri, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        "x-project":
          "ZXF1YWxpdHlyZWNvcmQ6cDV3YjJnd2M5aWVyZjhzem5ldDZibnNjcTA3bnVyYQ==",
      },
    });

    return res.data;
  } catch (error) {
    return error;
  }
};

export const uploadS3FilesProgressAPI = async (formData, setProgress) => {
  try {
    const uri = `https://app.equalityrecords.com/v2/api/lambda/s3/uploads/only/public`;
    const res = await axios.post(uri, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        "x-project":
          "ZXF1YWxpdHlyZWNvcmQ6cDV3YjJnd2M5aWVyZjhzem5ldDZibnNjcTA3bnVyYQ==",
      },
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        setProgress(percentCompleted);
      },
    });

    return res.data;
  } catch (error) {
    throw error;
  }
};

export const uploadS3FilesAPI = async (formData) => {
  try {
    const uri = `https://app.equalityrecords.com/v2/api/lambda/s3/uploads/only/public`;
    const res = await axios.post(uri, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        "x-project":
          "ZXF1YWxpdHlyZWNvcmQ6cDV3YjJnd2M5aWVyZjhzem5ldDZibnNjcTA3bnVyYQ==",
      },
    });

    return res.data;
  } catch (error) {
    return error;
  }
};

export const uploadFilesDataAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/upload_files_data`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllFilesByWorkOrderIdAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/files/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllWriterFilesByWorkOrderIdAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/writer/files/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllArtistFilesByWorkOrderIdAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/artist/files/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllEngineerFilesByWorkOrderIdAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/engineer/files/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllProducerFilesByWorkOrderIdAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/producer/files/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "GET");
    return res;
  } catch (error) {
    return error;
  }
};

/**
 * payload = {
 *  url: string,
 * }
 */
export const deleteS3FileAPI = async (url) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/public/file/s3/delete`;
    const res = await sdk.callRawAPI(
      uri,
      {
        url,
      },
      "DELETE"
    );
    return res;
  } catch (error) {
    return error;
  }
};

export const updateWorkOrderWriterAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/update/writer`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateWorkOrderArtistAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/update/artist`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateWorkOrderEngineerAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/work_order/update/engineer`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};
